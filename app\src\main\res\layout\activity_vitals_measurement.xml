<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ble.NewVitalsActivity">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/measurementView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent">

        <LinearLayout
            android:id="@+id/vitalTitle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/vitalCardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                app:cardCornerRadius="@dimen/d_12_d"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <TextView
                        android:id="@+id/measurementType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/lato_bold"
                        android:padding="@dimen/d_10_d"
                        android:textColor="@color/black"
                        android:textSize="@dimen/d_30_s"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/progress_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="visible"
            android:weightSum="2"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <ProgressBar
                android:id="@+id/progress_bar_dialog"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1.5" />

            <TextView
                android:id="@+id/statusTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="0.5"
                android:fontFamily="@font/lato_regular"
                android:gravity="center"
                android:text="@string/statusTitle"
                android:textColor="@color/black"
                android:textSize="25sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/bgLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/d_5_d"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/bgValueTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="Blood Glucose"
                    android:textColor="@color/black"
                    android:textSize="25sp" />

                <TextView
                    android:id="@+id/bgValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="35sp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/bgExtraLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            android:weightSum="4"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fontFamily="@font/lato_regular"
                android:gravity="center_vertical"
                android:text="@string/value"
                android:textColor="@color/black" />

            <TextView
                android:id="@+id/bgExtraValue"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fontFamily="@font/lato_bold"
                android:gravity="center_vertical"
                android:textAppearance="?android:attr/textAppearanceLarge" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fontFamily="@font/lato_bold"
                android:gravity="center_vertical"
                android:text="@string/state" />

            <TextView
                android:id="@+id/bgExtraState"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:fontFamily="@font/lato_regular"
                android:textAppearance="?android:attr/textAppearanceLarge" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/bpLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="1dp"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            android:weightSum="3"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/sysValueTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/sys_value"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/sysValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/diaValueTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/dia_value"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/diaValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/pulseValueTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/pul_value"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/pulseValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/thermometerLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="gone"
            android:weightSum="2"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <TextView
                android:id="@+id/thermometerValueTitle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:textColor="@color/black"
                android:textSize="25sp" />

            <TextView
                android:id="@+id/thermometerValue"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:fontFamily="@font/lato_regular"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="35sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/weightScaleLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/d_5_d"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/weightTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/weight_value"
                    android:textColor="@color/black"
                    android:textSize="25sp" />

                <TextView
                    android:id="@+id/weightValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/bmiTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/bmi_value"
                    android:textColor="@color/black"
                    android:textSize="25sp" />

                <TextView
                    android:id="@+id/bmiValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/spO2Layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/d_5_d"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/spO2Title"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/spO2_value"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/spO2Value"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:weightSum="2">

                <TextView
                    android:id="@+id/spO2PulseTitle"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start|center_vertical"
                    android:text="@string/pul_value"
                    android:textColor="@color/black"
                    android:textSize="22sp" />

                <TextView
                    android:id="@+id/spO2PulseValue"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="end|center_vertical"
                    android:textColor="@color/black"
                    android:textSize="30sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/secondLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/measurementView">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="10dp"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/dateTimeTv"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:fontFamily="@font/lato_regular"
                android:gravity="start|center_vertical"
                android:text="Date Time"
                android:visibility="gone"
                android:textColor="@color/black"
                android:textSize="22sp" />

            <TextView
                android:id="@+id/dateTimeValue"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:fontFamily="@font/lato_regular"
                android:gravity="start|center_vertical"
                android:textColor="@color/black"
                android:textSize="30sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/instructionLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="25dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/secondLayout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="10dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/instruction"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fontFamily="@font/lato_regular"
                android:gravity="start|center_vertical"
                android:text="@string/instruction"
                android:textColor="@color/black"
                android:textSize="20sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>