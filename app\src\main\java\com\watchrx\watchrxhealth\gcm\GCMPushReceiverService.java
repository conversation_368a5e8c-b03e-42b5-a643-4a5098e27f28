package com.watchrx.watchrxhealth.gcm;

import static com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService.REGISTRATION_ERROR;
import static com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService.REGISTRATION_SUCCESS;

import android.content.Context;
import android.content.Intent;
import android.os.PowerManager;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.twilio.voice.CallException;
import com.twilio.voice.CallInvite;
import com.twilio.voice.CancelledCallInvite;
import com.twilio.voice.MessageListener;
import com.twilio.voice.Voice;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.receivers.MessageHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.voip.VoiceManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;

public class GCMPushReceiverService extends FirebaseMessagingService {

    private static final String TAG = "GCMPushReceiverService";

    @Override
    public void onMessageReceived(@NonNull RemoteMessage message) {
        Log.i(TAG, message.toString());

        String from = message.getFrom();
        Map<String, String> data = message.getData();
        Log.e("FCM PAYLOAD", "" + data);

        Context context = getApplicationContext();
        String realData = "";
        for (Object key : data.keySet()) {
            Log.d("GCMPushReceiverService", key + " = \"" + data.get(key) + "\"");
            realData = data.get(key);
        }
        LogUtils.debug("FCM NOTIFICATION " + realData);
        Log.d(TAG, data.get("twi_message_type") + data.containsKey("twi_account_sid"));
        if ("twilio.voice.call".equals(data.get("twi_message_type"))) {
            long receivedTime = System.currentTimeMillis();
            Voice.handleMessage(context, data, new MessageListener() {
                @Override
                public void onCallInvite(@NonNull CallInvite callInvite) {
                    Log.d(TAG, "Received CallInvite at " + receivedTime + ", from: " + callInvite.getFrom());
                    VoiceManager.getInstance(context).handleCallInvite(callInvite);
                }

                @Override
                public void onCancelledCallInvite(@NonNull CancelledCallInvite cancelledCallInvite, @Nullable CallException callException) {
                    Log.d(TAG, "Received CancelledCallInvite for callSid: " + cancelledCallInvite.getCallSid() + " at " + System.currentTimeMillis());
                    NotificationHelper.clearIncomingCallNotification(context);
                }
            });
        }


        if (!WatchApp.isInForeground()) {
            PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            boolean isScreenOn = pm.isInteractive();
            if (!isScreenOn) {
                Log.d(TAG, "Screen is OFF. Waking device.");
                PowerManager.WakeLock wakeLock = pm.newWakeLock(
                        PowerManager.FULL_WAKE_LOCK | PowerManager.ACQUIRE_CAUSES_WAKEUP | PowerManager.ON_AFTER_RELEASE,
                        "watchrx:screenWakeLock"
                );
                wakeLock.acquire(10_000);
                wakeLock.release();

                PowerManager.WakeLock cpuWake = pm.newWakeLock(
                        PowerManager.PARTIAL_WAKE_LOCK,
                        "watchrx:cpuWakeLock"
                );
                cpuWake.acquire(10_000);
                cpuWake.release();
            }
            handleBackgroundNotificationsFromServer(realData, context);
        } else {
            MessageHandler.processIncomingMessage(context, realData);
        }
    }


    public void handleBackgroundNotificationsFromServer(String message, Context context) {
        try {
            JSONObject jsonMessage = new JSONObject(message);
            LogUtils.debug("i got message from GCM->" + message);
            String messageType = jsonMessage.getString("messageType");
            if (messageType.equalsIgnoreCase("UpdatedMedicationInfo")) {
                MessageHandler.processIncomingMedicationUpdate(context);
            } else if (messageType.equalsIgnoreCase("nurseReminder")) {
                try {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setBeforeOrAfterFood(jsonMessage.getString("missedBeforeOrAfterFood"));
                    info.setTimeSlot(jsonMessage.getString("missedTimeSlot"));
                    info.setAlertId(jsonMessage.getString("alertId"));
                    info.setCaregiverName(jsonMessage.getString("caregiverName"));
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("ReminderActivity");
                    Globals.priorityQueue.add(info);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                final NotificationHelper notificationHelper = new NotificationHelper(this);
                notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
            } else if (messageType.equalsIgnoreCase("nurseOnTheWay")) {
                try {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setTimeSlot(jsonMessage.getString("status"));
                    info.setCaregiverName(jsonMessage.getString("caregiverName"));
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("NurseOnTheWayActivity");
                    Globals.priorityQueue.add(info);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                final NotificationHelper notificationHelper = new NotificationHelper(this);
                notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
            } else if (messageType.equalsIgnoreCase("softwareUpgrade")) {
                MessageHandler.processIncomingSoftwareUpdate(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("heartBeat")) {
                MessageHandler.processIncomingWatchAliveStatus(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("logupdate")) {
                MessageHandler.processIncomingLogUpdate(context);
            } else if (messageType.equalsIgnoreCase("subscriptionCancelled") || messageType.equalsIgnoreCase("WatchUnAssign")) {
                MessageHandler.processSubscriptionFailed(context);
            } else if (messageType.equalsIgnoreCase("GPS Status")) {
                MessageHandler.processIncomingGPSEnableDisabledStatus(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("Tracking Status")) {
                MessageHandler.processIncomingGPSTrackingEnableDisabledStatus(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("AddressChanged")) {
                MessageHandler.processIncomingChangedAddressValue(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("sendHearRate")) {
                ActivityInfoForQueue info = new ActivityInfoForQueue();
                info.setContext(context);
                info.setNanoSecTime(System.nanoTime());
                info.setToActivityName("HeartRateActivity");
                Globals.priorityQueue.add(info);
                final NotificationHelper notificationHelper = new NotificationHelper(this);
                notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
            } else if (messageType.equalsIgnoreCase("textMessage")) {

                ActivityInfoForQueue info = new ActivityInfoForQueue();
                info.setContext(context);
                info.setNanoSecTime(System.nanoTime());
                info.setToActivityName("ChatActivity");
                Globals.priorityQueue.add(info);
                final NotificationHelper notificationHelper = new NotificationHelper(this);
                notificationHelper.notify("You have received message from care manager, open the app.", "WatchRx Notification");
            } else if (messageType.equalsIgnoreCase("pedometerAlert")) {
            } else if (messageType.equalsIgnoreCase("ScheduledMessageUpdated")) {
                MessageHandler.processScheduledTextMessage(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("vital")) {
                handleVitalNotification(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("videoRoom")) {
                long roomId = jsonMessage.optLong("roomId");
                LogUtils.debug("RoomId :" + roomId);
                ActivityInfoForQueue info = new ActivityInfoForQueue();
                info.setTriggerAt(roomId);
                info.setContext(context);
                info.setNanoSecTime(System.nanoTime());
                info.setToActivityName("VideoActivity");
                Globals.priorityQueue.add(info);
                final NotificationHelper notificationHelper = new NotificationHelper(context);
                notificationHelper.videoCallNotification("You have an video call invite, Please click here to join.",
                        "WatchRx Video Call Notification");
            } else if (messageType.equalsIgnoreCase("zoomVideoCall")) {
                long roomId = jsonMessage.optLong("roomId");
                String program = jsonMessage.optString("program", "rpm");
                LogUtils.debug("RoomId :" + roomId);
                ActivityInfoForQueue info = new ActivityInfoForQueue();
                info.setTriggerAt(roomId);
                info.setContext(context);
                info.setNanoSecTime(System.nanoTime());
                info.setBeforeOrAfterFood(program);
                info.setToActivityName("ZoomVideoCallScreen");
                Globals.priorityQueue.add(info);
                final NotificationHelper notificationHelper = new NotificationHelper(context);
                notificationHelper.showIncomingCallNotification(context);
            } else {
                LogUtils.debug("message type is not recognised" + message);
            }
        } catch (JSONException e) {
            Log.d("FCM Service", "Incoming Message Malformed: " + message);
        }
    }

    private void handleVitalNotification(Context context, JSONObject jsonMessage) {
        try {
            String status = jsonMessage.optString("status");
            if (status.equalsIgnoreCase("collectNow")) {
                if (jsonMessage.optString("deviceName").equalsIgnoreCase("Watch")
                        && jsonMessage.optString("vitalTypeName").equalsIgnoreCase("Heart Rate")) {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("HeartRateActivity");
                    info.setAlertId("0");
                    Globals.priorityQueue.add(info);
                    final NotificationHelper notificationHelper = new NotificationHelper(this);
                    notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                } else {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setAlertId("0");
                    info.setBeforeOrAfterFood(jsonMessage.optString("deviceName"));
                    info.setCaregiverName(jsonMessage.optString("vitalTypeName"));
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("VitalActivity");
                    Globals.priorityQueue.add(info);
                    final NotificationHelper notificationHelper = new NotificationHelper(this);
                    notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                }
            } else {
                MessageHandler.processVitalConfiguration(context, jsonMessage);
            }
        } catch (Exception ignored) {
        }
    }

    @Override
    public void onNewToken(String s) {
        super.onNewToken(s);
        Log.e("NEW_TOKEN", s);
//        VoiceManager.getInstance(getApplicationContext());
        registerGcm(s);
    }

    private void registerGcm(String token) {
        Intent registrationComplete;
        try {
            //on registration complete creating intent with success
            registrationComplete = new Intent(REGISTRATION_SUCCESS);
            //Putting the token to the intent
            registrationComplete.putExtra("token", token);
            Log.d("GCm", "GCM TOKEN =" + token);
        } catch (Exception e) {
            registrationComplete = new Intent(REGISTRATION_ERROR);
        }
        //Sending the broadcast that registration is completed
        LocalBroadcastManager.getInstance(this).sendBroadcast(registrationComplete);
    }
}
