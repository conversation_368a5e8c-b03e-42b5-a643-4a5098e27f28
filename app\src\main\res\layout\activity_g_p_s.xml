<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".GPSActivity">

    <ImageView
        android:id="@+id/ivGpsCrossed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/geo_fecne"
        app:layout_constraintBottom_toTopOf="@id/tvGpsCrossedMsg"
        app:layout_constraintTop_toBottomOf="@id/tvGpsCrossedTitle" />


    <TextView
        android:id="@+id/tvGpsCrossedTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/d_30_d"
        android:fontFamily="@font/lato_bold"
        android:gravity="center_horizontal"
        android:text="@string/gps_status"
        android:textColor="@color/black"
        android:textSize="@dimen/d_30_s"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvGpsCrossedMsg"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/d_30_d"
        android:fontFamily="@font/lato_bold"
        android:gravity="center_horizontal"
        android:textColor="@color/black"
        android:textSize="@dimen/d_30_s"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1.0"
        tools:layout_editor_absoluteX="0dp" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />


</androidx.constraintlayout.widget.ConstraintLayout>