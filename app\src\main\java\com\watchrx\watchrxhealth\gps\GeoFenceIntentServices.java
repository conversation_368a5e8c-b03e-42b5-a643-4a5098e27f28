package com.watchrx.watchrxhealth.gps;

import android.app.IntentService;
import android.content.Intent;
import android.location.Location;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.gms.location.Geofence;
import com.google.android.gms.location.GeofencingEvent;
import com.watchrx.watchrxhealth.GPSActivity;
import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.PhoneNumbers;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.List;

public class GeoFenceIntentServices extends IntentService {

    protected static final String TAG = "GeoFenceIntentServices";

    public GeoFenceIntentServices() {
        super(TAG);
    }

    @Override
    public void onCreate() {
        super.onCreate();
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        final GeofencingEvent geofencingEvent = GeofencingEvent.fromIntent(intent);
        if (geofencingEvent.hasError()) {
            String errorMessage = GeoFenceErrorMessages.getErrorString(this,
                    geofencingEvent.getErrorCode());
            LogUtils.debug("GeoFenceIntentServices error" + errorMessage);
            return;
        }
        int geofenceTrasition = geofencingEvent.getGeofenceTransition();
        List<PhoneNumbers> phoneNumbersList = PhoneNumbers.getFromDB();
        String number_to_call = "";
        String caregiver_name = "";
        if (phoneNumbersList.size() != 0) {
            number_to_call = phoneNumbersList.get(0).getPhonenumber();
            caregiver_name = phoneNumbersList.get(0).getContactName();
        }
        Location location = geofencingEvent.getTriggeringLocation();
        double latitude = 0.0;
        double longitude = 0.0;

        double dist = 0.0;
        PatientDetails details = PatientDetails.getFromDB();
        double radius = Double.parseDouble(details.getRadius());
        if (details.getRadius() != null && details.getLatLong() != null) {
            String[] latlngData = details.getLatLong().split(",");
            latitude = Double.parseDouble(latlngData[0].split(":")[1]);
            longitude = Double.parseDouble(latlngData[1].split(":")[1]);
            if (latitude != 0.0 && longitude != 0.0) {
                dist = Distance.distance(location.getLatitude(), latitude, location.getLongitude(), longitude, 0, 0);
            } else {
                dist = Distance.distance(location.getLatitude(), Globals.latitude, location.getLongitude(), Globals.longitude, 0, 0);
            }
        }

        if (geofenceTrasition == Geofence.GEOFENCE_TRANSITION_ENTER) {
            if (dist >= 0 && dist <= radius) {
                if (Globals.isCrossed) {
                    LogUtils.debug("Geo fence event occurs for GEOFENCE_TRANSITION_ENTER distance between added fence latitude :" + latitude + "Longitude :" + longitude +
                            "and current fence latitude:" + location.getLatitude() + " Longitude is :" + location.getLongitude() + "Distance is:" + dist);
                    Globals.isCrossed = false;
                    Globals.firstTimeCrossed = false;
                    Intent alertCount = new Intent(MainActivity.GPS_CROSSED_EVENT);
                    LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
                    GeneralUtils.stopSpeaking();
                    CommUtils.sendBatteryLogToServer(WatchApp.getContext(), "GPS Alert:Back Home", "You are back home fence Latitude:" + location.getLatitude() + " Longitude:" + location.getLongitude());
                    Intent crossedIntent = new Intent(this, GPSActivity.class);
                    crossedIntent.putExtra("CaregiverName", caregiver_name);
                    crossedIntent.putExtra("CaregiverNumber", number_to_call);
                    crossedIntent.putExtra("From", "Back");
                    crossedIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_FROM_BACKGROUND);
                    GeoFenceIntentServices.this.startActivity(crossedIntent);
                }
            }
        } else if (geofenceTrasition == Geofence.GEOFENCE_TRANSITION_EXIT) {
            if (dist >= radius) {
                if ((!Globals.firstTimeCrossed)) {
                    Globals.firstTimeCrossed = true;
                    Globals.isCrossed = true;
                    Intent alertCount = new Intent(MainActivity.GPS_CROSSED_EVENT);
                    LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
                    LogUtils.debug("Geo fence event occurs for GEOFENCE_TRANSITION_EXIT distance between added fence latitude :" + latitude + "Longitude :" + longitude +
                            "and current fence latitude:" + location.getLatitude() + " Longitude is :" + location.getLongitude() + "Distance is:" + dist);
                    CommUtils.sendBatteryLogToServer(WatchApp.getContext(), "GPS Alert:Geo Fence Crossed", "User crossed the fence Latitude:" + location.getLatitude() + " Longitude:" + location.getLongitude());
                    Intent crossedIntent = new Intent(GeoFenceIntentServices.this, GPSActivity.class);
                    crossedIntent.putExtra("CaregiverName", caregiver_name);
                    crossedIntent.putExtra("CaregiverNumber", number_to_call);
                    crossedIntent.putExtra("From", "Crossed");
                    crossedIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_FROM_BACKGROUND);
                    GeoFenceIntentServices.this.startActivity(crossedIntent);
                }
            }
        }
    }
}

