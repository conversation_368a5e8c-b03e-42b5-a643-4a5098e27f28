package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class Alerts {

    public static final String TABLE_ALERTS_DETAILS = "Alerts";
    private static final String COL_ALERT_TYPE = "AlertType";
    private static final String COL_ALERT_MESSAGE = "AlertMessage";
    private static final String COL_ALERT_DATE = "AlertDate";
    private static final String COL_MEDICINE_NAME = "MedicineName";
    private static final String COL_ROWID = "_id";

    static final String CREATE_ALERTS_DETAILS_TABLE =
            "CREATE TABLE " + TABLE_ALERTS_DETAILS +
                    "(" +
                    COL_ROWID + " integer primary key autoincrement," +
                    COL_ALERT_TYPE + " TEXT, " +
                    COL_ALERT_MESSAGE + " TEXT, " +
                    COL_MEDICINE_NAME + " TEXT, " +
                    COL_ALERT_DATE + " TEXT " +
                    ");";

    private static final String[] COLUMNS_ALERTS_DETAILS = {
            COL_ALERT_TYPE,
            COL_ALERT_MESSAGE
    };


    private String alertType;
    private long _id;
    private String alertMessage;
    private String medicineName;

    public String getMedicineName() {
        return medicineName;
    }

    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }


    public String getAlertDate() {
        return alertDate;
    }

    public void setAlertDate(String alertDate) {
        this.alertDate = alertDate;
    }

    private String alertDate;

    public long getRowId() {
        return _id;
    }

    public void setRowId(long rowId) {
        this._id = rowId;
    }

    public String getAlertType() {
        return alertType;
    }

    public void setAlertType(String alertType) {
        this.alertType = alertType;
    }

    public String getAlertMessage() {
        return alertMessage;
    }

    public void setAlertMessage(String alertMessage) {
        this.alertMessage = alertMessage;
    }


    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_ALERTS_DETAILS);
    }

    public static long addToDB(Alerts alerts) {
        //PhoneNumbers.deleteAllRows(); // force only one row in db

        ContentValues values = new ContentValues();
        values.put(COL_ALERT_TYPE, alerts.alertType);
        values.put(COL_ALERT_MESSAGE, alerts.alertMessage);
        values.put(COL_ALERT_DATE, alerts.alertDate);
        values.put(COL_MEDICINE_NAME, alerts.medicineName);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_ALERTS_DETAILS, values);
    }

    public static List<Alerts> getFromDB() {
        List<Alerts> matchingRecords = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_ALERTS_DETAILS, null, null, null, null);
        while (cursor.moveToNext()) {
            Alerts record = new Alerts();

            try {
                record.setRowId(cursor.getLong(cursor.getColumnIndex(COL_ROWID)));
                record.setAlertType(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALERT_TYPE)));
                record.setAlertMessage(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALERT_MESSAGE)));
                record.setAlertDate(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALERT_DATE)));
                record.setMedicineName(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDICINE_NAME)));
                matchingRecords.add(record);
            } catch (Exception e) {
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();
        return matchingRecords;
    }

    public static void updateState(long rowId, String msg) {
        String sql = "UPDATE " + TABLE_ALERTS_DETAILS + " set " + COL_ALERT_MESSAGE + "='" + msg + "' where " + COL_ROWID + "=" + rowId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void deleteRowAlert(String alertType) {
        String whereClause = COL_ALERT_TYPE + "='" + alertType + "'";
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_ALERTS_DETAILS, whereClause, null);
    }

    public static void deleteRowMissedCall(String alertMessage) {
        String whereClause = COL_ALERT_MESSAGE + "='" + alertMessage + "'";
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_ALERTS_DETAILS, whereClause, null);
    }

    public static Alerts getDetailsFromDB(String alertType) {
        Alerts record = new Alerts();
        String whereClause = COL_ALERT_TYPE + "='" + alertType + "'";
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_ALERTS_DETAILS, null, whereClause, null, null);
        while (cursor.moveToNext()) {
            try {
                record.setAlertMessage(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALERT_MESSAGE)));
            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();
        return record;
    }

    public static void deleteRow(String alertType, String alertDate, String alertMessage) {
        String whereClause = COL_ALERT_TYPE + " = \"" + alertType + "\" AND " +
                COL_ALERT_DATE + " = \"" + alertDate + "\" AND " +
                COL_ALERT_MESSAGE + " = \"" + alertMessage + "\"";

        DBAdaptor.getDbAdaptorInstance().delete(TABLE_ALERTS_DETAILS, whereClause, null);
    }
}

