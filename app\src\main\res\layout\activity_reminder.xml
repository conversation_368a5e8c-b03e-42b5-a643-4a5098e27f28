<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_reminder"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ReminderActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:weightSum="3"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/titleLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="5dp"
            android:layout_weight="0.5"
            android:orientation="vertical"
            android:weightSum="1"
            app:layout_constraintBottom_toBottomOf="parent">

            <Button
                android:id="@+id/snoozebutton"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:layout_weight="1"
                android:background="@drawable/button_gradient"
                android:fontFamily="@font/lato_regular"
                android:text="@string/snoozetext"
                android:textSize="30sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/LEDs"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="5dp"
            android:layout_weight="0.5"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/titleLayout">

            <View
                android:id="@+id/circle1"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="@dimen/d_15_d"
                android:background="@drawable/circle"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/circle2"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="@dimen/d_15_d"
                android:background="@drawable/circle"
                app:layout_constraintLeft_toRightOf="@id/circle1"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/circle3"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="@dimen/d_15_d"
                android:background="@drawable/circle"
                app:layout_constraintLeft_toRightOf="@id/circle2"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/circle4"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginTop="@dimen/d_15_d"
                android:background="@drawable/circle"
                app:layout_constraintLeft_toRightOf="@id/circle3"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/speechDetails"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="5dp"
            android:layout_weight="2"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/LEDs">

            <TextView
                android:id="@+id/speech"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="5dp"
                android:fontFamily="@font/lato_regular"
                android:gravity="start"
                android:textSize="25sp" />
        </LinearLayout>

    </LinearLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>