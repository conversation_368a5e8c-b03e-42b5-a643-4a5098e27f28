<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".VitalsGraphActivity">

    <TextView
        android:id="@+id/tvVitalName"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:gravity="center"
        android:text="Blood Pressure"
        android:textColor="@color/black"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/lineChart"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="1dp"
        app:layout_constraintBottom_toTopOf="@id/buttonGroup"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.8"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvVitalName" />

    <LinearLayout
        android:id="@+id/buttonGroup"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintStart_toStartOf="parent">

        <RadioGroup
            android:id="@+id/radioGroupToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- First Button -->
            <RadioButton
                android:id="@+id/radioGraph"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/toggle_selector_left"
                android:button="@android:color/transparent"
                android:checked="true"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp"
                android:text="Graph View"
                android:textColor="@color/toggle_text_selector"
                android:textSize="16sp" />

            <!-- Second Button -->
            <RadioButton
                android:id="@+id/radioTable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/toggle_selector_right"
                android:button="@android:color/transparent"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp"
                android:text="Table View"
                android:textColor="@color/toggle_text_selector"
                android:textSize="16sp" />
        </RadioGroup>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
