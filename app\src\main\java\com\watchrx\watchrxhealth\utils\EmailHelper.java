package com.watchrx.watchrxhealth.utils;

import java.util.regex.Pattern;
import android.util.Patterns;

public class EmailHelper {
    
    // Custom regex pattern for email validation
    private static final String EMAIL_REGEX = 
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

    private static final Pattern pattern = Pattern.compile(EMAIL_REGEX);

    /**
     * Validates an email address using a regex pattern.
     * @param email The email address to validate.
     * @return True if the email is valid, false otherwise.
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return pattern.matcher(email).matches();
    }

    /**
     * Validates an email address using Android's built-in Patterns.
     * @param email The email address to validate.
     * @return True if the email is valid, false otherwise.
     */
    public static boolean isValidEmailAndroid(String email) {
        return email != null && Patterns.EMAIL_ADDRESS.matcher(email).matches();
    }
}
