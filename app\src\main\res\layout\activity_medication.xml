<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/constraintLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white_smoke"
    android:padding="24dp"
    tools:context=".MedicationActivity">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/medicationCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="0dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_meds"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/textView_medicine_name_dosage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@android:color/black"
                android:textSize="26sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textView_dosage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>


    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image_from_database"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="16dp"
        android:scaleType="centerCrop"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/medicationCard"
        app:strokeWidth="3dp" />


    <com.google.android.material.card.MaterialCardView
        android:id="@+id/dosageCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="16dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/image_from_database"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_meds"
            android:gravity="center_vertical"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/textView_strength"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textColor="@android:color/black"
                android:textSize="26sp"
                android:textStyle="bold" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <View
        android:id="@+id/emptyView"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dosageCard" />

    <TextView
        android:id="@+id/tvQuestion"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="@string/have_you_taken_medications"
        android:textColor="@android:color/black"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/emptyView" />

    <View
        android:id="@+id/emptyView2"
        android:layout_width="match_parent"
        android:layout_height="15dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvQuestion" />

    <!-- YES Button -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/button_yes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="16dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/emptyView2"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_green_gradient"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="32dp"
            android:paddingTop="16dp"
            android:paddingEnd="32dp"
            android:paddingBottom="16dp">

            <!-- Text aligned left -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/lato_regular"
                android:text="YES"
                android:textColor="@android:color/white"
                android:textSize="22sp"
                android:textStyle="bold" />

            <!-- Icon aligned right -->
            <FrameLayout
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/bg_icon_semitrans_white">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/dummy_content"
                    android:src="@drawable/yes"
                    app:tint="@android:color/white" />
            </FrameLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>


    <com.google.android.material.card.MaterialCardView
        android:id="@+id/button_no"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="16dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/button_yes"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_red_gradient"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="32dp"
            android:paddingTop="16dp"
            android:paddingEnd="32dp"
            android:paddingBottom="16dp">

            <!-- Text aligned to left -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/lato_regular"
                android:text="NO"
                android:textColor="@android:color/white"
                android:textSize="22sp"
                android:textStyle="bold" />

            <!-- Icon aligned to right -->
            <FrameLayout
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/bg_icon_semitrans_white">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/dummy_content"
                    android:src="@drawable/no"
                    app:tint="@android:color/white" />
            </FrameLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/button_remind"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="16dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/button_no"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_organge_gradient"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="32dp"
            android:paddingTop="16dp"
            android:paddingEnd="32dp"
            android:paddingBottom="16dp">

            <!-- Text aligned to left -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/lato_regular"
                android:text="@string/remind_again"
                android:textColor="@android:color/white"
                android:textSize="22sp"
                android:textStyle="bold" />

            <!-- Icon aligned to right -->
            <FrameLayout
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/bg_icon_semitrans_white">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/dummy_content"
                    android:src="@drawable/later"
                    app:tint="@android:color/white" />
            </FrameLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
</androidx.constraintlayout.widget.ConstraintLayout>
