AWSTemplateFormatVersion: '2010-09-09'
Parameters:
  env:
    Type: String
  authRoleArn:
    Type: String
  unauthRoleArn:
    Type: String
  identityPoolName:
    Type: String
  allowUnauthenticatedIdentities:
    Type: String
  authSelections:
    Type: String
  resourceName:
    Type: String
  sharedId:
    Type: String
Conditions:
  ShouldNotCreateEnvResources:
    Fn::Equals:
      - Ref: env
      - NONE
Resources:
  IdentityPool:
    Type: AWS::Cognito::IdentityPool
    Properties:
      IdentityPoolName:
        Fn::If:
          - ShouldNotCreateEnvResources
          - lexa_identitypool_705857d4
          - Fn::Join:
              - ''
              - - lexa_identitypool_705857d4
                - __
                - Ref: env
      AllowUnauthenticatedIdentities:
        Ref: allowUnauthenticatedIdentities
  IdentityPoolRoleMap:
    Type: AWS::Cognito::IdentityPoolRoleAttachment
    Properties:
      IdentityPoolId:
        Ref: IdentityPool
      Roles:
        unauthenticated:
          Ref: unauthRoleArn
        authenticated:
          Ref: authRoleArn
    DependsOn: IdentityPool
Outputs:
  IdentityPoolId:
    Value:
      Ref: IdentityPool
    Description: Id for the identity pool
  IdentityPoolName:
    Value:
      Fn::GetAtt:
        - IdentityPool
        - Name
