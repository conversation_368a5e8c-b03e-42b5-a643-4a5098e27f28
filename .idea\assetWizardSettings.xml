<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="WizardSettings">
    <option name="children">
      <map>
        <entry key="imageWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="imageAssetPanel">
                    <value>
                      <PersistentState>
                        <option name="children">
                          <map>
                            <entry key="actionbar">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcher">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="backgroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="D:\SignedApk\watchrx.png" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundClipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="D:\SignedApk\watchrx.png" />
                                                <entry key="scalingPercent" value="21" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundTextAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="trimmed" value="true" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="outputName" value="watchrx_app_icon" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="launcherLegacy">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="notification">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="clipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="text">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="textAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="tvBanner">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="D:\SignedApk\watchrx.png" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                                <entry key="fontFamily" value="Calibri Light" />
                                                <entry key="scalingPercent" value="70" />
                                                <entry key="text" value="" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                  <option name="values">
                                    <map>
                                      <entry key="backgroundAssetType" value="IMAGE" />
                                      <entry key="backgroundColor" value="52ff22" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                            <entry key="tvChannel">
                              <value>
                                <PersistentState>
                                  <option name="children">
                                    <map>
                                      <entry key="foregroundClipArt">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="imagePath" value="C:\Users\<USER>\AppData\Local\Temp\ic_android_black_24dp.xml" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundImage">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundText">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                      <entry key="foregroundTextAsset">
                                        <value>
                                          <PersistentState>
                                            <option name="values">
                                              <map>
                                                <entry key="color" value="000000" />
                                              </map>
                                            </option>
                                          </PersistentState>
                                        </value>
                                      </entry>
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
        <entry key="vectorWizard">
          <value>
            <PersistentState>
              <option name="children">
                <map>
                  <entry key="vectorAssetStep">
                    <value>
                      <PersistentState>
                        <option name="children">
                          <map>
                            <entry key="clipartAsset">
                              <value>
                                <PersistentState>
                                  <option name="values">
                                    <map>
                                      <entry key="url" value="jar:file:/C:/Program%20Files/Android/Android%20Studio1/plugins/android/lib/android.jar!/images/material/icons/materialicons/help/baseline_help_24.xml" />
                                    </map>
                                  </option>
                                </PersistentState>
                              </value>
                            </entry>
                          </map>
                        </option>
                        <option name="values">
                          <map>
                            <entry key="outputName" value="ic_baseline_help_24" />
                            <entry key="sourceFile" value="D:\WatchRx_BLE_CODE" />
                          </map>
                        </option>
                      </PersistentState>
                    </value>
                  </entry>
                </map>
              </option>
            </PersistentState>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>