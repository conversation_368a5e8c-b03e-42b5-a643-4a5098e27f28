package com.watchrx.watchrxhealth.tasks;

import static android.content.Context.MODE_PRIVATE;
import static com.watchrx.watchrxhealth.utils.LogUpdateToServer.getLogTypeFromJson;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.util.Log;
import android.view.View;
import android.widget.ProgressBar;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.EncryptionDecryptionUtil;
import com.watchrx.watchrxhealth.utils.LogUpdateToServer;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HttpsURLConnection;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class RestAsyncTask extends AsyncTask<Object, Object, String> {

    private final URL url;
    private String json;
    private final ProgressBar progressBar;
    private final TaskResultHandler resultHandler;
    private final Object resultHandlerParam;

    public RestAsyncTask(URL url, String json, ProgressBar progressBar, TaskResultHandler resultHandler, Object resultHandlerParam) {
        this.url = url;
        this.json = json;
        this.progressBar = progressBar;
        this.resultHandler = resultHandler;
        this.resultHandlerParam = resultHandlerParam;
    }

    @Override
    protected void onPreExecute() {
        if (progressBar != null) {
            progressBar.setVisibility(View.VISIBLE);
        }
    }

    @Override
    protected String doInBackground(Object... params) {
        HttpsURLConnection urlConnection = null;
        BufferedReader reader = null;
        try {
            urlConnection = (HttpsURLConnection) url.openConnection();
            urlConnection.setDoOutput(true);
            urlConnection.setRequestProperty("Accept", "application/json");
            urlConnection.setRequestProperty("Content-Type", "application/json");
            Log.i("API Call : ", json + " URL :" + url.toString());
            if (!url.getPath().contains("authenticate") && !url.getPath().contains("patientOtpStatus")
                    && !url.getPath().contains("validateOTPAndSetPassword") && !url.getPath().contains("resentOTP")) {
                String token = getAuthenticateToken(WatchApp.getContext());
                if (token == null) {
                    return "{\"message\":\"Invalid Token\"}";
                }
                urlConnection.setRequestProperty("Authorization", "Bearer " + token);
                json = getEncryptPayload(json);
                LogUtils.debug("API Call : " + json + " URL :" + url.toString() + " With Token :" + token);
            } else {
                json = getEncryptPayload(json);
                LogUtils.debug("API Call : " + json + " URL :" + url.toString());
            }
            if (json.contains("updatelog")) {
                String fileName = LogUpdateToServer.getLogFileNameFromJson(json);
                String logType = getLogTypeFromJson(json);
                if (fileName != null && logType != null) {
                    File file = new File(fileName);
                    return uploadFile(url, file, Globals.imei, logType);
                }
            } else {
                urlConnection.setRequestProperty("Content-Type", "application/json");
                Writer writer = new BufferedWriter(new OutputStreamWriter(urlConnection.getOutputStream(), StandardCharsets.UTF_8));
                writer.write(json);
                writer.close();
            }
            InputStream inputStream;
            int status = urlConnection.getResponseCode();
            if (status != HttpURLConnection.HTTP_OK) {
                inputStream = urlConnection.getErrorStream();
                LogUtils.debug("Input Stream :" + inputStream);
                return null;
            } else {
                inputStream = urlConnection.getInputStream();
            }
            if (inputStream == null) {
                return null;
            }
            StringBuilder buffer = new StringBuilder();
            reader = new BufferedReader(new InputStreamReader(inputStream));
            String inputLine;
            while ((inputLine = reader.readLine()) != null)
                buffer.append(inputLine).append("\n");
            if (buffer.length() == 0) {
                return "";
            }
            return buffer.toString();
        } catch (Exception e) {
            LogUtils.debug("For Param: " + resultHandlerParam + " I got exception in RestAsyncTask Class at line [" + Thread.currentThread().getStackTrace()[1].getLineNumber() + "] \n\t- Exception is ->" + e.getMessage() + "\n\t- Most likely Internet is unavailable");
            CommUtils.printTraceToLogFile(e);
            return null;
        } finally {
            if (urlConnection != null) {
                urlConnection.disconnect();
            }

            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onPostExecute(String json) {
        json = getDecryptStringResponse(json);
        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }
        if (json != null) {
            Log.e("RESPONSE:", json);
        }
        if (resultHandler != null) {
            resultHandler.handleResult(new HandlerResult(json, resultHandlerParam));
        }
    }

    public String uploadFile(URL url, File sourceFile, String imei, String logType) {
        try {
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(10, TimeUnit.SECONDS)
                    .writeTimeout(180, TimeUnit.SECONDS)
                    .readTimeout(180, TimeUnit.SECONDS)
                    .build();
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                    .addFormDataPart("file", sourceFile.getName(),
                            RequestBody.create(MediaType.parse("text/plain"), sourceFile))
                    .addFormDataPart("imei", imei)
                    .addFormDataPart("logtype", logType)
                    .build();
            Request request = new Request.Builder().url(url).post(body).build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                assert response.body() != null;
                return response.body().string();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public String getAuthenticateToken(Context context) throws IOException {
        try {
            final MediaType JSON
                    = MediaType.parse("application/json; charset=utf-8");
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.AUTHENTICATE_URL);
            SharedPreferences prefs = context.getSharedPreferences("WatchRx", MODE_PRIVATE);

            loginModel.username = prefs.getString("username", null);
            loginModel.password = prefs.getString("password", null);
            String postBody = new Gson().toJson(loginModel);
            String payload = getEncryptPayload(postBody);

            OkHttpClient client = new OkHttpClient.Builder().connectTimeout(10, TimeUnit.SECONDS).writeTimeout(180, TimeUnit.SECONDS).readTimeout(180, TimeUnit.SECONDS).build();

            RequestBody body = RequestBody.create(JSON, payload);
            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Accept", "application/json")
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                assert response.body() != null;
                JSONObject jsonObject = new JSONObject(response.body().string());
                JSONObject decryptJson = getDecryptJsonResponse(jsonObject.toString());
                Log.i("DATA:", decryptJson.toString());
                Globals.authToken = decryptJson.optString("token");
                Log.e("Auth Token", Globals.authToken);
                return Globals.authToken;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public JSONObject getDecryptJsonResponse(String response) {
        try {
            JSONObject jsonObject = new JSONObject(response);
            EncryptionDecryptionUtil util = new EncryptionDecryptionUtil();
            String decryptResp = util.decrypt(jsonObject.optString("data"));
            Log.i("decryptResp", decryptResp);
            return new JSONObject(decryptResp);
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }

    public String getEncryptPayload(String payload) {
        EncryptionDecryptionUtil util = new EncryptionDecryptionUtil();
        String encPayload = util.encrypt(payload.trim());
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("data", encPayload);
            return jsonObject.toString();
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }


    public String getDecryptStringResponse(String response) {
        try {
            if (response == null)
                return null;
            JSONObject jsonObject = new JSONObject(response);
            EncryptionDecryptionUtil util = new EncryptionDecryptionUtil();
            return util.decrypt(jsonObject.optString("data"));
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }
    }
}

