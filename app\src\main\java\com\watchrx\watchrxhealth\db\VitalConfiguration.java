package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;

import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.ArrayList;
import java.util.List;

public class VitalConfiguration {
    public static final String TABLE_VITAL_DETAILS = "VitalDetails";
    private static final String COL_VITAL_SCHEDULE_ID = "VitalScheduleId";
    private static final String COL_VITAL_TYPE_NAME = "VitalTypeName";
    private static final String COL_VITAL_COLLECT_MODE = "CollectMode";
    private static final String COL_VITAL_SCHEDULE_OF_WEEK = "ScheduleDayOfWeek";
    private static final String COL_VITAL_TIME_SLOTS = "TimeSlots";
    private static final String COL_VITAL_FREQUENCY = "Frequency";
    private static final String COL_VITAL_DEVICE_SERIAL_ID = "DeviceSerialId";
    private static final String COL_VITAL_DEVICE_NAME = "DeviceName";
    private static final String COL_VITAL_DEVICE_MEASURES = "DeviceMeasures";
    private static final String COL_START_DATE = "StartDate";
    private static final String COL_END_DATE = "EndDate";

    private String vitalScheduleId;
    private String vitalTypeName;
    private String collectMode;
    private String scheduleDayOfWeek;
    private String frequency;
    private String timeSlots;
    private String deviceSerialId;
    private String deviceMeasures;
    private String deviceName;
    private String startDate;
    private String endDate;

    static final String DELETE_TABLE_VITAL_DETAILS = "DROP TABLE IF EXISTS " + TABLE_VITAL_DETAILS + ";";

    static final String CREATE_TABLE_VITAL_DETAILS = "CREATE TABLE " + TABLE_VITAL_DETAILS +
            "(" +
            COL_VITAL_SCHEDULE_ID + " TEXT, " +
            COL_VITAL_TYPE_NAME + " TEXT, " +
            COL_VITAL_COLLECT_MODE + " TEXT, " +
            COL_VITAL_SCHEDULE_OF_WEEK + " TEXT, " +
            COL_VITAL_TIME_SLOTS + " TEXT," +
            COL_VITAL_FREQUENCY + " TEXT, " +
            COL_VITAL_DEVICE_SERIAL_ID + " TEXT," +
            COL_VITAL_DEVICE_NAME + " TEXT, " +
            COL_VITAL_DEVICE_MEASURES + " TEXT, " +
            COL_START_DATE + " TEXT, " +
            COL_END_DATE + " TEXT " +
            ");";

    private static final String[] COLUMNS_TABLE_VITAL_DETAILS = {
            COL_VITAL_SCHEDULE_ID,
            COL_VITAL_TYPE_NAME,
            COL_VITAL_COLLECT_MODE,
            COL_VITAL_SCHEDULE_OF_WEEK,
            COL_VITAL_TIME_SLOTS,
            COL_VITAL_FREQUENCY,
            COL_VITAL_DEVICE_SERIAL_ID,
            COL_VITAL_DEVICE_NAME,
            COL_VITAL_DEVICE_MEASURES,
            COL_START_DATE,
            COL_END_DATE
    };

    public String getVitalScheduleId() {
        return vitalScheduleId;
    }

    public void setVitalScheduleId(String vitalScheduleId) {
        this.vitalScheduleId = vitalScheduleId;
    }

    public String getVitalTypeName() {
        return vitalTypeName;
    }

    public void setVitalTypeName(String vitalTypeName) {
        this.vitalTypeName = vitalTypeName;
    }

    public String getCollectMode() {
        return collectMode;
    }

    public void setCollectMode(String collectMode) {
        this.collectMode = collectMode;
    }

    public String getScheduleDayOfWeek() {
        return scheduleDayOfWeek;
    }

    public void setScheduleDayOfWeek(String scheduleDayOfWeek) {
        this.scheduleDayOfWeek = scheduleDayOfWeek;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public String getTimeSlots() {
        return timeSlots;
    }

    public void setTimeSlots(String timeSlots) {
        this.timeSlots = timeSlots;
    }

    public String getDeviceSerialId() {
        return deviceSerialId;
    }

    public void setDeviceSerialId(String deviceSerialId) {
        this.deviceSerialId = deviceSerialId;
    }

    public String getDeviceMeasures() {
        return deviceMeasures;
    }

    public void setDeviceMeasures(String deviceMeasures) {
        this.deviceMeasures = deviceMeasures;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_VITAL_DETAILS);
    }

    public static long addToDB(VitalConfiguration vitalConfiguration) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COL_VITAL_SCHEDULE_ID, vitalConfiguration.vitalScheduleId);
        contentValues.put(COL_VITAL_TYPE_NAME, vitalConfiguration.vitalTypeName);
        contentValues.put(COL_VITAL_COLLECT_MODE, vitalConfiguration.collectMode);
        contentValues.put(COL_VITAL_SCHEDULE_OF_WEEK, vitalConfiguration.scheduleDayOfWeek);
        contentValues.put(COL_VITAL_TIME_SLOTS, vitalConfiguration.timeSlots);
        contentValues.put(COL_VITAL_FREQUENCY, vitalConfiguration.frequency);
        contentValues.put(COL_VITAL_DEVICE_SERIAL_ID, vitalConfiguration.deviceSerialId);
        contentValues.put(COL_VITAL_DEVICE_NAME, vitalConfiguration.deviceName);
        contentValues.put(COL_VITAL_DEVICE_MEASURES, vitalConfiguration.deviceMeasures);
        contentValues.put(COL_START_DATE, vitalConfiguration.startDate);
        contentValues.put(COL_END_DATE, vitalConfiguration.endDate);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_VITAL_DETAILS, contentValues);
    }

    public static long updateVitalToDB(VitalConfiguration vitalConfiguration) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COL_VITAL_TYPE_NAME, vitalConfiguration.vitalTypeName);
        contentValues.put(COL_VITAL_COLLECT_MODE, vitalConfiguration.collectMode);
        contentValues.put(COL_VITAL_SCHEDULE_OF_WEEK, vitalConfiguration.scheduleDayOfWeek);
        contentValues.put(COL_VITAL_TIME_SLOTS, vitalConfiguration.timeSlots);
        contentValues.put(COL_VITAL_FREQUENCY, vitalConfiguration.frequency);
        contentValues.put(COL_VITAL_DEVICE_SERIAL_ID, vitalConfiguration.deviceSerialId);
        contentValues.put(COL_VITAL_DEVICE_NAME, vitalConfiguration.deviceName);
        contentValues.put(COL_VITAL_DEVICE_MEASURES, vitalConfiguration.deviceMeasures);
        contentValues.put(COL_START_DATE, vitalConfiguration.startDate);
        contentValues.put(COL_END_DATE, vitalConfiguration.endDate);
        return DBAdaptor.getDbAdaptorInstance().update(TABLE_VITAL_DETAILS, contentValues, COL_VITAL_SCHEDULE_ID + " = ?", new String[]{vitalConfiguration.vitalScheduleId});
    }

    public static long deleteVitalFromDB(String vitalId) {
        return DBAdaptor.getDbAdaptorInstance().delete(TABLE_VITAL_DETAILS, COL_VITAL_SCHEDULE_ID + " = ?", new String[]{vitalId});
    }

    public static List<VitalConfiguration> getFromDB() {
        List<VitalConfiguration> configurationList = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_VITAL_DETAILS, COLUMNS_TABLE_VITAL_DETAILS, null, null, null);
        while (cursor.moveToNext()) {
            try {
                VitalConfiguration vitalConfiguration = new VitalConfiguration();
                vitalConfiguration.vitalScheduleId = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_SCHEDULE_ID)));
                vitalConfiguration.vitalTypeName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_TYPE_NAME)));
                vitalConfiguration.collectMode = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_COLLECT_MODE)));
                vitalConfiguration.scheduleDayOfWeek = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_SCHEDULE_OF_WEEK)));
                vitalConfiguration.timeSlots = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_TIME_SLOTS)));
                vitalConfiguration.frequency = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_FREQUENCY)));
                vitalConfiguration.deviceSerialId = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_DEVICE_SERIAL_ID)));
                vitalConfiguration.deviceName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_DEVICE_NAME)));
                vitalConfiguration.deviceMeasures = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_DEVICE_MEASURES)));
                vitalConfiguration.startDate = (cursor.getString(cursor.getColumnIndexOrThrow(COL_START_DATE)));
                vitalConfiguration.endDate = (cursor.getString(cursor.getColumnIndexOrThrow(COL_END_DATE)));
                configurationList.add(vitalConfiguration);
            } catch (Exception e) {
                LogUtils.debug("Vital Details Exception While Fetching" + e.getMessage());
            }
        }
        cursor.close();
        return configurationList;
    }

    public static VitalConfiguration getVitalDetailsForId(String vitalScheduleId) {
        VitalConfiguration vitalConfiguration = new VitalConfiguration();
        String whereClause = COL_VITAL_SCHEDULE_ID + " = \"" + vitalScheduleId + "\"";
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_VITAL_DETAILS, COLUMNS_TABLE_VITAL_DETAILS, whereClause, null, null);
        if (cursor.getCount() == 1) {
            try {
                cursor.moveToNext();
                vitalConfiguration.vitalScheduleId = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_SCHEDULE_ID)));
                vitalConfiguration.vitalTypeName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_TYPE_NAME)));
                vitalConfiguration.collectMode = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_COLLECT_MODE)));
                vitalConfiguration.scheduleDayOfWeek = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_SCHEDULE_OF_WEEK)));
                vitalConfiguration.timeSlots = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_TIME_SLOTS)));
                vitalConfiguration.frequency = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_FREQUENCY)));
                vitalConfiguration.deviceSerialId = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_DEVICE_SERIAL_ID)));
                vitalConfiguration.deviceName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_DEVICE_NAME)));
                vitalConfiguration.deviceMeasures = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_DEVICE_MEASURES)));
                vitalConfiguration.startDate = (cursor.getString(cursor.getColumnIndexOrThrow(COL_START_DATE)));
                vitalConfiguration.endDate = (cursor.getString(cursor.getColumnIndexOrThrow(COL_END_DATE)));
                cursor.close();
                return vitalConfiguration;
            } catch (Exception e) {
                LogUtils.debug("Vital Details Exception While Fetching" + e.getMessage());
            }
        }
        return null;
    }
}