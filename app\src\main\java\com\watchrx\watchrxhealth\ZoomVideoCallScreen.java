package com.watchrx.watchrxhealth;

import android.Manifest;
import android.app.Dialog;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.URL;

import us.zoom.uitoolkit.SessionContext;
import us.zoom.uitoolkit.UiToolkitError;
import us.zoom.uitoolkit.UiToolkitView;

public class ZoomVideoCallScreen extends AppCompatActivity {

    private static final String TAG = "ZoomVideoCallActivity";
    private static final int PERMISSION_REQUEST_CODE = 100;

    private UiToolkitView uiToolkitView;
    //    private Button joinButton;
    private Dialog loadingDialog;

    private long roomId = 0;
    private String program = "rpm";
    private Button acceptCallButton, declineCallButton;
    private TextView callStatusText;


    private final UiToolkitView.Listener uiToolkitListener = new UiToolkitView.Listener() {
        @Override
        public void onError(@NonNull UiToolkitError error) {
            LogUtils.debug("Zoom Error:" + error);
            Toast.makeText(ZoomVideoCallScreen.this, "Zoom Error: " + error, Toast.LENGTH_SHORT).show();
            hideLoadingDialog();
        }

        @Override
        public void onViewStarted() {
            Log.i(TAG, "Inside onViewStarted....");
            uiToolkitView.setVisibility(View.VISIBLE);
//            joinButton.setVisibility(View.GONE);
            acceptCallButton.setVisibility(View.GONE);
            declineCallButton.setVisibility(View.GONE);
            callStatusText.setText("");
            new Handler(Looper.getMainLooper()).postDelayed(() -> hideLoadingDialog(), 5000);
        }

        @Override
        public void onViewStopped() {
            Log.i(TAG, "Inside onViewStopped....");
            uiToolkitView.setVisibility(View.GONE);
            acceptCallButton.setVisibility(View.VISIBLE);
            declineCallButton.setVisibility(View.VISIBLE);
            callStatusText.setText("Call Ended");
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                finish();
                Globals.isScreenRunning = false;
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
            }, 2000);
        }
    };

    private final View.OnClickListener buttonClickListener = v -> {
        if (hasPermissions()) {
            setAccessToken(roomId, program);
        } else {
            requestPermissions();
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_zoom_video_call_screen);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayShowHomeEnabled(true);
            getSupportActionBar().setLogo(R.drawable.home_logo);
            getSupportActionBar().setDisplayUseLogoEnabled(true);
            getSupportActionBar().setTitle("WatchRx");
        }
//        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);

        uiToolkitView = findViewById(R.id.ui_toolkit_view);
//        joinButton = findViewById(R.id.join_button);
        acceptCallButton = findViewById(R.id.accept_call_button);
        declineCallButton = findViewById(R.id.decline_call_button);
        callStatusText = findViewById(R.id.call_status_text);

        uiToolkitView.addListener(uiToolkitListener);
//        joinButton.setOnClickListener(buttonClickListener);
        uiToolkitView.setPadding(5, 5, 5, 5);

        setupLoadingDialog();

        roomId = getIntent().getLongExtra("roomId", 0);
        program = String.valueOf(getIntent().getStringExtra("program"));

        acceptCallButton.setOnClickListener(view -> {
            GeneralUtils.stopSpeaking();
            if (roomId != 0L) {
                callStatusText.setText("Connecting to session...");
                if (hasPermissions()) {
                    setAccessToken(roomId, program);
                } else {
                    requestPermissions();
                }
            } else {
                Toast.makeText(this, "Invalid Room ID", Toast.LENGTH_SHORT).show();
                finish();
                Globals.isScreenRunning = false;
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
            }
        });
        declineCallButton.setOnClickListener(view -> {
            callEndProcess();
        });
        GeneralUtils.startBeeping(this, mp -> GeneralUtils.speak("You have a video call request.$ Please acknowledge."));
    }

    private void callEndProcess() {
        GeneralUtils.stopSpeaking();
        finish();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
    }

    private void setupLoadingDialog() {
        loadingDialog = new Dialog(this, android.R.style.Theme_Translucent_NoTitleBar);
        loadingDialog.setContentView(R.layout.dialog_loading);
        loadingDialog.setCancelable(false);
        loadingDialog.setOnShowListener(dialog -> {
            ImageView loaderImage = loadingDialog.findViewById(R.id.loaderImage);
            if (loaderImage != null) {
                loaderImage.startAnimation(AnimationUtils.loadAnimation(this, R.anim.rotate));
            }
        });
    }

    private void showLoadingDialog() {
        if (!isFinishing() && loadingDialog != null && !loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private void hideLoadingDialog() {
        if (!isFinishing() && loadingDialog != null && loadingDialog.isShowing()) {
            loadingDialog.dismiss();
        }
    }

    private boolean hasPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED &&
                ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
    }

    private void requestPermissions() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA, Manifest.permission.RECORD_AUDIO},
                PERMISSION_REQUEST_CODE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (hasPermissions()) {
                setAccessToken(roomId, program);
            } else {
                Toast.makeText(this, "Permissions are required to join the session", Toast.LENGTH_LONG).show();
                finish();
                Globals.isScreenRunning = false;
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
            }
        }
    }

    private void setAccessToken(long roomId, String program) {
        try {
            showLoadingDialog();
            Log.i(TAG, "Calling AccessToken API...");
            URL url = new URL(URLConstants.ZOOM_VIDEO_CALL_ACCESS_TOKEN);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("patientId", String.valueOf(roomId));
            jsonObject.put("program", program);

            String payload = jsonObject.toString();

            new RestAsyncTask(url, payload, null, new AccessTokenResponseHandler(), null).execute();
        } catch (Exception e) {
            Log.e(TAG, "Failed to call AccessToken API", e);
            hideLoadingDialog();
        }
    }

    private class AccessTokenResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            hideLoadingDialog();
            if (handlerResult == null) {
                LogUtils.debug("Failed to get zoom access token: result is null");
                Toast.makeText(ZoomVideoCallScreen.this, "Failed to get Access token", Toast.LENGTH_SHORT).show();
                return;
            }

            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }
            try {
                assert result != null;
                JSONObject jsonObject = new JSONObject((String) result);
                Log.i("PAYLOAD", jsonObject.toString());
                LogUtils.debug("Zoom Auth Token Resp:" + jsonObject.toString());
                if (jsonObject.optString("responseCode").equalsIgnoreCase("200")) {
                    String sessionName = jsonObject.optString("sessionName");
                    String token = jsonObject.optString("zoomToken");
                    String name = jsonObject.optString("userName");
                    String passcode = jsonObject.optString("sessionPasscode");

                    if (!sessionName.isEmpty() && !token.isEmpty()) {
                        SessionContext sessionContext = new SessionContext(sessionName, token, name, passcode);
                        uiToolkitView.joinSession(sessionContext);
                    } else {
                        LogUtils.debug("Invalid Access Token or Session Name");
                        Toast.makeText(ZoomVideoCallScreen.this, "Invalid Zoom credentials", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            new android.app.AlertDialog.Builder(ZoomVideoCallScreen.this)
                                    .setTitle("WatchRx")
                                    .setMessage("The video consultation has already ended.\n" +
                                            "Please contact your healthcare provider to reschedule it.")
                                    .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                        public void onClick(DialogInterface dialog, int which) {
                                            dialog.dismiss();
                                            CommUtils.sendBatteryLogToServer(ZoomVideoCallScreen.this, "", "Patient available to take video call, Now you can call them");
                                            callEndProcess();
                                        }
                                    }).show();
                        }
                    });
                }
            } catch (JSONException e) {
                Log.e(TAG, "Error parsing access token JSON", e);
                Toast.makeText(ZoomVideoCallScreen.this, "Response parsing error", Toast.LENGTH_SHORT).show();
            }
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.dispatchKeyEvent(event);
    }
}
