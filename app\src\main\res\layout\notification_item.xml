<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/notification_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_bold"
            android:text="Notification Title"
            android:textColor="@color/black"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/notification_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/lato_regular"
            android:text="Notification message goes here."
            android:textColor="@color/colorText"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/notification_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/lato_light"
            android:gravity="end"
            android:text="2 hours ago"
            android:textColor="@color/colorText"
            android:textSize="15sp" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
