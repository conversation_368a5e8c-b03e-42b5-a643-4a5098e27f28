package com.watchrx.watchrxhealth;

import android.Manifest;
import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.VitalListAdapter;
import com.watchrx.watchrxhealth.ble.NewVitalsActivity;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.LatestVitalDataModel;
import com.watchrx.watchrxhealth.models.VitalDataModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.OnItemClickListener;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.watchrx.watchrxhealth.healthconnect.HealthConnectManager;

public class VitalDashboard extends AppCompatActivity {
    private RecyclerView recyclerView;
    private ProgressBar progressBar;
    private VitalListAdapter adapter;
    private TextView noItemsMessage;
    private Button btnHealthConnect;

    public int needRequestPermission;
    private static final int PERMISSION_REQUEST_COARSE_LOCATION = 1;

    private BluetoothAdapter bluetoothAdapter;
    private HealthConnectManager healthConnectManager;
    private ActivityResultLauncher<Set<String>> healthConnectPermissionLauncher;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        setContentView(R.layout.activity_vital_dashboard);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("My Vitals");

        recyclerView = findViewById(R.id.vital_list);
        progressBar = findViewById(R.id.loader);
        noItemsMessage = findViewById(R.id.no_items_message);
        btnHealthConnect = findViewById(R.id.btn_health_connect);

        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        showLoading(true);
        getLatestVitalInfo();
        setBottomNavigation();
        setUpBluetoothConfig();
        
        // Initialize Health Connect Manager
        try {
            healthConnectManager = new HealthConnectManager(this);
            setupHealthConnectPermissionLauncher();
            setupHealthConnectButton();
            LogUtils.debug("Health Connect Manager initialized successfully");
        } catch (Exception e) {
            LogUtils.debug("Error initializing Health Connect Manager: " + e.getMessage());
            e.printStackTrace();
        }

        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        if (bluetoothAdapter == null) {
            return;
        }

        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                return;
            }
            startActivity(enableBtIntent);
        }
    }

    private final ActivityResultLauncher<Intent> enableBluetoothLauncher =
            registerForActivityResult(new ActivityResultContracts.StartActivityForResult(),
                    result -> {
                        if (result.getResultCode() == Activity.RESULT_OK) {
                            // Bluetooth enabled successfully
                        } else {
                            // Bluetooth enabling failed or canceled
                        }
                    });

    public void enableBluetoothApi30(Activity activity) {
        if (bluetoothAdapter == null) {
            return; // Device does not support Bluetooth
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(activity, Manifest.permission.BLUETOOTH_CONNECT)
                    != PackageManager.PERMISSION_GRANTED) {
                ActivityCompat.requestPermissions(activity,
                        new String[]{Manifest.permission.BLUETOOTH_CONNECT}, 100);
                return;
            }
        }

        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            enableBluetoothLauncher.launch(enableBtIntent);
        }
    }

    private void setUpBluetoothConfig() {
        int requestedCnt = 0;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestedCnt = 3;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            requestedCnt = 2;
        }

        if (requestedCnt > 0) {
            if (needRequestPermission == 0) {
                needRequestPermission = !checkPermissions() ? 1 : requestedCnt;
                if (needRequestPermission == 1) {
                    requestPermissions();
                }
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.M)
    public boolean checkPermissions() {
        boolean result = true;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (checkSelfPermission(android.Manifest.permission.BLUETOOTH_SCAN)
                    != PackageManager.PERMISSION_GRANTED ||
                    checkSelfPermission(android.Manifest.permission.BLUETOOTH_CONNECT)
                            != PackageManager.PERMISSION_GRANTED) {
                result = false;
            }
        } else {
            if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION)
                    != PackageManager.PERMISSION_GRANTED) {
                result = false;
            }
        }

        if (result) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
                if (checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE)
                        != PackageManager.PERMISSION_GRANTED ||
                        checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE)
                                != PackageManager.PERMISSION_GRANTED) {
                    result = false;
                }
            }
        }
        return result;
    }

    @TargetApi(Build.VERSION_CODES.M)
    public void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            requestPermissions(new String[]{
                            Manifest.permission.BLUETOOTH_SCAN,
                            Manifest.permission.BLUETOOTH_CONNECT},
                    PERMISSION_REQUEST_COARSE_LOCATION);
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                requestPermissions(new String[]{
                                Manifest.permission.ACCESS_FINE_LOCATION},
                        PERMISSION_REQUEST_COARSE_LOCATION);
            } else {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    requestPermissions(new String[]{
                                    Manifest.permission.ACCESS_FINE_LOCATION,
                                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                                    Manifest.permission.READ_EXTERNAL_STORAGE},
                            PERMISSION_REQUEST_COARSE_LOCATION);
                }
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (grantResults.length == 0) {
            return;
        }
        if (requestCode == PERMISSION_REQUEST_COARSE_LOCATION) {
            for (int i = 0; i < permissions.length; i++) {
                if (grantResults[i] == PackageManager.PERMISSION_GRANTED) {
                    if (permissions[i].equals(Manifest.permission.BLUETOOTH_SCAN) ||
                            permissions[i].equals(Manifest.permission.BLUETOOTH_CONNECT)) {
                        if (needRequestPermission >= 1) {
                            needRequestPermission++;
                        }
                    } else if (permissions[i].equals(Manifest.permission.ACCESS_FINE_LOCATION)) {
                        if (needRequestPermission == 1) {
                            needRequestPermission = 2;
                        }
                    }
                }
            }
        }
    }

    private void showLoading(boolean isLoading) {
        if (isLoading) {
            progressBar.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
            noItemsMessage.setVisibility(View.GONE);
        } else {
            progressBar.setVisibility(View.GONE);
            recyclerView.setVisibility(View.VISIBLE);
        }
    }

    private void showNoItemsMessage(boolean show) {
        if (show) {
            noItemsMessage.setVisibility(View.VISIBLE);
            recyclerView.setVisibility(View.GONE);
        } else {
            noItemsMessage.setVisibility(View.GONE);
        }
    }


    private void getLatestVitalInfo() {
        try {
            LogUtils.debug("Going to getLatestVitalInfo");
            URL url = new URL(URLConstants.LATEST_VITAL_DATA);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            new RestAsyncTask(url, jsonObject.toString(), null, new LatestVitalInfoResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }


    private class LatestVitalInfoResponseHandler implements TaskResultHandler {
        @SuppressLint("NotifyDataSetChanged")
        @Override
        public void handleResult(HandlerResult handlerResult) {
            showLoading(false);
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }

            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        LatestVitalDataModel responseMessage = new Gson().fromJson((String) result, LatestVitalDataModel.class);
                        if (responseMessage != null && responseMessage.isStatus()
                                && responseMessage.getData() != null && responseMessage.getData().size() > 0) {
                            List<VitalDataModel> vitalList = new ArrayList<>(responseMessage.data);
                            Map<String, Integer> vitalTypeToImageMap = new HashMap<>();
                            vitalTypeToImageMap.put("Heart Rate", R.drawable.ic_heart_rate);
                            vitalTypeToImageMap.put("Pedometer", R.drawable.ic_baseline_directions_walk_24);
                            vitalTypeToImageMap.put("Weight", R.drawable.ic_weight_40);
                            vitalTypeToImageMap.put("Temperature", R.drawable.ic_thermo_40);
                            vitalTypeToImageMap.put("Random Blood Sugar", R.drawable.ic_blood_sugar);
                            vitalTypeToImageMap.put("Fasting Blood Sugar", R.drawable.ic_blood_sugar);
                            vitalTypeToImageMap.put("Oxygen Saturation", R.drawable.ic_baseline_bloodtype_24);
                            vitalTypeToImageMap.put("Blood Pressure", R.drawable.bp);
                            vitalTypeToImageMap.put("Sleep Monitor", R.drawable.sleep);

                            for (VitalDataModel vitalDataModel : vitalList) {
                                String vitalType = vitalDataModel.getVitalType();
                                if (vitalTypeToImageMap.containsKey(vitalType)) {
                                    Integer imageRes = vitalTypeToImageMap.get(vitalType);
                                    if (imageRes != null) {
                                        vitalDataModel.setImage(imageRes);
                                    }
                                }
                            }
                            recyclerView.setAdapter(new VitalListAdapter(vitalList, new OnItemClickListener() {
                                @Override
                                public void onItemClicked(VitalDataModel item) {
                                    Intent intent = new Intent(VitalDashboard.this, VitalDetailsActivity.class);
                                    String vitalType = item.getVitalType();

                                    if (vitalType.contains("Health Connect")) {
                                        vitalType = getServerVitalTypeFromHealthConnect(vitalType);
                                    }

                                    if (vitalType.equalsIgnoreCase("Random Blood Sugar") ||
                                            vitalType.equalsIgnoreCase("Fasting Blood Sugar") ||
                                            vitalType.equalsIgnoreCase("Blood Glucose")) {
                                        intent.putExtra("vitalType", "Blood Sugar");
                                    } else {
                                        intent.putExtra("vitalType", vitalType);
                                    }
                                    startActivity(intent);
                                }

                                @Override
                                public void onButtonClicked(VitalDataModel item) {
                                    if (item.getVitalType().contains("Health Connect")) {
                                        handleHealthConnectCollect(item);
                                        return;
                                    }

                                    String vitalType = "";
                                    if (item.getVitalType().equalsIgnoreCase("Random Blood Sugar") ||
                                            item.getVitalType().equalsIgnoreCase("Fasting Blood Sugar")) {
                                        vitalType = "Blood Sugar";
                                    } else {
                                        vitalType = item.getVitalType();
                                    }
                                    String deviceName = CommUtils.getMedicalDeviceType(vitalType);
                                    if (deviceName == null) {
                                        Toast.makeText(VitalDashboard.this, "Medical device type not found", Toast.LENGTH_SHORT).show();
                                        return;
                                    }
                                    Intent intent = new Intent(VitalDashboard.this, NewVitalsActivity.class);
                                    intent.putExtra("vitalScheduleId", "");
                                    intent.putExtra("deviceName", deviceName);
                                    intent.putExtra("vitalTypeName", vitalType);
                                    startActivity(intent);
                                }
                            }));
                            
                            addHealthConnectBloodGlucoseData(vitalList);
                            
                            recyclerView.invalidate();
                            Objects.requireNonNull(recyclerView.getAdapter()).notifyDataSetChanged();
                        } else {
                            showNoItemsMessage(true);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        getLatestVitalInfo();
    }

    private void setBottomNavigation() {
        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }
    
    private VitalDataModel createHealthConnectVital(String vitalType) {
        try {
            VitalDataModel vital = new VitalDataModel();

            switch (vitalType) {
                case "heartRate":
                    vital.setVitalType("Heart Rate (Health Connect)");
                    vital.setImage(R.drawable.ic_heart_rate);
                    boolean hasHeartRatePermissions = healthConnectManager.hasHeartRatePermissionsSync();
                    LogUtils.debug("Heart Rate permissions check: " + hasHeartRatePermissions);
                    if (hasHeartRatePermissions) {
                        HealthConnectManager.HeartRateData heartRateData = healthConnectManager.getLatestHeartRateSync();
                        LogUtils.debug("Heart Rate data retrieved: " + (heartRateData != null ? heartRateData.getBeatsPerMinute() + " bpm" : "null"));
                        if (heartRateData != null) {
                            vital.setVitalData(heartRateData.getBeatsPerMinute() + " bpm");
                            vital.setMeasureDateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date.from(heartRateData.getTime())));
                            vital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                        } else {
                            vital.setVitalData("No data available");
                        }
                    } else {
                        vital.setVitalData("Connect to view");
                    }
                    break;

                case "bloodSugar":
                    vital.setVitalType("Blood Glucose (Health Connect)");
                    vital.setImage(R.drawable.ic_blood_sugar);
                    boolean hasBloodGlucosePermissions = healthConnectManager.hasBloodGlucosePermissionsSync();
                    LogUtils.debug("Blood Glucose permissions check: " + hasBloodGlucosePermissions);
                    if (hasBloodGlucosePermissions) {
                        HealthConnectManager.BloodGlucoseData glucoseData = healthConnectManager.getLatestBloodGlucoseSync();
                        LogUtils.debug("Blood Glucose data retrieved: " + (glucoseData != null ? glucoseData.getLevel() + " mmol/L" : "null"));
                        if (glucoseData != null) {
                            vital.setVitalData(String.format("%.1f mmol/L", glucoseData.getLevel()));
                            vital.setMeasureDateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date.from(glucoseData.getTime())));
                            vital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                        } else {
                            vital.setVitalData("No data available");
                        }
                    } else {
                        vital.setVitalData("Connect to view");
                    }
                    break;

                case "bloodPressure":
                    vital.setVitalType("Blood Pressure (Health Connect)");
                    vital.setImage(R.drawable.ic_round_bloodpressure_24);
                    boolean hasBloodPressurePermissions = healthConnectManager.hasBloodPressurePermissionsSync();
                    LogUtils.debug("Blood Pressure permissions check: " + hasBloodPressurePermissions);
                    if (hasBloodPressurePermissions) {
                        HealthConnectManager.BloodPressureData bpData = healthConnectManager.getLatestBloodPressureSync();
                        LogUtils.debug("Blood Pressure data retrieved: " + (bpData != null ? bpData.getSystolic() + "/" + bpData.getDiastolic() + " mmHg" : "null"));
                        if (bpData != null) {
                            vital.setVitalData(String.format("%.0f/%.0f mmHg", bpData.getSystolic(), bpData.getDiastolic()));
                            vital.setMeasureDateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date.from(bpData.getTime())));
                            vital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                        } else {
                            vital.setVitalData("No data available");
                            LogUtils.debug("Blood Pressure: No data available, showing 'No data available'");
                        }
                    } else {
                        vital.setVitalData("Connect to view");
                        LogUtils.debug("Blood Pressure: No permissions, showing 'Connect to view'");
                    }
                    break;

                case "spo2":
                    vital.setVitalType("Oxygen Saturation (Health Connect)");
                    vital.setImage(R.drawable.ic_spo2);
                    boolean hasOxygenSaturationPermissions = healthConnectManager.hasOxygenSaturationPermissionsSync();
                    LogUtils.debug("Oxygen Saturation permissions check: " + hasOxygenSaturationPermissions);
                    if (hasOxygenSaturationPermissions) {
                        HealthConnectManager.OxygenSaturationData spo2Data = healthConnectManager.getLatestOxygenSaturationSync();
                        LogUtils.debug("Oxygen Saturation data retrieved: " + (spo2Data != null ? spo2Data.getPercentage() + "%" : "null"));
                        if (spo2Data != null) {
                            vital.setVitalData(String.format("%.1f%%", spo2Data.getPercentage()));
                            vital.setMeasureDateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date.from(spo2Data.getTime())));
                            vital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                        } else {
                            vital.setVitalData("No data available");
                            LogUtils.debug("Oxygen Saturation: No data available, showing 'No data available'");
                        }
                    } else {
                        vital.setVitalData("Connect to view");
                        LogUtils.debug("Oxygen Saturation: No permissions, showing 'Connect to view'");
                    }
                    break;

                case "steps":
                    vital.setVitalType("Steps (Health Connect)");
                    vital.setImage(R.drawable.ic_baseline_directions_walk_24);
                    boolean hasStepsPermissions = healthConnectManager.hasStepsPermissionsSync();
                    LogUtils.debug("Steps permissions check: " + hasStepsPermissions);
                    if (hasStepsPermissions) {
                        long stepsData = healthConnectManager.getTodayStepsSync();
                        LogUtils.debug("Steps data retrieved: " + stepsData + " steps");
                        if (stepsData >= 0) {
                            vital.setVitalData(stepsData + " steps");
                            vital.setMeasureDateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                            vital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                        } else {
                            vital.setVitalData("Connect to view");
                            LogUtils.debug("Steps: Invalid data (" + stepsData + "), showing 'Connect to view'");
                        }
                    } else {
                        vital.setVitalData("Connect to view");
                        LogUtils.debug("Steps: No permissions, showing 'Connect to view'");
                    }
                    break;

                case "sleep":
                    vital.setVitalType("Sleep (Health Connect)");
                    vital.setImage(R.drawable.sleep);
                    boolean hasSleepPermissions = healthConnectManager.hasSleepPermissionsSync();
                    LogUtils.debug("Sleep permissions check: " + hasSleepPermissions);
                    if (hasSleepPermissions) {
                        HealthConnectManager.SleepData sleepData = healthConnectManager.getLatestSleepSync();
                        LogUtils.debug("Sleep data retrieved: " + (sleepData != null ? sleepData.getDuration() + " minutes" : "null"));
                        if (sleepData != null) {
                            long hours = sleepData.getDuration() / 60;
                            long minutes = sleepData.getDuration() % 60;
                            vital.setVitalData(String.format("%dh %dm", hours, minutes));
                            vital.setMeasureDateTime(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date.from(sleepData.getStartTime())));
                            vital.setDate(new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(new java.util.Date()));
                        } else {
                            vital.setVitalData("No data available");
                            LogUtils.debug("Sleep: No data available, showing 'No data available'");
                        }
                    } else {
                        vital.setVitalData("Connect to view");
                        LogUtils.debug("Sleep: No permissions, showing 'Connect to view'");
                    }
                    break;

                default:
                    LogUtils.debug("Unknown Health Connect vital type: " + vitalType);
                    return null;
            }

            return vital;

        } catch (Exception e) {
            LogUtils.debug("Error creating Health Connect vital for " + vitalType + ": " + e.getMessage());
            return null;
        }
    }

    private void addHealthConnectBloodGlucoseData(List<VitalDataModel> vitalList) {
        if (healthConnectManager == null) {
            return;
        }

        if (healthConnectManager.checkHealthConnectAvailability() != HealthConnectManager.HealthConnectAvailability.AVAILABLE) {
            LogUtils.debug("Health Connect is not available");
            return;
        }

        new Thread(() -> {
            try {
                List<VitalDataModel> healthConnectVitals = new ArrayList<>();
                boolean hasAllPermissions = true;
                boolean hasAnyPermissions = false;

                for (String vitalType : com.watchrx.watchrxhealth.globals.Globals.healthConnectVitalsList) {
                    LogUtils.debug("Processing Health Connect vital: " + vitalType);
                    VitalDataModel vital = createHealthConnectVital(vitalType);
                    if (vital != null) {
                        healthConnectVitals.add(vital);
                        LogUtils.debug("Created vital: " + vital.getVitalType() + " with data: " + vital.getVitalData());

                        if (vital.getVitalData().equals("Connect to view")) {
                            hasAllPermissions = false;
                            LogUtils.debug("Vital " + vitalType + " shows 'Connect to view' - missing permissions");
                        } else {
                            hasAnyPermissions = true;
                            LogUtils.debug("Vital " + vitalType + " has permissions (data: " + vital.getVitalData() + ")");
                        }
                    } else {
                        LogUtils.debug("Failed to create vital for: " + vitalType);
                    }
                }

                LogUtils.debug("Permission summary - hasAllPermissions: " + hasAllPermissions + ", hasAnyPermissions: " + hasAnyPermissions);

                final boolean finalHasAllPermissions = hasAllPermissions;
                final boolean finalHasAnyPermissions = hasAnyPermissions;

                runOnUiThread(() -> {
                    for (VitalDataModel healthConnectVital : healthConnectVitals) {
                        String healthConnectType = healthConnectVital.getVitalType();
                        String serverVitalType = getServerVitalTypeFromHealthConnect(healthConnectType);

                        vitalList.removeIf(vital -> vital.getVitalType().equals(serverVitalType));

                        vitalList.add(0, healthConnectVital);
                        LogUtils.debug("Replaced server vital '" + serverVitalType + "' with Health Connect vital '" + healthConnectType + "'");
                    }

                    if (!finalHasAllPermissions) {
                        btnHealthConnect.setVisibility(View.VISIBLE);
                        LogUtils.debug("Showing connect button - missing some Health Connect permissions");
                    } else {
                        btnHealthConnect.setVisibility(View.GONE);
                        LogUtils.debug("Hiding connect button - all Health Connect permissions granted");
                    }

                    if (finalHasAnyPermissions) {
                        com.watchrx.watchrxhealth.utils.ReminderUtils.setReminderToSendHealthConnectDataToServer(VitalDashboard.this);
                        LogUtils.debug("Started Health Connect data scheduler on app load");
                    }

                    if (recyclerView.getAdapter() != null) {
                        recyclerView.getAdapter().notifyDataSetChanged();
                    }

                    LogUtils.debug("Added " + healthConnectVitals.size() + " Health Connect vitals to list");
                });

            } catch (Exception e) {
                LogUtils.debug("Error adding Health Connect vitals: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    private String getServerVitalTypeFromHealthConnect(String healthConnectType) {
        switch (healthConnectType) {
            case "Heart Rate (Health Connect)":
                return "Heart Rate";
            case "Steps (Health Connect)":
                return "Pedometer";
            case "Blood Pressure (Health Connect)":
                return "Blood Pressure";
            case "Blood Glucose (Health Connect)":
                return "Random Blood Sugar";
            case "Oxygen Saturation (Health Connect)":
                return "Oxygen Saturation";
            case "Weight (Health Connect)":
                return "Weight";
            case "Body Temperature (Health Connect)":
                return "Temperature";
            case "Sleep (Health Connect)":
                return "Sleep Monitor";
            default:
                return healthConnectType.replace(" (Health Connect)", "");
        }
    }

    private void setupHealthConnectPermissionLauncher() {
        try {
            healthConnectPermissionLauncher = registerForActivityResult(
                healthConnectManager.createPermissionRequestContract(),
                grantedPermissions -> {
                    LogUtils.debug("Health Connect permission result: " + grantedPermissions);
                    LogUtils.debug("Total granted permissions: " + grantedPermissions.size());

                    boolean hasAnyPermissions = !grantedPermissions.isEmpty();

                    if (hasAnyPermissions) {
                        String message = "Health Connect permissions granted! Refreshing vitals...";
                        LogUtils.debug("Some Health Connect permissions granted, refreshing data");

                        com.watchrx.watchrxhealth.utils.ReminderUtils.setReminderToSendHealthConnectDataToServer(this);
                        LogUtils.debug("Started Health Connect data scheduler");

                        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

                        refreshHealthConnectVitals();
                    } else {
                        Toast.makeText(this, "Health Connect permissions are required to show health data", Toast.LENGTH_LONG).show();
                        LogUtils.debug("No Health Connect permissions granted");
                    }
                }
        );
        LogUtils.debug("Health Connect permission launcher setup completed");
        } catch (Exception e) {
            LogUtils.debug("Error setting up permission launcher: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void refreshHealthConnectVitals() {
        LogUtils.debug("Refreshing Health Connect vitals after permission grant");

        new Thread(() -> {
            try {
                runOnUiThread(() -> {
                    if (recyclerView.getAdapter() instanceof VitalListAdapter) {
                        VitalListAdapter currentAdapter = (VitalListAdapter) recyclerView.getAdapter();
                        List<VitalDataModel> currentVitalsList = currentAdapter.getVitalsList();

                        List<VitalDataModel> vitalsToRemove = new ArrayList<>();
                        for (VitalDataModel vital : currentVitalsList) {
                            if (vital.getVitalType().contains("Health Connect")) {
                                vitalsToRemove.add(vital);
                            }
                        }
                        currentVitalsList.removeAll(vitalsToRemove);

                        currentAdapter.notifyDataSetChanged();

                        LogUtils.debug("Removed old Health Connect vitals, reloading...");

                        new Thread(() -> {
                            try {
                                Thread.sleep(500);

                                addHealthConnectBloodGlucoseData(currentVitalsList);

                            } catch (Exception e) {
                                LogUtils.debug("Error reloading Health Connect vitals: " + e.getMessage());
                                e.printStackTrace();
                            }
                        }).start();
                    } else {
                        LogUtils.debug("Adapter is not VitalListAdapter, refreshing entire vitals list");
                        getLatestVitalInfo();
                    }
                });

            } catch (Exception e) {
                LogUtils.debug("Error refreshing Health Connect vitals: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    private void setupHealthConnectButton() {
        btnHealthConnect.setOnClickListener(v -> requestHealthConnectPermissions());
    }

    private void handleHealthConnectCollect(VitalDataModel item) {
        LogUtils.debug("Collect Now clicked for: " + item.getVitalType());

        new Thread(() -> {
            try {
                if (item.getVitalType().contains("Blood Glucose")) {
                    handleBloodGlucoseCollect();
                } else if (item.getVitalType().contains("Heart Rate")) {
                    handleHeartRateCollect();
                } else if (item.getVitalType().contains("Blood Pressure")) {
                    handleBloodPressureCollect();
                } else if (item.getVitalType().contains("Oxygen Saturation")) {
                    handleOxygenSaturationCollect();
                } else if (item.getVitalType().contains("Steps")) {
                    handleStepsCollect();
                } else if (item.getVitalType().contains("Sleep")) {
                    handleSleepCollect();
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Unknown vital type: " + item.getVitalType(), Toast.LENGTH_SHORT).show();
                    });
                }

            } catch (Exception e) {
                LogUtils.debug("Error in Data Collection: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    private void handleBloodGlucoseCollect() {
        try {
            if (healthConnectManager.hasBloodGlucosePermissionsSync()) {
                HealthConnectManager.BloodGlucoseData latestGlucose = healthConnectManager.getLatestBloodGlucoseSync();
                if (latestGlucose != null) {
                    String measurementTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date.from(latestGlucose.getTime()));

                    double glucoseMmolL = latestGlucose.getLevel();
                    double glucoseMgDl = CommUtils.convertBloodGlucoseMmolToMgDl(glucoseMmolL);

                    LogUtils.debug("Sending blood glucose data to server: " + glucoseMmolL + " mmol/L = " + String.format("%.0f", glucoseMgDl) + " mg/dL");
                    CommUtils.sendHealthConnectBloodGlucoseToServer(this, glucoseMmolL, measurementTime);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "Blood glucose sent (" + String.format("%.1f", glucoseMmolL) + " mmol/L = " + String.format("%.0f", glucoseMgDl) + " mg/dL)", Toast.LENGTH_LONG).show();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "No blood glucose data available", Toast.LENGTH_SHORT).show();
                    });
                }
            } else {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Blood glucose permissions not available", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            LogUtils.debug("Error collecting blood glucose: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleHeartRateCollect() {
        try {
            if (healthConnectManager.hasHeartRatePermissionsSync()) {
                HealthConnectManager.HeartRateData heartRateData = healthConnectManager.getLatestHeartRateSync();
                if (heartRateData != null) {
                    String measurementTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date.from(heartRateData.getTime()));

                    LogUtils.debug("Sending heart rate data to server: " + heartRateData.getBeatsPerMinute() + " bpm");
                    CommUtils.sendHealthConnectHeartRateToServer(this, heartRateData.getBeatsPerMinute(), measurementTime);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "Heart rate sent (" + heartRateData.getBeatsPerMinute() + " bpm)", Toast.LENGTH_LONG).show();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "No heart rate data available", Toast.LENGTH_SHORT).show();
                    });
                }
            } else {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Heart rate permissions not available", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            LogUtils.debug("Error collecting heart rate: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleBloodPressureCollect() {
        try {
            if (healthConnectManager.hasBloodPressurePermissionsSync()) {
                HealthConnectManager.BloodPressureData bpData = healthConnectManager.getLatestBloodPressureSync();
                if (bpData != null) {
                    String measurementTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date.from(bpData.getTime()));

                    LogUtils.debug("Sending blood pressure data to server: " + bpData.getSystolic() + "/" + bpData.getDiastolic() + " mmHg");
                    CommUtils.sendHealthConnectBloodPressureToServer(this, bpData.getSystolic(), bpData.getDiastolic(), measurementTime);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "Blood pressure sent (" + String.format("%.0f/%.0f", bpData.getSystolic(), bpData.getDiastolic()) + " mmHg)", Toast.LENGTH_LONG).show();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "No blood pressure data available", Toast.LENGTH_SHORT).show();
                    });
                }
            } else {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Blood pressure permissions not available", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            LogUtils.debug("Error collecting blood pressure: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleOxygenSaturationCollect() {
        try {
            if (healthConnectManager.hasOxygenSaturationPermissionsSync()) {
                HealthConnectManager.OxygenSaturationData spo2Data = healthConnectManager.getLatestOxygenSaturationSync();
                if (spo2Data != null) {
                    String measurementTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date.from(spo2Data.getTime()));

                    LogUtils.debug("Sending oxygen saturation data to server: " + spo2Data.getPercentage() + "%");
                    CommUtils.sendHealthConnectOxygenSaturationToServer(this, spo2Data.getPercentage(), measurementTime);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "Oxygen saturation sent (" + String.format("%.1f", spo2Data.getPercentage()) + "%)", Toast.LENGTH_LONG).show();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "No oxygen saturation data available", Toast.LENGTH_SHORT).show();
                    });
                }
            } else {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Oxygen saturation permissions not available", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            LogUtils.debug("Error collecting oxygen saturation: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleStepsCollect() {
        try {
            if (healthConnectManager.hasStepsPermissionsSync()) {
                long stepsData = healthConnectManager.getTodayStepsSync();
                if (stepsData >= 0) {
                    String measurementTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(new java.util.Date());

                    LogUtils.debug("Sending steps data to server: " + stepsData + " steps");
                    CommUtils.sendHealthConnectStepsToServer(this, stepsData, measurementTime);

                    runOnUiThread(() -> {
                        Toast.makeText(this, "Steps sent (" + stepsData + " steps)", Toast.LENGTH_LONG).show();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "No steps data available", Toast.LENGTH_SHORT).show();
                    });
                }
            } else {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Steps permissions not available", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            LogUtils.debug("Error collecting steps: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleSleepCollect() {
        try {
            if (healthConnectManager.hasSleepPermissionsSync()) {
                HealthConnectManager.SleepData sleepData = healthConnectManager.getLatestSleepSync();
                if (sleepData != null) {
                    String sleepStartTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date.from(sleepData.getStartTime()));
                    String sleepEndTime = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date.from(sleepData.getEndTime()));

                    LogUtils.debug("Sending sleep data to server: " + sleepData.getDuration() + " minutes");
                    CommUtils.sendHealthConnectSleepToServer(this, sleepData.getDuration(), sleepStartTime, sleepEndTime);

                    long hours = sleepData.getDuration() / 60;
                    long minutes = sleepData.getDuration() % 60;
                    runOnUiThread(() -> {
                        Toast.makeText(this, "Sleep data sent (" + String.format("%dh %dm", hours, minutes) + ")", Toast.LENGTH_LONG).show();
                    });
                } else {
                    runOnUiThread(() -> {
                        Toast.makeText(this, "No sleep data available", Toast.LENGTH_SHORT).show();
                    });
                }
            } else {
                runOnUiThread(() -> {
                    Toast.makeText(this, "Sleep permissions not available", Toast.LENGTH_SHORT).show();
                });
            }
        } catch (Exception e) {
            LogUtils.debug("Error collecting sleep: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void requestHealthConnectPermissions() {
        try {
            HealthConnectManager.HealthConnectAvailability availability = healthConnectManager.checkHealthConnectAvailability();
            if (availability != HealthConnectManager.HealthConnectAvailability.AVAILABLE) {
                String message = "Health Connect is not available. Please install or update Health Connect app.";
                Toast.makeText(this, message, Toast.LENGTH_LONG).show();
                LogUtils.debug("Health Connect not available: " + availability);
                return;
            }
            Set<String> missingPermissions = new HashSet<>();

            for (String vitalType : com.watchrx.watchrxhealth.globals.Globals.healthConnectVitalsList) {
                switch (vitalType) {
                    case "heartRate":
                        if (!healthConnectManager.hasHeartRatePermissionsSync()) {
                            missingPermissions.addAll(healthConnectManager.getHeartRatePermissions());
                            LogUtils.debug("Adding heart rate permissions to request");
                        }
                        break;
                    case "bloodSugar":
                        if (!healthConnectManager.hasBloodGlucosePermissionsSync()) {
                            missingPermissions.addAll(healthConnectManager.getBloodGlucosePermissions());
                            LogUtils.debug("Adding blood glucose permissions to request");
                        }
                        break;
                    case "bloodPressure":
                        if (!healthConnectManager.hasBloodPressurePermissionsSync()) {
                            missingPermissions.addAll(healthConnectManager.getBloodPressurePermissions());
                            LogUtils.debug("Adding blood pressure permissions to request");
                        }
                        break;
                    case "spo2":
                        if (!healthConnectManager.hasOxygenSaturationPermissionsSync()) {
                            missingPermissions.addAll(healthConnectManager.getOxygenSaturationPermissions());
                            LogUtils.debug("Adding oxygen saturation permissions to request");
                        }
                        break;
                    case "steps":
                        if (!healthConnectManager.hasStepsPermissionsSync()) {
                            missingPermissions.addAll(healthConnectManager.getStepsPermissions());
                            LogUtils.debug("Adding steps permissions to request");
                        }
                        break;
                    case "sleep":
                        if (!healthConnectManager.hasSleepPermissionsSync()) {
                            missingPermissions.addAll(healthConnectManager.getSleepPermissions());
                            LogUtils.debug("Adding sleep permissions to request");
                        }
                        break;
                }
            }

            if (missingPermissions.isEmpty()) {
                LogUtils.debug("No missing permissions, refreshing data");
                Toast.makeText(this, "All permissions already granted!", Toast.LENGTH_SHORT).show();
                getLatestVitalInfo();
                return;
            }

            LogUtils.debug("Requesting missing Health Connect permissions: " + missingPermissions);
            Toast.makeText(this, "Opening Health Connect permissions...", Toast.LENGTH_SHORT).show();

            try {
                healthConnectPermissionLauncher.launch(missingPermissions);
                LogUtils.debug("Permission launcher called successfully");
            } catch (Exception launchException) {
                LogUtils.debug("Error launching permission request: " + launchException.getMessage());
                launchException.printStackTrace();

                try {
                    Intent intent = new Intent();
                    intent.setAction("androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE");
                    intent.setPackage("com.google.android.apps.healthdata");
                    if (intent.resolveActivity(getPackageManager()) != null) {
                        startActivity(intent);
                        LogUtils.debug("Opened Health Connect app directly");
                    } else {
                        Intent playStoreIntent = new Intent(Intent.ACTION_VIEW);
                        playStoreIntent.setData(android.net.Uri.parse("market://details?id=com.google.android.apps.healthdata"));
                        if (playStoreIntent.resolveActivity(getPackageManager()) != null) {
                            startActivity(playStoreIntent);
                            Toast.makeText(this, "Please install Health Connect from Play Store", Toast.LENGTH_LONG).show();
                        } else {
                            Toast.makeText(this, "Unable to open Health Connect permissions", Toast.LENGTH_LONG).show();
                        }
                    }
                } catch (Exception fallbackException) {
                    LogUtils.debug("Fallback also failed: " + fallbackException.getMessage());
                    Toast.makeText(this, "Unable to open Health Connect. Please check if it's installed.", Toast.LENGTH_LONG).show();
                }
            }

        } catch (Exception e) {
            LogUtils.debug("Error requesting Health Connect permissions: " + e.getMessage());
            e.printStackTrace();
            Toast.makeText(this, "Error opening Health Connect permissions: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
}