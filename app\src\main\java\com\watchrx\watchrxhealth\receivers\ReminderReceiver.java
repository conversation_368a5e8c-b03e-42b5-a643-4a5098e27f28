package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.List;

import static com.watchrx.watchrxhealth.constants.CommonConstants.MAX_TRIGGER_TIME_DIFFERENCE;
import static java.lang.Math.abs;

public class ReminderReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {

        LogUtils.debug("Reminder alarm to be triggered at: '" + intent.getLongExtra("triggerAt", -1)
                + " for the: " + intent.getIntExtra("timesAlreadyCalled", -1) + " time was received at: '"
                + System.currentTimeMillis() + "' for: '" + intent.getStringExtra("beforeOrAfterFood") + "-" + intent.getStringExtra("timeSlot") + "'");
        long triggerTimeMillis = intent.getLongExtra("triggerAt", -1);
        long currentTimeMillis = System.currentTimeMillis();

        if (abs(currentTimeMillis - triggerTimeMillis) > MAX_TRIGGER_TIME_DIFFERENCE) {
            LogUtils.debug("This alarm is  outside the time window");
            return; // too much difference; no longer valid alarm
        }

        Globals.intentMap.remove(intent.getStringExtra("beforeOrAfterFood") + "-" + intent.getStringExtra("timeSlot"));

        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setBeforeOrAfterFood(intent.getStringExtra("beforeOrAfterFood"));
        info.setTimeSlot(intent.getStringExtra("timeSlot"));
        info.setTimesAlreadyCalled(intent.getIntExtra("timesAlreadyCalled", -1));
        info.setTriggerAt(intent.getLongExtra("triggerAt", -1));
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("ReminderActivity");
        Globals.priorityQueue.add(info);

        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(intent.getStringExtra("beforeOrAfterFood"), intent.getStringExtra("timeSlot"));
            LogUtils.debug("Total Medication needs to take this time slot :" + intent.getStringExtra("timeSlot") + " AND " + intent.getStringExtra("beforeOrAfterFood"));
            if (!scheduleInstanceList.isEmpty()) {
                CommUtils.sendReminderDisplayedLogToServer(context, "Displayed Notification", intent.getStringExtra("alertId"), intent.getStringExtra("beforeOrAfterFood"), intent.getStringExtra("timeSlot"), scheduleInstanceList);
                final NotificationHelper notificationHelper = new NotificationHelper(context);
                notificationHelper.medicationNotification("You have to take "
                        + scheduleInstanceList.size() + " Medication, Please click on here", "WatchRx Medication Notification");

                Intent alertCount = new Intent(MainActivity.SEND_MESSAGE_TO_WATCH);
                alertCount.putExtra("message", "WatchRx Medication Notification \n You have to take " + scheduleInstanceList.size() + "  Medication.");
                LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
            }
        }
    }
}
