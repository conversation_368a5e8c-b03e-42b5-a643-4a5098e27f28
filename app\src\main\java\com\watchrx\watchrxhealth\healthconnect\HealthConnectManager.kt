package com.watchrx.watchrxhealth.healthconnect

import android.content.Context
import android.util.Log
import androidx.health.connect.client.HealthConnectClient
import androidx.health.connect.client.PermissionController
import androidx.health.connect.client.aggregate.AggregationResult
import androidx.health.connect.client.permission.HealthPermission
import androidx.health.connect.client.records.StepsRecord
import androidx.health.connect.client.records.BloodGlucoseRecord
import androidx.health.connect.client.records.HeartRateRecord
import androidx.health.connect.client.records.BloodPressureRecord
import androidx.health.connect.client.records.OxygenSaturationRecord
import androidx.health.connect.client.records.SleepSessionRecord
import androidx.health.connect.client.request.AggregateRequest
import androidx.health.connect.client.request.ReadRecordsRequest
import androidx.health.connect.client.time.TimeRangeFilter
import com.watchrx.watchrxhealth.utils.LogUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset

class HealthConnectManager(private val context: Context) {
    
    companion object {
        private const val TAG = "HealthConnectManager"
        const val HEALTH_CONNECT_PACKAGE_NAME = "com.google.android.apps.healthdata"
    }
    
    private var healthConnectClient: HealthConnectClient? = null

    fun checkHealthConnectAvailability(): HealthConnectAvailability {
        return when (HealthConnectClient.getSdkStatus(context, HEALTH_CONNECT_PACKAGE_NAME)) {
            HealthConnectClient.SDK_UNAVAILABLE -> {
                LogUtils.debug("Health Connect SDK is unavailable")
                HealthConnectAvailability.NOT_SUPPORTED
            }
            HealthConnectClient.SDK_UNAVAILABLE_PROVIDER_UPDATE_REQUIRED -> {
                LogUtils.debug("Health Connect app needs to be updated")
                HealthConnectAvailability.NEEDS_UPDATE
            }
            else -> {
                LogUtils.debug("Health Connect is available")
                HealthConnectAvailability.AVAILABLE
            }
        }
    }

    private fun getHealthConnectClient(): HealthConnectClient {
        if (healthConnectClient == null) {
            healthConnectClient = HealthConnectClient.getOrCreate(context)
        }
        return healthConnectClient!!
    }

    fun getStepsPermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(StepsRecord::class),
            HealthPermission.getWritePermission(StepsRecord::class)
        )
    }

    fun getBloodGlucosePermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(BloodGlucoseRecord::class),
            HealthPermission.getWritePermission(BloodGlucoseRecord::class)
        )
    }

    fun getHeartRatePermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(HeartRateRecord::class),
            HealthPermission.getWritePermission(HeartRateRecord::class)
        )
    }

    fun getBloodPressurePermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(BloodPressureRecord::class),
            HealthPermission.getWritePermission(BloodPressureRecord::class)
        )
    }

    fun getOxygenSaturationPermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(OxygenSaturationRecord::class),
            HealthPermission.getWritePermission(OxygenSaturationRecord::class)
        )
    }

    fun getSleepPermissions(): Set<String> {
        return setOf(
            HealthPermission.getReadPermission(SleepSessionRecord::class),
            HealthPermission.getWritePermission(SleepSessionRecord::class)
        )
    }

    fun getAllHealthConnectPermissions(): Set<String> {
        val permissions = mutableSetOf<String>()

        com.watchrx.watchrxhealth.globals.Globals.healthConnectVitalsList.forEach { vital ->
            when (vital) {
                "steps" -> permissions.addAll(getStepsPermissions())
                "bloodSugar" -> permissions.addAll(getBloodGlucosePermissions())
                "heartRate" -> permissions.addAll(getHeartRatePermissions())
                "bloodPressure" -> permissions.addAll(getBloodPressurePermissions())
                "spo2" -> permissions.addAll(getOxygenSaturationPermissions())
                "sleep" -> permissions.addAll(getSleepPermissions())
            }
        }

        return permissions
    }

    suspend fun hasStepsPermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getStepsPermissions()
                LogUtils.debug("Steps granted permissions: $grantedPermissions")
                LogUtils.debug("Steps required permissions: $requiredPermissions")
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking steps permissions", e)
                false
            }
        }
    }

    suspend fun hasBloodGlucosePermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getBloodGlucosePermissions()
                LogUtils.debug("Blood glucose granted permissions: $grantedPermissions")
                LogUtils.debug("Blood glucose required permissions: $requiredPermissions")
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking blood glucose permissions", e)
                false
            }
        }
    }

    suspend fun hasHeartRatePermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getHeartRatePermissions()
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking heart rate permissions", e)
                false
            }
        }
    }

    suspend fun hasBloodPressurePermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getBloodPressurePermissions()
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking blood pressure permissions", e)
                false
            }
        }
    }

    suspend fun hasOxygenSaturationPermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getOxygenSaturationPermissions()
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking oxygen saturation permissions", e)
                false
            }
        }
    }

    suspend fun hasSleepPermissions(): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val grantedPermissions = client.permissionController.getGrantedPermissions()
                val requiredPermissions = getSleepPermissions()
                grantedPermissions.containsAll(requiredPermissions)
            } catch (e: Exception) {
                Log.e(TAG, "Error checking sleep permissions", e)
                false
            }
        }
    }

    fun createPermissionRequestContract() = PermissionController.createRequestPermissionResultContract()
    suspend fun getTodaySteps(): Long {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val today = LocalDate.now()
                val startOfDay = today.atStartOfDay(ZoneId.systemDefault()).toInstant()
                val endOfDay = today.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()
                LogUtils.debug("Reading steps from $startOfDay to $endOfDay")
                val request = AggregateRequest(
                    metrics = setOf(StepsRecord.COUNT_TOTAL),
                    timeRangeFilter = TimeRangeFilter.between(startOfDay, endOfDay)
                )
                
                val response = client.aggregate(request)
                val totalSteps = response[StepsRecord.COUNT_TOTAL] ?: 0L
                
                LogUtils.debug("Total steps for today: $totalSteps")
                totalSteps
                
            } catch (e: Exception) {
                Log.e(TAG, "Error reading steps data", e)
                0L
            }
        }
    }

    suspend fun getStepsInRange(startTime: Instant, endTime: Instant): List<StepData> {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val request = ReadRecordsRequest(
                    recordType = StepsRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )
                val response = client.readRecords(request)
                val stepDataList = response.records.map { record ->
                    StepData(
                        count = record.count,
                        startTime = record.startTime,
                        endTime = record.endTime
                    )
                }
                LogUtils.debug("Found ${stepDataList.size} step records")
                stepDataList
            } catch (e: Exception) {
                Log.e(TAG, "Error reading steps range data", e)
                emptyList()
            }
        }
    }

    suspend fun writeStepsData(count: Long, startTime: Instant, endTime: Instant): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val stepsRecord = StepsRecord(
                    count = count,
                    startTime = startTime,
                    endTime = endTime,
                    startZoneOffset = ZoneOffset.systemDefault().rules.getOffset(startTime),
                    endZoneOffset = ZoneOffset.systemDefault().rules.getOffset(endTime)
                )
                client.insertRecords(listOf(stepsRecord))
                LogUtils.debug("Successfully wrote $count steps from $startTime to $endTime")
                true
            } catch (e: Exception) {
                Log.e(TAG, "Error writing steps data", e)
                false
            }
        }
    }

    suspend fun getLatestBloodGlucose(): BloodGlucoseData? {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val endTime = Instant.now()
                val startTime = endTime.minus(30, java.time.temporal.ChronoUnit.DAYS)

                LogUtils.debug("Reading blood glucose from $startTime to $endTime")

                val request = ReadRecordsRequest(
                    recordType = BloodGlucoseRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )

                val response = client.readRecords(request)
                val bloodGlucoseRecords = response.records
                if (bloodGlucoseRecords.isNotEmpty()) {
                    val latestReading = bloodGlucoseRecords.maxByOrNull { it.time }
                    if (latestReading != null) {
                        LogUtils.debug("Found latest blood glucose: ${latestReading.level.inMillimolesPerLiter} mmol/L")
                        return@withContext BloodGlucoseData(
                            level = latestReading.level.inMillimolesPerLiter,
                            time = latestReading.time,
                            specimenSource = latestReading.specimenSource,
                            mealType = latestReading.mealType,
                            relationToMeal = latestReading.relationToMeal
                        )
                    }
                }
                LogUtils.debug("No blood glucose data found")
                null
            } catch (e: Exception) {
                Log.e(TAG, "Error reading blood glucose data", e)
                null
            }
        }
    }

    suspend fun getBloodGlucoseInRange(startTime: Instant, endTime: Instant): List<BloodGlucoseData> {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val request = ReadRecordsRequest(
                    recordType = BloodGlucoseRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )
                val response = client.readRecords(request)
                val bloodGlucoseDataList = response.records.map { record ->
                    BloodGlucoseData(
                        level = record.level.inMillimolesPerLiter,
                        time = record.time,
                        specimenSource = record.specimenSource,
                        mealType = record.mealType,
                        relationToMeal = record.relationToMeal
                    )
                }

                LogUtils.debug("Found ${bloodGlucoseDataList.size} blood glucose records")
                bloodGlucoseDataList

            } catch (e: Exception) {
                Log.e(TAG, "Error reading blood glucose range data", e)
                emptyList()
            }
        }
    }

    data class StepData(
        val count: Long,
        val startTime: Instant,
        val endTime: Instant
    )

    data class BloodGlucoseData(
        val level: Double,
        val time: Instant,
        val specimenSource: Int,
        val mealType: Int,
        val relationToMeal: Int
    )

    data class HeartRateData(
        val beatsPerMinute: Long,
        val time: Instant
    )

    data class BloodPressureData(
        val systolic: Double,
        val diastolic: Double,
        val time: Instant
    )

    data class OxygenSaturationData(
        val percentage: Double,
        val time: Instant
    )

    data class SleepData(
        val startTime: Instant,
        val endTime: Instant,
        val duration: Long
    )

    suspend fun getLatestHeartRate(): HeartRateData? {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val endTime = Instant.now()
                val startTime = endTime.minus(7, java.time.temporal.ChronoUnit.DAYS)

                val request = ReadRecordsRequest(
                    recordType = HeartRateRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )

                val response = client.readRecords(request)
                val heartRateRecords = response.records

                if (heartRateRecords.isNotEmpty()) {
                    val latestReading = heartRateRecords.maxByOrNull { it.startTime }
                    if (latestReading != null && latestReading.samples.isNotEmpty()) {
                        val latestSample = latestReading.samples.maxByOrNull { it.time }
                        if (latestSample != null) {
                            LogUtils.debug("Found latest heart rate: ${latestSample.beatsPerMinute} bpm")
                            return@withContext HeartRateData(
                                beatsPerMinute = latestSample.beatsPerMinute,
                                time = latestSample.time
                            )
                        }
                    }
                }

                LogUtils.debug("No heart rate data found")
                null

            } catch (e: Exception) {
                Log.e(TAG, "Error reading heart rate data", e)
                null
            }
        }
    }

    suspend fun getLatestBloodPressure(): BloodPressureData? {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val endTime = Instant.now()
                val startTime = endTime.minus(7, java.time.temporal.ChronoUnit.DAYS)

                val request = ReadRecordsRequest(
                    recordType = BloodPressureRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )

                val response = client.readRecords(request)
                val bloodPressureRecords = response.records

                if (bloodPressureRecords.isNotEmpty()) {
                    val latestReading = bloodPressureRecords.maxByOrNull { it.time }
                    if (latestReading != null) {
                        LogUtils.debug("Found latest blood pressure: ${latestReading.systolic.inMillimetersOfMercury}/${latestReading.diastolic.inMillimetersOfMercury} mmHg")
                        return@withContext BloodPressureData(
                            systolic = latestReading.systolic.inMillimetersOfMercury,
                            diastolic = latestReading.diastolic.inMillimetersOfMercury,
                            time = latestReading.time
                        )
                    }
                }

                LogUtils.debug("No blood pressure data found")
                null

            } catch (e: Exception) {
                Log.e(TAG, "Error reading blood pressure data", e)
                null
            }
        }
    }

    suspend fun getLatestOxygenSaturation(): OxygenSaturationData? {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val endTime = Instant.now()
                val startTime = endTime.minus(7, java.time.temporal.ChronoUnit.DAYS)

                val request = ReadRecordsRequest(
                    recordType = OxygenSaturationRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )

                val response = client.readRecords(request)
                val oxygenSaturationRecords = response.records

                if (oxygenSaturationRecords.isNotEmpty()) {
                    val latestReading = oxygenSaturationRecords.maxByOrNull { it.time }
                    if (latestReading != null) {
                        LogUtils.debug("Found latest oxygen saturation: ${latestReading.percentage.value}%")
                        return@withContext OxygenSaturationData(
                            percentage = latestReading.percentage.value,
                            time = latestReading.time
                        )
                    }
                }

                LogUtils.debug("No oxygen saturation data found")
                null

            } catch (e: Exception) {
                Log.e(TAG, "Error reading oxygen saturation data", e)
                null
            }
        }
    }

    suspend fun getLatestSleep(): SleepData? {
        return withContext(Dispatchers.IO) {
            try {
                val client = getHealthConnectClient()
                val endTime = Instant.now()
                val startTime = endTime.minus(7, java.time.temporal.ChronoUnit.DAYS)

                val request = ReadRecordsRequest(
                    recordType = SleepSessionRecord::class,
                    timeRangeFilter = TimeRangeFilter.between(startTime, endTime)
                )

                val response = client.readRecords(request)
                val sleepRecords = response.records

                if (sleepRecords.isNotEmpty()) {
                    val latestSleep = sleepRecords.maxByOrNull { it.startTime }
                    if (latestSleep != null) {
                        val durationMinutes = java.time.Duration.between(latestSleep.startTime, latestSleep.endTime).toMinutes()
                        LogUtils.debug("Found latest sleep: ${durationMinutes} minutes")
                        return@withContext SleepData(
                            startTime = latestSleep.startTime,
                            endTime = latestSleep.endTime,
                            duration = durationMinutes
                        )
                    }
                }

                LogUtils.debug("No sleep data found")
                null

            } catch (e: Exception) {
                Log.e(TAG, "Error reading sleep data", e)
                null
            }
        }
    }

    fun hasStepsPermissionsSync(): Boolean {
        return try {
            kotlinx.coroutines.runBlocking {
                hasStepsPermissions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking steps permissions sync", e)
            false
        }
    }

    fun hasBloodGlucosePermissionsSync(): Boolean {
        return try {
            kotlinx.coroutines.runBlocking {
                hasBloodGlucosePermissions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking blood glucose permissions sync", e)
            false
        }
    }

    fun getTodayStepsSync(): Long {
        return try {
            kotlinx.coroutines.runBlocking {
                getTodaySteps()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting today steps sync", e)
            0L
        }
    }

    fun getLatestBloodGlucoseSync(): BloodGlucoseData? {
        return try {
            kotlinx.coroutines.runBlocking {
                getLatestBloodGlucose()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting latest blood glucose sync", e)
            null
        }
    }

    fun getLatestHeartRateSync(): HeartRateData? {
        return try {
            kotlinx.coroutines.runBlocking {
                getLatestHeartRate()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting latest heart rate sync", e)
            null
        }
    }

    fun getLatestBloodPressureSync(): BloodPressureData? {
        return try {
            kotlinx.coroutines.runBlocking {
                getLatestBloodPressure()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting latest blood pressure sync", e)
            null
        }
    }

    fun getLatestOxygenSaturationSync(): OxygenSaturationData? {
        return try {
            kotlinx.coroutines.runBlocking {
                getLatestOxygenSaturation()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting latest oxygen saturation sync", e)
            null
        }
    }

    fun getLatestSleepSync(): SleepData? {
        return try {
            kotlinx.coroutines.runBlocking {
                getLatestSleep()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting latest sleep sync", e)
            null
        }
    }

    fun hasHeartRatePermissionsSync(): Boolean {
        return try {
            kotlinx.coroutines.runBlocking {
                hasHeartRatePermissions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking heart rate permissions sync", e)
            false
        }
    }

    fun hasBloodPressurePermissionsSync(): Boolean {
        return try {
            kotlinx.coroutines.runBlocking {
                hasBloodPressurePermissions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking blood pressure permissions sync", e)
            false
        }
    }

    fun hasOxygenSaturationPermissionsSync(): Boolean {
        return try {
            kotlinx.coroutines.runBlocking {
                hasOxygenSaturationPermissions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking oxygen saturation permissions sync", e)
            false
        }
    }

    fun hasSleepPermissionsSync(): Boolean {
        return try {
            kotlinx.coroutines.runBlocking {
                hasSleepPermissions()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking sleep permissions sync", e)
            false
        }
    }

    enum class HealthConnectAvailability {
        AVAILABLE,
        NOT_SUPPORTED,
        NEEDS_UPDATE
    }
}