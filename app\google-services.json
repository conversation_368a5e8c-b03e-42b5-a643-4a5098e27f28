{"project_info": {"project_number": "132597499285", "firebase_url": "https://watchrx-1007.firebaseio.com", "project_id": "watchrx-1007", "storage_bucket": "watchrx-1007.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:132597499285:android:9757de861b4f59e2", "android_client_info": {"package_name": "com.watchrx.caregiver"}}, "oauth_client": [{"client_id": "132597499285-j5595fr9kvpgutlfhaapv4bkv2rk24ij.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "132597499285-93gsl2ko3e3thth1cimerda4i819940b.apps.googleusercontent.com", "client_type": 3}, {"client_id": "132597499285-4lgve0hkbarufbv1c6qe019ced83d10n.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.watchrx.hospitalcaregiver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:132597499285:android:7ea6d40ba08d6cc319e29c", "android_client_info": {"package_name": "com.watchrx.hospitalcaregiver"}}, "oauth_client": [{"client_id": "132597499285-j5595fr9kvpgutlfhaapv4bkv2rk24ij.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "132597499285-93gsl2ko3e3thth1cimerda4i819940b.apps.googleusercontent.com", "client_type": 3}, {"client_id": "132597499285-4lgve0hkbarufbv1c6qe019ced83d10n.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.watchrx.hospitalcaregiver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:132597499285:android:16ee1271ab01ff17", "android_client_info": {"package_name": "com.watchrx.watch"}}, "oauth_client": [{"client_id": "132597499285-56qutt68hnhafk05jmkodeefm70togb9.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.watchrx.watch", "certificate_hash": "ea3ffdcbb631d61c744e5fbc80526abd2431ebf1"}}, {"client_id": "132597499285-j5595fr9kvpgutlfhaapv4bkv2rk24ij.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "132597499285-93gsl2ko3e3thth1cimerda4i819940b.apps.googleusercontent.com", "client_type": 3}, {"client_id": "132597499285-4lgve0hkbarufbv1c6qe019ced83d10n.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.watchrx.hospitalcaregiver"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:132597499285:android:ebc4577d313cba0519e29c", "android_client_info": {"package_name": "com.watchrx.watchrxhealth"}}, "oauth_client": [{"client_id": "132597499285-6ac0i1gjnpseipba5fcgs100uv4ftukt.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.watchrx.watchrxhealth", "certificate_hash": "6f07744de6119893eed23bdb77394e651711dbd6"}}, {"client_id": "132597499285-j5595fr9kvpgutlfhaapv4bkv2rk24ij.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "132597499285-93gsl2ko3e3thth1cimerda4i819940b.apps.googleusercontent.com", "client_type": 3}, {"client_id": "132597499285-4lgve0hkbarufbv1c6qe019ced83d10n.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.watchrx.hospitalcaregiver"}}]}}}], "configuration_version": "1"}