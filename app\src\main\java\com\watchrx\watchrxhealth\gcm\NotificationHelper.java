package com.watchrx.watchrxhealth.gcm;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.Intent;
import android.media.AudioAttributes;
import android.media.RingtoneManager;
import android.net.Uri;

import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.twilio.voice.CallInvite;
import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.WatchRxForegroundService;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.voip.IncomingCallActivity;

public class NotificationHelper extends ContextWrapper {

    private static final String CHANNEL_NAME = "WatchRx Notifications";
    private static final String CHANNEL_DESCRIPTION = "WatchRx Notification Description";
    private static final String CHANNEL_ID = "WatchRxNotificationChannel";
    private static NotificationManager notificationManager;
    private final Context base;

    public NotificationHelper(Context base) {
        super(base);
        this.base = base;
        createChannel();
    }

    private void createChannel() {
        Uri soundUri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.video_call_tone);

        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build();

        NotificationChannel notificationChannel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
        );

        notificationChannel.setDescription(CHANNEL_DESCRIPTION);
        notificationChannel.enableVibration(true);
        notificationChannel.enableLights(true);
        notificationChannel.setLightColor(getResources().getColor(R.color.colorAccent));
        notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        notificationChannel.setVibrationPattern(new long[]{0, 1000, 500, 1000});
        notificationChannel.setSound(soundUri, audioAttributes); // 🔔 Set custom sound here

        getNotificationManager().createNotificationChannel(notificationChannel);
    }


    public NotificationManager getNotificationManager() {
        if (notificationManager == null) {
            notificationManager = (NotificationManager) base.getSystemService(Context.NOTIFICATION_SERVICE);
        }
        return notificationManager;
    }


    public void notify(String message, String title) {
        Intent serviceIntent = new Intent(this, WatchRxForegroundService.class);
        serviceIntent.putExtra("inputExtra", message);
        ContextCompat.startForegroundService(this, serviceIntent);
    }

    public void medicationNotification(String message, String title) {
        getNotificationManager().cancelAll();
        NotificationCompat.Builder notification = new NotificationCompat.Builder(base, CHANNEL_ID);
        Uri alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        Intent notificationIntent = new Intent(this, MainActivity.class);
        Globals.reminderAlarmCount++;
        PendingIntent pendingIntent;
        pendingIntent = PendingIntent.getActivity(this, Globals.reminderAlarmCount, notificationIntent, PendingIntent.FLAG_IMMUTABLE);

        notification.setContentTitle(title);
        notification.setSmallIcon(R.drawable.logo);
        notification.setContentText(message);
        notification.setSound(alarmSound);
        notification.setDefaults(Notification.DEFAULT_ALL);
        notification.setPriority(Notification.PRIORITY_HIGH);
        notification.setStyle(new NotificationCompat.BigTextStyle().bigText(message));
        notification.setContentIntent(pendingIntent);
        getNotificationManager().notify(Globals.reminderAlarmCount++, notification.build());
    }

    public void videoCallNotification(String message, String title) {
        getNotificationManager().cancelAll();
        NotificationCompat.Builder notification = new NotificationCompat.Builder(base, CHANNEL_ID);
        Uri alarmSound = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, Globals.reminderAlarmCount, notificationIntent, PendingIntent.FLAG_IMMUTABLE);

        notification.setContentTitle(title);
        notification.setSmallIcon(R.drawable.logo);
        notification.setContentText(message);
        notification.setSound(alarmSound);
        notification.setVibrate(new long[]{0, 1000, 500, 1000});
        notification.setDefaults(Notification.DEFAULT_ALL);
        notification.setPriority(Notification.PRIORITY_HIGH);
        notification.setStyle(new NotificationCompat.BigTextStyle().bigText(message));
        notification.setContentIntent(pendingIntent);
        getNotificationManager().notify(Globals.reminderAlarmCount++, notification.build());
    }


    public void cancelAllNotifications() {
        getNotificationManager().cancelAll();
    }

    public void showIncomingCallNotification(Context context) {

        // Create intent for full-screen call activity
        Intent fullScreenIntent = new Intent(context, MainActivity.class); // Replace with your call activity
        fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        PendingIntent fullScreenPendingIntent = PendingIntent.getActivity(
                context,
                0,
                fullScreenIntent,
                PendingIntent.FLAG_IMMUTABLE
        );

        // Define custom sound URI from res/raw/ringtone.mp3
        Uri soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/" + R.raw.video_call_tone);

        // Build notification
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.app_logo_1)
                .setContentTitle("Incoming Zoom Call")
                .setContentText("Call from WatchRx")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_CALL)
                .setAutoCancel(true)
                .setSound(soundUri) // 🔊 Custom sound
                .setFullScreenIntent(fullScreenPendingIntent, true);

        // Create or update notification channel with custom sound
        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build();

        NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
        );
        channel.setDescription("Notifications for incoming Zoom video calls");
        channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        channel.enableLights(true);
        channel.enableVibration(true);
        channel.setSound(soundUri, audioAttributes);

        notificationManager.createNotificationChannel(channel);

        // Show notification
        notificationManager.notify(1001, builder.build());
    }


    private static final int NOTIFICATION_ID = 999;

    public static void showIncomingCallNotification(Context context, CallInvite invite) {

        Intent fullScreenIntent = new Intent(context, IncomingCallActivity.class);
        fullScreenIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        fullScreenIntent.putExtra("from", "Care Team");

        PendingIntent fullScreenPendingIntent = PendingIntent.getActivity(
                context,
                0,
                fullScreenIntent,
                PendingIntent.FLAG_IMMUTABLE
        );

        Uri soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/" + R.raw.video_call_tone);


        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
                .setSmallIcon(R.drawable.app_logo_1)
                .setContentTitle("Incoming Voice Call")
                .setContentText("From: Care Team")
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setCategory(NotificationCompat.CATEGORY_CALL)
                .setAutoCancel(true)
                .setSound(soundUri) // 🔊 Custom sound
                .setFullScreenIntent(fullScreenPendingIntent, true);

        NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

        AudioAttributes audioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build();

        NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
        );
        channel.setDescription("Notifications for incoming Zoom video calls");
        channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        channel.enableLights(true);
        channel.enableVibration(true);
        channel.setSound(soundUri, audioAttributes);

        notificationManager.createNotificationChannel(channel);

        notificationManager.notify(NOTIFICATION_ID, builder.build());
    }

    public static void clearIncomingCallNotification(Context context) {
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        manager.cancel(NOTIFICATION_ID);
    }

    private static void createNotificationChannel(Context context) {
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        if (manager != null) {
            NotificationChannel existingChannel = manager.getNotificationChannel(CHANNEL_ID);
            if (existingChannel == null) {
                Uri soundUri = Uri.parse("android.resource://" + context.getPackageName() + "/" + R.raw.video_call_tone);
                AudioAttributes audioAttributes = new AudioAttributes.Builder()
                        .setUsage(AudioAttributes.USAGE_NOTIFICATION_RINGTONE)
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                        .build();
                NotificationChannel channel = new NotificationChannel(
                        CHANNEL_ID,
                        "WatchRx Voice Calls",
                        NotificationManager.IMPORTANCE_HIGH
                );
                channel.setDescription("Used for incoming voice calls.");
                channel.enableVibration(true);
                channel.setSound(soundUri, audioAttributes);
                channel.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
                manager.createNotificationChannel(channel);
            }
        }
    }
}
