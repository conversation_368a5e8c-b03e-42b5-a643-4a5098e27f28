package com.watchrx.watchrxhealth;

import android.annotation.SuppressLint;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.net.ConnectivityManager;
import android.net.Network;
import android.os.Build;
import android.util.Log;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.Lifecycle;
import androidx.lifecycle.LifecycleObserver;
import androidx.lifecycle.OnLifecycleEvent;
import androidx.lifecycle.ProcessLifecycleOwner;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.DefaultExceptionHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;

public class WatchApp extends Application implements LifecycleObserver {
    public static AudioManager audioManager;
    @SuppressLint("StaticFieldLeak")
    private static Context mContext;

    @Override
    public void onTerminate() {
        LogUtils.debug("WatchApp Application onTerminated");
        super.onTerminate();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mContext = getApplicationContext();
        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);

        ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            cm.registerDefaultNetworkCallback(new ConnectivityManager.NetworkCallback() {
                @Override
                public void onAvailable(Network network) {
                    LogUtils.debug("Internet connectivity Established.....");
                    if (Globals.isWatchRegistered) {
                        LogUtils.debug("Network change detected, Checking for any data need to be sent to server. isWatchRegistered:-" + true);
                        Intent serverCommRequestedIntent = new Intent(MainActivity.SERVER_COMM_INITIATED_INDICATOR_INTENT_FILTER);
                        LocalBroadcastManager.getInstance(mContext).sendBroadcast(serverCommRequestedIntent);
                    }
                }

                @Override
                public void onLost(Network network) {
                    super.onLost(network);
                    LogUtils.debug("Internet connectivity Lost....");
                }
            });
        }
        Thread.setDefaultUncaughtExceptionHandler(new DefaultExceptionHandler());
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    public void appInForeground() {
        Log.e("WatchApp Screen:", "App came to Foreground");
        LogUtils.debug("App came to foreground");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
    public void appInBackground() {
        Log.e("WatchApp Screen::", "App went to appInBackground");
        LogUtils.debug("App went to background");
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    public void appInResumeState() {
        LogUtils.debug("Is Watch registered..." + Globals.isWatchRegistered);
        if (Globals.isWatchRegistered) {
            Log.e("WatchApp Screen::", "App went to appInResumeState");
            if (Globals.priorityQueue.size() == 0) {
                Intent serviceIntent = new Intent(this, WatchRxForegroundService.class);
                serviceIntent.putExtra("inputExtra", "WatchRx App Running...");
                ContextCompat.startForegroundService(this, serviceIntent);
            }
            LogUtils.debug("App came on Resume");
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    public void appInDestroyedState() {
        LogUtils.debug("Application destroyed by OS SO Lifecycle.Event.ON_DESTROY triggers");
    }

    public static boolean isInForeground() {
        return ProcessLifecycleOwner.get().getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.STARTED);
    }

    public static Context getContext() {
        return mContext;
    }
}
