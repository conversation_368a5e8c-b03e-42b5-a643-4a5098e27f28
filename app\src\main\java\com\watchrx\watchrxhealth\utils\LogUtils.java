package com.watchrx.watchrxhealth.utils;


import android.graphics.Bitmap;
import android.media.MediaScannerConnection;
import android.os.Environment;

import com.watchrx.watchrxhealth.WatchApp;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class LogUtils {

    public static final int INFO_LEVEL = 0;
    public static final int WARN_LEVEL = 1;
    public static final int DEBUG_LEVEL = 2;
    public static final int NO_LOGS_LEVEL = 3;

    private static int logLevel = NO_LOGS_LEVEL;

    public static void setLogLevel(int level) {
        logLevel = level;
    }

    public static void debug(String msg) {
        if (logLevel >= DEBUG_LEVEL) {
            formAndWriteLog(msg);
        }
    }

    public static void warn(String msg) {
        if (logLevel >= WARN_LEVEL) {
            formAndWriteLog(msg);
        }
    }

    public static void info(String msg) {
        if (logLevel >= INFO_LEVEL) {
            formAndWriteLog(msg);
        }
    }

    private static void formAndWriteLog(String msg) {
        String fullClassName = Thread.currentThread().getStackTrace()[4].getClassName();
        String className = fullClassName.substring(fullClassName.lastIndexOf(".") + 1);

        String methodName = Thread.currentThread().getStackTrace()[4].getMethodName();

        int lineNo = Thread.currentThread().getStackTrace()[4].getLineNumber();

        writeLogToFile(getTime() + " :: " + className + "->" + methodName + " [" + lineNo + "] :: " + msg);
    }

    private static void writeLogToFile(String msg) {

        try {
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS) + "/WatchRx_DebugFile/";
            String dateFormatted = new SimpleDateFormat("yyyy-MM-dd", Locale.US).format(new Date(System.currentTimeMillis()));
            int pid = android.os.Process.myPid();

            String PID = String.valueOf(pid);
            File root = new File(rootPath);
            if (!root.exists()) {
                root.mkdirs();
            }

            File f = new File(rootPath + PID + "-" + dateFormatted + ".txt");
            f.setExecutable(true);
            f.setReadable(true);
            f.setWritable(true);
            MediaScannerConnection.scanFile(WatchApp.getContext(), new String[]{f.toString()}, null, null);
            if (f.exists()) {
                BufferedWriter writer = new BufferedWriter(new FileWriter(f, true /*append*/));
                writer.write(msg);
                writer.newLine();
                writer.append("\n\n");
                writer.close();


            } else {
                f.createNewFile();
                BufferedWriter writer = new BufferedWriter(new FileWriter(f, true /*append*/));
                writer.write(msg);
                writer.newLine();
                writer.close();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void SaveLogToFile(String msg) {

        try {
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + "/WatchRx_DataBase/WatchRx_Logs/";

            File root = new File(rootPath);
            if (!root.exists()) {
                root.mkdirs();
                root.canWrite();
                root.canRead();
                root.canExecute();
            }

            File f = new File(rootPath + "Logs.txt");
            if (f.exists()) {
                BufferedWriter writer = new BufferedWriter(new FileWriter(f, true /*append*/));
                writer.write(msg);
                writer.newLine();
                writer.append("\n\r");
                writer.close();
            } else {
                f.createNewFile();
                BufferedWriter writer = new BufferedWriter(new FileWriter(f, true /*append*/));
                writer.write(msg);
                writer.newLine();
                writer.close();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void SaveTimeZOneToFile(String msg) {
        try {
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + "/WatchRx_DataBase/";
            File root = new File(rootPath);
            if (!root.exists()) {
                root.mkdirs();
            }

            File file = new File(rootPath, "TimeZoneFile.txt");
            if (file.exists()) file.delete();
            {
                file.createNewFile();
                BufferedWriter writer = new BufferedWriter(new FileWriter(file));
                writer.write(msg);
                writer.newLine();
                writer.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void saveDataToFile(String msg) {

        try {
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + "/WatchRx_DataBase/";
            File root = new File(rootPath);
            if (!root.exists()) {
                root.mkdirs();
            }

            File file = new File(rootPath, "WatchRx.txt");
            if (file.exists()) file.delete();
            {
                file.createNewFile();
                BufferedWriter writer = new BufferedWriter(new FileWriter(file));
                writer.write(msg);
                writer.newLine();
                writer.close();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void SaveImage(Bitmap finalBitmap, String filenmae) {

        String root = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).getAbsolutePath();
        File myDir = new File(root + "/WatchRx_DataBase/WatchRx_Images");
        myDir.mkdirs();
        File file = new File(myDir, filenmae + ".png");
        if (file.exists()) file.delete();
        try {
            FileOutputStream out = new FileOutputStream(file);
            finalBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
            out.flush();
            out.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void saveDataToFileForSync(String msg) {

        try {
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + "/WatchRx_DataBase/SyncDatabase/";
            File root = new File(rootPath);
            if (!root.exists()) {
                root.mkdirs();
            }

            File file = new File(rootPath, "WatchRx.txt");
            if (file.exists()) file.delete();
            {
                file.createNewFile();
                BufferedWriter writer = new BufferedWriter(new FileWriter(file));
                writer.write(msg);
                writer.newLine();
                writer.close();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void SaveImageForSync(Bitmap finalBitmap, String filenmae) {

        String root = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).getAbsolutePath();
        File myDir = new File(root + "/WatchRx_DataBase/SyncDatabase/WatchRx_Images");
        myDir.mkdirs();
        File file = new File(myDir, filenmae + ".png");
        if (file.exists()) file.delete();
        try {
            FileOutputStream out = new FileOutputStream(file);
            finalBitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
            out.flush();
            out.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static String getTime() {
        return new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss.S", Locale.US).format(new Date());
    }
}
