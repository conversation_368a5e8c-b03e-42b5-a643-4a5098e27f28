package com.watchrx.watchrxhealth.adapter;

import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.Message;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class ChatAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final int VIEW_TYPE_SENT = 1;
    private static final int VIEW_TYPE_RECEIVED = 2;
    private static final int VIEW_TYPE_DATE = 3;

    private Map<String, List<Message>> groupedMessages;
    private List<Object> items;

    public ChatAdapter(Map<String, List<Message>> groupedMessages) {
        this.groupedMessages = groupedMessages;
        this.items = new ArrayList<>();
        prepareItems();
    }

    private void prepareItems() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            groupedMessages.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey()) // Sort keys in ascending order
                    .forEach(entry -> {
                        items.add(entry.getKey());       // Add the date key
                        items.addAll(entry.getValue());  // Add the messages
                    });
        } else {
            List<Map.Entry<String, List<Message>>> sortedEntries = new ArrayList<>(groupedMessages.entrySet());
            Collections.sort(sortedEntries, (e1, e2) -> e1.getKey().compareTo(e2.getKey()));

            for (Map.Entry<String, List<Message>> entry : sortedEntries) {
                items.add(entry.getKey());
                items.addAll(entry.getValue());
            }
        }
    }

    @Override
    public int getItemViewType(int position) {
        Object item = items.get(position);
        if (item instanceof String) {
            return VIEW_TYPE_DATE;
        } else if (item instanceof Message message) {
            return "mobile".equals(message.getType()) ? VIEW_TYPE_SENT : VIEW_TYPE_RECEIVED;
        }
        return -1;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == VIEW_TYPE_DATE) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_date, parent, false);
            return new DateViewHolder(view);
        } else if (viewType == VIEW_TYPE_SENT) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_message_sent, parent, false);
            return new SentMessageViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_message_received, parent, false);
            return new ReceivedMessageViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        Object item = items.get(position);
        if (holder instanceof DateViewHolder) {
            ((DateViewHolder) holder).bind((String) item);
        } else if (holder instanceof SentMessageViewHolder) {
            ((SentMessageViewHolder) holder).bind((Message) item);
        } else if (holder instanceof ReceivedMessageViewHolder) {
            ((ReceivedMessageViewHolder) holder).bind((Message) item);
        }
    }

    @Override
    public int getItemCount() {
        return items.size();
    }


    public static class DateViewHolder extends RecyclerView.ViewHolder {
        TextView dateText;

        public DateViewHolder(View itemView) {
            super(itemView);
            dateText = itemView.findViewById(R.id.text_date);
        }

        public void bind(String date) {
            dateText.setText(date);
        }
    }


    public static class SentMessageViewHolder extends RecyclerView.ViewHolder {
        TextView messageText, timeText;

        public SentMessageViewHolder(View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.textMessage);
            timeText = itemView.findViewById(R.id.textTime);
        }

        public void bind(Message message) {
            messageText.setText(message.getMessage());
            timeText.setText(message.getTime());
        }
    }


    public static class ReceivedMessageViewHolder extends RecyclerView.ViewHolder {
        TextView messageText, timeText;

        public ReceivedMessageViewHolder(View itemView) {
            super(itemView);
            messageText = itemView.findViewById(R.id.textMessage);
            timeText = itemView.findViewById(R.id.textTime);
        }

        public void bind(Message message) {
            messageText.setText(message.getMessage());
            timeText.setText(message.getTime());
        }
    }
}

