package com.watchrx.watchrxhealth.utils;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;

public class LogoutDialog {

    public static void showLogoutConfirmation(Context context, Runnable onConfirm) {
        new AlertDialog.Builder(context)
                .setTitle("Logout")
                .setMessage("Are you sure you want to log out?")
                .setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        onConfirm.run();
                    }
                })
                .setNegativeButton("Cancel", null)
                .show();
    }
}
