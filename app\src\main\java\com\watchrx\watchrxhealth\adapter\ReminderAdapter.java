package com.watchrx.watchrxhealth.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.Reminder;

import java.text.MessageFormat;
import java.util.List;

public class ReminderAdapter extends RecyclerView.Adapter<ReminderAdapter.ReminderViewHolder> {

    private final List<Reminder> reminderList;

    public ReminderAdapter(List<Reminder> reminders) {
        this.reminderList = reminders;
    }

    @NonNull
    @Override
    public ReminderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.reminder_item, parent, false);
        return new ReminderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ReminderViewHolder holder, int position) {
        Reminder reminder = reminderList.get(position);
        holder.tvTabletName.setText(reminder.getTabletName());
        holder.tvMedicineType.setText(reminder.getMedicineType());
        holder.tvFrequency.setText(reminder.getFrequency());
        holder.tvReminderTime.setText(MessageFormat.format("Reminder: {0}, {1}", reminder.getNextReminder(), reminder.getTime()));
        int imageResId = reminder.getImageId();
        holder.imageType.setImageResource(imageResId);

    }

    @Override
    public int getItemCount() {
        return reminderList.size();
    }

    public static class ReminderViewHolder extends RecyclerView.ViewHolder {
        TextView tvTabletName, tvMedicineType, tvFrequency, tvReminderTime;
        ImageView imageType;

        public ReminderViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTabletName = itemView.findViewById(R.id.tvTabletName);
            tvMedicineType = itemView.findViewById(R.id.tvMedicineType);
            tvFrequency = itemView.findViewById(R.id.tvFrequency);
            tvReminderTime = itemView.findViewById(R.id.tvTime);
            imageType = itemView.findViewById(R.id.imageType);
        }
    }
}
