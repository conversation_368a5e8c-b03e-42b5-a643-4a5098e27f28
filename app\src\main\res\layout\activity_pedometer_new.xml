<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ble.NewVitalsActivity">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/measurementView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_default="percent">

        <LinearLayout
            android:id="@+id/vitalTitle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/vitalCardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                app:cardCornerRadius="@dimen/d_12_d"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/measurementType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:padding="@dimen/d_10_d"
                    android:text="@string/today_s_steps"
                    android:textColor="@color/black"
                    android:textSize="@dimen/d_30_s"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/progress_bar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="2"
            app:layout_constraintTop_toBottomOf="@id/vitalTitle">

            <androidx.cardview.widget.CardView
                android:id="@+id/stepsCountCv"
                android:layout_width="0dp"
                android:layout_height="150dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                app:cardCornerRadius="@dimen/d_15_d">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/purple_200"
                    android:orientation="vertical"
                    android:weightSum="2">

                    <ImageView
                        android:id="@+id/stepsCountImage"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:src="@drawable/ic_baseline_directions_walk_24" />

                    <TextView
                        android:id="@+id/stepsCount"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_bold"
                        android:gravity="center"
                        android:padding="@dimen/d_5_d"
                        android:textColor="@color/white"
                        android:textSize="@dimen/d_25_s" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/calorieCv"
                android:layout_width="0dp"
                android:layout_height="150dp"
                android:layout_margin="5dp"
                android:layout_weight="1"
                app:cardCornerRadius="@dimen/d_15_d">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/green"
                    android:orientation="vertical"
                    android:weightSum="2">

                    <ImageView
                        android:id="@+id/calorieImage"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:src="@drawable/ic_calorie" />

                    <TextView
                        android:id="@+id/calorieCount"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_gravity="center_horizontal|center_vertical"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_bold"
                        android:gravity="center"
                        android:padding="@dimen/d_5_d"
                        android:textColor="@color/white"
                        android:textSize="@dimen/d_25_s" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/d_5_d"
        android:background="@color/grey85"
        app:layout_constraintTop_toBottomOf="@id/measurementView">

        <ImageView
            android:id="@+id/before_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/d_20_d"
            android:padding="@dimen/d_10_d"
            android:rotation="180"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/after_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/d_20_d"
            android:padding="@dimen/d_10_d"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_regular"
            android:textColor="@color/black"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/vitalRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/d_5_d"
        android:layout_marginTop="@dimen/d_5_d"
        android:layout_marginEnd="@dimen/d_5_d"
        android:layout_marginBottom="@dimen/d_10_d"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintTop_toBottomOf="@id/clDate" />

</androidx.constraintlayout.widget.ConstraintLayout>