<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:cardView="http://schemas.android.com/apk/res-auto"
    android:id="@+id/carView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    cardView:cardCornerRadius="8dp"
    cardView:cardElevation="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/image"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginStart="5dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/ic_alert"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:textSize="25sp"
            cardView:cornerRadius="35dp"
            cardView:layout_constraintStart_toStartOf="parent"
            cardView:layout_constraintTop_toTopOf="parent"
            cardView:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.rounded" />

        <TextView
            android:id="@+id/alert_desc"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:fontFamily="@font/lato_light"
            android:padding="5dp"
            android:textColor="@android:color/black"
            android:textSize="25sp"
            cardView:layout_constraintLeft_toRightOf="@id/image"
            cardView:layout_constraintRight_toRightOf="parent"
            cardView:layout_constraintTop_toBottomOf="@id/alert_type" />

        <TextView
            android:id="@+id/alert_type"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="8dp"
            android:fontFamily="@font/lato_bold"
            android:gravity="top"
            android:lines="1"
            android:padding="5dp"
            android:textColor="@android:color/holo_red_dark"
            android:textSize="30sp"
            android:textStyle="bold"
            cardView:layout_constraintLeft_toRightOf="@id/image"
            cardView:layout_constraintRight_toRightOf="parent"
            cardView:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>