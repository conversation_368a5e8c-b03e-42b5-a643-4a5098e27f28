<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/video_call_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF"
    android:keepScreenOn="true"
    tools:context=".ZoomVideoCallActivity">

    <!-- Zoom Video View -->
    <us.zoom.uitoolkit.UiToolkitView
        android:id="@+id/ui_toolkit_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Call Status Text -->
    <TextView
        android:id="@+id/call_status_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:gravity="center"
        android:text="Initializing..."
        android:textColor="#000000"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Button Layout -->
    <LinearLayout
        android:id="@+id/button_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/call_status_text">

        <!-- Accept Button -->
        <Button
            android:id="@+id/accept_call_button"
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:backgroundTint="#4CAF50"
            android:drawableStart="@drawable/ic_call"
            android:padding="12dp"
            android:text="Accept Call"
            android:textColor="#FFFFFF" />

        <!-- Decline Button -->
        <Button
            android:id="@+id/decline_call_button"
            android:layout_width="250dp"
            android:layout_height="wrap_content"
            android:backgroundTint="#F44336"
            android:drawableStart="@drawable/ic_stop_call"
            android:padding="12dp"
            android:text="Decline Call"
            android:textColor="#FFFFFF" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
