package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.AnswersModel;

import java.util.Arrays;
import java.util.List;

public class AnswersAdapter extends RecyclerView.Adapter<AnswersAdapter.MyViewHolder> {
    Context mContext;
    List<AnswersModel> answersList;

    public interface OnItemClickListener {
        void onItemClick(AnswersModel answersModel);
    }

    private final AnswersAdapter.OnItemClickListener listener;

    public AnswersAdapter(Context context, List<AnswersModel> answersList, AnswersAdapter.OnItemClickListener listener) {
        this.mContext = context;
        this.answersList = answersList;
        this.listener = listener;
        Log.e("AnswersAdapter", "" + Arrays.toString(answersList.toArray()));
    }

    @NonNull
    @Override
    public AnswersAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.answer_list_item, parent, false);
        return new AnswersAdapter.MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull AnswersAdapter.MyViewHolder holder, int position) {
        Log.e("onBindViewHolder", "" + position);
        AnswersModel model = answersList.get(position);
        holder.answer.setText(model.getAnswer());
        holder.bind(model, listener);
    }

    @Override
    public int getItemCount() {
        Log.e("AnswersView", "" + answersList.size());
        return answersList.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView answer;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            answer = itemView.findViewById(R.id.answer);
        }

        public void bind(final AnswersModel item, final AnswersAdapter.OnItemClickListener listener) {
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(item);
                }
            });
        }
    }
}

