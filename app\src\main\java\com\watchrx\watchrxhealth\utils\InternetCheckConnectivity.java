package com.watchrx.watchrxhealth.utils;

import android.os.Handler;

import com.watchrx.watchrxhealth.constants.URLConstants;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

public class InternetCheckConnectivity {

    public static void isNetworkAvailable(final Handler handler, final int timeout) {
        // ask fo message '0' (not connected) or '1' (connected) on 'handler'
        // the answer must be send before before within the 'timeout' (in milliseconds)

        new Thread() {
            private boolean responded = false;

            @Override
            public void run() {
                // set 'responded' to TRUE if is able to connect with google mobile (responds fast)
                new Thread() {
                    @Override
                    public void run() {

                        HttpURLConnection urlc;
                        try {
                            //https://10-dot-watchrx-1007.appspot.com
                            urlc = (HttpURLConnection) (new URL(URLConstants.MAIN_URL).openConnection());
                            urlc.setRequestProperty("User-Agent", "Test");
                            urlc.setRequestProperty("Connection", "close");
                            urlc.setConnectTimeout(timeout);
                            urlc.connect();
                            responded = (urlc.getResponseCode() == 200);
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                    }
                }.start();

                try {
                    int waited = 0;
                    while (!responded && (waited < timeout)) {
                        sleep(100);
                        if (!responded) {
                            waited += 100;
                        }
                    }
                } catch (InterruptedException ignored) {
                } // do nothing
                finally {
                    if (!responded) {
                        handler.sendEmptyMessage(0);
                    } else {
                        handler.sendEmptyMessage(1);
                    }
                }
            }
        }.start();
    }

}
