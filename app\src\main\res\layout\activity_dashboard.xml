<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white_smoke"
    android:keepScreenOn="true">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toTopOf="@id/nav_view"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="10dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/user_card"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="5dp"
                app:cardBackgroundColor="@color/white"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/patient_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="10dp"
                            android:fontFamily="@font/lato_regular"
                            android:text="@string/p_name"
                            android:textColor="@color/black"
                            android:textSize="20sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/light_grey" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/doctor"
                            app:tint="@color/light_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:orientation="vertical"
                            android:padding="3dp">

                            <TextView
                                android:id="@+id/physician_name"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:fontFamily="@font/lato_bold"
                                android:gravity="center_vertical"
                                android:text="@string/provider_name"
                                android:textColor="@color/black"
                                android:textSize="18sp" />

                            <TextView
                                android:id="@+id/physician_phone"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:fontFamily="@font/lato_regular"
                                android:gravity="center_vertical"
                                android:text="@string/provider_phone"
                                android:textColor="@color/light_blue"
                                android:textSize="16sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/light_grey" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/nurse_2"
                            app:tint="@color/light_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:orientation="vertical"
                            android:padding="3dp">

                            <TextView
                                android:id="@+id/cm_name"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:fontFamily="@font/lato_bold"
                                android:gravity="center_vertical"
                                android:text="@string/cm_name"
                                android:textColor="@color/black"
                                android:textSize="18sp" />

                            <TextView
                                android:id="@+id/cm_phone"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:fontFamily="@font/lato_regular"
                                android:gravity="center_vertical"
                                android:text="@string/cm_phone"
                                android:textColor="@color/light_blue"
                                android:textSize="16sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/light_grey" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="60dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/fm_person"
                            app:tint="@color/light_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginStart="15dp"
                            android:orientation="vertical"
                            android:padding="3dp">

                            <TextView
                                android:id="@+id/family_name"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:fontFamily="@font/lato_bold"
                                android:gravity="center_vertical"
                                android:text="@string/cm_name"
                                android:textColor="@color/black"
                                android:textSize="18sp" />

                            <TextView
                                android:id="@+id/family_phone"
                                android:layout_width="match_parent"
                                android:layout_height="30dp"
                                android:fontFamily="@font/lato_regular"
                                android:gravity="center_vertical"
                                android:text="@string/cm_phone"
                                android:textColor="@color/light_blue"
                                android:textSize="16sp" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/text_health_manager"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/lato_bold"
                android:text="@string/health_manage"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/user_card" />


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/health_manager_grid"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text_health_manager">


                <androidx.cardview.widget.CardView
                    android:id="@+id/card_my_vitals"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="6dp"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="3dp"
                    app:layout_constraintDimensionRatio="1.1:1"
                    app:layout_constraintEnd_toStartOf="@id/card_messages"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/vital_signs" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/lato_bold"
                            android:gravity="center"
                            android:text="@string/my_vitals"
                            android:textColor="#000"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>


                <androidx.cardview.widget.CardView
                    android:id="@+id/card_messages"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="6dp"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="3dp"
                    app:layout_constraintDimensionRatio="1.1:1"
                    app:layout_constraintEnd_toStartOf="@id/card_medications"
                    app:layout_constraintStart_toEndOf="@id/card_my_vitals"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:contentDescription="Messages"
                            android:src="@drawable/comments" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/lato_bold"
                            android:gravity="center"
                            android:text="@string/messages"
                            android:textColor="#000"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <!-- Card 3 -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/card_medications"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="6dp"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="3dp"
                    app:layout_constraintDimensionRatio="1.1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/card_messages"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:contentDescription="Medications"
                            android:src="@drawable/capsules" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/lato_bold"
                            android:gravity="center"
                            android:text="@string/medications"
                            android:textColor="#000"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/health_manager_grid_2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/health_manager_grid">

                <androidx.cardview.widget.CardView
                    android:id="@+id/reminder_card"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="6dp"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="3dp"
                    app:layout_constraintDimensionRatio="1:0.6"
                    app:layout_constraintEnd_toStartOf="@id/appointment_card"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:contentDescription="My Vitals"
                            android:src="@drawable/reminder" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/lato_bold"
                            android:gravity="center"
                            android:text="@string/reminders"
                            android:textColor="#000"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

                <androidx.cardview.widget.CardView
                    android:id="@+id/appointment_card"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_margin="6dp"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="3dp"
                    app:layout_constraintDimensionRatio="1:0.6"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/reminder_card"
                    app:layout_constraintTop_toTopOf="parent">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:contentDescription="Messages"
                            android:src="@drawable/appointment" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:fontFamily="@font/lato_bold"
                            android:gravity="center"
                            android:text="@string/appointment"
                            android:textColor="#000"
                            android:textSize="14sp" />
                    </LinearLayout>
                </androidx.cardview.widget.CardView>

            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:id="@+id/alerts_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/lato_bold"
                android:text="@string/alerts"
                android:textColor="@color/black"
                android:textSize="18sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/health_manager_grid_2" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/notification_list"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:fadingEdgeLength="20dp"
                android:requiresFadingEdge="horizontal"
                android:scrollbars="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/alerts_text" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />
</androidx.constraintlayout.widget.ConstraintLayout>