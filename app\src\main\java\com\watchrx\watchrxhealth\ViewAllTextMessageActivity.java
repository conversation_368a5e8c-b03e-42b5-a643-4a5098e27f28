package com.watchrx.watchrxhealth;

import android.os.Bundle;
import android.widget.ProgressBar;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.ViewAllTextMessageAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.ViewAllTextMessageModel;
import com.watchrx.watchrxhealth.models.ViewTextMessageListModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ViewAllTextMessageActivity extends AppCompatActivity {

    private RecyclerView recyclerView;
    private ProgressBar progressBar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_view_all_text_message);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        recyclerView = findViewById(R.id.viewAllTextMessageRV);
        progressBar = findViewById(R.id.progressbar);
        getTextMessageCount();

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }

    private void getTextMessageCount() {
        try {
            URL url = new URL(URLConstants.TEXT_MESSAGE_COUNT);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            new RestAsyncTask(url, jsonObject.toString(), progressBar, new TextMessageCountResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }

    private class TextMessageCountResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }

            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        ViewTextMessageListModel responseMessage = new Gson().fromJson((String) result, ViewTextMessageListModel.class);
                        if (responseMessage != null && responseMessage.isStatus() && responseMessage.getData() != null && responseMessage.getData().size() > 0) {

                            List<ViewAllTextMessageModel> allTextMessageList = new ArrayList<>(responseMessage.data);
                            recyclerView.setHasFixedSize(true);
                            recyclerView.setLayoutManager(new LinearLayoutManager(ViewAllTextMessageActivity.this));
                            recyclerView.setAdapter(new ViewAllTextMessageAdapter(ViewAllTextMessageActivity.this, allTextMessageList, new ViewAllTextMessageAdapter.OnItemClickListener() {
                                @Override
                                public void onItemClick(ViewAllTextMessageModel item) {

                                }
                            }));
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}