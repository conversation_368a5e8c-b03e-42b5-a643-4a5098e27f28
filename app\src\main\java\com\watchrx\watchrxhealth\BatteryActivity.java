package com.watchrx.watchrxhealth;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.SoundUtils;

import java.util.List;
import java.util.Objects;

public class BatteryActivity extends AppCompatActivity implements Runnable {

    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private Handler timeoutHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        setContentView(R.layout.activity_battery);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        LogUtils.debug("Showing battery level screen full or low ");
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);

        ImageView imageView = (ImageView) findViewById(R.id.batteryimage);
        TextView textView = (TextView) findViewById(R.id.battery_status);
        String status = getIntent().getStringExtra("status");
        if (status.equalsIgnoreCase("BatteryFull")) {
            if (imageView != null) {
                imageView.setImageResource(R.drawable.full_bat_green);
                if (textView != null) {
                    textView.setText(R.string.take_me_out);
                }
            }
        } else {
            if (imageView != null) {
                imageView.setImageResource(R.drawable.low_bat_green);
                if (textView != null) {
                    textView.setText(R.string.charge_me);
                }
            }
        }
        if (imageView != null) {
            imageView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    LogUtils.debug("Onclick event detected. screen goes off");
                    if (timeoutHandler != null) {
                        GeneralUtils.stopSpeaking();
                        timeoutHandler.removeCallbacks(BatteryActivity.this);
                    }
                    Globals.isScreenRunning = false;
                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    finish();
                }
            });
        }

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    public void run() {
        LogUtils.debug("Onclick event doesn't detected. screen goes off");
        timeoutHandler.removeCallbacks(this);
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        finish();
    }
}
