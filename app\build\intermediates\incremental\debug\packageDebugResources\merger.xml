<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res"><file name="rotate" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\anim\rotate.xml" qualifiers="" type="anim"/><file name="toggle_text_selector" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\color\toggle_text_selector.xml" qualifiers="" type="color"/><file name="appointment" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\appointment.png" qualifiers="" type="drawable"/><file name="app_logo_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\app_logo_1.png" qualifiers="" type="drawable"/><file name="background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\background.xml" qualifiers="" type="drawable"/><file name="background_with_border" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\background_with_border.xml" qualifiers="" type="drawable"/><file name="bg_appointment_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_appointment_icon.xml" qualifiers="" type="drawable"/><file name="bg_btn_login" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_btn_login.png" qualifiers="" type="drawable"/><file name="bg_btn_welcome" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_btn_welcome.png" qualifiers="" type="drawable"/><file name="bg_circle_white" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_circle_white.xml" qualifiers="" type="drawable"/><file name="bg_edittext" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_edittext.xml" qualifiers="" type="drawable"/><file name="bg_green_gradient" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_green_gradient.xml" qualifiers="" type="drawable"/><file name="bg_icon_semitrans_white" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_icon_semitrans_white.xml" qualifiers="" type="drawable"/><file name="bg_icon_white" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_icon_white.xml" qualifiers="" type="drawable"/><file name="bg_medications_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_medications_icon.xml" qualifiers="" type="drawable"/><file name="bg_meds" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_meds.xml" qualifiers="" type="drawable"/><file name="bg_messages_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_messages_icon.xml" qualifiers="" type="drawable"/><file name="bg_no_alerts" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_no_alerts.xml" qualifiers="" type="drawable"/><file name="bg_organge_gradient" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_organge_gradient.xml" qualifiers="" type="drawable"/><file name="bg_purple_gradient" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_purple_gradient.xml" qualifiers="" type="drawable"/><file name="bg_received_message" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_received_message.xml" qualifiers="" type="drawable"/><file name="bg_red_gradient" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_red_gradient.xml" qualifiers="" type="drawable"/><file name="bg_reminders_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_reminders_icon.xml" qualifiers="" type="drawable"/><file name="bg_sent_message" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_sent_message.xml" qualifiers="" type="drawable"/><file name="bg_summary_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_summary_icon.xml" qualifiers="" type="drawable"/><file name="bg_txt_password" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_txt_password.png" qualifiers="" type="drawable"/><file name="bg_txt_username" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_txt_username.png" qualifiers="" type="drawable"/><file name="bg_vitals_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bg_vitals_icon.xml" qualifiers="" type="drawable"/><file name="bluetooth_connected" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bluetooth_connected.xml" qualifiers="" type="drawable"/><file name="bluetooth_orange" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bluetooth_orange.xml" qualifiers="" type="drawable"/><file name="bluetooth_searching" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bluetooth_searching.xml" qualifiers="" type="drawable"/><file name="blue_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\blue_icon.png" qualifiers="" type="drawable"/><file name="blue_outline" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\blue_outline.xml" qualifiers="" type="drawable"/><file name="bordered_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bordered_background.xml" qualifiers="" type="drawable"/><file name="bottom_navigation_selector" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bottom_navigation_selector.xml" qualifiers="" type="drawable"/><file name="bp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\bp.png" qualifiers="" type="drawable"/><file name="button_gradient" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\button_gradient.xml" qualifiers="" type="drawable"/><file name="button_login" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\button_login.xml" qualifiers="" type="drawable"/><file name="button_shape" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\button_shape.xml" qualifiers="" type="drawable"/><file name="button_shape_green" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\button_shape_green.xml" qualifiers="" type="drawable"/><file name="button_shape_red" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\button_shape_red.xml" qualifiers="" type="drawable"/><file name="calendar" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\calendar.png" qualifiers="" type="drawable"/><file name="call_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\call_24.png" qualifiers="" type="drawable"/><file name="call_end" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\call_end.png" qualifiers="" type="drawable"/><file name="call_open" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\call_open.png" qualifiers="" type="drawable"/><file name="capsules" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\capsules.png" qualifiers="" type="drawable"/><file name="center_circle" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\center_circle.xml" qualifiers="" type="drawable"/><file name="check" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\check.xml" qualifiers="" type="drawable"/><file name="circle" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\circle.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circular_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\circular_round.xml" qualifiers="" type="drawable"/><file name="clock" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\clock.png" qualifiers="" type="drawable"/><file name="comments" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\comments.png" qualifiers="" type="drawable"/><file name="conta_name_shape" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\conta_name_shape.xml" qualifiers="" type="drawable"/><file name="custome_loader" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\custome_loader.xml" qualifiers="" type="drawable"/><file name="custom_day_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\custom_day_background.xml" qualifiers="" type="drawable"/><file name="custom_thumb" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\custom_thumb.xml" qualifiers="" type="drawable"/><file name="cutsom_progress_bar" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\cutsom_progress_bar.xml" qualifiers="" type="drawable"/><file name="dash_chat" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\dash_chat.png" qualifiers="" type="drawable"/><file name="dash_doc" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\dash_doc.png" qualifiers="" type="drawable"/><file name="deafult_medicine" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\deafult_medicine.PNG" qualifiers="" type="drawable"/><file name="dial" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\dial.png" qualifiers="" type="drawable"/><file name="dialog_bg" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\dialog_bg.xml" qualifiers="" type="drawable"/><file name="dial_pink" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\dial_pink.PNG" qualifiers="" type="drawable"/><file name="dial_pink1_blue" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\dial_pink1_blue.png" qualifiers="" type="drawable"/><file name="doctor" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\doctor.png" qualifiers="" type="drawable"/><file name="down_arrow" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\down_arrow.png" qualifiers="" type="drawable"/><file name="drugs" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\drugs.png" qualifiers="" type="drawable"/><file name="fm_person" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\fm_person.png" qualifiers="" type="drawable"/><file name="full_bat_green" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\full_bat_green.png" qualifiers="" type="drawable"/><file name="geo_fecne" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\geo_fecne.png" qualifiers="" type="drawable"/><file name="gradient_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="gray_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\gray_icon.png" qualifiers="" type="drawable"/><file name="green_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\green_background.xml" qualifiers="" type="drawable"/><file name="home_logo" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\home_logo.png" qualifiers="" type="drawable"/><file name="icons8_thermometer_40" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\icons8_thermometer_40.png" qualifiers="" type="drawable"/><file name="icons_meds_40" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\icons_meds_40.png" qualifiers="" type="drawable"/><file name="ic_alarm_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_alarm_icon.png" qualifiers="" type="drawable"/><file name="ic_alert" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_alert.xml" qualifiers="" type="drawable"/><file name="ic_appointment_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_appointment_icon.png" qualifiers="" type="drawable"/><file name="ic_baseline_bloodtype_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_bloodtype_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_chat_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_chat_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_device_thermostat_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_device_thermostat_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_directions_walk_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_directions_walk_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_error_outline_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_error_outline_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_favorite_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_favorite_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_help_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_help_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_medical_services_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_medical_services_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_navigate_next" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_navigate_next.xml" qualifiers="" type="drawable"/><file name="ic_baseline_refresh_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_refresh_24.xml" qualifiers="" type="drawable"/><file name="ic_baseline_sms_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_baseline_sms_24.xml" qualifiers="" type="drawable"/><file name="ic_bg_topheader" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_bg_topheader.xml" qualifiers="" type="drawable"/><file name="ic_blood_sugar" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_blood_sugar.png" qualifiers="" type="drawable"/><file name="ic_bluetooth" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_bluetooth.xml" qualifiers="" type="drawable"/><file name="ic_bluetooth_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_bluetooth_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_call" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_call.xml" qualifiers="" type="drawable"/><file name="ic_call_black_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_call_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_call_end_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_call_end_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_call_end_white_24px" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_call_end_white_24px.xml" qualifiers="" type="drawable"/><file name="ic_calorie" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_calorie.xml" qualifiers="" type="drawable"/><file name="ic_cancel" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_cancel.xml" qualifiers="" type="drawable"/><file name="ic_chat_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_chat_1.png" qualifiers="" type="drawable"/><file name="ic_chat_40" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_chat_40.png" qualifiers="" type="drawable"/><file name="ic_chat_40_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_chat_40_1.png" qualifiers="" type="drawable"/><file name="ic_contact" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_contact.xml" qualifiers="" type="drawable"/><file name="ic_dot" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_dot.png" qualifiers="" type="drawable"/><file name="ic_emergency" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_emergency.png" qualifiers="" type="drawable"/><file name="ic_eye" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_eye.png" qualifiers="" type="drawable"/><file name="ic_eye_off" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_eye_off.png" qualifiers="" type="drawable"/><file name="ic_fill_heart" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_fill_heart.xml" qualifiers="" type="drawable"/><file name="ic_foot_steps" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_foot_steps.xml" qualifiers="" type="drawable"/><file name="ic_headset_mic_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_headset_mic_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_heart" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_heart.xml" qualifiers="" type="drawable"/><file name="ic_heart_filled" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_heart_filled.png" qualifiers="" type="drawable"/><file name="ic_heart_pulse" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_heart_pulse.xml" qualifiers="" type="drawable"/><file name="ic_heart_rate" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_heart_rate.png" qualifiers="" type="drawable"/><file name="ic_heart_rate_pink" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_heart_rate_pink.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_medical" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_medical.png" qualifiers="" type="drawable"/><file name="ic_medication" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_medication.xml" qualifiers="" type="drawable"/><file name="ic_medicine" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_medicine.xml" qualifiers="" type="drawable"/><file name="ic_meds_filled" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_meds_filled.png" qualifiers="" type="drawable"/><file name="ic_message" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_message.xml" qualifiers="" type="drawable"/><file name="ic_mic_off_black_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_mic_off_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_mic_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_mic_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_mic_white_off_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_mic_white_off_24dp.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_person_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_person_icon.png" qualifiers="" type="drawable"/><file name="ic_phonelink_ring_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_phonelink_ring_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_photo_user" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_photo_user.png" qualifiers="" type="drawable"/><file name="ic_plus" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_plus.xml" qualifiers="" type="drawable"/><file name="ic_round_bloodpressure_24" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_round_bloodpressure_24.xml" qualifiers="" type="drawable"/><file name="ic_sound" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_sound.xml" qualifiers="" type="drawable"/><file name="ic_spo2" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_spo2.xml" qualifiers="" type="drawable"/><file name="ic_spo2_40" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_spo2_40.png" qualifiers="" type="drawable"/><file name="ic_stop_call" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_stop_call.xml" qualifiers="" type="drawable"/><file name="ic_success" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_success.xml" qualifiers="" type="drawable"/><file name="ic_switch_camera_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_switch_camera_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_temp_f" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_temp_f.xml" qualifiers="" type="drawable"/><file name="ic_thermo" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_thermo.png" qualifiers="" type="drawable"/><file name="ic_thermo_40" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_thermo_40.png" qualifiers="" type="drawable"/><file name="ic_thermo_64" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_thermo_64.png" qualifiers="" type="drawable"/><file name="ic_videocam_off_black_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_videocam_off_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_videocam_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_videocam_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_video_call" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_video_call.xml" qualifiers="" type="drawable"/><file name="ic_video_call_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_video_call_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_volume_up_white_24dp" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_volume_up_white_24dp.xml" qualifiers="" type="drawable"/><file name="ic_weight_40" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_weight_40.png" qualifiers="" type="drawable"/><file name="ic_weigt" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\ic_weigt.xml" qualifiers="" type="drawable"/><file name="imeiwarning" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\imeiwarning.png" qualifiers="" type="drawable"/><file name="item_count" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\item_count.xml" qualifiers="" type="drawable"/><file name="item_selector" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\item_selector.xml" qualifiers="" type="drawable"/><file name="later" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\later.png" qualifiers="" type="drawable"/><file name="layout_corner" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\layout_corner.xml" qualifiers="" type="drawable"/><file name="list_view_custom" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\list_view_custom.xml" qualifiers="" type="drawable"/><file name="logo" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\logo.png" qualifiers="" type="drawable"/><file name="low_bat_green" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\low_bat_green.png" qualifiers="" type="drawable"/><file name="mail" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\mail.png" qualifiers="" type="drawable"/><file name="medical_assistance" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\medical_assistance.png" qualifiers="" type="drawable"/><file name="medicine" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\medicine.jpg" qualifiers="" type="drawable"/><file name="message" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\message.png" qualifiers="" type="drawable"/><file name="mic_off" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\mic_off.png" qualifiers="" type="drawable"/><file name="mic_on" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\mic_on.png" qualifiers="" type="drawable"/><file name="mute_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\mute_1.png" qualifiers="" type="drawable"/><file name="mute_sound" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\mute_sound.xml" qualifiers="" type="drawable"/><file name="no" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\no.png" qualifiers="" type="drawable"/><file name="nurse" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\nurse.png" qualifiers="" type="drawable"/><file name="nurse_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\nurse_1.png" qualifiers="" type="drawable"/><file name="nurse_2" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\nurse_2.png" qualifiers="" type="drawable"/><file name="paracetmaol" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\paracetmaol.jpg" qualifiers="" type="drawable"/><file name="pedometer_item_bg" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\pedometer_item_bg.xml" qualifiers="" type="drawable"/><file name="pgoress_screen" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\pgoress_screen.xml" qualifiers="" type="drawable"/><file name="phn" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\phn.png" qualifiers="" type="drawable"/><file name="phone" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\phone.png" qualifiers="" type="drawable"/><file name="progress_bg" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\progress_bg.xml" qualifiers="" type="drawable"/><file name="reminder" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\reminder.png" qualifiers="" type="drawable"/><file name="reply_view" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\reply_view.xml" qualifiers="" type="drawable"/><file name="resend_otp_button" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\resend_otp_button.xml" qualifiers="" type="drawable"/><file name="right_arrow" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\right_arrow.png" qualifiers="" type="drawable"/><file name="rounded_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="rounded_corners" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\rounded_corners.xml" qualifiers="" type="drawable"/><file name="rounded_corner_img" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\rounded_corner_img.xml" qualifiers="" type="drawable"/><file name="rounded_edittext" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\rounded_edittext.xml" qualifiers="" type="drawable"/><file name="rounded_logo_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\rounded_logo_background.xml" qualifiers="" type="drawable"/><file name="sample_circle" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\sample_circle.xml" qualifiers="" type="drawable"/><file name="sample_three_icons" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\sample_three_icons.xml" qualifiers="" type="drawable"/><file name="send" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\send.png" qualifiers="" type="drawable"/><file name="send_button_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\send_button_background.xml" qualifiers="" type="drawable"/><file name="shape_bg_incoming_bubble" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\shape_bg_incoming_bubble.xml" qualifiers="" type="drawable"/><file name="shape_bg_outgoing_bubble" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\shape_bg_outgoing_bubble.xml" qualifiers="" type="drawable"/><file name="sleep" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\sleep.png" qualifiers="" type="drawable"/><file name="sleep_monitor" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\sleep_monitor.png" qualifiers="" type="drawable"/><file name="speaker_off" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\speaker_off.png" qualifiers="" type="drawable"/><file name="speaker_on" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\speaker_on.png" qualifiers="" type="drawable"/><file name="support" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\support.png" qualifiers="" type="drawable"/><file name="support_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\support_icon.png" qualifiers="" type="drawable"/><file name="text" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\text.png" qualifiers="" type="drawable"/><file name="toggle_selected_left" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_selected_left.xml" qualifiers="" type="drawable"/><file name="toggle_selected_right" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_selected_right.xml" qualifiers="" type="drawable"/><file name="toggle_selector" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_selector.xml" qualifiers="" type="drawable"/><file name="toggle_selector_left" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_selector_left.xml" qualifiers="" type="drawable"/><file name="toggle_selector_right" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_selector_right.xml" qualifiers="" type="drawable"/><file name="toggle_text_selector" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_text_selector.xml" qualifiers="" type="drawable"/><file name="toggle_unselected_left" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_unselected_left.xml" qualifiers="" type="drawable"/><file name="toggle_unselected_right" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\toggle_unselected_right.xml" qualifiers="" type="drawable"/><file name="turn_off" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\turn_off.png" qualifiers="" type="drawable"/><file name="unmute_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\unmute_1.png" qualifiers="" type="drawable"/><file name="user" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\user.png" qualifiers="" type="drawable"/><file name="user_icon_1" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\user_icon_1.png" qualifiers="" type="drawable"/><file name="utensils" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\utensils.png" qualifiers="" type="drawable"/><file name="vital_signs" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\vital_signs.png" qualifiers="" type="drawable"/><file name="voice_assistance" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\voice_assistance.png" qualifiers="" type="drawable"/><file name="warning" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\warning.png" qualifiers="" type="drawable"/><file name="watch" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\watch.png" qualifiers="" type="drawable"/><file name="yes" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\drawable\yes.png" qualifiers="" type="drawable"/><file name="font" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\font.xml" qualifiers="" type="font"/><file name="lato_black" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_black.ttf" qualifiers="" type="font"/><file name="lato_black_italic" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_black_italic.ttf" qualifiers="" type="font"/><file name="lato_bold" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_bold.ttf" qualifiers="" type="font"/><file name="lato_bold_italic" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_bold_italic.ttf" qualifiers="" type="font"/><file name="lato_italic" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_italic.ttf" qualifiers="" type="font"/><file name="lato_light" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_light.ttf" qualifiers="" type="font"/><file name="lato_light_italic" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_light_italic.ttf" qualifiers="" type="font"/><file name="lato_regular" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_regular.ttf" qualifiers="" type="font"/><file name="lato_thin" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_thin.ttf" qualifiers="" type="font"/><file name="lato_thin_italic" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\font\lato_thin_italic.ttf" qualifiers="" type="font"/><file name="activity_add_medication" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_add_medication.xml" qualifiers="" type="layout"/><file name="activity_answer" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_answer.xml" qualifiers="" type="layout"/><file name="activity_battery" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_battery.xml" qualifiers="" type="layout"/><file name="activity_bluetooth_devices" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_bluetooth_devices.xml" qualifiers="" type="layout"/><file name="activity_chat" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_chat.xml" qualifiers="" type="layout"/><file name="activity_contacts_list" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_contacts_list.xml" qualifiers="" type="layout"/><file name="activity_custom_alerts" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_custom_alerts.xml" qualifiers="" type="layout"/><file name="activity_dashboard" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_dashboard.xml" qualifiers="" type="layout"/><file name="activity_daughter_reminder" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_daughter_reminder.xml" qualifiers="" type="layout"/><file name="activity_enter_otpatictivity" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_enter_otpatictivity.xml" qualifiers="" type="layout"/><file name="activity_enter_password" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_enter_password.xml" qualifiers="" type="layout"/><file name="activity_g_p_s" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_g_p_s.xml" qualifiers="" type="layout"/><file name="activity_heart_rate" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_heart_rate.xml" qualifiers="" type="layout"/><file name="activity_incoming_call" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_incoming_call.xml" qualifiers="" type="layout"/><file name="activity_interaction" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_interaction.xml" qualifiers="" type="layout"/><file name="activity_interactive_voice" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_interactive_voice.xml" qualifiers="" type="layout"/><file name="activity_latest_alerts" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_latest_alerts.xml" qualifiers="" type="layout"/><file name="activity_login" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_login_screen" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_login_screen.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_main_dashboard" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_main_dashboard.xml" qualifiers="" type="layout"/><file name="activity_medication" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_medication.xml" qualifiers="" type="layout"/><file name="activity_medication_details" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_medication_details.xml" qualifiers="" type="layout"/><file name="activity_my_task_calendar" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_my_task_calendar.xml" qualifiers="" type="layout"/><file name="activity_new_vitals" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_new_vitals.xml" qualifiers="" type="layout"/><file name="activity_notifications" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_notifications.xml" qualifiers="" type="layout"/><file name="activity_nurse_on_the_way" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_nurse_on_the_way.xml" qualifiers="" type="layout"/><file name="activity_patient_diary" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_patient_diary.xml" qualifiers="" type="layout"/><file name="activity_pedometer" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_pedometer.xml" qualifiers="" type="layout"/><file name="activity_pedometer_new" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_pedometer_new.xml" qualifiers="" type="layout"/><file name="activity_question" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_question.xml" qualifiers="" type="layout"/><file name="activity_reminder" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_reminder.xml" qualifiers="" type="layout"/><file name="activity_reminder_details" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_reminder_details.xml" qualifiers="" type="layout"/><file name="activity_resend_otpscreen" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_resend_otpscreen.xml" qualifiers="" type="layout"/><file name="activity_sleep_monitor" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_sleep_monitor.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_text_message" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_text_message.xml" qualifiers="" type="layout"/><file name="activity_video" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_video.xml" qualifiers="" type="layout"/><file name="activity_video_call_actiity" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_video_call_actiity.xml" qualifiers="" type="layout"/><file name="activity_view_all_text_message" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_view_all_text_message.xml" qualifiers="" type="layout"/><file name="activity_visit_verification" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_visit_verification.xml" qualifiers="" type="layout"/><file name="activity_vital" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_vital.xml" qualifiers="" type="layout"/><file name="activity_vitals_graph" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_vitals_graph.xml" qualifiers="" type="layout"/><file name="activity_vitals_measurement" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_vitals_measurement.xml" qualifiers="" type="layout"/><file name="activity_vital_dashboard" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_vital_dashboard.xml" qualifiers="" type="layout"/><file name="activity_vital_detail" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_vital_detail.xml" qualifiers="" type="layout"/><file name="activity_vital_details" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_vital_details.xml" qualifiers="" type="layout"/><file name="activity_web_view" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_web_view.xml" qualifiers="" type="layout"/><file name="activity_wifi_config" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_wifi_config.xml" qualifiers="" type="layout"/><file name="activity_zoom_video_call" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_zoom_video_call.xml" qualifiers="" type="layout"/><file name="activity_zoom_video_call_screen" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_zoom_video_call_screen.xml" qualifiers="" type="layout"/><file name="alert_list_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\alert_list_item.xml" qualifiers="" type="layout"/><file name="answer_item_list" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\answer_item_list.xml" qualifiers="" type="layout"/><file name="answer_list_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\answer_list_item.xml" qualifiers="" type="layout"/><file name="bluetooth_device_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\bluetooth_device_item.xml" qualifiers="" type="layout"/><file name="contact_list_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\contact_list_item.xml" qualifiers="" type="layout"/><file name="content_video" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\content_video.xml" qualifiers="" type="layout"/><file name="custom_action_bar" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\custom_action_bar.xml" qualifiers="" type="layout"/><file name="custom_calendar_day" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\custom_calendar_day.xml" qualifiers="" type="layout"/><file name="custom_dialog" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\custom_dialog.xml" qualifiers="" type="layout"/><file name="custom_progress_dialog_view" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\custom_progress_dialog_view.xml" qualifiers="" type="layout"/><file name="custom_reply_layout" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\custom_reply_layout.xml" qualifiers="" type="layout"/><file name="dialog_loading" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\dialog_loading.xml" qualifiers="" type="layout"/><file name="dummy_lay" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\dummy_lay.xml" qualifiers="" type="layout"/><file name="get_latest_measurement_record" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\get_latest_measurement_record.xml" qualifiers="" type="layout"/><file name="item_date" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\item_date.xml" qualifiers="" type="layout"/><file name="item_event" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\item_event.xml" qualifiers="" type="layout"/><file name="item_message_received" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\item_message_received.xml" qualifiers="" type="layout"/><file name="item_message_sent" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\item_message_sent.xml" qualifiers="" type="layout"/><file name="layout_confirmation_dialog" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\layout_confirmation_dialog.xml" qualifiers="" type="layout"/><file name="login_dialog" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\login_dialog.xml" qualifiers="" type="layout"/><file name="marker_view" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\marker_view.xml" qualifiers="" type="layout"/><file name="medication_list_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\medication_list_item.xml" qualifiers="" type="layout"/><file name="med_details_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\med_details_item.xml" qualifiers="" type="layout"/><file name="notification_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\notification_item.xml" qualifiers="" type="layout"/><file name="preference_dialog_number_edittext" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\preference_dialog_number_edittext.xml" qualifiers="" type="layout"/><file name="reminder_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\reminder_item.xml" qualifiers="" type="layout"/><file name="text_answer_sq" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\text_answer_sq.xml" qualifiers="" type="layout"/><file name="text_center" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\text_center.xml" qualifiers="" type="layout"/><file name="text_phone" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\text_phone.xml" qualifiers="" type="layout"/><file name="view_text_message_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\view_text_message_item.xml" qualifiers="" type="layout"/><file name="vital_details_lit_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\vital_details_lit_item.xml" qualifiers="" type="layout"/><file name="vital_list_item" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\vital_list_item.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="menu" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\menu\menu.xml" qualifiers="" type="menu"/><file name="menu_patient_diary" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\menu\menu_patient_diary.xml" qualifiers="" type="menu"/><file name="menu_video_activity" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\menu\menu_video_activity.xml" qualifiers="" type="menu"/><file name="refresh" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\menu\refresh.xml" qualifiers="" type="menu"/><file name="watchrx_app_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-anydpi-v26\watchrx_app_icon.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="watchrx_app_icon_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-anydpi-v26\watchrx_app_icon_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_loading" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-hdpi\ic_loading.png" qualifiers="hdpi-v4" type="mipmap"/><file name="watchrx_app_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-hdpi\watchrx_app_icon.png" qualifiers="hdpi-v4" type="mipmap"/><file name="watchrx_app_icon_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-hdpi\watchrx_app_icon_background.png" qualifiers="hdpi-v4" type="mipmap"/><file name="watchrx_app_icon_foreground" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-hdpi\watchrx_app_icon_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="watchrx_app_icon_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-hdpi\watchrx_app_icon_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="watchrx_app_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-mdpi\watchrx_app_icon.png" qualifiers="mdpi-v4" type="mipmap"/><file name="watchrx_app_icon_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-mdpi\watchrx_app_icon_background.png" qualifiers="mdpi-v4" type="mipmap"/><file name="watchrx_app_icon_foreground" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-mdpi\watchrx_app_icon_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="watchrx_app_icon_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-mdpi\watchrx_app_icon_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="watchrx_app_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xhdpi\watchrx_app_icon.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xhdpi\watchrx_app_icon_background.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_foreground" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xhdpi\watchrx_app_icon_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xhdpi\watchrx_app_icon_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_blue_connected" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxhdpi\ic_blue_connected.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxhdpi\watchrx_app_icon.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxhdpi\watchrx_app_icon_background.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_foreground" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxhdpi\watchrx_app_icon_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxhdpi\watchrx_app_icon_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxxhdpi\watchrx_app_icon.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_background" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxxhdpi\watchrx_app_icon_background.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_foreground" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxxhdpi\watchrx_app_icon_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="watchrx_app_icon_round" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\mipmap-xxxhdpi\watchrx_app_icon_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="amplifyconfiguration" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\raw\amplifyconfiguration.json" qualifiers="" type="raw"/><file name="awsconfiguration" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\raw\awsconfiguration.json" qualifiers="" type="raw"/><file name="video_call_tone" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\raw\video_call_tone.mpeg" qualifiers="" type="raw"/><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\arrays.xml" qualifiers=""><integer-array name="audio_error_nums">
        <item>23</item>
        <item>24</item>
        <item>25</item>
        <item>26</item>
        <item>27</item>
        <item>28</item>
        <item>30</item>
        <item>40</item>
        <item>41</item>
        <item>42</item>
        <item>44</item>
        <item>46</item>
    </integer-array><string-array name="IHBValue">
        <item>Normal</item>
        <item>IHB</item>
        <item>Tachycardia or Bradycardia</item>
        <item>Varied Heart Rate (±20%)</item>
        <item>Atrail Fibrillation (AF)</item>
    </string-array><string-array name="battery_status">
        <item>Not support</item>
        <item>Good</item>
        <item>Low</item>
        <item>Dead</item>
    </string-array><string-array name="BaudRate_Var">
        <item>9600</item>
        <item>19200</item>
        <item>115200</item>
    </string-array><string-array name="audio_errors">
        <item>@string/err_23</item>
        <item>@string/err_24</item>
        <item>@string/err_25</item>
        <item>@string/err_26</item>
        <item>@string/err_27</item>
        <item>@string/err_28</item>
        <item>@string/err_30</item>
        <item>@string/err_40</item>
        <item>@string/err_41</item>
        <item>@string/err_42</item>
        <item>@string/err_44</item>
        <item>@string/err_46</item>
    </string-array><string-array name="abpm_types">
        <item>@string/abpm_general</item>
        <item>@string/abpm_daytime</item>
        <item>@string/abpm_nighttime</item>
        <item>@string/abpm_specialtime</item>
        <item>@string/abpm_backlight</item>
    </string-array></file><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\attrs.xml" qualifiers=""><declare-styleable name="ButtonBarContainerTheme">
        <attr format="reference" name="metaButtonBarStyle"/>
        <attr format="reference" name="metaButtonBarButtonStyle"/>
    </declare-styleable></file><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#3F51B5</color><color name="colorPrimaryDark">#303F9F</color><color name="colorAccent">#FF4081</color><color name="black_overlay">#66000000</color><color name="App_color">#000000</color><color name="red">#EF5350</color><color name="orange">#FFA726</color><color name="background">#EFEFEF</color><color name="yellow">#FFFF00</color><color name="white">#FFFFFF</color><color name="blue">#2196F3</color><color name="black">#FF000000</color><color name="light_blue">#007aff</color><color name="status_blue">#24b4e4</color><color name="purple_200">#FFBB86FC</color><color name="purple_500">#AB47BC</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#EA80FC</color><color name="sky_blue">#82B1FF</color><color name="lightgrey">#D3D3D3</color><color name="grey">#cccccc</color><color name="green">#00E676</color><color name="violet">#673fb4</color><color name="white_smoke">#efefef</color><color name="grey85">#d9d9d9</color><color name="cardColor">#35b736</color><color name="textcardColor">#3f3f3f</color><color name="transperant">#80000000</color><color name="statusbar">#FF3C9EF3</color><color name="light_b">#18FFFF</color><color name="light_grey">#BDBDBD</color><color name="colorText">#504F60</color><color name="dark_blue">#1976D2</color><color name="purple_7000">#7B61FF</color></file><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="d_3_d">3dp</dimen><dimen name="d_15_d">15dp</dimen><dimen name="d_25_d">25dp</dimen><dimen name="d_30_d">30dp</dimen><dimen name="d_50_d">50dp</dimen><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="d_30_s">30sp</dimen><dimen name="d_10_s">15sp</dimen><dimen name="d_25_s">25sp</dimen><dimen name="d_20_s">20sp</dimen><dimen name="d_20_d">20dp</dimen><dimen name="d_10_d">10dp</dimen><dimen name="d_5_d">5dp</dimen><dimen name="d_35_s">35sp</dimen><dimen name="d_12_d">12dp</dimen><dimen name="time">60sp</dimen><dimen name="date">40sp</dimen><dimen name="message">35sp</dimen><dimen name="d_7_d">10dp</dimen><dimen name="dashboard_vital_text">20sp</dimen><dimen name="message_text">16sp</dimen><dimen name="fab_margin">16dp</dimen><dimen name="margin_small">8dp</dimen><dimen name="card_view_padding">4dp</dimen><dimen name="padding_medium">8dp</dimen><dimen name="padding_normal">5dp</dimen><dimen name="padding_average">12dp</dimen><dimen name="margin_normal">10dp</dimen><dimen name="padding_small">8dp</dimen><dimen name="item_offset">4dp</dimen><dimen name="padding_empty_bottom">16dp</dimen><dimen name="app_bar_height">180dp</dimen><dimen name="text_margin">16dp</dimen></file><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">WatchRx Health</string><string name="title_activity_splash">WatchRx</string><string name="dummy_button">Dummy Button</string><string name="dummy_content">DUMMY\nCONTENT</string><string name="reminder">Reminder</string><string name="nurse_is_reminding_you">Nurse is reminding you</string><string name="nurseIsRemindingText">Your Nurse is reminding you</string><string name="internetcheck">Internet is not connected ! please connect</string><string name="watchRegFailedMsg">Watch registration rejected by server.either IMEI is not registered or parent is not assigned to this watch.</string><string name="gcmRegFailedMsg">GCM Registration failed. This device does not support for Google Play Service!</string><string name="gcmRegFailedNoPlayServiceMsg">GCM Registration failed. Google Play Service is not installed/enabled in this device!</string><string name="date">Date</string><string name="remindertext">REMINDER</string><string name="snoozetext">SNOOZE</string><string name="watchrx">WatchRx</string><string name="geofence_transition_entered">Entered</string><string name="geofence_transition_exited">Exited</string><string name="unknown_geofence_transition">Unknown Transition</string><string name="fence_crossed">Fence Crossed</string><string name="accessibility_service_description">
        Smart installation service, you can automatically install the program without any user action. </string><string name="connect_wifi">Refresh</string><string name="answer">answer</string><string name="select_option">Select Option</string><string name="pedometer">Pedometer</string><string name="no_device">No Device</string><string name="connect">CONFIRM</string><string name="exit">EXIT</string><string name="todo">Mute/Un-mute</string><string name="loading">Loading...</string><string name="vital_name">Vital name</string><string name="scan">SCAN</string><string name="common_ok">OK</string><string name="common_warning">Warning</string><string name="common_message">Message</string><string name="common_cancel">Cancel</string><string name="common_error">Error</string><string name="common_yes">Yes</string><string name="common_no">No</string><string name="common_confirm">Confirm</string><string name="common_delete">Delete</string><string name="common_save_and_leave">Save and leave?</string><string name="common_connect_internet_first">Please connect to the Internet first.</string><string name="common_on">on</string><string name="common_off">off</string><string name="common_min">Min.</string><string name="common_max">Max.</string><string name="common_back">Back</string><string name="common_export">Export</string><string name="confirm">Confirm</string><string name="command_test_menu">Choose One Command Type</string><string name="baudrate">BaudRate</string><string name="pair_result">Pair Device Result</string><string name="get_project_code">Get Project Code</string><string name="get_serial_number">Get Serial Number</string><string name="get_storage_count">Get Storage Count</string><string name="get_measurement_record">Measurement Record</string><string name="get_meter_system_clock">Get Meter System Clock</string><string name="set_meter_system_clock">Set Meter System Clock</string><string name="get_battery_status">Get Battery Status</string><string name="battery_status">Battery Status</string><string name="device_name">Device Name:</string><string name="device_list">Device List:</string><string name="meter_bt_transfer_type">Meter Bluetooth Transfer Type</string><string name="bt_type_one">Connect</string><string name="bt_type_two">Listen</string><string name="hint">Please choose one pair meter in list</string><string name="connect_meter">Connect Meter</string><string name="project_code">Project Code:</string><string name="meter_system_clock">Meter Time:</string><string name="before_meter_system_clock">Before Sync Meter Time:</string><string name="after_meter_system_clock">After Sync Meter Time:</string><string name="measurement_type">Measurement Type:</string><string name="value">Value</string><string name="bg_value">BG(mg/dL):</string><string name="thermometer_value">Body Temperature(°C):</string><string name="spO2_value">SpO2:</string><string name="gender_value">Gender:</string><string name="age_value">Age:</string><string name="height_value">Height:</string><string name="weight_value">Weight(lbs):</string><string name="bmi_value">BMI:</string><string name="bmr_value">BMR:</string><string name="bf_value">BF:</string><string name="sys_value">Systolic(mmHg):</string><string name="dia_value">Diastolic(mmHg):</string><string name="pul_value">Pulse(BPM):</string><string name="ihb_value">IHB:</string><string name="measurement_date">Measurement Date:</string><string name="serial_number">Serial Number:</string><string name="storage_count">Total Records:</string><string name="device_mac_address">Device Mac Address:</string><string name="start_search_meter">Start searching meters,please wait a while</string><string name="connection_meter_and_get_result">Start connecting meter and get result, please wait a while</string><string name="bluetooth_occur_error">Bluetooth occur error,please enable bluetooth and pair remot devices</string><string name="bluetooth_need_to_pair"><![CDATA[Please go to "Settings—>Wireless & networks" pair Bluetooth devices]]></string><string name="check_bt_fail">device singal to low!!!!</string><string name="not_support_meter">Not support meter!!!!</string><string name="not_connect_serial_port">Not connect serial port!!!!</string><string name="connect_meter_fail">Connect meter fail, Please Check the Bluetooth module, and retry!!</string><string name="connect_meter_success">Connect meter success, you can choose one of the command to test meter communication!!</string><string name="meter_was_power_off">Meter was power off, click OK and go to the meter menu.</string><string name="clear_records">Meter records were cleaned.</string><string name="meter_trans_type_fail">Please choose meter transfer type!!!!</string><string name="pair_meter_first">Please Pair Device First!!</string><string name="disable_alwayson_success">Disable function success!</string><string name="disable_alwayson_fail">Disable function fail!</string><string name="cd_image">image</string><string name="error_location_not_supported">Please grant location access so this app can detect beacons.</string><string name="error_location_not_supported2">Since location access has not been granted, this app will not be able to discover beacons when in the background.</string><string name="state">State</string><string name="yes">Yes</string><string name="no">No</string><string name="cancel">Cancel</string><string name="delete">Delete</string><string name="tab_setting">SETTING</string><string name="saveandleave">Save and leave?</string><string name="setting_home_meter">Bluetooth Smart Meter</string><string name="connect_4026">Connect TD4026</string><string name="add">Add</string><string name="setting_current_ble_meter">Current Meters</string><string name="setting_available_meter">Available Meters</string><string name="meter_search">Search</string><string name="setting_meter_stop">Stop</string><string name="knv_user">User</string><string name="knv_gender">Gender</string><string name="knv_age">Age</string><string name="knv_height">Height</string><string name="knv_weight">Weight</string><string name="knv_bfr">Body Fat Ratio</string><string name="knv_bm">Basal Metabolism</string><string name="knv_mc">Moisture Content</string><string name="knv_mr">Muscle Ratio</string><string name="knv_sw">Skeleton Weight</string><string name="knv_back">Back</string><string name="power_on">Power on</string><string name="power_off">Power off</string><string name="err_23">Internal memory data incorrect or system calibration is not finished.</string><string name="err_24">User inserts used strip.</string><string name="err_25">Ambient temperature is less than the limitation.(Depend on code parameter)(10℃)</string><string name="err_26">Ambient temperature is more than the limitation.(Depend on code parameter)(40℃)</string><string name="err_27">Expiry Date_Error (HCT Only)</string><string name="err_28">Code Strip data is incorrect.</string><string name="err_30">Rcode Out of Range</string><string name="err_40">User remove the strip when measuring blood glucose.</string><string name="err_41">Internal EEprom not response ACK.</string><string name="err_42">Battery Low &lt; 3.0v!!</string><string name="err_44">User cancel the glucose measuring.</string><string name="err_46">HCT value is incorrect. (HCT Only)</string><string name="over_error">Inflation or Pressure Over Error.</string><string name="measure_error">Blood Pressure Measurement Error.</string><string name="rom_not_resp_error">Internal EEprom not response ACK.</string><string name="low_battery_error">Battery is too low.</string><string name="data_not_correct_error">Calibration data not correct.</string><string name="unknow_error">Unknown error!!!</string><string name="abpm_general">General</string><string name="abpm_daytime">Day-time</string><string name="abpm_nighttime">Night-time</string><string name="abpm_specialtime">Special-time</string><string name="abpm_backlight">backlight</string><string name="abpm_start_after">Start after(min)</string><string name="abpm_hours_of_study">Hours of Study</string><string name="abpm_normal_period">Normal Period(min)</string><string name="abpm_start_hour">Start(hour)</string><string name="abpm_end_hour">End(hour)</string><string name="abpm_period">Period(min)</string><string name="abpm_on_time">ON time(second)</string><string name="abpm_dim">DIM</string><string name="abpm_get_config">get configuration</string><string name="nfc_listen">NFC listening</string><string name="nfc_read">NFC reading</string><string name="nfc_read_timeout">Read NFC timeout</string><string name="nfc_disable">NFC disable</string><string name="turn_off">Turn off meter</string><string name="title_activity_fullscreen">FullscreenActivity</string><string name="connect_me">Connect Me !!</string><string name="wait">Please Wait I am reading Data :-) </string><string name="retry">Retry</string><string name="disconnected">Device Disconnected</string><string name="vitals">Vitals</string><string name="BP">BP</string><string name="BG">BG</string><string name="thermometer">Thermometer</string><string name="Sp02">spO2</string><string name="wc">Weight Scale</string><string name="statusTitle">Connecting…</string><string name="temp_c">Temperature(°C):</string><string name="temp_f">Temperature(°F):</string><string name="weight_lbs">Weight(lbs):</string><string name="weight_kg">Weight(Kg):</string><string name="imei_not_found_in_db">Imei is not found in the database</string><string name="imei_not_assigned">Imei is not associated with any patient</string><string name="subscription_not_active">Subscription is not active</string><string name="gcm_registration_failed">GCM token Registration with server failed.</string><string name="app_version">App version 1.0</string><string name="app_logo">App Logo</string><string name="nav_sound">Support</string><string name="nav_alerts">Alerts</string><string name="nav_contacts">Contacts</string><string name="take">Take</string><string name="message">Message</string><string name="gps_status">GPS Status</string><string name="far_home">You have come too far from home</string><string name="back_home">You are back home</string><string name="take_me_out">Take Me Out!</string><string name="charge_me">Charge Me!</string><string name="_7278">7278</string><string name="medicationimage">MedicationImage</string><string name="_0">0</string><string name="no_data_found">No data found.</string><string name="blood_pressure">Blood Pressure</string><string name="today_s_steps">Today Steps</string><string name="user_respond_text">Please select any option to respond it.</string><string name="user_respond_text_no_answer_text">Please tap anywhere to respond it.</string><string name="default_notification_channel_id" translatable="false">fcm_default_channel</string><string name="email_id">Enter Email Id Or Phone</string><string name="password">Enter Password</string><string name="login">Login</string><string name="token_failed">Failed to get auth token</string><string name="custom_reply">Custom Reply</string><string name="enter_your_message_here">Enter your message here</string><string name="enter_message">Type a message</string><string name="send">Send</string><string name="real_time">Real Time</string><string name="one_time">One Time</string><string name="_0s">0s</string><string name="scanning">Scanning…</string><string name="plz">Please wait…</string><string name="not_spport">Device wont support</string><string name="otp_validate"><u>Validate OTP</u></string><string name="forgot_your_password"><u>Forgot your password?</u></string><string name="otp">Enter OTP</string><string name="validateotp">Validate OTP</string><string name="already_validated"><u>Login</u></string><string name="resendOTP">Resend OTP</string><string name="sleep_start_time">Sleep Start Time :</string><string name="sleep_end_time">Sleep End Time :</string><string name="update_settings">Update Settings</string><string name="days_to_take">Days to Take:</string><string name="freq">Frequency(Time-Quantity):</string><string name="sunday">Sun</string><string name="monday">Mon</string><string name="tuesday">Tue</string><string name="wednesday">Wed</string><string name="fri">Fri</string><string name="thursday">Thu</string><string name="sat">Sat</string><string name="medication_name">Medication Name</string><string name="dosage">Dosage</string><string name="color">Color</string><string name="tablet">Tablet</string><string name="injection">Injection</string><string name="inhaler">Inhaler</string><string name="spray">Nosal Spray</string><string name="syrup">Syrup</string><string name="all">All</string><string name="sun">Sun</string><string name="mon">Mon</string><string name="tue">Tue</string><string name="wed">Wed</string><string name="thur">Thur</string><string name="browse">Browse</string><string name="choose">Choose Image</string><string name="meds_type">Medication Type</string><string name="select_days">Select Days</string><string name="select_timeslots">Select Timeslots</string><string name="early_morning">Early Morning</string><string name="breakfast">Breakfast</string><string name="lunch">Lunch</string><string name="afternoon">Afternoon Snack</string><string name="dinner">Dinner</string><string name="bed">Bed</string><string name="quantity">Quantity</string><string name="submit">Submit</string><string name="medication">+Medication</string><string name="dairy">Patient Diary</string><string name="title_activity_patient_diary">ScrollingActivity</string><string name="action_settings">Settings</string><string name="addnewdairy">addNewDiary</string><string name="add_new_dairy">Patient Diary</string><string name="enter_meal_taken_time">Enter Meal Taken Time</string><string name="enter_medication_taken_time">Enter Medication Taken Time</string><string name="bluetooth">Bluetooth</string><string name="ok">OK</string><string name="no_device_mesg">You have not connected any device…</string><string name="no_device_f">No device found.</string><string name="view_device">View Device</string><string name="available_devices">Available Devices</string><string name="room_status">status</string><string name="error_retrieving_access_token">Error retrieving token</string><string name="permissions_needed">Camera and Microphone permissions needed. Please allow in App Settings for additional functionality.</string><string name="settings">Settings</string><string name="title_activity_settings">Settings</string><string name="codec_title">Set your preferred audio and video codec. Not all codecs are supported with Group rooms. The media server will fallback to OPUS or VP8 if a preferred codec is not supported.</string><string name="pref_title_audio_codec">Audio Codec</string><string name="pref_title_video_codec">Video Codec</string><string name="sender_bandwidth_constraints">Set sender bandwidth constraints. Zero represents the WebRTC default value which varies by codec.</string><string name="max_audio_bitrate">Max Audio Bitrate (bits per second)</string><string name="max_video_bitrate">Max Video Bitrate (bits per second)</string><string name="vp8_simulcast">VP8 Simulcast</string><string name="audio_device">Audio Device</string><string name="room_screen_select_device">Select Device</string><string name="logout">Logout</string><string name="p_name">Patient Name</string><string name="provider_name">Provider Name</string><string name="provider_phone">Phone</string><string name="cm_name">Care Manager Name</string><string name="cm_phone">Phone</string><string name="health_manage">Health Manager</string><string name="my_vitals">My Vitals</string><string name="messages">Messages</string><string name="medications">Medications</string><string name="reminders">Reminders</string><string name="appointment">Appointments</string><string name="assistance">Assistance</string><string name="alerts">Alerts (Last 2 Days)</string><string name="vital_config">My Vitals</string><string name="privacy_policy">Privacy Policy</string><string name="instruction">Please switch on your medical device to begin health data collection. Let us know if you need any help!</string><string name="password_hint">Password must contain 8–20 characters, including a number, a special character, a lowercase and an uppercase letter.</string><string name="username_hint">Please enter valid email or phone number with country code (Example:+18001232323)</string><string name="reject">Reject</string><string name="accept">Accept</string><string name="speaker">Speaker</string><string name="mute">Mute</string><string name="incoming_call">Incoming Call</string><string name="_00_00">00:00</string><string name="quality_good">Quality: Good</string><string name="calling">calling</string><string name="vital_icon">Vital Icon</string><string name="message_icon">Message Icon</string><string name="medication_icon">Medication Icon</string><string name="family_icon">family_icon</string><string name="summary">Summary</string><string name="how_is_your_health">How is your health?</string><string name="care_manager">Care Manager</string><string name="emergency_contact">Emergency Contact</string><string name="daily_notifications">Daily notifications</string><string name="health_overview">Health overview</string><string name="schedule_visits">Schedule visits</string><string name="manage_prescriptions">Manage prescriptions</string><string name="chat_with_care_team">Chat with care team</string><string name="track_health_metrics">Track health metrics</string><string name="last_two_days_alerts">Last two days alerts</string><string name="no_alerts_in_the_last_two_days">No alerts in the last two days</string><string name="have_you_taken_meds">Have you taken meds?</string><string name="remind_me_again">Remind Me Again</string><string name="have_you_taken_medications">HAVE YOU TAKEN MEDICATIONS?</string><string name="remind_again">REMIND AGAIN</string></file><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="FullscreenTheme" parent="AppTheme">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="metaButtonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="metaButtonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
    </style><style name="FullscreenActionBarStyle" parent="Widget.AppCompat.ActionBar">
        <item name="android:background">@color/black_overlay</item>
    </style><style name="UiTestTextView">
        <item name="android:textColor">#ff0000</item>
        <item name="android:textSize">16sp</item>

        <item name="android:shadowColor">#ffffff</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">3</item>
    </style><style name="CustomProgressDialog" parent="Theme.AppCompat.Dialog">
        
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style><style name="Theme.WatchRx" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/blue</item>
        <item name="colorPrimaryVariant">@color/dark_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">@color/blue</item>
    </style><style name="ShapeAppearanceOverlay.App.rounded" parent="">
        <item name="cornerSize">50%</item>
    </style><style name="AppTheme1" parent="Theme.AppCompat.Light.NoActionBar">
        
        <item name="colorPrimary">@color/transperant</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style><style name="loginButton" parent="@android:style/Widget.Button">
        <item name="android:background">#8BC34A</item>
    </style><style name="cancelButton" parent="@android:style/Widget.Button">
        <item name="android:background">#FF5722</item>
    </style><style name="PasswordCriteria">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#FF0000</item>
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/lato_regular</item>
        <item name="android:paddingBottom">2dp</item>
    </style><style name="CircleImage" parent="">
        <item name="cornerSize">50%</item>
    </style></file><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.WatchRx.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.WatchRx.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar"/><style name="Theme.WatchRx.PopupOverlay" parent="ThemeOverlay.AppCompat.Light"/></file><file name="accessibility_service_config" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="custom_prog" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\xml\custom_prog.xml" qualifiers="" type="xml"/><file name="filepaths" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\xml\filepaths.xml" qualifiers="" type="xml"/><file name="settings" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\xml\settings.xml" qualifiers="" type="xml"/><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\values\health_permissions.xml" qualifiers=""><array name="health_permissions">
        
        <item>androidx.health.permission.BloodGlucose.READ</item>
        
        <item>androidx.health.permission.HeartRate.READ</item>
        
        <item>androidx.health.permission.BloodPressure.READ</item>
        
        <item>androidx.health.permission.OxygenSaturation.READ</item>
        
        <item>androidx.health.permission.Steps.READ</item>
        
        <item>androidx.health.permission.SleepSession.READ</item>
        
        <item>androidx.health.permission.Weight.READ</item>
        
        <item>androidx.health.permission.BodyTemperature.READ</item>
    </array></file><file name="activity_permissions_rationale" path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\res\layout\activity_permissions_rationale.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">132597499285-j5595fr9kvpgutlfhaapv4bkv2rk24ij.apps.googleusercontent.com</string><string name="firebase_database_url" translatable="false">https://watchrx-1007.firebaseio.com</string><string name="gcm_defaultSenderId" translatable="false">132597499285</string><string name="google_api_key" translatable="false">AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4</string><string name="google_app_id" translatable="false">1:132597499285:android:ebc4577d313cba0519e29c</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBi1y0GE8xqODDgfo7J2NrtLYFVpZDjtq4</string><string name="google_storage_bucket" translatable="false">watchrx-1007.appspot.com</string><string name="project_id" translatable="false">watchrx-1007</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems><configuration qualifiers=""><declare-styleable name="ButtonBarContainerTheme">
        <attr format="reference" name="metaButtonBarStyle"/>
        <attr format="reference" name="metaButtonBarButtonStyle"/>
    </declare-styleable></configuration></mergedItems></merger>