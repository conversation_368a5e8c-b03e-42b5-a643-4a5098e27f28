package com.watchrx.watchrxhealth;

import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.Objects;

public class HeartRateActivity extends AppCompatActivity implements SensorEventListener, Runnable {
    private TextView mTextView;
    private Button btnStart;
    private Button btnPause;
    private SensorManager mSensorManager;
    private Sensor mHeartRateSensor;
    SensorEventListener sensorEventListener;
    String heartRateValue;
    private static final long[] vibratePattern = {0, 500, 200};
    RelativeLayout hrtLayout;
    private static final int cycle = 0;

    private static final Handler timeoutHandler = new Handler();
    private int reminderRetryCount = 0;
    private int vitalScheduleId = 0;
    private static final long REMINDER_TIME_OUT = 30 * 1000;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_heart_rate);
        Window window = this.getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        mTextView = (TextView) findViewById(R.id.heartRateText);

        ImageView iv = (ImageView) findViewById(R.id.heartImage);
        final ObjectAnimator scaleDown = ObjectAnimator.ofPropertyValuesHolder(
                iv,
                PropertyValuesHolder.ofFloat("scaleX", 1.2f),
                PropertyValuesHolder.ofFloat("scaleY", 1.2f));
        scaleDown.setDuration(600);

        scaleDown.setRepeatCount(ObjectAnimator.INFINITE);
        scaleDown.setRepeatMode(ObjectAnimator.REVERSE);

        GeneralUtils.stopSpeaking();
        GeneralUtils.stopBeeping();

        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);
        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                GeneralUtils.stopSpeaking();
                GeneralUtils.stopBeeping();
                GeneralUtils.speak("Please wait $ I am collecting your heart rate");
                ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                startMeasure();
                scaleDown.start();
            }
        });

        hrtLayout = findViewById(R.id.hrtlayout);
        hrtLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                GeneralUtils.stopBeeping();
                GeneralUtils.speak("Please wait $ I am collecting your heart rate");
            }
        });

        mSensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT_WATCH) {
            mHeartRateSensor = mSensorManager.getDefaultSensor(Sensor.TYPE_HEART_RATE);
        }
        if (timeoutHandler == null) {
            Handler timeoutHandler = new Handler();
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
            LogUtils.debug("timeoutHandler object died. so using new handler object Inside OnCreate. Going to set timeout Handler.");
        } else {
            LogUtils.debug("Inside OnCreate. Going to set timeout Handler.");
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        }

        String vitalScheduleIdStr = getIntent().getStringExtra("vitalScheduleId");
        if (vitalScheduleIdStr != null && !vitalScheduleIdStr.isEmpty()) {
            vitalScheduleId = Integer.parseInt(vitalScheduleIdStr);
            LogUtils.debug("Schedule Vital ID :" + vitalScheduleId);
        }

    }

    private void startMeasure() {
        boolean sensorRegistered = mSensorManager.registerListener(this, mHeartRateSensor, SensorManager.SENSOR_DELAY_FASTEST);
        LogUtils.debug("Sensor Status: Sensor registered: " + (sensorRegistered ? "yes" : "no"));

    }

    private void stopMeasure() {
        mSensorManager.unregisterListener(this);

    }

    @Override
    public void onSensorChanged(SensorEvent sensorEvent) {
        heartRateValue = Integer.toString((int) (sensorEvent.values.length > 0 ? sensorEvent.values[0] : 0.0f));
        mTextView.setText(heartRateValue);
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int i) {

    }

    @Override
    protected void onResume() {
        super.onResume();

    }

    @Override
    protected void onPause() {
        super.onPause();
//        mSensorManager.unregisterListener(this);
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    public void run() {
        timeoutHandler.removeCallbacks(this);
        reminderRetryCount++;
        if (reminderRetryCount >= 2) {
            LogUtils.debug("Exiting the screen.");
            GeneralUtils.stopSpeaking();
            if (Integer.parseInt(mTextView.getText().toString().replace("--", "0")) == 0) {
                GeneralUtils.speak("Sorry! I am not able to read your'e heart rate ");
                CommUtils.sendHeartLogToServer(getApplicationContext(), "" + 0, "Failed", vitalScheduleId);
            } else {
                GeneralUtils.speak("Great job! your'e heart rate is " + mTextView.getText().toString());
                CommUtils.sendHeartLogToServer(getApplicationContext(), "" + mTextView.getText().toString(), "Success", vitalScheduleId);
            }
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            mSensorManager.unregisterListener(this);
            ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
        } else {
            LogUtils.debug("Checking the heart rate after 30sec");
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        }
    }
}
