<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:cardView="http://schemas.android.com/apk/res-auto"
    android:id="@+id/carView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    cardView:cardCornerRadius="8dp"
    cardView:cardElevation="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/answer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fontFamily="@font/lato_regular"
            android:gravity="top"
            android:padding="5dp"
            android:textColor="@android:color/black"
            android:textSize="25sp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>