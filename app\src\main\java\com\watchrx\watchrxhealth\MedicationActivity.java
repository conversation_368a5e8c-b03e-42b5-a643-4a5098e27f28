package com.watchrx.watchrxhealth;

import static com.watchrx.watchrxhealth.constants.CommonConstants.MAX_REMINDER_REPETITION;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.material.card.MaterialCardView;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.util.List;
import java.util.Objects;

public class MedicationActivity extends AppCompatActivity implements Runnable, View.OnClickListener {
    private static final long REMINDER_TIME_OUT = 60 * 1000;
    private static final Handler timeoutHandler = new Handler();
    private List<MedicationScheduleInstance> scheduleInstances;
    private int currentReminder = -1;
    private int reminderRetryCount = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initReminders();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        if (scheduleInstances == null || scheduleInstances.isEmpty()) {
            LogUtils.debug("There are no medications to display for this alarm. The schedule instances are either null or empty.");
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
            return;
        }
        setContentView(R.layout.activity_medication);

        if (timeoutHandler == null) {
            Handler timeoutHandler = new Handler();
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
            LogUtils.debug("timeoutHandler object died. so using new handler object Inside OnCreate. Going to set timeout Handler.");
        } else {
            LogUtils.debug("Inside OnCreate. Going to set timeout Handler.");
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        }
        showNextReminder();
        setButtonActions();
    }

    private void initReminders() {
        String timeModifier = getIntent().getStringExtra("beforeOrAfterFood");
        String time = getIntent().getStringExtra("timeSlot");
        scheduleInstances = MedicationScheduleInstance.getFromDB(timeModifier, time);
        LogUtils.debug("MedicationActivity -> scheduleInstanceList size for" + timeModifier + "-" + time + "--" + scheduleInstances.size());
    }

    private boolean showNextReminder() {
        currentReminder++;
        if (currentReminder > (scheduleInstances.size() - 1)) {
            LogUtils.debug("All medications have been shown. No more medication reminders to display.");
            GeneralUtils.stopSpeaking();
            timeoutHandler.removeCallbacks(this);
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            return false;
        }
        String medicationId = scheduleInstances.get(currentReminder).getMedicineID();
        MedicationScheduleMaster medicationDetails = MedicationScheduleMaster.getForMedicineId(medicationId);
        TextView medicineName = findViewById(R.id.textView_medicine_name_dosage);
        TextView medicineStrength = findViewById(R.id.textView_strength);
        TextView medsDosage = findViewById(R.id.textView_dosage);
        ImageView medicineImage = findViewById(R.id.image_from_database);

        if (medicineStrength != null) {
            String count = scheduleInstances.get(currentReminder).getQuantities();
            String take = "Take " + count + " " + medicationDetails.getMedicineForm();
            medicineStrength.setText(take);
            String medNameDosage = medicationDetails.getMedicineName();
            medicineName.setText(medNameDosage);
            if (medicationDetails.getMedicineStrength() != null && !medicationDetails.getMedicineStrength().isEmpty()) {
                medsDosage.setText(medicationDetails.getMedicineStrength() != null ? medicationDetails.getMedicineStrength() : "");
            } else {
                medsDosage.setVisibility(View.GONE);
            }
        }
        LogUtils.debug("Will show medication #" + currentReminder + " reminder for medication with details: [" + medicationId + "; " + medicationDetails.getMedicineName() + "; " + medicationDetails.getMedicineDosage() + "; " + medicationDetails.getMedicineStrength() + "; " + medicationDetails.getBeforeOrAfterFood() + "]");
        byte[] image = medicationDetails.getMedicineImage();
        if (medicineImage != null) {
            if (image != null) {
                Bitmap bitmap = BitmapFactory.decodeByteArray(image, 0, image.length);
                if (bitmap != null) {
                    LogUtils.debug("Medication image displaying from SQLite Database");
                    medicineImage.setVisibility(View.VISIBLE);
                    medicineImage.setImageBitmap(bitmap);
                    findViewById(R.id.emptyView2).setVisibility(View.GONE);
                    findViewById(R.id.emptyView).setMinimumHeight(15);
                } else {
                    medicineImage.setVisibility(View.GONE);
                    LogUtils.debug("Showing default image from app assets  ");
                }
            } else {
                medicineImage.setVisibility(View.GONE);
            }
        }
        Intent alertCount = new Intent(MainActivity.SEND_MESSAGE_TO_WATCH);
        GeneralUtils.stopSpeaking();
        speechAnnounce(medicationDetails);
        LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
        LogUtils.debug("Setting a timeout for " + REMINDER_TIME_OUT + " ms. in case this medication is not acknowledged.");
        timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        return true;
    }

    @Override
    public void run() {
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(this);
        reminderRetryCount++;
        LogUtils.debug("Timeout detected. Medication was not acknowledged. reminderRetryCount: " + reminderRetryCount);

        if (reminderRetryCount < 3) {
            String medicationId = scheduleInstances.get(currentReminder).getMedicineID();
            final MedicationScheduleMaster medicationDetails = MedicationScheduleMaster.getForMedicineId(medicationId);
            LogUtils.debug("Reminding again by speaking...");
            speechAnnounce(medicationDetails);
            LogUtils.debug("Setting " + REMINDER_TIME_OUT + " ms timeout for acknowledgement.");
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        } else {
            LogUtils.debug("Exceeded the reminder retry count.");

            String timeModifier = getIntent().getStringExtra("beforeOrAfterFood");
            String time = getIntent().getStringExtra("timeSlot");
            String alertId = getIntent().getStringExtra("alertId");

            if (alertId == null) {
                long triggerAt = getIntent().getLongExtra("triggerAt", -1);
                if (triggerAt == -1) {
                    triggerAt = System.currentTimeMillis();
                }
                int timesAlreadyCalled = getIntent().getIntExtra("timesAlreadyCalled", -1);

                LogUtils.debug("This is a Normal reminder which has been already called: " + timesAlreadyCalled + " times. Max repititions is: " + (MAX_REMINDER_REPETITION - 1));

                if (timesAlreadyCalled < MAX_REMINDER_REPETITION) {
                    if (timeModifier.equalsIgnoreCase("Fixed")) {
                        ReminderUtils.next2MinReminder(triggerAt, timesAlreadyCalled, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"));
                    } else {
                        ReminderUtils.next15MinReminder(triggerAt, timesAlreadyCalled, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"));
                    }
                } else {
                    LogUtils.debug("We have reached maximum reminder limit.");
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(timeModifier, time);
                    CommUtils.sendReminderDisplayedLogToServer(this, "MissedRegularReminder", alertId, timeModifier, time, scheduleInstanceList.subList(currentReminder, scheduleInstanceList.size()));
                }
            } else {
                LogUtils.debug("This is a NURSE reminder.");
                ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(timeModifier, time);
                CommUtils.sendReminderDisplayedLogToServer(this, "MissedNurseReminder", alertId, timeModifier, time, scheduleInstanceList);

            }
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            LogUtils.debug("Exiting the medication screen");
            finish();
        }
    }

    private void speechAnnounce(MedicationScheduleMaster medicationDetails) {
        String medicationForm = medicationDetails.getMedicineForm();
        if (medicationForm.equalsIgnoreCase("Tablet")) {
            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                GeneralUtils.speak("Okay. Take " + scheduleInstances.get(currentReminder).getQuantities() + " capsules of " + medicationDetails.getMedicineName() + ".  " + medicationDetails.getMedicineStrength() + ". Tap ANYWHERE, to confirm you have taken them.");
            } else {
                GeneralUtils.speak("Favor de tomar " + scheduleInstances.get(currentReminder).getQuantities() + " capsulas de" + medicationDetails.getMedicineName() + ".  " + medicationDetails.getMedicineStrength() + ". Toque su reloj dondequiera para confirmar que la tomo");
            }

        } else if (medicationForm.equalsIgnoreCase("Injection")) {
            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                GeneralUtils.speak("Okay. please inject " + scheduleInstances.get(currentReminder).getQuantities() + "  of " + medicationDetails.getMedicineName() + ".  " + medicationDetails.getMedicineStrength() + ". Tap ANYWHERE, to confirm you have injected.");
            } else {
                GeneralUtils.speak("Favor de inyectar " + scheduleInstances.get(currentReminder).getQuantities() + "  de la " + medicationDetails.getMedicineName() + ".  " + medicationDetails.getMedicineStrength() + " . Toque su reloj dondequiera para confirmar que se inyectó");
            }
        } else if (medicationForm.equalsIgnoreCase("Inhaler")) {
            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                GeneralUtils.speak("Okay. Take " + scheduleInstances.get(currentReminder).getQuantities() + ". of " + medicationDetails.getMedicineName() + "$ by mouth " + "$ Tap ANYWHERE, to confirm you have taken them.");
            } else {
                GeneralUtils.speak("Favor de tomar ." + scheduleInstances.get(currentReminder).getQuantities() + ". de la " + medicationDetails.getMedicineName() + "$ oral " + "$ Toque su reloj dondequiera para confirmar que la tomo .");
            }
        } else if (medicationForm.equalsIgnoreCase("Nasal Spray")) {
            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                GeneralUtils.speak("Okay. Use " + scheduleInstances.get(currentReminder).getQuantities() + ". " + medicationDetails.getMedicineName() + "$ in each nostril " + ". Tap ANYWHERE, to confirm you have Use them.");
            } else {
                GeneralUtils.speak("Favor de usar ." + scheduleInstances.get(currentReminder).getQuantities() + ". " + medicationDetails.getMedicineName() + "$  en cada orificio nasal " + ". . Toque su reloj dondequiera para confirmar que la tomo.");
            }
        } else if (medicationForm.equalsIgnoreCase("Syrup")) {
            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                GeneralUtils.speak("Okay. Take " + scheduleInstances.get(currentReminder).getQuantities() + " " + medicationDetails.getMedicineStrength() + " of " + medicationDetails.getMedicineName() + " syrup $" + " Tap ANYWHERE, to confirm you have Taken them.");
            } else {
                GeneralUtils.speak("Favor de tomar " + scheduleInstances.get(currentReminder).getQuantities() + " $del jarabe " + medicationDetails.getMedicineName() + ". Toque su reloj dondequiera para confirmar que la tomo..");
            }
        } else if (medicationForm.equalsIgnoreCase("Powder")) {
            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                GeneralUtils.speak("Okay. Take " + scheduleInstances.get(currentReminder).getQuantities() + " " + medicationDetails.getMedicineStrength() + " of powder " + medicationDetails.getMedicineName() + "$ mix with water and drink $" + " Tap ANYWHERE, to confirm you have taken them.");
            } else {
                GeneralUtils.speak("Okay. Take " + scheduleInstances.get(currentReminder).getQuantities() + " " + medicationDetails.getMedicineStrength() + " of powder " + medicationDetails.getMedicineName() + "$ mix with water and drink $" + " Tap ANYWHERE, to confirm you have taken them.");
            }
        }
    }

    @Override
    public void onClick(View v) {
    }

    private void setButtonActions() {
        MaterialCardView yes = findViewById(R.id.button_yes);
        yes.setOnClickListener(view -> {
            if (scheduleInstances != null && !scheduleInstances.isEmpty()) {
                timeoutHandler.removeCallbacks(MedicationActivity.this);
                String medicationId = scheduleInstances.get(currentReminder).getMedicineID();
                String timeModifier = getIntent().getStringExtra("beforeOrAfterFood");
                String time = getIntent().getStringExtra("timeSlot");
                MedicationScheduleMaster medicationDetails = MedicationScheduleMaster.getForMedicineId(medicationId);
                LogUtils.debug("Detected onClick event as an acknowledgement for medication with details: [" + medicationId + "; " + medicationDetails.getMedicineName() + "]");
                MedicationScheduleInstance.deleteRow(medicationId, timeModifier, time);
                String alertId = getIntent().getStringExtra("alertId");
                PatientDetails patientDetails = PatientDetails.getFromDB();
                CommUtils.acknowledgeReminderLogToServer(MedicationActivity.this, medicationDetails.getMedicineName(), "Reminder Acknowledged", timeModifier, medicationId, time, patientDetails.getPatientId(), alertId);
                if (!showNextReminder()) {
                    GeneralUtils.stopSpeaking();
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Great job! We're all done here.");
                    } else {
                        GeneralUtils.speak("Bien Hecho! Ya acabamos");
                    }
                    LogUtils.debug("Medication Task completed here.");
                    Globals.isScreenRunning = false;
                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    finish();
                }
            }
        });

        MaterialCardView no = findViewById(R.id.button_no);
        no.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String timeModifier = getIntent().getStringExtra("beforeOrAfterFood");
                String time = getIntent().getStringExtra("timeSlot");
                String alertId = getIntent().getStringExtra("alertId");
                String medicationId = scheduleInstances.get(currentReminder).getMedicineID();
                MedicationScheduleMaster medicationDetails = MedicationScheduleMaster.getForMedicineId(medicationId);
                LogUtils.debug("Clicked NO for medication :" + medicationDetails.getMedicineName());
                if (alertId == null) {
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(timeModifier, time);
                    List<MedicationScheduleInstance> currentMeds = scheduleInstanceList.size() == 1 ? scheduleInstanceList : scheduleInstanceList.subList(currentReminder, scheduleInstanceList.size());
                    CommUtils.sendReminderDisplayedLogToServer(MedicationActivity.this, "MissedRegularReminder", null,
                            timeModifier, time, currentMeds
                    );
                    if (!showNextReminder()) {
                        GeneralUtils.stopSpeaking();
                        LogUtils.debug("Medication Task completed here.");
                        Globals.isScreenRunning = false;
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                        finish();
                    }
                } else {
                    LogUtils.debug("This is a NURSE reminder.");
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(timeModifier, time);
                    CommUtils.sendReminderDisplayedLogToServer(MedicationActivity.this, "MissedNurseReminder", alertId,
                            timeModifier, time, scheduleInstanceList);

                    timeoutHandler.removeCallbacks(MedicationActivity.this);
                    Globals.isScreenRunning = false;
                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    LogUtils.debug("Exiting the medication screen");
                    finish();
                }
            }
        });

        MaterialCardView remindMeAgain = findViewById(R.id.button_remind);
        remindMeAgain.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String timeModifier = getIntent().getStringExtra("beforeOrAfterFood");
                String time = getIntent().getStringExtra("timeSlot");
                String alertId = getIntent().getStringExtra("alertId");
                LogUtils.debug("onClicked -> Remind Me Again -> For timeModifier -> " + timeModifier + " ->time " + time);
                if (alertId == null) {
                    long triggerAt = getIntent().getLongExtra("triggerAt", -1);
                    if (triggerAt == -1) {
                        triggerAt = System.currentTimeMillis();
                    }
                    int timesAlreadyCalled = getIntent().getIntExtra("timesAlreadyCalled", -1);
                    LogUtils.debug("This is a Normal reminder which has been already called: " + timesAlreadyCalled + " times. Max repititions is: " + (MAX_REMINDER_REPETITION - 1));
                    if (timesAlreadyCalled < MAX_REMINDER_REPETITION) {
                        GeneralUtils.stopSpeaking();
                        if (timeModifier.equalsIgnoreCase("Fixed")) {
                            GeneralUtils.speak("Alright. I'll try REMINDING you again, in couple of minutes ");
                            ReminderUtils.next2MinReminder(triggerAt, timesAlreadyCalled, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"));
                        } else {
                            GeneralUtils.speak("Alright. I'll try REMINDING you again, in 15 minutes ");
                            ReminderUtils.next15MinReminder(triggerAt, timesAlreadyCalled, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"));
                        }
                    } else {
                        LogUtils.debug("We have reached maximum reminder limit.");
                        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                        List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(timeModifier, time);
                        CommUtils.sendReminderDisplayedLogToServer(MedicationActivity.this, "MissedRegularReminder", null,
                                timeModifier, time, scheduleInstanceList.size() > 1 ? scheduleInstanceList.subList(currentReminder, scheduleInstanceList.size())
                                        : scheduleInstanceList
                        );
                    }
                } else {
                    LogUtils.debug("This is a NURSE reminder.");
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(timeModifier, time);
                    CommUtils.sendReminderDisplayedLogToServer(MedicationActivity.this, "MissedNurseReminder", alertId, timeModifier, time, scheduleInstanceList);
                }
                timeoutHandler.removeCallbacks(MedicationActivity.this);
                Globals.isScreenRunning = false;
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
                LogUtils.debug("Exiting the medication screen");
                finish();
            }
        });
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }
}

