package com.watchrx.watchrxhealth.utils;

/**
 * Created by mukes on 2/11/2017.
 */

import android.os.Environment;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.CommonConstants;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

enum downloadingStatus {
    DOWNLOADING_INPROGRESS,
    DOWNLOADING_COMPLETED,
    DONWLOADING_NOTSTARTED,
    DONWLOADING_STARTED,
    INTALLATION_STARTED,
    RELEASE_INSTALLED,
    UPGRADEINTRANSIT,
    ROL<PERSON>INGBACK
};

public class SWStatusKeeper {
    private String source;
    private String relVersion;
    private downloadingStatus ds;
    private String apkFile;
    private String statusFile;

    public void SWStatusKeeper(String url) {
        source = url;
        statusFile = getStatusFileName();
        this.createStatusFile(statusFile);
        ds = downloadingStatus.DONWLOADING_NOTSTARTED;
        File sdcard = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        apkFile = sdcard.toString() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + getFileNameFromUrl();
        this.storeToFile("status", downloadingStatus.DONWLOADING_NOTSTARTED.toString());
    }

    public static String getStatusFileName() {
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath();
        return rootPath + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + "SWDnStatus.json";
    }


    public void setStatusDownloadStarted() {
        ds = downloadingStatus.DONWLOADING_STARTED;
        this.storeToFile("status", downloadingStatus.DONWLOADING_STARTED.toString());
    }

    public void setStatusDownloadCompleted() {
        ds = downloadingStatus.DOWNLOADING_COMPLETED;
        this.storeToFile("status", downloadingStatus.DOWNLOADING_COMPLETED.toString());
    }

    public void setStatusReleaseInstalled() {
        ds = downloadingStatus.RELEASE_INSTALLED;
        this.storeToFileForTransit("status", downloadingStatus.RELEASE_INSTALLED.toString());
    }

    public void setStatusInstalltionStarted() {
        ds = downloadingStatus.INTALLATION_STARTED;
        this.storeToFile("status", downloadingStatus.RELEASE_INSTALLED.toString());
    }

    public void setUpgradeInTransit() {
        ds = downloadingStatus.UPGRADEINTRANSIT;
        this.storeToFileForTransit("status", downloadingStatus.UPGRADEINTRANSIT.toString());
    }

    public void setRollingBackStatus(String oldapk) {
        ds = downloadingStatus.ROLLINGBACK;
        this.storeToFileForRollBack("status", downloadingStatus.ROLLINGBACK.toString(), oldapk);
    }

    public String getApkFile() {
        return apkFile;
    }


    public String getSource() {
        return source;
    }


    private void createStatusFile(String file) {

        // File sdcard = Environment.getExternalStorageDirectory();
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY;
        File root = new File(rootPath);
        if (!root.exists()) {
            root.mkdirs();
        }
        File fileName = new File(rootPath, "SWDnStatus.json");
        if (!fileName.exists()) {
            try {
                fileName.createNewFile();
                fileName.canWrite();
                fileName.canRead();
            } catch (IOException e) {
                CommUtils.printTraceToLogFile(e);
            }

        }
        /*return rootPath + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + "SWDnStatus.json";*/
        /*File xx = new File(file);
        try {
            if (!xx.exists()) {
                xx.createNewFile();
                xx.canWrite();
                xx.canRead();
            }
        } catch (IOException e) {

        }*/
    }

    private void storeToFile(String key, String value) {
        try {
            JSONObject obj = new JSONObject();

            obj.put(key, value);
            obj.put("url", getSource());
            obj.put("apk_version", SoftwareUpdateUtil.getAppVersion(WatchApp.getContext()));
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + "SWDnStatus.json";
            FileWriter file = new FileWriter(rootPath);

            file.write(obj.toString());
            file.flush();
            file.close();

        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void storeToFileForRollBack(String key, String value, String oldAPk) {
        try {
            JSONObject obj = new JSONObject();

            obj.put(key, value);
            obj.put("url", getSource());
            obj.put("apk_version", oldAPk);
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + "SWDnStatus.json";
            FileWriter file = new FileWriter(rootPath);

            file.write(obj.toString());
            file.flush();
            file.close();

        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void storeToFileForTransit(String key, String value) {
        try {
            JSONObject obj = new JSONObject();

            obj.put(key, value);
            obj.put("url", getSource());
            obj.put("apk_version", SoftwareUpdateUtil.getAppVersion(WatchApp.getContext()));
            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + "SWDnStatus.json";
            FileWriter file = new FileWriter(rootPath);
            LogUtils.debug("Status for Software Upgrade :" + obj.toString());
            file.write(obj.toString());
            file.flush();
            file.close();

        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void cleanup() {
        File xx = new File(this.statusFile);
        if (xx.exists()) {
            xx.delete();
        }

        return;
    }

    public String getFileNameFromUrl() {
        String fileName = this.getSource();
        int idx = fileName.lastIndexOf("/");
        String downloadableFile = idx >= 0 ? fileName.substring(idx + 1) : fileName;
        LogUtils.debug("file name identified from URL as : " + downloadableFile);
        return downloadableFile;
    }
};
