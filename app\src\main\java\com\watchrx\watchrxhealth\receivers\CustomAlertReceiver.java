package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;

public class CustomAlertReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        Globals.intentMap.remove(intent.getStringExtra("alertType") + "-" + intent.getStringExtra("alertDetail"));
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setTimeSlot(intent.getStringExtra("alertType"));
        info.setBeforeOrAfterFood(intent.getStringExtra("alertDetail"));
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("CustomAlertActivity");
        info.setTriggerAt(intent.getLongExtra("triggerAt", -1));
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
        }
    }
}
