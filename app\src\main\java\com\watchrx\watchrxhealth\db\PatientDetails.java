package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

public class PatientDetails {

    public static final String TABLE_PATIENT_DETAILS = "PatientDetails";
    private static final String COL_PATIENT_ID = "PatientId";
    private static final String COL_PATIENT_NAME = "PatientName";
    private static final String COL_PATIENT_CAREGIVER_ID = "CaregiverId";
    private static final String COL_PATIENT_CAREGIVER_NAME = "CaregiverName";
    private static final String COL_PATIENT_TIMESLOTS_TIME = "ChosenTimeForTimeSlots";
    private static final String COL_PATIENT_SOS_MOBILE_NO = "SosMobileNo";
    private static final String COL_PATIENT_ADDRESS = "Address";
    private static final String COL_GPS_STATUS = "GpsStatus";
    private static final String COL_RADIUS = "Radius";
    private static final String COL_TRACKING_STATUS = "TrackStatus";
    private static final String COL_LAT_LNG = "LatLng";
    private static final String COL_APP_COLOR = "AppColor";
    private static final String COL_APP_LANGUAGE = "AppLanguage";
    private static final String COL_HEART_SCHEDULE_STATUS = "HeartRateScheduleStatus";
    private static final String COL_HEART_SCHEDULE_DAYS = "HeartRateScheduleDays";
    private static final String COL_HEART_SCHEDULE_TIME_SLOTS = "HeartRateScheduleTimeSlots";

    private static final String COL_PEDOMETER_STATUS = "PedoMeterStatus";
    private static final String COL_PEDOMETER_INTERVAL = "PedoMeterInterval";

    private static final String COL_SLEEP_START_TIME = "SleepStartTime";
    private static final String COL_SLEEP_END_TIME = "SleepEndTime";

    private static final String COL_PROVIDER_NAME = "ProviderName";
    private static final String COL_PROVIDER_PHONE = "ProviderPhone";
    private static final String COL_CM_NAME = "CMName";
    private static final String COL_CM_PHONE = "CMPhone";

    private static final String COL_CLINIC_NAME = "ClinicName";

    static final String DELETE_PATIENT_DETAILS_TABLE = "DROP TABLE IF EXISTS " + TABLE_PATIENT_DETAILS + ";";

    static final String CREATE_PATIENT_DETAILS_TABLE =
            "CREATE TABLE " + TABLE_PATIENT_DETAILS +
                    "(" +
                    COL_PATIENT_ID + " TEXT, " +
                    COL_PATIENT_NAME + " TEXT, " +
                    COL_PATIENT_CAREGIVER_ID + " TEXT, " +
                    COL_PATIENT_CAREGIVER_NAME + " TEXT, " +
                    COL_PATIENT_TIMESLOTS_TIME + " TEXT," +
                    COL_PATIENT_SOS_MOBILE_NO + " TEXT, " +
                    COL_PATIENT_ADDRESS + " TEXT," +
                    COL_TRACKING_STATUS + " TEXT, " +
                    COL_LAT_LNG + " TEXT, " +
                    COL_RADIUS + " TEXT," +
                    COL_GPS_STATUS + " TEXT, " +
                    COL_APP_COLOR + " TEXT ," +
                    COL_APP_LANGUAGE + " TEXT ," +
                    COL_HEART_SCHEDULE_STATUS + " TEXT , " +
                    COL_HEART_SCHEDULE_DAYS + " TEXT , " +
                    COL_HEART_SCHEDULE_TIME_SLOTS + " TEXT ," +
                    COL_PEDOMETER_STATUS + " TEXT ," +
                    COL_PEDOMETER_INTERVAL + " TEXT ," +
                    COL_SLEEP_START_TIME + " TEXT ," +
                    COL_SLEEP_END_TIME + " TEXT ," +
                    COL_PROVIDER_NAME + " TEXT ," +
                    COL_PROVIDER_PHONE + " TEXT ," +
                    COL_CM_NAME + " TEXT ," +
                    COL_CM_PHONE + " TEXT ," +
                    COL_CLINIC_NAME + " TEXT " +
                    ");";

    private static final String[] COLUMNS_PATIENT_DETAILS = {
            COL_PATIENT_ID,
            COL_PATIENT_NAME,
            COL_PATIENT_CAREGIVER_ID,
            COL_PATIENT_CAREGIVER_NAME,
            COL_PATIENT_TIMESLOTS_TIME,
            COL_PATIENT_SOS_MOBILE_NO,
            COL_PATIENT_ADDRESS,
            COL_GPS_STATUS,
            COL_RADIUS,
            COL_LAT_LNG,
            COL_TRACKING_STATUS,
            COL_APP_LANGUAGE,
            COL_APP_COLOR,
            COL_HEART_SCHEDULE_STATUS,
            COL_HEART_SCHEDULE_DAYS,
            COL_HEART_SCHEDULE_TIME_SLOTS,
            COL_PEDOMETER_STATUS,
            COL_PEDOMETER_INTERVAL,
            COL_SLEEP_START_TIME,
            COL_SLEEP_END_TIME,
            COL_PROVIDER_NAME,
            COL_PROVIDER_PHONE,
            COL_CM_NAME,
            COL_CM_PHONE,
            COL_CLINIC_NAME
    };

    private String patientId;
    private String patientName;
    private String caregiverId;
    private String caregiverName;
    private String chosenTimeForTimeSlots;
    private String sosMobileNo;
    private String address;
    private String gpsStatus;
    private String radius;
    private String heartRateStatus;
    private String heartScheduleDays;
    private String heartRateScheduleTimeSlots;
    private String pedoMeterStatus;
    private String pedoMeterInterval;
    private String sleepStartTime;
    private String sleepEndTime;
    private String providerName;
    private String providerPhone;
    private String cmName;
    private String cmPhone;

    private String clinicName;

    public String getAppColor() {
        return appColor;
    }

    public void setAppColor(String appColor) {
        this.appColor = appColor;
    }

    public String getAppLanguage() {
        return appLanguage;
    }

    public void setAppLanguage(String appLanguage) {
        this.appLanguage = appLanguage;
    }

    private String appColor;
    private String appLanguage;

    public String getLatLong() {
        return latLong;
    }

    public void setLatLong(String latLong) {
        this.latLong = latLong;
    }

    public String getTrackingStatus() {
        return trackingStatus;
    }

    public void setTrackingStatus(String trackingStatus) {
        this.trackingStatus = trackingStatus;
    }

    private String trackingStatus;
    private String latLong;

    public String getGpsStatus() {
        return gpsStatus;
    }

    public void setGpsStatus(String gpsStatus) {
        this.gpsStatus = gpsStatus;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getRadius() {
        return radius;
    }

    public void setRadius(String radius) {
        this.radius = radius;
    }

    public String getHeartRateStatus() {
        return heartRateStatus;
    }

    public void setHeartRateStatus(String heartRateStatus) {
        this.heartRateStatus = heartRateStatus;
    }

    public String getHeartScheduleDays() {
        return heartScheduleDays;
    }

    public void setHeartScheduleDays(String heartScheduleDays) {
        this.heartScheduleDays = heartScheduleDays;
    }

    public String getHeartRateScheduleTimeSlots() {
        return heartRateScheduleTimeSlots;
    }

    public void setHeartRateScheduleTimeSlots(String heartRateScheduleTimeSlots) {
        this.heartRateScheduleTimeSlots = heartRateScheduleTimeSlots;
    }

    public String getPedoMeterStatus() {
        return pedoMeterStatus;
    }

    public void setPedoMeterStatus(String pedoMeterStatus) {
        this.pedoMeterStatus = pedoMeterStatus;
    }

    public String getPedoMeterInterval() {
        return pedoMeterInterval;
    }

    public void setPedoMeterInterval(String pedoMeterInterval) {
        this.pedoMeterInterval = pedoMeterInterval;
    }

    public String getSleepStartTime() {
        return sleepStartTime;
    }

    public void setSleepStartTime(String sleepStartTime) {
        this.sleepStartTime = sleepStartTime;
    }

    public String getSleepEndTime() {
        return sleepEndTime;
    }

    public void setSleepEndTime(String sleepEndTime) {
        this.sleepEndTime = sleepEndTime;
    }

    public String getProviderName() {
        return providerName;
    }

    public void setProviderName(String providerName) {
        this.providerName = providerName;
    }

    public String getProviderPhone() {
        return providerPhone;
    }

    public void setProviderPhone(String providerPhone) {
        this.providerPhone = providerPhone;
    }

    public String getCmName() {
        return cmName;
    }

    public void setCmName(String cmName) {
        this.cmName = cmName;
    }

    public String getCmPhone() {
        return cmPhone;
    }

    public String getClinicName() {
        return clinicName;
    }

    public void setClinicName(String clinicName) {
        this.clinicName = clinicName;
    }

    public void setCmPhone(String cmPhone) {
        this.cmPhone = cmPhone;
    }

    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_PATIENT_DETAILS);
    }

    public static long addToDB(PatientDetails patientDetails) {
        PatientDetails.deleteAllRows(); // force only one row in db

        ContentValues values = new ContentValues();

        values.put(COL_PATIENT_ID, patientDetails.patientId);
        values.put(COL_PATIENT_NAME, patientDetails.patientName);
        values.put(COL_PATIENT_CAREGIVER_ID, patientDetails.caregiverId);
        values.put(COL_PATIENT_CAREGIVER_NAME, patientDetails.caregiverName);
        values.put(COL_PATIENT_TIMESLOTS_TIME, patientDetails.chosenTimeForTimeSlots);
        values.put(COL_PATIENT_SOS_MOBILE_NO, patientDetails.sosMobileNo);
        values.put(COL_PATIENT_ADDRESS, patientDetails.address);
        values.put(COL_GPS_STATUS, patientDetails.gpsStatus);
        values.put(COL_RADIUS, patientDetails.radius);
        values.put(COL_LAT_LNG, patientDetails.latLong);
        values.put(COL_TRACKING_STATUS, patientDetails.trackingStatus);
        values.put(COL_APP_COLOR, patientDetails.appColor);
        values.put(COL_APP_LANGUAGE, patientDetails.appLanguage);
        values.put(COL_HEART_SCHEDULE_STATUS, patientDetails.heartRateStatus);
        values.put(COL_HEART_SCHEDULE_DAYS, patientDetails.heartScheduleDays);
        values.put(COL_HEART_SCHEDULE_TIME_SLOTS, patientDetails.heartRateScheduleTimeSlots);
        values.put(COL_PEDOMETER_STATUS, patientDetails.pedoMeterStatus);
        values.put(COL_PEDOMETER_INTERVAL, patientDetails.pedoMeterInterval);
        values.put(COL_SLEEP_START_TIME, patientDetails.sleepStartTime);
        values.put(COL_SLEEP_END_TIME, patientDetails.sleepEndTime);
        values.put(COL_PROVIDER_NAME, patientDetails.providerName);
        values.put(COL_PROVIDER_PHONE, patientDetails.providerPhone);
        values.put(COL_CM_NAME, patientDetails.cmName);
        values.put(COL_CM_PHONE, patientDetails.cmPhone);
        values.put(COL_CLINIC_NAME, patientDetails.clinicName);

        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_PATIENT_DETAILS, values);
    }

    public static PatientDetails getFromDB() {
        PatientDetails patientDetails = new PatientDetails();

        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_PATIENT_DETAILS, COLUMNS_PATIENT_DETAILS, null, null, null);

        try {
            if (cursor.moveToFirst()) {
                patientDetails.setPatientId(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_ID)));
                patientDetails.setPatientName(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_NAME)));
                patientDetails.setCaregiverId(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_CAREGIVER_ID)));
                patientDetails.setCaregiverName(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_CAREGIVER_NAME)));
                patientDetails.setChosenTimeForTimeSlots(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_TIMESLOTS_TIME)));
                patientDetails.setSosMobileNo(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_SOS_MOBILE_NO)));
                patientDetails.setAddress(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_ADDRESS)));
                patientDetails.setGpsStatus(cursor.getString(cursor.getColumnIndexOrThrow(COL_GPS_STATUS)));
                patientDetails.setRadius(cursor.getString(cursor.getColumnIndexOrThrow(COL_RADIUS)));
                patientDetails.setLatLong(cursor.getString(cursor.getColumnIndexOrThrow(COL_LAT_LNG)));
                patientDetails.setTrackingStatus(cursor.getString(cursor.getColumnIndexOrThrow(COL_TRACKING_STATUS)));
                patientDetails.setAppColor(cursor.getString(cursor.getColumnIndexOrThrow(COL_APP_COLOR)));
                patientDetails.setAppLanguage(cursor.getString(cursor.getColumnIndexOrThrow(COL_APP_LANGUAGE)));
                patientDetails.setHeartRateStatus(cursor.getString(cursor.getColumnIndexOrThrow(COL_HEART_SCHEDULE_STATUS)));
                patientDetails.setHeartScheduleDays(cursor.getString(cursor.getColumnIndexOrThrow(COL_HEART_SCHEDULE_DAYS)));
                patientDetails.setHeartRateScheduleTimeSlots(cursor.getString(cursor.getColumnIndexOrThrow(COL_HEART_SCHEDULE_TIME_SLOTS)));
                patientDetails.setPedoMeterStatus(cursor.getString(cursor.getColumnIndexOrThrow(COL_PEDOMETER_STATUS)));
                patientDetails.setPedoMeterInterval(cursor.getString(cursor.getColumnIndexOrThrow(COL_PEDOMETER_INTERVAL)));
                patientDetails.setSleepStartTime(cursor.getString(cursor.getColumnIndexOrThrow(COL_SLEEP_START_TIME)));
                patientDetails.setSleepEndTime(cursor.getString(cursor.getColumnIndexOrThrow(COL_SLEEP_END_TIME)));
                patientDetails.setProviderName(cursor.getString(cursor.getColumnIndexOrThrow(COL_PROVIDER_NAME)));
                patientDetails.setProviderPhone(cursor.getString(cursor.getColumnIndexOrThrow(COL_PROVIDER_PHONE)));
                patientDetails.setCmName(cursor.getString(cursor.getColumnIndexOrThrow(COL_CM_NAME)));
                patientDetails.setCmPhone(cursor.getString(cursor.getColumnIndexOrThrow(COL_CM_PHONE)));
                patientDetails.setClinicName(cursor.getString(cursor.getColumnIndexOrThrow(COL_CLINIC_NAME)));
            }

            cursor.close();
        } catch (Exception e) {
            Log.e("PatientDetails", "" + e);
        }

        return patientDetails;
    }

    public static void updateGPSAddressChangedValue(String pId, String value) {
        String sql = "UPDATE " + TABLE_PATIENT_DETAILS + " set " + COL_LAT_LNG + "='" + value + "' where " + COL_PATIENT_ID + "=" + pId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void updateGPSStatus(String pId, String value) {
        String sql = "UPDATE " + TABLE_PATIENT_DETAILS + " set " + COL_GPS_STATUS + "='" + value + "' where " + COL_PATIENT_ID + "=" + pId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void updateGPSTrackStatus(String pId, String value) {
        String sql = "UPDATE " + TABLE_PATIENT_DETAILS + " set " + COL_TRACKING_STATUS + "='" + value + "' where " + COL_PATIENT_ID + "=" + pId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void updatePedoMeter(String pId, String status, String interval) {
        String sql = "UPDATE " + TABLE_PATIENT_DETAILS + " set " + COL_PEDOMETER_STATUS + "='" + status + "' ,"
                + COL_PEDOMETER_INTERVAL + " = '" + interval + "' where " + COL_PATIENT_ID + "=" + pId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void updatePedoMeterInterVal(String pId, String interval) {
        String sql = "UPDATE " + TABLE_PATIENT_DETAILS + " set " + COL_PEDOMETER_INTERVAL + "='" + interval + "' where " + COL_PATIENT_ID + "=" + pId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void updateGPSInfo(String pId, String latLong, String radius, String gpsStatus, String trackingStatus) {
        String sql = "UPDATE PatientDetails set " + COL_LAT_LNG + " = '" + latLong + "' , " + COL_RADIUS + " = '" + radius + "', "
                + COL_GPS_STATUS + " = '" + gpsStatus + "' ," + COL_TRACKING_STATUS + " = '" + trackingStatus + "' where " + COL_PATIENT_ID + " = '" + pId + "'";
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static void updateSleepTime(String pId, String sleepStartTime, String sleepEndTime) {
        String sql = "UPDATE PatientDetails set " + COL_SLEEP_START_TIME + " = '" + sleepStartTime + "' , " + COL_SLEEP_END_TIME + " = '" +
                sleepEndTime + "' where " + COL_PATIENT_ID + " = '" + pId + "'";
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public String getSosMobileNo() {
        return sosMobileNo;
    }

    public void setSosMobileNo(String sosMobileNo) {
        this.sosMobileNo = sosMobileNo;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getPatientName() {
        return patientName;
    }

    public void setPatientName(String patientName) {
        this.patientName = patientName;
    }

    public String getCaregiverId() {
        return caregiverId;
    }

    public void setCaregiverId(String caregiverId) {
        this.caregiverId = caregiverId;
    }

    public String getCaregiverName() {
        return caregiverName;
    }

    public void setCaregiverName(String caregiverName) {
        this.caregiverName = caregiverName;
    }

    public String getChosenTimeForTimeSlots() {
        return chosenTimeForTimeSlots;
    }

    public void setChosenTimeForTimeSlots(String chosenTimeForTimeSlots) {
        this.chosenTimeForTimeSlots = chosenTimeForTimeSlots;
    }

    public int getHourOfChosenTimeForTimeSlot(String timeSlot) {
        if (timeSlot != null && !timeSlot.isEmpty()) {
            int index = this.chosenTimeForTimeSlots.indexOf(timeSlot) + timeSlot.length() + 1;
            String str = this.chosenTimeForTimeSlots.substring(index, index + 2);

            return Integer.parseInt(str);
        }
        return 0;
    }

    public int getMinutesOfChosenTimeForTimeSlot(String timeSlot) {
        if (timeSlot != null && !timeSlot.isEmpty()) {
            int index = this.chosenTimeForTimeSlots.indexOf(timeSlot) + timeSlot.length() + 4;
            String str = this.chosenTimeForTimeSlots.substring(index, index + 2);

            return Integer.parseInt(str);
        }
        return 0;
    }
}
