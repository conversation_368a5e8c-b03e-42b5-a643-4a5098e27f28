<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.watchrx.watchrxhealth"
    android:versionCode="44"
    android:versionName="v1.0.44" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="35" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <!-- Health Connect permissions for all enabled vitals -->
    <uses-permission android:name="android.permission.health.READ_BLOOD_GLUCOSE" />
    <uses-permission android:name="android.permission.health.WRITE_BLOOD_GLUCOSE" />
    <uses-permission android:name="android.permission.health.READ_HEART_RATE" />
    <uses-permission android:name="android.permission.health.WRITE_HEART_RATE" />
    <uses-permission android:name="android.permission.health.READ_BLOOD_PRESSURE" />
    <uses-permission android:name="android.permission.health.WRITE_BLOOD_PRESSURE" />
    <uses-permission android:name="android.permission.health.READ_OXYGEN_SATURATION" />
    <uses-permission android:name="android.permission.health.WRITE_OXYGEN_SATURATION" />
    <uses-permission android:name="android.permission.health.READ_STEPS" />
    <uses-permission android:name="android.permission.health.WRITE_STEPS" />
    <uses-permission android:name="android.permission.health.READ_SLEEP" />
    <uses-permission android:name="android.permission.health.WRITE_SLEEP" />
    <uses-permission android:name="android.permission.health.READ_WEIGHT" />
    <uses-permission android:name="android.permission.health.WRITE_WEIGHT" />
    <uses-permission android:name="android.permission.health.READ_BODY_TEMPERATURE" />
    <uses-permission android:name="android.permission.health.WRITE_BODY_TEMPERATURE" />

    <queries>
        <package android:name="com.google.android.apps.healthdata" />
        <!-- Needs to be explicitly declared on Android R+ -->
        <package android:name="com.google.android.apps.maps" />
    </queries>

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.bluetooth"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" />

    <uses-permission android:name="android.permission.BROADCAST_STICKY" /> <!-- In Meeting "share screen" will need the following Permissions -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <permission
        android:name="com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.watchrx.watchrxhealth.WatchApp"
        android:allowBackup="true"
        android:appComponentFactory="@string/app_name"
        android:debuggable="true"
        android:enabled="true"
        android:extractNativeLibs="true"
        android:fullBackupContent="false"
        android:icon="@mipmap/watchrx_app_icon_round"
        android:label="@string/app_name"
        android:persistent="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.WatchRx"
        android:usesCleartextTraffic="true"
        android:windowSoftInputMode="adjustResize" >
        <activity
            android:name="com.watchrx.watchrxhealth.LatestAlertsActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.ZoomVideoCallScreen"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.auth.EnterOTPActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.auth.EnterPasswordActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.auth.ResendOTPScreen"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.auth.LoginScreen"
            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
            android:exported="true"
            android:launchMode="standard"
            android:singleUser="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name="com.watchrx.watchrxhealth.LoginActivity" />
        <activity
            android:name="com.watchrx.watchrxhealth.ChatActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.VitalsGraphActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.MyTaskCalendar"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.WebViewActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.ReminderDetailsActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.VitalDashboard"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.InteractiveVoiceActivity"
            android:exported="false" />
        <activity
            android:name="com.watchrx.watchrxhealth.PatientDiaryActivity"
            android:screenOrientation="fullSensor" />
        <activity android:name="com.watchrx.watchrxhealth.SleepMonitorActivity" />
        <activity android:name="com.watchrx.watchrxhealth.SplashActivity" />
        <activity
            android:name="com.watchrx.watchrxhealth.VitalDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.ViewAllTextMessageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.MedicationDetailsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.GPSActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.TextMessageActivity"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.watchrx.watchrxhealth.PhoneCallsActivity"
            android:screenOrientation="portrait" />
        <activity android:name="com.watchrx.watchrxhealth.WifiConfig" />
        <activity
            android:name="com.watchrx.watchrxhealth.CustomAlertActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.ReminderActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.AddMedication"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name="com.watchrx.watchrxhealth.MedicationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.NurseOnTheWayActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.VisitVerificationActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.BatteryActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.AlertsActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.HeartRateActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.PedoMeterActivity"
            android:screenOrientation="portrait" /> <!-- <activity -->
        <!-- android:name=".ble.NewVitalsActivity" -->
        <!-- android:screenOrientation="portrait" /> -->
        <activity
            android:name="com.watchrx.watchrxhealth.ScheduleTextMessageActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.twilio.VideoActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.watchrx.watchrxhealth.twilio.SettingsActivity"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />
        <activity android:name="com.watchrx.watchrxhealth.ble.NewVitalsActivity" />
        <activity
            android:name="com.watchrx.watchrxhealth.voip.IncomingCallActivity"
            android:launchMode="singleTask" />
        <activity
            android:name="com.watchrx.watchrxhealth.PermissionsRationaleActivity"
            android:exported="true"
            android:screenOrientation="portrait" >
            <intent-filter>
                <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name="com.watchrx.watchrxhealth.ViewPermissionUsageActivity"
            android:exported="true"
            android:permission="android.permission.START_VIEW_PERMISSION_USAGE"
            android:targetActivity="com.watchrx.watchrxhealth.PermissionsRationaleActivity" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW_PERMISSION_USAGE" />

                <category android:name="android.intent.category.HEALTH_PERMISSIONS" />
            </intent-filter>
        </activity-alias>

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="firebase_messaging_auto_init_enabled"
            android:value="false" />
        <meta-data
            android:name="firebase_analytics_collection_enabled"
            android:value="false" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyDhGEgJCRCYBnheNQ9TVFY3h7Byn0oq6R4" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.watchrx.watchrxhealth.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />
        </provider>

        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.CheckQueueReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.ReminderReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.CustomAlertReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.syncup.SyncupReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.RebootComplete"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.TimeZoneChangedReceiver"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.NewPackageInstalled"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_INSTALL" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />
                <action android:name="android.intent.action.PACKAGE_REPLACED" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="package" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.InternetStatusChangeReceiver"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <!-- <action android:name="android.net.wifi.WIFI_STATE_CHANGED" /> -->
            </intent-filter>
        </receiver>
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.HrtBroadcastReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.NetworkHeartBeat"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.ScheduleMessageReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.SendPedoMeterReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.SendHealthConnectDataReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.MidnightPedoMeterResetReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.VitalReminderReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.MidnightLogFileUploadReceiver"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.watchrx.watchrxhealth.receivers.BleBroadCastReceiver"
            android:enabled="true"
            android:exported="true" />

        <service
            android:name="com.watchrx.watchrxhealth.gcm.GCMPushReceiverService"
            android:directBootAware="true"
            android:exported="true"
            android:permission="true" >
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService"
            android:exported="false" />
        <service
            android:name="com.watchrx.watchrxhealth.gps.GeoFenceIntentServices"
            android:exported="false" />
        <service
            android:name="com.watchrx.watchrxhealth.pedometer.SensorListener"
            android:exported="false" />
        <service
            android:name="com.watchrx.watchrxhealth.WatchRxForegroundService"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback"
            android:permission="android.permission.FOREGROUND_SERVICE" />
        <service
            android:name="com.zipow.videobox.share.ScreenShareServiceForSDK"
            android:exported="false"
            android:foregroundServiceType="microphone|connectedDevice"
            android:label="Zoom" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <receiver
            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>

            <meta-data
                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
                android:value="true" />
        </receiver>
        <!--
             FirebaseMessagingService performs security checks at runtime,
             but set to not exported to explicitly avoid allowing another app to call it.
        -->
        <service
            android:name="com.google.firebase.messaging.FirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false" >
            <intent-filter android:priority="-500" >
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name="com.google.firebase.components.ComponentDiscoveryService"
            android:directBootAware="true"
            android:exported="false" >
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
            <meta-data
                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
                android:value="com.google.firebase.components.ComponentRegistrar" />
        </service>

        <receiver
            android:name="com.google.firebase.messaging.directboot.FirebaseMessagingDirectBootReceiver"
            android:directBootAware="true"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.firebase.messaging.RECEIVE_DIRECT_BOOT" />
            </intent-filter>
        </receiver>

        <activity
            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--
            Service handling Google Sign-In user revocation. For apps that do not integrate with
            Google Sign-In, this service will never be started.
        -->
        <service
            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
            android:exported="true"
            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
            android:visibleToInstantApps="true" />
        <service
            android:name="androidx.health.platform.client.impl.sdkservice.HealthDataSdkService"
            android:enabled="true"
            android:exported="true" >
            <intent-filter>
                <action android:name="androidx.health.platform.client.ACTION_BIND_SDK_SERVICE" />
            </intent-filter>
        </service>

        <activity
            android:name="com.google.android.gms.common.api.GoogleApiActivity"
            android:exported="false"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />

        <provider
            android:name="com.google.firebase.provider.FirebaseInitProvider"
            android:authorities="com.watchrx.watchrxhealth.firebaseinitprovider"
            android:directBootAware="true"
            android:exported="false"
            android:initOrder="100" />
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.watchrx.watchrxhealth.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.work.WorkManagerInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <service
            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" />
        <service
            android:name="androidx.work.impl.background.systemjob.SystemJobService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_job_service_default"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />
        <service
            android:name="androidx.work.impl.foreground.SystemForegroundService"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_foreground_service_default"
            android:exported="false" />

        <receiver
            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="false" />
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.BATTERY_LOW" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
            android:directBootAware="false"
            android:enabled="false"
            android:exported="false" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.TIME_SET" />
                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
            android:directBootAware="false"
            android:enabled="@bool/enable_system_alarm_service_default"
            android:exported="false" >
            <intent-filter>
                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
            </intent-filter>
        </receiver>
        <receiver
            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
            </intent-filter>
        </receiver>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
            android:exported="false" >
            <meta-data
                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
                android:value="cct" />
        </service>
        <service
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" >
        </service>

        <receiver
            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
            android:exported="false" />

        <service
            android:name="androidx.room.MultiInstanceInvalidationService"
            android:directBootAware="true"
            android:exported="false" />
    </application>

</manifest>