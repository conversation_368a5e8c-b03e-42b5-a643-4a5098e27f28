package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.PedoMeterActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.pedometer.Database;
import com.watchrx.watchrxhealth.pedometer.Util;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.util.Date;

public class SendPedoMeterReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        Log.e("SendPedoMeterReceiver", "SendPedoMeterReceiver Triggered" + new Date());
        Database db = Database.getInstance(WatchApp.getContext());
        int todayOffset = db.getSteps(Util.getToday());
        int since_boot = db.getCurrentSteps();
        int steps_today = Math.max(todayOffset + since_boot, 0);
        Log.i("Steps To Send", " Date : " + steps_today);

        LogUtils.debug("While Sending Steps to server : todayOffset value = " + todayOffset + " Since Boot Complete Sensor Counts : " + since_boot + " Current Counts Now : " + steps_today);
        CommUtils.sendPedoMeterLogToServer(WatchApp.getContext(), steps_today, false, false);
        ReminderUtils.setReminderToSendStepsToServer(context);

        Intent reminderIntent = new Intent(context, PedoMeterActivity.class);
        reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        reminderIntent.putExtra("stepsCount", steps_today);
        reminderIntent.putExtra("status", "Steps");
        context.startActivity(reminderIntent);
    }
}
