<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".VitalDetailsActivity">

    <androidx.cardview.widget.CardView
        android:id="@+id/vitalCardView"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:layout_margin="@dimen/d_5_d"
        android:backgroundTint="@color/white_smoke"
        app:cardCornerRadius="@dimen/d_12_d"
        app:layout_constraintHorizontal_weight="0.8"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:weightSum="3">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="0.8"
                android:orientation="horizontal"
                android:weightSum="2">

                <ImageView
                    android:id="@+id/img_loading"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:layout_weight="0.3"
                    android:src="@mipmap/ic_loading"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/remainingTime"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1.7"
                    android:gravity="end|center_vertical|center_horizontal"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textSize="18sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/margin_normal"
                android:layout_weight="1.2"
                android:orientation="horizontal"
                android:weightSum="1">

                <TextView
                    android:id="@+id/vital_value"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textAppearance="@style/TextAppearance.AppCompat.Title"
                    android:textSize="20sp"
                    android:visibility="visible" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="bottom"
                android:layout_weight="1"
                android:gravity="bottom"
                android:orientation="horizontal"
                android:weightSum="2">

                <ProgressBar
                    android:id="@+id/progressBar"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom"
                    android:layout_margin="5dp"
                    android:layout_weight="2"
                    android:indeterminate="false"
                    android:max="60"
                    android:minWidth="200dp"
                    android:minHeight="50dp"
                    android:progress="1"
                    android:visibility="gone" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/oneTime"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom|start"
                    android:layout_weight="1"
                    android:text="@string/one_time"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/realTime"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="bottom|end"
                    android:layout_weight="1"
                    android:text="@string/real_time"
                    android:textStyle="bold"
                    android:visibility="visible" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/d_10_d"
        android:background="@color/grey"
        app:layout_constraintTop_toBottomOf="@+id/vitalCardView">

        <ImageView
            android:id="@+id/before_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/d_20_d"
            android:padding="@dimen/d_10_d"
            android:rotation="180"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/after_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/d_20_d"
            android:padding="@dimen/d_10_d"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_bold"
            android:textColor="@color/black"
            android:textSize="25sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/vitalRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/d_10_d"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintTop_toBottomOf="@+id/clDate"
        tools:layout_editor_absoluteX="10dp" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>