package com.watchrx.watchrxhealth;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.ChatAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.ChatMessageData;
import com.watchrx.watchrxhealth.models.ChatMessageResponse;
import com.watchrx.watchrxhealth.models.Message;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ChatActivity extends AppCompatActivity {

    private RecyclerView recyclerView;
    private ChatAdapter chatAdapter;
    private EditText editMessage;
    private Button sendButton;
    private ProgressBar progressBar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_chat);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        recyclerView = findViewById(R.id.recycler_view);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(ChatActivity.this));

        editMessage = findViewById(R.id.edit_message);
        sendButton = findViewById(R.id.button_send);

        progressBar = findViewById(R.id.progress_bar);

        getHistoryMessages();

        sendButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String newMessageText = editMessage.getText().toString();
                if (!TextUtils.isEmpty(newMessageText)) {
                    editMessage.setText("");
                    sendOrReplyMessage(newMessageText);
                    closeKeyboard();
                }
            }
        });
    }

    private void getHistoryMessages() {
        try {
            URL url = new URL(URLConstants.CHAT_DATA);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            new RestAsyncTask(url, jsonObject.toString(), progressBar, new ChatMessageResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }

    private class ChatMessageResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }
            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        ChatMessageResponse responseMessage = new Gson().fromJson((String) result, ChatMessageResponse.class);
                        Log.i("Messages:", (String) result);
                        if (responseMessage != null && responseMessage.isStatus() && responseMessage.getData() != null && responseMessage.getData().size() > 0) {
                            List<ChatMessageData> dataList = new ArrayList<>(responseMessage.data);
                            chatAdapter = new ChatAdapter(getGroupedMessages(dataList));
                            recyclerView.setAdapter(chatAdapter);
                            recyclerView.scrollToPosition(chatAdapter.getItemCount() - 1);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        }
    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    private Map<String, List<Message>> getGroupedMessages(List<ChatMessageData> messageList) {
        Map<String, List<Message>> groupedMessages = new LinkedHashMap<>();
        try {
            for (ChatMessageData chatMessageData : messageList) {
                if (!groupedMessages.containsKey(chatMessageData.time)) {
                    groupedMessages.put(chatMessageData.time, new ArrayList<>(chatMessageData.data));
                } else {
                    Objects.requireNonNull(groupedMessages.get(chatMessageData.time)).addAll(chatMessageData.data);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return groupedMessages;
    }

    private void sendOrReplyMessage(String message) {
        try {
            URL url = new URL(URLConstants.SEND_CHAT_DATA);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            jsonObject.accumulate("isSenderApp", true);
            jsonObject.accumulate("isSenderServer", false);
            jsonObject.accumulate("chatText", message);

            new RestAsyncTask(url, jsonObject.toString(), progressBar, new SentMessageResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending sending message " + e.getMessage());
            e.printStackTrace();
        }
    }

    private class SentMessageResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            getHistoryMessages();
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        Log.i("ChatActivity", " Activity refreshed...");
        getHistoryMessages();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
    }
}
