package com.watchrx.watchrxhealth.syncup;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Environment;
import android.util.Log;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.CustomAlertsDetails;
import com.watchrx.watchrxhealth.db.CustomerAlertsDB;
import com.watchrx.watchrxhealth.db.MedicationDetail;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ScheduleMessageVO;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.db.VitalConfiguration;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.messages.RegisterResponseMessage;
import com.watchrx.watchrxhealth.messages.VitalConfigVO;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;

public class SyncupFromDatabase {

    public static String registerWatchForSync(String result) {

        String isRegistrationSuccessful = "";

        if ((result != null)) {

            RegisterResponseMessage responseMessage = new Gson().fromJson(result, RegisterResponseMessage.class);
            LogUtils.debug("Response message ->" + result);

            if (responseMessage != null && responseMessage.isSuccessResponse()) {
                PatientDetails patientDetails = new PatientDetails();

                patientDetails.setPatientId(responseMessage.patientId);
                patientDetails.setPatientName(responseMessage.patientId);
                patientDetails.setCaregiverId(responseMessage.patientId);
                patientDetails.setCaregiverName(responseMessage.patientId);
                patientDetails.setChosenTimeForTimeSlots(responseMessage.chosenTimeForTimeSlots);
                patientDetails.setSosMobileNo(responseMessage.sosMobileNo);
                patientDetails.setAddress(responseMessage.address);

                patientDetails.setGpsStatus(responseMessage.gpsStatus);
                patientDetails.setRadius(responseMessage.radius);
                patientDetails.setTrackingStatus(responseMessage.trackingStatus);
                patientDetails.setLatLong(responseMessage.latLong);
                patientDetails.setAppColor(responseMessage.appColor);
                patientDetails.setAppLanguage(responseMessage.appLanguage);
                patientDetails.setHeartRateStatus(responseMessage.heartRateStatus);
                patientDetails.setHeartScheduleDays(responseMessage.heartScheduleDaysOfWeek);
                patientDetails.setHeartRateScheduleTimeSlots(responseMessage.heartRateScheduleTimeSlots);
                if (responseMessage.pedometerConfigurationInfo != null) {
                    if (responseMessage.pedometerConfigurationInfo.state != null) {
                        patientDetails.setPedoMeterStatus(responseMessage.pedometerConfigurationInfo.state);
                    } else {
                        patientDetails.setPedoMeterStatus("disable");
                    }

                    if (responseMessage.pedometerConfigurationInfo.timeInterval != null) {
                        patientDetails.setPedoMeterInterval(responseMessage.pedometerConfigurationInfo.timeInterval);
                    } else {
                        patientDetails.setPedoMeterStatus("60");
                    }
                } else {
                    patientDetails.setPedoMeterStatus("disable");
                    patientDetails.setPedoMeterStatus("60");
                }

                if (responseMessage.sleepData != null) {
                    patientDetails.setSleepStartTime(responseMessage.sleepData.sleepStartTime);
                    patientDetails.setSleepEndTime(responseMessage.sleepData.sleepEndTime);
                } else {
                    patientDetails.setSleepStartTime("21:30");
                    patientDetails.setSleepEndTime("7:30");
                }

                PatientDetails.addToDB(patientDetails);

                MedicationScheduleMaster.deleteAllRows();

                if (responseMessage.medicationDetail != null) {
                    LogUtils.debug("Going to add " + responseMessage.medicationDetail.size() + " medications in schedule master.");
                    for (MedicationDetail medicationDetail : responseMessage.medicationDetail) {

                        MedicationScheduleMaster scheduleMaster = new MedicationScheduleMaster();
                        scheduleMaster.setMedicineId(medicationDetail.medicineId);
                        scheduleMaster.setMedicineName(medicationDetail.medicineName);
                        scheduleMaster.setMedicineDosage(medicationDetail.dosage);
                        scheduleMaster.setMedicineStrength(medicationDetail.strength);
                        scheduleMaster.setMedicineReminderColor(medicationDetail.color);
                        scheduleMaster.setMedicineImage(getImageFromFlashMemory(medicationDetail.medicineId));
                        scheduleMaster.setMedicineForm(medicationDetail.medicineForm);
                        scheduleMaster.setBeforeOrAfterFood(medicationDetail.medtime_rel_food);
                        scheduleMaster.setTimeSlots(medicationDetail.timeSlots);
                        scheduleMaster.setDaysOfWeek(medicationDetail.daysOfWeek);
                        scheduleMaster.setQuantities(medicationDetail.quantities);
                        MedicationScheduleMaster.addToDB(scheduleMaster);

                        LogUtils.debug("Added a medication with details: [" + scheduleMaster.getMedicineName() + "; " + scheduleMaster.getTimeSlots() + "; " + scheduleMaster.getBeforeOrAfterFood() + "; " + scheduleMaster.getDaysOfWeek() + "]");
                    }
                }
                CustomerAlertsDB.deleteAllRows();
                if (responseMessage.customAlertsDetails != null) {

                    for (CustomAlertsDetails medicationDetail : responseMessage.customAlertsDetails) {
                        CustomerAlertsDB alertsDetails = new CustomerAlertsDB();

                        alertsDetails.setPatientId(medicationDetail.patientId);
                        alertsDetails.setAlterType(medicationDetail.alertType);
                        alertsDetails.setAlterTime(medicationDetail.alertTime);
                        alertsDetails.setAlterDetail(medicationDetail.alertDetail);
                        alertsDetails.setType(medicationDetail.type);
                        alertsDetails.setStartDate(medicationDetail.startDate);
                        alertsDetails.setEndDate(medicationDetail.endDate);
                        Long res = CustomerAlertsDB.addToDB(alertsDetails);
                        Log.e("SplashActivity ", " CustomAlertsDetails res : " + res);

                    }
                }
                ScheduleMessagesDB.deleteAllRows();
                if (responseMessage.scheduledTextMessageInfoList != null) {
                    for (ScheduleMessageVO scheduleMessage : responseMessage.scheduledTextMessageInfoList) {
                        ScheduleMessagesDB messagesDB = new ScheduleMessagesDB();
                        messagesDB.setQuestionId(scheduleMessage.scheduledTextMessagesId);
                        messagesDB.setQuestionName(scheduleMessage.question);
                        messagesDB.setAnswer(scheduleMessage.answer);
                        messagesDB.setSenderName(scheduleMessage.senderName);
                        messagesDB.setDayOfWeek(scheduleMessage.dayOfWeek);
                        messagesDB.setTimeSlots(scheduleMessage.timeSlots);
                        Long messageInserted = ScheduleMessagesDB.addToDB(messagesDB);
                        Log.e("SplashActivity ", " Scheduled Message Inserted To DB : " + messageInserted);
                    }
                }
                VitalConfiguration.deleteAllRows();
                if (responseMessage.vitalScheduleInfoList != null && responseMessage.vitalScheduleInfoList.size() > 0) {
                    for (VitalConfigVO configuration : responseMessage.vitalScheduleInfoList) {
                        VitalConfiguration vitalConfiguration = new VitalConfiguration();
                        vitalConfiguration.setVitalScheduleId(configuration.vitalScheduleId);
                        vitalConfiguration.setVitalTypeName(configuration.vitalTypeName);
                        vitalConfiguration.setCollectMode(configuration.collectMode);
                        vitalConfiguration.setScheduleDayOfWeek(configuration.scheduleDayOfWeek);
                        vitalConfiguration.setFrequency(configuration.frequency);
                        vitalConfiguration.setTimeSlots(configuration.timeSlots);
                        vitalConfiguration.setDeviceSerialId(configuration.deviceSerialId);
                        vitalConfiguration.setDeviceMeasures(configuration.deviceMeasures);
                        vitalConfiguration.setDeviceName(configuration.deviceName);
                        vitalConfiguration.setStartDate(configuration.startDate);
                        vitalConfiguration.setEndDate(configuration.endDate);
                        long messageInserted = VitalConfiguration.addToDB(vitalConfiguration);
                        Log.e("SplashActivity ", "Vital Added: " + messageInserted);
                    }
                }
                VitalStatusDetails.deleteAllRows();
                if (responseMessage.vitalTypeStatusList != null && responseMessage.vitalTypeStatusList.size() > 0) {
                    for (VitalConfigVO configuration : responseMessage.vitalTypeStatusList) {
                        VitalStatusDetails vitalStatusDetails = new VitalStatusDetails();
                        vitalStatusDetails.setVitalTypeName(configuration.vitalTypeName);
                        vitalStatusDetails.setVitalStatus(configuration.vitalStatus);
                        long messageInserted = VitalStatusDetails.addToDB(vitalStatusDetails);
                        Log.e("SplashActivity ", "Vital Status Added: " + messageInserted);
                    }
                }
                isRegistrationSuccessful = responseMessage.responseMessage;
            } else if (responseMessage != null && responseMessage.isImeNotFoundResponse()) {
                isRegistrationSuccessful = responseMessage.responseMessage;

            } else if (responseMessage != null && responseMessage.isImeNotAssignedResponse()) {
                isRegistrationSuccessful = responseMessage.responseMessage;
            }
        }
        return isRegistrationSuccessful;
    }

    public static byte[] getImageFromFlashMemory(String medicationId) {

        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/WatchRx_Images";
        File file = new File(rootPath, medicationId + ".png");
        byte[] image = new byte[0];
        if (file != null && file.exists()) {
            Bitmap bitmap = BitmapFactory.decodeFile(file.toString());

            if (bitmap != null) {
                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
                image = stream.toByteArray();
            }
            return image;
        } else {
            LogUtils.debug("Image not found in FLash memory for this medication Id " + medicationId + "we are using default image");
            Bitmap bitmap = BitmapFactory.decodeResource(WatchApp.getContext().getResources(), R.drawable.deafult_medicine);
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            return stream.toByteArray();
        }

    }

}
