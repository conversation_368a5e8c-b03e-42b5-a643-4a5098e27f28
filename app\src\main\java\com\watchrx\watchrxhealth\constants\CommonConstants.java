package com.watchrx.watchrxhealth.constants;

public class CommonConstants {
    public static final Long MAX_TRIGGER_TIME_DIFFERENCE = 120L * 60L * 1000; // 120 minutes
    public static final Long REMINDER_TIMEOUT = (15L * 60 * 1000); // 15 minutes
    public static final int MAX_REMINDER_REPETITION = 5; // repeat five times, if needed
    public static final Long REMINDER_TIMEOUT_FOR_FIXED = (150000L); // 2.5 minutes
    public static final Long REMINDER_TIMEOUT_FOR_30MIN = (30L * 60 * 1000);// 30 min
    public static final String RELEASE_DOWNLOAD_DIRECTORY = "/WatchRx_DataBase/releases/";
    public static final String BACK_UP_FILE_DIRECTORY = "/WatchRxAPK/";
    public static final String LOG_DIRECTORY = "/WatchRx_DebugFile/";

    public static final Long MAX_VITAL_TRIGGER_TIME_DIFFERENCE = 30 * 60L * 1000; // 5 min

    public static final int Max_Time_Out_For_Ping = 5000; // 5seconds to ping to server
}
