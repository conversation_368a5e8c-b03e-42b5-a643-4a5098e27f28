package com.watchrx.watchrxhealth.auth;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.SplashActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.EmailHelper;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONObject;

import java.net.URL;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EnterPasswordActivity extends AppCompatActivity {

    private EditText userNameET, passwordET;
    private Dialog loadingDialog;
    private Boolean isOtpValidated;

    public SharedPreferences sharedpreferences;

    public static final String mypreference = "WatchRx";
    public static final String userNameKey = "username";
    public static final String passwordKey = "password";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_enter_password);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.layout), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        Objects.requireNonNull(getSupportActionBar()).hide();
        sharedpreferences = WatchApp.getContext().getSharedPreferences(mypreference, Context.MODE_PRIVATE);
        setupLoadingDialog();
        userNameET = findViewById(R.id.userName);
        passwordET = findViewById(R.id.password);
        Button continueOrLoginBtn = findViewById(R.id.loginOrNexButton);
        isOtpValidated = getIntent().getBooleanExtra("isOtpValidated", false);
        String userName = getIntent().getStringExtra("userName");
        userNameET.setText(userName);

        userNameET.setEnabled(false);
        userNameET.setFocusable(false);
        userNameET.setClickable(false);

        if (isOtpValidated) {
            continueOrLoginBtn.setText("Login");
            passwordET.setHint("Enter password");
            findViewById(R.id.passwordHint).setVisibility(View.GONE);
        } else {
            findViewById(R.id.passwordHint).setVisibility(View.VISIBLE);
            continueOrLoginBtn.setText("Continue");
            passwordET.setHint("Enter new password");

            TextView criteriaUpper = findViewById(R.id.criteriaUppercase);
            TextView criteriaLower = findViewById(R.id.criteriaLowercase);
            TextView criteriaDigit = findViewById(R.id.criteriaDigit);
            TextView criteriaSpecial = findViewById(R.id.criteriaSpecial);
            TextView criteriaLength = findViewById(R.id.criteriaLength);
            TextView criteriaWhitespace = findViewById(R.id.criteriaWhitespace);

            passwordET.addTextChangedListener(new TextWatcher() {
                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    String password = s.toString();

                    updateCriteria(criteriaUpper, password.matches(".*[A-Z].*"), "At least 1 uppercase letter");
                    updateCriteria(criteriaLower, password.matches(".*[a-z].*"), "At least 1 lowercase letter");
                    updateCriteria(criteriaDigit, password.matches(".*[0-9].*"), "At least 1 digit");
                    updateCriteria(criteriaSpecial, password.matches(".*[@#$%^&+=!].*"), "At least 1 special character");
                    updateCriteria(criteriaLength, password.length() >= 8 && password.length() <= 20, "8–20 characters");
                    updateCriteria(criteriaWhitespace, !password.matches(".*\\s.*"), "No spaces allowed");
                }

                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                }

                @Override
                public void afterTextChanged(Editable s) {
                }
            });
        }


        continueOrLoginBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (isOtpValidated) {
                    String email = userNameET.getText() != null ? userNameET.getText().toString().trim() : null;
                    if (email == null || email.isEmpty() || (!EmailHelper.isValidEmail(email) && isValidPhoneNumber(email))) {
                        Toast.makeText(EnterPasswordActivity.this, "Enter valid email or phone number (with country code)", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    String password = passwordET.getText() != null ? passwordET.getText().toString().trim() : null;
                    if (password == null || password.isEmpty()) {
                        Toast.makeText(EnterPasswordActivity.this, "Enter valid password", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    loginMethod(email, password);
                } else {
                    String email = userNameET.getText() != null ? userNameET.getText().toString().trim() : null;
                    if (email == null || email.isEmpty() || (!EmailHelper.isValidEmail(email) && isValidPhoneNumber(email))) {
                        Toast.makeText(EnterPasswordActivity.this, "Enter valid email or phone number (with country code)", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    String password = passwordET.getText() != null ? passwordET.getText().toString().trim() : null;
                    if (password == null || password.isEmpty() || !isValidPassword(password)) {
                        Toast.makeText(EnterPasswordActivity.this, "Enter valid password", Toast.LENGTH_SHORT).show();
                        return;
                    }
                    resendOTPApiCall(email);
                }
            }
        });

        passwordET.setOnTouchListener((v, event) -> {
            final int DRAWABLE_RIGHT = 2; // Index for drawableEnd
            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (event.getRawX() >= (passwordET.getRight() - passwordET.getCompoundDrawables()[DRAWABLE_RIGHT].getBounds().width())) {
                    int selection = passwordET.getSelectionEnd();
                    if (passwordET.getInputType() == (InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD)) {
                        passwordET.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);
                        passwordET.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_eye, 0);
                    } else {
                        passwordET.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_PASSWORD);
                        passwordET.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.ic_eye_off, 0);
                    }
                    passwordET.setSelection(selection);
                    return true;
                }
            }
            return false;
        });

        TextView forgotPassword = findViewById(R.id.forgotPassword);
        forgotPassword.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(EnterPasswordActivity.this, ResendOTPScreen.class);
                startActivity(intent);
            }
        });
    }

    private void updateCriteria(TextView view, boolean isValid, String text) {
        if (isValid) {
            view.setText("✔ " + text);
            view.setTextColor(Color.parseColor("#00C853")); // Green
        } else {
            view.setText("✖ " + text);
            view.setTextColor(Color.parseColor("#D50000")); // Red
        }
    }

    private boolean isValidPassword(String password) {
        // Regex to check valid password:
        // - At least one digit [0-9]
        // - At least one lowercase letter [a-z]
        // - At least one uppercase letter [A-Z]
        // - At least one special character from [@#$%^&+=!]
        // - No whitespace
        // - Length between 8 to 20 characters
        String regex = "^(?=.*[0-9])" +           // at least 1 digit
                "(?=.*[a-z])" +            // at least 1 lowercase letter
                "(?=.*[A-Z])" +            // at least 1 uppercase letter
                "(?=.*[@#$%^&+=!])" +      // at least 1 special character
                "(?=\\S+$)" +              // no whitespace
                ".{8,20}$";                // between 8 to 20 characters

        if (password == null) {
            return false;
        }

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(password);
        return matcher.matches();
    }


    private boolean isValidPhoneNumber(String phone) {
        return !phone.matches("^\\+[1-9]\\d{9,14}$");
    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    private void loginMethod(String email, String password) {
        try {
            showLoadingDialog();
            closeKeyboard();
            LogUtils.debug("Going call AUTHENTICATE API with User Name:" + email + " Password: " + password);
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.AUTHENTICATE_URL);
            loginModel.username = email;
            loginModel.password = password;
            String json = new Gson().toJson(loginModel);
            SharedPreferences.Editor editor = sharedpreferences.edit();
            editor.putString(userNameKey, email);
            editor.putString(passwordKey, password);
            editor.apply();
            new RestAsyncTask(url, json, null, new AuthTokenResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class AuthTokenResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            LogUtils.debug("Received the response from AuthTokenResponseHandler");
            loadingDialog.dismiss();
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Toast.makeText(EnterPasswordActivity.this, "Failed to login", Toast.LENGTH_LONG).show();
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("Invalid Credentials.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterPasswordActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("OTP is not validate, Click resend OTP link below ")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("OTP is already validated. Invalid Credentials.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterPasswordActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Invalid user name or password")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").trim().equalsIgnoreCase("OTP not validated.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterPasswordActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("You have not validated your OTP yet, Please validate OTP along with set your new password.")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        Globals.authToken = jsonObject.optString("token");
                        Toast.makeText(EnterPasswordActivity.this, "Login successful", Toast.LENGTH_SHORT).show();
                        Intent intent = new Intent(EnterPasswordActivity.this, SplashActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                    }
                } catch (Exception ignored) {
                    Toast.makeText(EnterPasswordActivity.this, "Unable to login, Please try after sometime", Toast.LENGTH_LONG).show();
                }
            }
        }
    }

    private void setupLoadingDialog() {
        loadingDialog = new Dialog(this);
        loadingDialog.setContentView(R.layout.dialog_loading);
        loadingDialog.setCancelable(false);

        loadingDialog.setOnShowListener(dialog -> {
            ImageView loaderImage = loadingDialog.findViewById(R.id.loaderImage);
            Animation rotateAnimation = AnimationUtils.loadAnimation(this, R.anim.rotate);
            loaderImage.startAnimation(rotateAnimation);
        });
    }

    private void resendOTPApiCall(String emailId) {
        try {
            showLoadingDialog();
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.RESEND_OTP);
            loginModel.username = emailId.trim();
            String json = new Gson().toJson(loginModel);
            new RestAsyncTask(url, json, null, new ResendOTPResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showLoadingDialog() {
        if (loadingDialog == null) {
            setupLoadingDialog();
        }

        if (!loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private class ResendOTPResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            if (loadingDialog != null && !loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }

            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error :", "");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("200")
                            || jsonObject.optString("responseMessage").equalsIgnoreCase("Success")) {
                        Intent intent = new Intent(EnterPasswordActivity.this, EnterOTPActivity.class);
                        intent.putExtra("userName", userNameET.getText().toString().trim());
                        intent.putExtra("password", passwordET.getText().toString().trim());
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterPasswordActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Failed to send OTP to your registered Email or Phone. Please check with your case manager")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                                if (loadingDialog != null) {
                                                    loadingDialog.dismiss();
                                                }
                                                finish();
                                            }
                                        }).show();
                            }
                        });
                    }
                } catch (Exception ignored) {
                    ignored.printStackTrace();
                }
            }
        }
    }
}