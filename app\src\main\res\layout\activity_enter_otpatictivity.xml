<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="10dp"
    android:background="@drawable/gradient_background"
    android:padding="16dp"
    tools:context=".auth.EnterOTPActivity">

    <ImageView
        android:id="@+id/appLogo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_marginTop="32dp"
        android:src="@drawable/app_logo_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/otpDescription"
        android:layout_width="0dp"
        android:layout_height="25dp"
        android:layout_marginTop="25dp"
        android:fontFamily="@font/lato_regular"
        android:text="Enter the OTP sent to your email or phone"
        android:textAlignment="center"
        android:textColor="@android:color/black"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appLogo" />

    <com.goodiebag.pinview.Pinview
        android:id="@+id/otpET"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="25dp"
        android:focusable="true"
        app:cursorVisible="false"
        app:forceKeyboard="true"
        app:hint="0"
        app:inputType="number"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otpDescription"
        app:password="false"
        app:pinBackground="@drawable/background"
        app:pinHeight="35dp"
        app:pinLength="6"
        app:pinWidth="35dp" />

    <Button
        android:id="@+id/loginButton"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_gradient"
        android:fontFamily="@font/lato_regular"
        android:text="LOGIN"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otpET" />

</androidx.constraintlayout.widget.ConstraintLayout>
