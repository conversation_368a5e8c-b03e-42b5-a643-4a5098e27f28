package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class CustomerAlertsDB {

    public static final String TABLE_CUSTOM_ALTER_DETAILS = "CustomAlterDetails";

    private static final String COL_PATIENT_ID = "PatientId";
    private static final String COL_ALTER_TYPE = "AlertType";
    private static final String COL_ALTER_TIME = "AlertTime";
    private static final String COL_ALTER_DETAIL = "AlertDetail";
    private static final String COL_SCHEDULE_TYPE = "AlertScheduleType";
    private static final String COL_START_DATE = "AlertStartDate";
    private static final String COL_END_DATE = "AlertEndDate";

    static final String CREATE_CUSTOM_ALTER_DETAIL_TABLE =
            "CREATE TABLE " + TABLE_CUSTOM_ALTER_DETAILS +
                    "(" +
                    COL_PATIENT_ID + " TEXT," +
                    COL_ALTER_TYPE + " TEXT, " +
                    COL_ALTER_TIME + " TEXT ," +
                    COL_ALTER_DETAIL + " TEXT," +
                    COL_SCHEDULE_TYPE + " TEXT," +
                    COL_START_DATE + " TEXT," +
                    COL_END_DATE + " TEXT " +
                    ");";
    private String patientId;
    private String alterType;
    private String alterTime;
    private String alterDetail;
    private String type;
    private String startDate;
    private String endDate;


    private static final String[] COLUMNS_CUSTOM_ALERTS_DETAILS = {
            COL_PATIENT_ID,
            COL_ALTER_TYPE,
            COL_ALTER_TIME,
            COL_ALTER_DETAIL,
            COL_SCHEDULE_TYPE,
            COL_START_DATE,
            COL_END_DATE
    };

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }

    public String getAlterType() {
        return alterType;
    }

    public void setAlterType(String alterType) {
        this.alterType = alterType;
    }

    public String getAlterTime() {
        return alterTime;
    }

    public void setAlterTime(String alterTime) {
        this.alterTime = alterTime;
    }

    public String getAlterDetail() {
        return alterDetail;
    }

    public void setAlterDetail(String alterDetail) {
        this.alterDetail = alterDetail;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public static long addToDB(CustomerAlertsDB customerAlertsDB) {
        // force only one row in db
        ContentValues values = new ContentValues();

        values.put(COL_PATIENT_ID, customerAlertsDB.patientId);
        values.put(COL_ALTER_TYPE, customerAlertsDB.alterType);
        values.put(COL_ALTER_TIME, customerAlertsDB.alterTime);
        values.put(COL_ALTER_DETAIL, customerAlertsDB.alterDetail);
        values.put(COL_SCHEDULE_TYPE, customerAlertsDB.type);
        values.put(COL_START_DATE, customerAlertsDB.startDate);
        values.put(COL_END_DATE, customerAlertsDB.endDate);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_CUSTOM_ALTER_DETAILS, values);
    }


    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_CUSTOM_ALTER_DETAILS);
    }

    public static List<CustomerAlertsDB> getFromDB() {
        List<CustomerAlertsDB> customerAlertsDBS = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_CUSTOM_ALTER_DETAILS, COLUMNS_CUSTOM_ALERTS_DETAILS, null, null, null);
        while (cursor.moveToNext()) {
            CustomerAlertsDB alertsDB = new CustomerAlertsDB();
            try {
                alertsDB.setPatientId(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_ID)));
                alertsDB.setAlterType(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALTER_TYPE)));
                alertsDB.setAlterTime(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALTER_TIME)));
                alertsDB.setAlterDetail(cursor.getString(cursor.getColumnIndexOrThrow(COL_ALTER_DETAIL)));
                alertsDB.setType(cursor.getString(cursor.getColumnIndexOrThrow(COL_SCHEDULE_TYPE)));
                alertsDB.setStartDate(cursor.getString(cursor.getColumnIndexOrThrow(COL_START_DATE)));
                alertsDB.setEndDate(cursor.getString(cursor.getColumnIndexOrThrow(COL_END_DATE)));
                customerAlertsDBS.add(alertsDB);
            } catch (Exception e) {
                Log.e("Custom Alert", "" + e);
            }
        }
        cursor.close();
        return customerAlertsDBS;
    }
}
