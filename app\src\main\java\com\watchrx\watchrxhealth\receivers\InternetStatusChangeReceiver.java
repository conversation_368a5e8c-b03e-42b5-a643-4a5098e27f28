package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.SplashActivity;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.utils.LogUtils;

public class InternetStatusChangeReceiver extends BroadcastReceiver {
    private static long alertLastRecv = 0;

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getExtras() != null) {

            final ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
            final NetworkInfo ni = connectivityManager.getActiveNetworkInfo();
            if ((ni != null) && (ni.isConnected())) {
                if (((System.currentTimeMillis() - alertLastRecv) >= 5 * 1000)) {
                    String networkType = ni.getTypeName();
                    LogUtils.debug("While checking,Internet is connected to ===>" + networkType);
                    if (Globals.isWatchRegistered) {
                        LogUtils.debug("Network change detected, Checking for any data need to be sent to server. isWatchRegistered:-" + Globals.isWatchRegistered);
                        Intent serverCommRequestedIntent = new Intent(MainActivity.SERVER_COMM_INITIATED_INDICATOR_INTENT_FILTER);
                        LocalBroadcastManager.getInstance(context).sendBroadcast(serverCommRequestedIntent);
                    } else {
                        LogUtils.debug("Network change detected, Checking for Watch is Registered or Not. isWatchRegistered:-" + Globals.isWatchRegistered);
                        Intent internetConnectedIntent = new Intent(SplashActivity.INTERNET_CONNECTED_INDICATOR_INTENT_FILTER);
                        LocalBroadcastManager.getInstance(context).sendBroadcast(internetConnectedIntent);
                    }
                    alertLastRecv = System.currentTimeMillis();
                } else {
                    LogUtils.debug("Multiple BroadcastReceiver events restricted,Bcz less than 5 second same event happens so.  ");
                }
            } else {
                LogUtils.debug("######  Network connection lost...#isWatchRegistered:" + Globals.isWatchRegistered);
            }
        }
    }

    @NonNull
    private static Boolean isAccessible(NetworkInfo networkInfo) {
        try {
            String networkType = networkInfo.getTypeName();
            LogUtils.debug("While checking,Internet is connected to ===>" + networkType);
            Process p1 = java.lang.Runtime.getRuntime().exec("ping -c 1 www.watchrx-1007.appspot.com");
            int returnVal = p1.waitFor();

            return (returnVal == 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
