<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="10dp"
    android:background="@drawable/gradient_background"
    android:padding="16dp"
    tools:context=".auth.LoginScreen">

    <ImageView
        android:id="@+id/appLogo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_marginTop="32dp"
        android:src="@drawable/app_logo_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/userName"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_edittext"
        android:hint="@string/email_id"
        android:imeOptions="actionNext"
        android:inputType="text"
        android:paddingStart="16dp"
        android:textColor="@color/colorText"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appLogo" />


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/loginButton"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_gradient"
        android:fontFamily="@font/lato_regular"
        android:text="SEND OTP"
        android:textColor="@color/white"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/userName" />

    <TextView
        android:id="@+id/loginBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/lato_black"
        android:text="Go back to login?"
        android:textColor="@color/dark_blue"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginButton" />

    <TextView
        android:id="@+id/privacyPolicy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:fontFamily="@font/lato_black"
        android:text="@string/privacy_policy"
        android:textColor="@color/colorPrimary"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
