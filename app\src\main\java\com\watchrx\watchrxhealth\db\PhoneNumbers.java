package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class PhoneNumbers {
    public static final String TABLE_PHONE_NUMBERS_DETAILS = "PhoneNumbers";
    private static final String COL_PATIENT_ID = "PatientId";
    private static final String COL_CONTACT_NAME = "ContactName";
    private static final String COL_PHONE_NUMBER = "PhoneNumber";

    static final String DELETE_TABLE_PHONE_NUMBERS_DETAILS_TABLE =
            "DROP TABLE IF EXISTS " + TABLE_PHONE_NUMBERS_DETAILS + ";";

    static final String CREATE_PHONE_NUMBERS_DETAILS_TABLE =
            "CREATE TABLE " + TABLE_PHONE_NUMBERS_DETAILS +
                    "(" +
                    COL_PATIENT_ID + " TEXT, " +
                    COL_CONTACT_NAME + " TEXT, " +
                    COL_PHONE_NUMBER + " TEXT " +
                    ");";

    private static final String[] COLUMNS_PHONE_NUMBER_DETAILS = {
            COL_PATIENT_ID,
            COL_CONTACT_NAME,
            COL_PHONE_NUMBER
    };

    private String patientId;
    private String contactName;
    private String phonenumber;

    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_PHONE_NUMBERS_DETAILS);
    }

    public static long addToDB(PhoneNumbers phoneNumbers) {
        //PhoneNumbers.deleteAllRows(); // force only one row in db

        ContentValues values = new ContentValues();

        values.put(COL_PATIENT_ID, phoneNumbers.patientId);
        values.put(COL_CONTACT_NAME, phoneNumbers.contactName);
        values.put(COL_PHONE_NUMBER, phoneNumbers.phonenumber);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_PHONE_NUMBERS_DETAILS, values);
    }

    public static List<PhoneNumbers> getFromDB() {
        List<PhoneNumbers> matchingRecords = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_PHONE_NUMBERS_DETAILS, COLUMNS_PHONE_NUMBER_DETAILS, null, null, null);
        while (cursor.moveToNext()) {
            PhoneNumbers record = new PhoneNumbers();

            try {
                record.setPatientId(cursor.getString(cursor.getColumnIndexOrThrow(COL_PATIENT_ID)));
                record.setContactName(cursor.getString(cursor.getColumnIndexOrThrow(COL_CONTACT_NAME)));
                record.setPhonenumber(cursor.getString(cursor.getColumnIndexOrThrow(COL_PHONE_NUMBER)));
                matchingRecords.add(record);
            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }

        cursor.close();

        return matchingRecords;
    }

    public static List<PhoneNumbers> getPhoneNumberFromDB() {
        List<PhoneNumbers> matchingRecords = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_PHONE_NUMBERS_DETAILS, COLUMNS_PHONE_NUMBER_DETAILS, null, null, null);
        while (cursor.moveToNext()) {
            PhoneNumbers record = new PhoneNumbers();

            try {
                record.setPhonenumber(cursor.getString(cursor.getColumnIndexOrThrow(COL_PHONE_NUMBER)));
                record.setContactName(cursor.getString(cursor.getColumnIndexOrThrow(COL_CONTACT_NAME)));
                matchingRecords.add(record);
            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();

        return matchingRecords;
    }

    public static PhoneNumbers getNumberFromDB(String contactName) {

        String whereClause = COL_CONTACT_NAME + " LIKE \"%" + contactName + "%\"";

        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_PHONE_NUMBERS_DETAILS, COLUMNS_PHONE_NUMBER_DETAILS, whereClause, null, null);
        PhoneNumbers record = new PhoneNumbers();
        while (cursor.moveToNext()) {
            try {
                record.setPhonenumber(cursor.getString(cursor.getColumnIndexOrThrow(COL_PHONE_NUMBER)));

            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();
        return record;
    }

    public static PhoneNumbers getNameFromDB(String contactNumber) {

        String whereClause = COL_PHONE_NUMBER + " LIKE \"%" + contactNumber + "%\"";

        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_PHONE_NUMBERS_DETAILS, COLUMNS_PHONE_NUMBER_DETAILS, whereClause, null, null);
        PhoneNumbers record = new PhoneNumbers();
        while (cursor.moveToNext()) {
            try {
                record.setContactName(cursor.getString(cursor.getColumnIndexOrThrow(COL_CONTACT_NAME)));

            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();
        return record;
    }


    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getPatientId() {
        return patientId;
    }

    public void setPatientId(String patientId) {
        this.patientId = patientId;
    }


}
