<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="d_3_d">3dp</dimen>
    <dimen name="d_15_d">15dp</dimen>
    <dimen name="d_25_d">25dp</dimen>
    <dimen name="d_30_d">30dp</dimen>
    <dimen name="d_50_d">50dp</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="d_30_s">30sp</dimen>
    <dimen name="d_10_s">15sp</dimen>
    <dimen name="d_25_s">25sp</dimen>
    <dimen name="d_20_s">20sp</dimen>
    <dimen name="d_20_d">20dp</dimen>
    <dimen name="d_10_d">10dp</dimen>
    <dimen name="d_5_d">5dp</dimen>
    <dimen name="d_35_s">35sp</dimen>
    <dimen name="d_12_d">12dp</dimen>
    <dimen name="time">60sp</dimen>
    <dimen name="date">40sp</dimen>
    <dimen name="message">35sp</dimen>
    <dimen name="d_7_d">10dp</dimen>
    <dimen name="dashboard_vital_text">20sp</dimen>
    <dimen name="message_text">16sp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="card_view_padding">4dp</dimen>
    <dimen name="padding_medium">8dp</dimen>
    <dimen name="padding_normal">5dp</dimen>
    <dimen name="padding_average">12dp</dimen>
    <dimen name="margin_normal">10dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="item_offset">4dp</dimen>
    <dimen name="padding_empty_bottom">16dp</dimen>
    <dimen name="app_bar_height">180dp</dimen>
    <dimen name="text_margin">16dp</dimen>
</resources>
