package com.watchrx.watchrxhealth.utils;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.InsetDrawable;

import androidx.core.content.ContextCompat;

import com.watchrx.watchrxhealth.R;

public final class DrawableUtils {

    public static Drawable getThreeDots(Context context) {
        Drawable drawable = ContextCompat.getDrawable(context, R.drawable.sample_three_icons);
        return new InsetDrawable(drawable, 100, 0, 100, 0);
    }
}