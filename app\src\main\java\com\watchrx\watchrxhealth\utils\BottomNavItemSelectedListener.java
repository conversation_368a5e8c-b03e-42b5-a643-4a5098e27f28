package com.watchrx.watchrxhealth.utils;

import android.content.Context;
import android.content.Intent;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.android.material.navigation.NavigationBarView;
import com.watchrx.watchrxhealth.AlertsActivity;
import com.watchrx.watchrxhealth.InteractiveVoiceActivity;
import com.watchrx.watchrxhealth.PhoneCallsActivity;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.db.Alerts;

import java.util.List;

public class BottomNavItemSelectedListener implements NavigationBarView.OnItemSelectedListener {

    private final Context context;

    public BottomNavItemSelectedListener(Context context) {
        this.context = context;
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.nav_sound) {
            Intent intent = new Intent(context, InteractiveVoiceActivity.class);
            context.startActivity(intent);
        } else if (id == R.id.nav_alerts) {
            List<Alerts> alertCount = Alerts.getFromDB();
            LogUtils.debug("Onclick event detected on Alert button, Number of alerts to show= " + alertCount.size());
            if (alertCount.isEmpty()) {
                Toast.makeText(context, "No Alerts", Toast.LENGTH_LONG).show();
            } else {
                Intent intent = new Intent(context, AlertsActivity.class);
                context.startActivity(intent);
            }
        } else if (id == R.id.nav_contacts) {
            LogUtils.debug("onCLick event detected on Phone Calls button");
            Intent intent = new Intent(context, PhoneCallsActivity.class);
            context.startActivity(intent);
        }
        return true;
    }
}
