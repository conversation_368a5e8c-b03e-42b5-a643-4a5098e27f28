package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.LinkedList;
import java.util.List;

public class ServerQueue {

    public static final String TABLE_PUSH_TO_SERVER_QUEUE = "ServerQueue";
    static final String DELETE_TABLE_PUSH_TO_SERVER_QUEUE =
            "DROP TABLE IF EXISTS " + TABLE_PUSH_TO_SERVER_QUEUE + ";";
    private static final String COL_URL = "url";
    private static final String COL_MSG = "msg";
    private static final String COL_ROWID = "_id";
    private static final String COL_STATE = "state";
    static final String CREATE_TABLE_PUSH_TO_SERVER_QUEUE =
            "CREATE TABLE " + TABLE_PUSH_TO_SERVER_QUEUE +
                    "(" +
                    COL_ROWID + " integer primary key autoincrement," +
                    COL_MSG + " TEXT, " +
                    COL_URL + " TEXT, " +
                    COL_STATE + " TEXT" +

                    ");";
    private long _id;
    private String msg;
    private String url;
    private String state;


    public static void deleteAllRows() {
        // Delete any rows already existing
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_PUSH_TO_SERVER_QUEUE);
    }

    public static void deleteRow(long rowId) {
        String whereClause = "ROWID = " + rowId;
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_PUSH_TO_SERVER_QUEUE, whereClause, null);
    }

    public static long addToDB(String json, String url) {
        ContentValues values = new ContentValues();
        values.put(COL_MSG, json);
        values.put(COL_URL, url);
        values.put(COL_STATE, "NOT_SENT");
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_PUSH_TO_SERVER_QUEUE, values);
    }

    public static void updateState(long rowId, String state) {
        String sql = "UPDATE " + TABLE_PUSH_TO_SERVER_QUEUE + " set " + COL_STATE + "='" + state + "' where " + COL_ROWID + "=" + rowId;
        DBAdaptor.getDbAdaptorInstance().execSQL(sql);
    }

    public static List<ServerQueue> getFromDB() {
        List<ServerQueue> recordList = new LinkedList<>();

        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_PUSH_TO_SERVER_QUEUE, null, null, null, null);

        try {
            while (cursor.moveToNext()) {
                ServerQueue record = new ServerQueue();
                record.setMsg(cursor.getString(cursor.getColumnIndexOrThrow(COL_MSG)));
                record.setUrl(cursor.getString(cursor.getColumnIndexOrThrow(COL_URL)));
                record.setRowId(cursor.getLong(cursor.getColumnIndex(COL_ROWID)));
                record.setState(cursor.getString(cursor.getColumnIndex(COL_STATE)));
                recordList.add(record);
            }

            cursor.close();
        } catch (Exception e) {
            Log.e("serverQueue", "" + e);
            LogUtils.debug("i got exception serverQueue ->" + e.getMessage());
        }

        return recordList;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getRowId() {
        return _id;
    }

    public void setRowId(long rowId) {
        this._id = rowId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
