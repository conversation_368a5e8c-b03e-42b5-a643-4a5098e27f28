package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;

public class ScheduleMessageReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d("ScheduleMessageReceiver", "Reminder alarm to be triggered at: '" + intent.getLongExtra("triggerAt", -1) +
                "  time was received at: '" +
                System.currentTimeMillis() + "' for: '" + intent.getStringExtra("day") + "-" + intent.getStringExtra("time")
                + "-" + intent.getStringExtra("questionId") + "'");
        int isPresentInDB = ScheduleMessagesDB.getEntryFromDB(intent.getStringExtra("questionId"), intent.getStringExtra("time"));
        if (isPresentInDB == 0) {
            return;
        }
        Globals.intentMap.remove(intent.getStringExtra("day") + "-" + intent.getStringExtra("time") + intent.getStringExtra("questionId"));
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setTimeSlot(intent.getStringExtra("time"));
        info.setBeforeOrAfterFood(intent.getStringExtra("day"));
        info.setContext(context);
        info.setAlertId(intent.getStringExtra("questionId"));
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("ScheduleTextMessageActivity");
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        }else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
        }
    }
}
