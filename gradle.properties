## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
#Sat Mar 15 13:50:47 IST 2025
MY_KEYSTORE_FILE=app/keystore/mobileHealthApp.jks
MY_KEYSTORE_PASSWORD=Watch@123
MY_KEY_ALIAS=upload
MY_KEY_PASSWORD=Watch@123
android.enableJetifier=true
android.enableUnitTestBinaryResources=false
android.useAndroidX=true
org.gradle.jvmargs=-Xmx2048M -Dkotlin.daemon.jvm.options\="-Xmx2048M"
