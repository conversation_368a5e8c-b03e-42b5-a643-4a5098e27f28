package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class MedicationScheduleMaster {

    public static final String TABLE_MEDICATION_SCHEDULE_MASTER = "MedicationScheduleMaster";
    public static final String DELETE_MEDSCHED_MASTER_TABLE =
            "DROP TABLE IF EXISTS " + TABLE_MEDICATION_SCHEDULE_MASTER + ";";
    private static final String COL_MEDSCHED_M_MEDICINE_NAME = "MedicineName";
    private static final String COL_MEDSCHED_M_MEDICINE_ID = "MedicineID";
    private static final String COL_MEDSCHED_M_MEDICINE_DOSAGE = "MedicineDosage";
    private static final String COL_MEDSCHED_M_MEDICINE_REMINDER_COLOR = "ReminderColor";
    private static final String COL_MEDSCHED_M_MEDICINE_STRENGTH = "MedicineStrength";
    private static final String COL_MEDSCHED_M_MEDICINE_IMAGE = "MedicineImage";
    private static final String COL_MEDSCHED_M_FOOD = "Food";
    private static final String COL_MEDSCHED_M_TIMESLOTS = "TimeSlots";
    private static final String COL_MEDSCHED_M_DAYS_OF_WEEK = "DaysOfWeek";
    private static final String COL_MEDSCHED_MEDICINE_FORM = "medicineForm";
    private static final String COL_MEDSCHED_QUANTITIES = "Quantities";

    public static final String CREATE_MEDSCHED_MASTER_TABLE =
            "CREATE TABLE " + TABLE_MEDICATION_SCHEDULE_MASTER +
                    "(" +
                    COL_MEDSCHED_M_MEDICINE_NAME + " TEXT not null, " +
                    COL_MEDSCHED_M_MEDICINE_ID + " TEXT not null, " +
                    COL_MEDSCHED_M_MEDICINE_DOSAGE + " TEXT not null, " +
                    COL_MEDSCHED_M_MEDICINE_STRENGTH + " TEXT not null, " +
                    COL_MEDSCHED_M_MEDICINE_REMINDER_COLOR + " TEXT not null, " +
                    COL_MEDSCHED_M_MEDICINE_IMAGE + " BLOB, " +
                    COL_MEDSCHED_M_FOOD + " TEXT not null, " +
                    COL_MEDSCHED_M_TIMESLOTS + " TEXT not null, " +
                    COL_MEDSCHED_M_DAYS_OF_WEEK + " TEXT not null," +
                    COL_MEDSCHED_QUANTITIES + " TEXT not null," +
                    COL_MEDSCHED_MEDICINE_FORM + " TEXT not null" +
                    ");";

    public static final String[] COLUMNS_MEDSCHED_MASTER = {
            COL_MEDSCHED_M_MEDICINE_NAME,
            COL_MEDSCHED_M_MEDICINE_ID,
            COL_MEDSCHED_M_MEDICINE_DOSAGE,
            COL_MEDSCHED_M_MEDICINE_STRENGTH,
            COL_MEDSCHED_M_MEDICINE_REMINDER_COLOR,
            COL_MEDSCHED_M_MEDICINE_IMAGE,
            COL_MEDSCHED_M_FOOD,
            COL_MEDSCHED_M_TIMESLOTS,
            COL_MEDSCHED_M_DAYS_OF_WEEK,
            COL_MEDSCHED_MEDICINE_FORM,
            COL_MEDSCHED_QUANTITIES
    };

    private String medicineName;
    private String medicineId;
    private String medicineDosage;
    private String medicineStrength;
    private String medicineReminderColor;
    private byte[] medicineImage;
    private String beforeOrAfterFood;
    private String timeSlots;
    private String daysOfWeek;
    private String medicineForm;
    private String quantities;


    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_MEDICATION_SCHEDULE_MASTER);
    }

    public static long addToDB(MedicationScheduleMaster record) {
        ContentValues values = new ContentValues();

        values.put(COL_MEDSCHED_M_MEDICINE_ID, record.medicineId);
        values.put(COL_MEDSCHED_M_MEDICINE_NAME, record.medicineName);
        values.put(COL_MEDSCHED_M_MEDICINE_DOSAGE, record.medicineDosage);
        values.put(COL_MEDSCHED_M_MEDICINE_STRENGTH, record.medicineStrength);
        values.put(COL_MEDSCHED_M_MEDICINE_REMINDER_COLOR, record.medicineReminderColor);
        values.put(COL_MEDSCHED_M_MEDICINE_IMAGE, record.medicineImage);
        values.put(COL_MEDSCHED_M_FOOD, record.beforeOrAfterFood);
        values.put(COL_MEDSCHED_M_TIMESLOTS, record.timeSlots);
        values.put(COL_MEDSCHED_M_DAYS_OF_WEEK, record.daysOfWeek);
        values.put(COL_MEDSCHED_MEDICINE_FORM, record.medicineForm);
        values.put(COL_MEDSCHED_QUANTITIES, record.quantities);


        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_MEDICATION_SCHEDULE_MASTER, values);
    }

    public static List<MedicationScheduleMaster> getFromDB() {
        List<MedicationScheduleMaster> medicationDetails = new ArrayList<>();

        //Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_MEDICATION_SCHEDULE_MASTER, COLUMNS_MEDSCHED_MASTER, null, null, null);
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet("select * from MedicationScheduleMaster");

        while (cursor.moveToNext()) {
            try {
                MedicationScheduleMaster medicationDetail = new MedicationScheduleMaster();

                medicationDetail.medicineId = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_ID)));
                medicationDetail.medicineName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_NAME)));
                medicationDetail.medicineImage = (cursor.getBlob(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_IMAGE)));
                medicationDetail.medicineReminderColor = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_REMINDER_COLOR)));

                medicationDetail.medicineDosage = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_DOSAGE)));
                medicationDetail.medicineStrength = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_STRENGTH)));

                medicationDetail.daysOfWeek = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_DAYS_OF_WEEK)));
                medicationDetail.timeSlots = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_TIMESLOTS)));
                medicationDetail.beforeOrAfterFood = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_FOOD)));
                medicationDetail.medicineForm = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_MEDICINE_FORM)));
                medicationDetail.quantities = (cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_QUANTITIES)));

                medicationDetails.add(medicationDetail);
            } catch (Exception e) {
                Log.e("MedicationDetails", "" + e);
            }
        }

        cursor.close();

        return medicationDetails;
    }

    public static MedicationScheduleMaster getForMedicineId(String medicineID) {
        String whereClause = COL_MEDSCHED_M_MEDICINE_ID + " = \"" + medicineID + "\"";
        MedicationScheduleMaster record = new MedicationScheduleMaster();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_MEDICATION_SCHEDULE_MASTER, COLUMNS_MEDSCHED_MASTER, whereClause, null, null);
        if (cursor.getCount() == 1) {
            cursor.moveToNext();
            record.setMedicineId(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_ID)));
            record.setMedicineName(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_NAME)));
            record.setMedicineDosage(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_DOSAGE)));
            record.setMedicineStrength(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_STRENGTH)));
            record.setMedicineReminderColor(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_REMINDER_COLOR)));
            record.setMedicineImage(cursor.getBlob(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_MEDICINE_IMAGE)));
            record.setBeforeOrAfterFood(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_FOOD)));
            record.setDaysOfWeek(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_DAYS_OF_WEEK)));
            record.setTimeSlots(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_M_TIMESLOTS)));
            record.setMedicineForm(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_MEDICINE_FORM)));
            record.setQuantities(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_QUANTITIES)));
        }
        return record;
    }

    public String getMedicineName() {
        return medicineName;
    }

    public void setMedicineName(String medicineName) {
        this.medicineName = medicineName;
    }

    public String getMedicineId() {
        return medicineId;
    }

    public void setMedicineId(String medicineId) {
        this.medicineId = medicineId;
    }

    public String getMedicineDosage() {
        return medicineDosage;
    }

    public void setMedicineDosage(String medicineDosage) {
        this.medicineDosage = medicineDosage;
    }

    public String getMedicineStrength() {
        return medicineStrength;
    }

    public void setMedicineStrength(String medicineStrength) {
        this.medicineStrength = medicineStrength;
    }

    public String getMedicineReminderColor() {
        return medicineReminderColor;
    }

    public void setMedicineReminderColor(String medicineReminderColor) {
        this.medicineReminderColor = medicineReminderColor;
    }

    public byte[] getMedicineImage() {
        return medicineImage;
    }

    public void setMedicineImage(byte[] medicineImage) {
        this.medicineImage = medicineImage;
    }

    public String getBeforeOrAfterFood() {
        return beforeOrAfterFood;
    }

    public void setBeforeOrAfterFood(String beforeOrAfterFood) {
        this.beforeOrAfterFood = beforeOrAfterFood;
    }

    public String getTimeSlots() {
        return timeSlots;
    }

    public void setTimeSlots(String timeSlots) {
        this.timeSlots = timeSlots;
    }

    public String getDaysOfWeek() {
        return daysOfWeek;
    }

    public String getMedicineForm() {
        return medicineForm;
    }

    public void setMedicineForm(String medicineForm) {
        this.medicineForm = medicineForm;
    }

    public void setDaysOfWeek(String daysOfWeek) {
        this.daysOfWeek = daysOfWeek;
    }

    public void setMedicineImage(String image) {

    }

    public String getQuantities() {
        return quantities;
    }

    public void setQuantities(String quantities) {
        this.quantities = quantities;
    }
}
