<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    android:elevation="10dp"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="20dp">

    <ImageView
        android:id="@+id/loaderImage"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:src="@drawable/custome_loader" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:text="Please wait..."
        android:textColor="@android:color/black"
        android:textSize="16sp" />
</LinearLayout>
