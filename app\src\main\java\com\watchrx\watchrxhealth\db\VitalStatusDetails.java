package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;

import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.ArrayList;
import java.util.List;

public class VitalStatusDetails {

    public static final String TABLE_VITAL_STATUS_DETAILS = "VitalStatusDetails";
    private static final String COL_VITAL_TYPE_NAME = "VitalTypeName";
    private static final String COL_VITAL_STATUS = "VitalStatus";

    private String vitalTypeName;
    private String vitalStatus;

    static final String DELETE_TABLE_VITAL_STATUS_DETAILS = "DROP TABLE IF EXISTS " + TABLE_VITAL_STATUS_DETAILS + ";";

    static final String CREATE_TABLE_STATUS_DETAILS = "CREATE TABLE " + TABLE_VITAL_STATUS_DETAILS +
            "(" +
            COL_VITAL_TYPE_NAME + " TEXT, " +
            COL_VITAL_STATUS + " TEXT ," +
            "UNIQUE(" + COL_VITAL_TYPE_NAME + ")" +
            ");";
    private static final String[] COLUMNS_TABLE_STATUS_DETAILS = {
            COL_VITAL_TYPE_NAME,
            COL_VITAL_STATUS
    };

    public String getVitalTypeName() {
        return vitalTypeName;
    }

    public void setVitalTypeName(String vitalTypeName) {
        this.vitalTypeName = vitalTypeName;
    }

    public String getVitalStatus() {
        return vitalStatus;
    }

    public void setVitalStatus(String vitalStatus) {
        this.vitalStatus = vitalStatus;
    }

    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_VITAL_STATUS_DETAILS);
    }

    public static long addToDB(VitalStatusDetails vitalStatusDetails) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COL_VITAL_TYPE_NAME, vitalStatusDetails.vitalTypeName);
        contentValues.put(COL_VITAL_STATUS, vitalStatusDetails.vitalStatus);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_VITAL_STATUS_DETAILS, contentValues);
    }

    public static List<VitalStatusDetails> getFromDB() {
        List<VitalStatusDetails> configurationList = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_VITAL_STATUS_DETAILS, COLUMNS_TABLE_STATUS_DETAILS, null, null, null);
        while (cursor.moveToNext()) {
            try {
                VitalStatusDetails vitalStatusDetails = new VitalStatusDetails();
                vitalStatusDetails.vitalTypeName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_TYPE_NAME)));
                vitalStatusDetails.vitalStatus = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_STATUS)));
                configurationList.add(vitalStatusDetails);
            } catch (Exception e) {
                LogUtils.debug("Vital Details Exception While Fetching" + e.getMessage());
            }
        }
        cursor.close();
        return configurationList;
    }

    public static VitalStatusDetails getVitalStatusByVitalName(String vitalTypeName) {
        VitalStatusDetails vitalStatusDetails = new VitalStatusDetails();
        String whereClause = COL_VITAL_TYPE_NAME + " = \"" + vitalTypeName + "\"";
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_VITAL_STATUS_DETAILS, COLUMNS_TABLE_STATUS_DETAILS, whereClause, null, null);
        if (cursor.getCount() == 1) {
            try {
                cursor.moveToNext();
                vitalStatusDetails.vitalTypeName = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_TYPE_NAME)));
                vitalStatusDetails.vitalStatus = (cursor.getString(cursor.getColumnIndexOrThrow(COL_VITAL_STATUS)));
                cursor.close();
                return vitalStatusDetails;
            } catch (Exception e) {
                LogUtils.debug("Vital Details Exception While Fetching" + e.getMessage());
            }
        }
        return null;
    }

    public static long updateVitalStatusToDB(VitalStatusDetails vitalStatusDetails) {
        ContentValues contentValues = new ContentValues();
        contentValues.put(COL_VITAL_STATUS, vitalStatusDetails.vitalStatus);
        return DBAdaptor.getDbAdaptorInstance().update(TABLE_VITAL_STATUS_DETAILS, contentValues, COL_VITAL_TYPE_NAME + " = ?", new String[]{vitalStatusDetails.vitalTypeName});
    }
}
