//package com.watchrx.watchrxhealth.receivers;
//
//import android.content.BroadcastReceiver;
//import android.content.Context;
//import android.content.Intent;
//import android.os.BatteryManager;
//
//import com.watchrx.watchrxhealth.WatchApp;
//import com.watchrx.watchrxhealth.db.PatientDetails;
//import com.watchrx.watchrxhealth.gcm.NotificationHelper;
//import com.watchrx.watchrxhealth.globals.Globals;
//import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
//import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
//import com.watchrx.watchrxhealth.utils.CommUtils;
//import com.watchrx.watchrxhealth.utils.GeneralUtils;
//import com.watchrx.watchrxhealth.utils.LogUtils;
//
//import java.util.Calendar;
//
//public class BatteryLevelReceiver extends BroadcastReceiver {
//
//    private Integer lastLevelAt = 0;
//
//    private static int alertCount = 0;
//    private static long alertLastSentAt = 0;
//
//    @Override
//    public void onReceive(Context context, Intent intent) {
//
//        Calendar cal = Calendar.getInstance();
//        cal.setTimeInMillis(System.currentTimeMillis());
//
//        int currentLevel = intent.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
//        int scale = intent.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
//        int level = -1;
//
//        int status = intent.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
//
//        boolean isCharging = (status == BatteryManager.BATTERY_STATUS_CHARGING);
//
//        int chargePlug = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
//        boolean usbCharge = chargePlug == BatteryManager.BATTERY_PLUGGED_USB;
//        boolean acCharge = chargePlug == BatteryManager.BATTERY_PLUGGED_AC;
//
//        if (currentLevel >= 0 && scale > 0) {
//            level = (currentLevel * 100) / scale;
//        }
//        Calendar timeNow = Calendar.getInstance();
//        timeNow.setTimeInMillis(System.currentTimeMillis());
//
//        boolean shouldSpeak = false;
//        if (timeNow.get(Calendar.HOUR_OF_DAY) >= 6 && timeNow.get(Calendar.HOUR_OF_DAY) <= 22) {
//            shouldSpeak = true;
//        }
//
//
//        if (!isCharging) {
//            synchronized (lastLevelAt) {
//
//                if (lastLevelAt != level) {
//                    lastLevelAt = level;
//
//                    if (level == 19 || level == 15 || level == 10 || level == 5 || level == 1) {
//                        if (shouldSpeak) {
//                            if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
//                                GeneralUtils.speak("My battery charge is running low  Please connect me to a charger soon.");
//                            } else {
//                                GeneralUtils.speak("La carga de mi batería se está agotando. Por favor, conéctame a un cargador pronto");
//                            }
//
//                        }
//                        LogUtils.debug("sending battery alert is " + lastLevelAt);
//                        updateBatteryScreen(context, "BatteryLow");
//                        CommUtils.sendBatteryLogToServer(context, "Battery Low", "Battery is too low " + level);
//                    }
//                }
//            }
//        }
//
//        //Initialising aletCount to 0, when not charging, else we would repeatedly get final reminder when charger connected and bat  100%
//        if (!(usbCharge || acCharge)) {
//            alertCount = 0;
//        }
//
//        if ((level == 100) && (usbCharge || acCharge)) {
//            if (alertCount == 4 && ((System.currentTimeMillis() - alertLastSentAt) >= 150 * 1000)) {
//                if (shouldSpeak) {
//                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
//                        GeneralUtils.speak("My battery is fully charged now at " + level + " percent." +
//                                " You havent taken me out of charger and worn the watch!");
//                    } else {
//                        GeneralUtils.speak("Mi batería está completamente cargada a " + level + "porciento. me puedes sacar del cargador y usarme!");
//                    }
//                }
//                updateBatteryScreen(context, "BatteryFull");
//                CommUtils.sendBatteryLogToServer(context, "Battery Full", " Watch not worn by patient after 4 reminders");
//                alertCount++;
//                alertLastSentAt = System.currentTimeMillis();
//            }
//
//            if (alertCount < 4 && ((System.currentTimeMillis() - alertLastSentAt) >= 150 * 1000)) {
//                if (shouldSpeak) {
//                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
//                        GeneralUtils.speak("My battery is fully charged now at " + level + " percent. You can take me out of the charger, and wear me!");
//                    } else {
//                        GeneralUtils.speak("Mi batería está completamente cargada a " + level + "porciento. No me has sacado del cargador y no me has usado");
//                    }
//
//                }
//                updateBatteryScreen(context, "BatteryFull");
//                CommUtils.sendBatteryLogToServer(context, "Battery Full", "Battery charged completely");
//                alertCount++;
//                alertLastSentAt = System.currentTimeMillis();
//            }
//        }
//    }
//
//    private void updateBatteryScreen(Context context, String status) {
//        ActivityInfoForQueue info = new ActivityInfoForQueue();
//        info.setTimeSlot(status);
//        info.setContext(context);
//        info.setNanoSecTime(System.nanoTime());
//        info.setToActivityName("BatteryActivity");
//        Globals.priorityQueue.add(info);
//        if (WatchApp.isInForeground()) {
//            NotifyNewEntryInQueue.notifyNewEntryInQueue();
//        }else {
//            final NotificationHelper notificationHelper = new NotificationHelper(context);
//            notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
//        }
//    }
//}
