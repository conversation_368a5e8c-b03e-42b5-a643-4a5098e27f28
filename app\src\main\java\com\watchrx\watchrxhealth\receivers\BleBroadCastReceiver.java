package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

public class BleBroadCastReceiver extends BroadcastReceiver {
    public static final String TAG = BleBroadCastReceiver.class.getSimpleName();

    @Override
    public void onReceive(Context context, Intent intent) {
        LogUtils.debug("Alarm received for vital collection...");
        ReminderUtils.setupReminderForCollectVitals(context);
        Intent alertCount = new Intent(MainActivity.VITAL_SCAN_START);
        LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
    }
}
