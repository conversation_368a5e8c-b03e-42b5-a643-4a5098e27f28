package com.watchrx.watchrxhealth;

import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.res.ResourcesCompat;

import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.watchrx.watchrxhealth.utils.CustomMarkerView;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class VitalsGraphActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_vitals_graph);
        LineChart lineChart = findViewById(R.id.lineChart);

        ArrayList<Entry> systolicEntries = new ArrayList<>();
        ArrayList<Entry> diastolicEntries = new ArrayList<>();

        Typeface customFont = ResourcesCompat.getFont(this, R.font.lato_regular);

        Legend legend = lineChart.getLegend();
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.BOTTOM);
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.CENTER);
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
        legend.setDrawInside(false);
        legend.setYOffset(10f);
        legend.setTextSize(14f);
        legend.setFormSize(14f);
        legend.setTypeface(customFont);

        systolicEntries.add(new Entry(0, 120));
        systolicEntries.add(new Entry(1, 126));
        systolicEntries.add(new Entry(2, 122));
        systolicEntries.add(new Entry(3, 130));
        systolicEntries.add(new Entry(4, 128));
        systolicEntries.add(new Entry(5, 132));
        systolicEntries.add(new Entry(7, 128));
        systolicEntries.add(new Entry(8, 128));
        systolicEntries.add(new Entry(9, 128));
        systolicEntries.add(new Entry(10, 128));

        diastolicEntries.add(new Entry(0, 80));
        diastolicEntries.add(new Entry(1, 83));
        diastolicEntries.add(new Entry(2, 82));
        diastolicEntries.add(new Entry(3, 88));
        diastolicEntries.add(new Entry(4, 84));
        diastolicEntries.add(new Entry(5, 86));
        diastolicEntries.add(new Entry(6, 83));
        diastolicEntries.add(new Entry(7, 88));
        diastolicEntries.add(new Entry(8, 84));
        diastolicEntries.add(new Entry(9, 86));
        diastolicEntries.add(new Entry(10, 83));


        LineDataSet systolicDataSet = new LineDataSet(systolicEntries, "Systolic");
        systolicDataSet.setColor(Color.RED);
        systolicDataSet.setCircleColor(Color.RED);
        systolicDataSet.setLineWidth(3f);
        systolicDataSet.setValueTextSize(12f);
        systolicDataSet.setValueTypeface(customFont);
        systolicDataSet.setValueTextColor(Color.RED);
        systolicDataSet.setFillAlpha(70);
        systolicDataSet.setDrawValues(false);

        LineDataSet diastolicDataSet = new LineDataSet(diastolicEntries, "Diastolic");
        diastolicDataSet.setColor(Color.BLUE);
        diastolicDataSet.setCircleColor(Color.BLUE);
        diastolicDataSet.setLineWidth(3f);
        diastolicDataSet.setValueTextSize(12f);
        diastolicDataSet.setValueTypeface(customFont);
        diastolicDataSet.setValueTextColor(Color.BLUE);
        diastolicDataSet.setFillAlpha(70);
        diastolicDataSet.setDrawValues(false);

        LineData lineData = new LineData(systolicDataSet, diastolicDataSet);
        lineChart.setData(lineData);

        lineChart.getAxisLeft().setTextSize(12f);
        lineChart.getAxisLeft().setTypeface(customFont);
        lineChart.getAxisRight().setEnabled(false);

        XAxis xAxis = lineChart.getXAxis();
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setTextSize(12f);
        xAxis.setLabelRotationAngle(-45f);
        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DAY_OF_YEAR, -7 + (int) value);
                Date date = calendar.getTime();
                SimpleDateFormat dateFormat = new SimpleDateFormat("MM/dd", Locale.US);
                return dateFormat.format(date);
            }
        });

        lineChart.setExtraBottomOffset(20f);
        lineChart.setExtraTopOffset(10f);

        lineChart.getDescription().setEnabled(false);
        lineChart.getAxisRight().setEnabled(false);
        CustomMarkerView markerView = new CustomMarkerView(this, R.layout.marker_view);
        lineChart.setMarker(markerView);
        lineChart.invalidate();
    }
}