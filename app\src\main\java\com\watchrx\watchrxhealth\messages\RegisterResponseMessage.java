package com.watchrx.watchrxhealth.messages;

import com.watchrx.watchrxhealth.db.CustomAlertsDetails;
import com.watchrx.watchrxhealth.db.MedicationDetail;
import com.watchrx.watchrxhealth.db.ScheduleMessageVO;

import java.util.List;

public class RegisterResponseMessage {

    public String responseCode;
    public String responseMessage;
    public String responseType;
    public String status;
    public String patientId;
    public String imeiNo;
    public String address;
    public String phone;
    public String visitReason;
    public String insuranceCompany;
    public String insuranceId;
    public String image;
    public String chosenTimeForTimeSlots;
    public List<MedicationDetail> medicationDetail;
    public String kind;
    public String etag;
    public String sosMobileNo;
    public String medicineForm;
    public String gpsStatus;
    public String radius;
    public String trackingStatus;
    public String latLong;
    public String appColor;
    public String appLanguage;
    public String heartRateStatus;
    public String heartScheduleDaysOfWeek;
    public String heartRateScheduleTimeSlots;

    public List<CustomAlertsDetails> customAlertsDetails;

    public List<ScheduleMessageVO> scheduledTextMessageInfoList;

    public List<VitalConfigVO> vitalScheduleInfoList;

    public List<VitalConfigVO> vitalTypeStatusList;

    public PedoMeterConfig pedometerConfigurationInfo;

    public SleepMonitor sleepData;

    public String patientName;
    public String provider;
    public String providerPhone;
    public String caseManager;
    public String caseManagerPhone;

    public String clinicName;

    public boolean isSuccessResponse() {
        return (responseMessage != null && responseMessage.equalsIgnoreCase("operationsuccessful"));
    }

    public boolean isImeNotFoundResponse() {
        return (responseMessage != null && responseMessage.equalsIgnoreCase("Imei is not found in the database"));
    }

    public boolean isImeNotAssignedResponse() {
        return (responseMessage != null && responseMessage.equalsIgnoreCase("Imei is not associated with any patient"));
    }

    public boolean isWatchInActive() {
        return (responseMessage != null && responseMessage.equalsIgnoreCase("Subscription is not active"));
    }

    public boolean isPatientInfoNotFound() {
        return (responseMessage != null && responseMessage.equalsIgnoreCase("Patient details not found with provided info."));
    }
}
