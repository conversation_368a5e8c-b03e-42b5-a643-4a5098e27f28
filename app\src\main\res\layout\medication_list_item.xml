<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginTop="10dp"
    android:layout_marginBottom="5dp"
    app:cardBackgroundColor="#FFFFFF"
    app:cardCornerRadius="12dp"
    app:cardElevation="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <!-- Medicine Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="80dp"
                android:layout_weight="1.5"
                android:orientation="vertical"
                android:weightSum="2">

                <TextView
                    android:id="@+id/medicineName"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="start"
                    android:textColor="#000000"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/medicineColor"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="start"
                    android:textColor="#555555"
                    android:textSize="14sp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/medicineImage"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center_vertical|center_horizontal"
                android:layout_weight="0.5"
                android:contentDescription="Pills image"
                android:padding="10dp"
                android:src="@drawable/deafult_medicine" />

        </LinearLayout>

        <!-- Schedule with Icon -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/scheduleIcon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:contentDescription="Schedule icon"
                android:src="@drawable/calendar" />

            <TextView
                android:id="@+id/schedule"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/lato_bold"
                android:textColor="#555555"
                android:textSize="14sp" />
        </LinearLayout>

        <!-- Dynamic Timing Grid -->
        <androidx.gridlayout.widget.GridLayout
            android:id="@+id/timingGrid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:columnCount="1"
            app:orientation="horizontal"></androidx.gridlayout.widget.GridLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
