package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.Event;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class EventsAdapter extends RecyclerView.Adapter<EventsAdapter.EventViewHolder> {

    private final Context context;
    private final List<Event> eventList;

    public EventsAdapter(Context context, List<Event> eventList) {
        this.context = context;
        this.eventList = eventList;
    }

    @NonNull
    @Override
    public EventViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_event, parent, false);
        return new EventViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EventViewHolder holder, int position) {
        Event event = eventList.get(position);
        holder.titleTextView.setText(event.getTitle());
        if (event.getPriority().equalsIgnoreCase("Critical")) {
            int backgroundColor = Color.parseColor("#80EF5350");
            holder.titleTextView.setTextColor(backgroundColor);
        } else if (event.getPriority().equalsIgnoreCase("High")) {
            int backgroundColor = Color.parseColor("#80FFA726");
            holder.titleTextView.setTextColor(backgroundColor);
        } else if (event.getPriority().equalsIgnoreCase("Medium")) {
            int backgroundColor = Color.parseColor("#80007aff");
            holder.titleTextView.setTextColor(backgroundColor);
        } else if (event.getPriority().equalsIgnoreCase("Low")) {
            int backgroundColor = Color.parseColor("#80FFBB86FC");
            holder.titleTextView.setTextColor(backgroundColor);
        }
        holder.descriptionTextView.setText(event.getDescription());
        holder.timeRange.setText(MessageFormat.format("{0} {1} {2}", formatDateTime(event.getStart()), " - ", formatDateTime(event.getEnd())));
    }

    public static String formatDateTime(String inputDateTime) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault());
        SimpleDateFormat outputFormat = new SimpleDateFormat("h:mm a", Locale.getDefault());
        try {
            Date date = inputFormat.parse(inputDateTime);
            assert date != null;
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    @Override
    public int getItemCount() {
        return eventList.size();
    }

    public static class EventViewHolder extends RecyclerView.ViewHolder {
        TextView titleTextView, descriptionTextView, timeRange;
        CardView cardView;

        public EventViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.event_card);
            titleTextView = itemView.findViewById(R.id.title);
            descriptionTextView = itemView.findViewById(R.id.description);
            timeRange = itemView.findViewById(R.id.timeRange);
        }
    }
}
