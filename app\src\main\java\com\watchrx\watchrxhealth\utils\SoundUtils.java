package com.watchrx.watchrxhealth.utils;

import android.content.Context;
import android.media.AudioManager;
import android.view.MenuItem;
import android.widget.ToggleButton;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.WatchApp;

public class SoundUtils {
    private static AudioManager audioManager = WatchApp.audioManager;

    public static void soundMute(Context context) {
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0);
    }

    public static void soundUnMute(Context context) {

        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        float percent = 1.0f;
        int seventyVolume = (int) (maxVolume * percent);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, seventyVolume, 0);
    }

    public static boolean checkStateOfSound(ToggleButton muteUnmute, Context context) {

        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (currentVolume > 0) {
            SoundUtils.soundUnMute(context);
            if (muteUnmute != null) {
                muteUnmute.setChecked(true);
            }
            return true;
        } else {
            SoundUtils.soundMute(context);
            if (muteUnmute != null) {
                muteUnmute.setChecked(false);
            }
            return true;
        }

    }

    public static int getSoundLevel() {
        return audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
    }


    public static boolean checkStateOfSoundV1(MenuItem item, Context context) {
        int currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_MUSIC);
        if (currentVolume > 0) {
            SoundUtils.soundUnMute(context);
            if (item != null) {
                item.setIcon(R.drawable.ic_sound);
            }
            return true;
        } else {
            SoundUtils.soundMute(context);
            if (item != null) {
                item.setIcon(R.drawable.mute_sound);
            }
            return true;
        }
    }
}