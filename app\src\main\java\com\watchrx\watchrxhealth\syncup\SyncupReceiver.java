package com.watchrx.watchrxhealth.syncup;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Message;

import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.InternetCheckConnectivity;
import com.watchrx.watchrxhealth.utils.LogUtils;

import static com.watchrx.watchrxhealth.constants.CommonConstants.Max_Time_Out_For_Ping;

public class SyncupReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(final Context context, Intent intent) {
        if (CommUtils.isNetworkAvailable(context)) {
            Handler h = new Handler() {
                @Override
                public void handleMessage(Message msg) {
                    if (msg.what != 1) {
                        LogUtils.debug("Internet is connected , but Ping is not happening properly, so sync up not happening");
                    } else {
                        LogUtils.debug("****Going to sync with server*****Day Time");
                        SyncUpWithServer.registerWatchAtDay(context);
                    }
                }
            };
            InternetCheckConnectivity.isNetworkAvailable(h, Max_Time_Out_For_Ping);
        } else {
            LogUtils.debug("Internet is not connected ");
        }
    }
}
