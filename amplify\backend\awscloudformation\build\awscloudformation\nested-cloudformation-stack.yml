{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Root Stack for AWS Amplify CLI", "Parameters": {"DeploymentBucketName": {"Description": "Name of the common deployment bucket provided by the parent stack", "Type": "String", "Default": "DeploymentBucket"}, "AuthRoleName": {"Type": "String", "Default": "AuthRoleName"}, "UnauthRoleName": {"Type": "String", "Default": "UnauthRoleName"}}, "Resources": {"DeploymentBucket": {"Type": "AWS::S3::<PERSON><PERSON>", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"BucketName": {"Ref": "DeploymentBucketName"}, "BucketEncryption": {"ServerSideEncryptionConfiguration": [{"ServerSideEncryptionByDefault": {"SSEAlgorithm": "AES256"}}]}}}, "AuthRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Ref": "AuthRoleName"}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "", "Effect": "<PERSON><PERSON>", "Principal": {"Federated": "cognito-identity.amazonaws.com"}, "Action": "sts:AssumeRoleWithWebIdentity"}]}}}, "UnauthRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Ref": "UnauthRoleName"}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Sid": "", "Effect": "<PERSON><PERSON>", "Principal": {"Federated": "cognito-identity.amazonaws.com"}, "Action": "sts:AssumeRoleWithWebIdentity"}]}}}, "authcognito705857d4": {"Type": "AWS::CloudFormation::Stack", "Properties": {"TemplateURL": "https://s3.amazonaws.com/amplify-lexa-dev-210043-deployment/amplify-cfn-templates/auth/cognito705857d4-cloudformation-template.yml", "Parameters": {"identityPoolName": "lexa_identitypool_705857d4", "allowUnauthenticatedIdentities": true, "authSelections": "identityPoolOnly", "resourceName": "cognito705857d4", "sharedId": "705857d4", "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "env": "dev"}}}, "interactionslexandroid": {"Type": "AWS::CloudFormation::Stack", "Properties": {"TemplateURL": "https://s3.amazonaws.com/amplify-lexa-dev-210043-deployment/amplify-cfn-templates/interactions/lexandroid-cloudformation-template.json", "Parameters": {"authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "authRoleName": {"Ref": "AuthRoleName"}, "unauthRoleName": {"Ref": "UnauthRoleName"}, "deploymentBucketName": "amplify-lexa-dev-210043-deployment", "s3Key": "amplify-builds/lexandroid-5742396a4b6137364f6e-build.zip", "env": "dev"}}}, "UpdateRolesWithIDPFunction": {"DependsOn": ["AuthRole", "UnauthRole", "authcognito705857d4"], "Type": "AWS::Lambda::Function", "Properties": {"Code": {"ZipFile": {"Fn::Join": ["\n", ["const response = require('cfn-response');", "const aws = require('aws-sdk');", "let responseData = {};", "exports.handler = function(event, context) {", "  try {", "    let authRoleName = event.ResourceProperties.authRoleName;", "    let unauthRoleName = event.ResourceProperties.unauthRoleName;", "    let idpId = event.ResourceProperties.idpId;", "    let promises = [];", "    let authParamsJson = { 'Version': '2012-10-17','Statement': [{'Effect': 'Allow','Principal': {'Federated': 'cognito-identity.amazonaws.com'},'Action': 'sts:AssumeRoleWithWebIdentity','Condition': {'StringEquals': {'cognito-identity.amazonaws.com:aud': idpId},'ForAnyValue:StringLike': {'cognito-identity.amazonaws.com:amr': 'authenticated'}}}]};", "    let unauthParamsJson = { 'Version': '2012-10-17','Statement': [{'Effect': 'Allow','Principal': {'Federated': 'cognito-identity.amazonaws.com'},'Action': 'sts:AssumeRoleWithWebIdentity','Condition': {'StringEquals': {'cognito-identity.amazonaws.com:aud': idpId},'ForAnyValue:StringLike': {'cognito-identity.amazonaws.com:amr': 'unauthenticated'}}}]};", "    if (event.RequestType == 'Delete') {", "        delete authParamsJson.Statement[0].Condition;", "        delete unauthParamsJson.Statement[0].Condition;", "        let authParams = { PolicyDocument: JSON.stringify(authParamsJson),RoleName: authRoleName};", "        let unauthParams = {PolicyDocument: JSON.stringify(unauthParamsJson),RoleName: unauthRoleName};", "        const iam = new aws.IAM({ apiVersion: '2010-05-08', region: event.ResourceProperties.region});", "        promises.push(iam.updateAssumeRolePolicy(authParams).promise());", "        promises.push(iam.updateAssumeRolePolicy(unauthParams).promise());", "        Promise.all(promises)", "         .then((res) => {", "            console.log(\"delete response data\" + JSON.stringify(res));", "            response.send(event, context, response.SUCCESS, {});", "         });", "    }", "    if (event.RequestType == 'Update' || event.RequestType == 'Create') {", "       const iam = new aws.IAM({ apiVersion: '2010-05-08', region: event.ResourceProperties.region});", "        let authParams = { PolicyDocument: JSON.stringify(authParamsJson),RoleName: authRoleName};", "        let unauthParams = {PolicyDocument: JSON.stringify(unauthParamsJson),RoleName: unauthRoleName};", "        promises.push(iam.updateAssumeRolePolicy(authParams).promise());", "        promises.push(iam.updateAssumeRolePolicy(unauthParams).promise());", "        Promise.all(promises)", "         .then((res) => {", "            console.log(\"createORupdate\" + res);", "            console.log(\"response data\" + JSON.stringify(res));", "            response.send(event, context, response.SUCCESS, {});", "         });", "    }", "  } catch(err) {", "       console.log(err.stack);", "       responseData = {Error: err};", "       response.send(event, context, response.FAILED, responseData);", "       throw err;", "  }", "};"]]}}, "Handler": "index.handler", "Runtime": "nodejs12.x", "Timeout": 300, "Role": {"Fn::GetAtt": ["UpdateRolesWithIDPFunctionRole", "<PERSON><PERSON>"]}}}, "UpdateRolesWithIDPFunctionOutputs": {"Type": "Custom::LambdaCallout", "Properties": {"ServiceToken": {"Fn::GetAtt": ["UpdateRolesWithIDPFunction", "<PERSON><PERSON>"]}, "region": {"Ref": "AWS::Region"}, "idpId": {"Fn::GetAtt": ["authcognito705857d4", "Outputs.IdentityPoolId"]}, "authRoleName": {"Ref": "AuthRoleName"}, "unauthRoleName": {"Ref": "UnauthRoleName"}}}, "UpdateRolesWithIDPFunctionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::Join": ["", [{"Ref": "AuthRoleName"}, "-idp"]]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}, "Policies": [{"PolicyName": "UpdateRolesWithIDPFunctionPolicy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:*:*:*"}, {"Effect": "Allow", "Action": "iam:UpdateAssumeRolePolicy", "Resource": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}}, {"Effect": "Allow", "Action": "iam:UpdateAssumeRolePolicy", "Resource": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}}]}}]}}}, "Outputs": {"Region": {"Description": "CloudFormation provider root stack Region", "Value": {"Ref": "AWS::Region"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-Region"}}}, "StackName": {"Description": "CloudFormation provider root stack ID", "Value": {"Ref": "AWS::StackName"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-StackName"}}}, "StackId": {"Description": "CloudFormation provider root stack name", "Value": {"Ref": "AWS::StackId"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-StackId"}}}, "DeploymentBucketName": {"Description": "CloudFormation provider root stack deployment bucket name", "Value": {"Ref": "DeploymentBucketName"}, "Export": {"Name": {"Fn::Sub": "${AWS::StackName}-DeploymentBucketName"}}}, "AuthRoleArn": {"Value": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}}, "UnauthRoleArn": {"Value": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}}, "AuthRoleName": {"Value": {"Ref": "AuthRole"}}, "UnauthRoleName": {"Value": {"Ref": "UnauthRole"}}}}