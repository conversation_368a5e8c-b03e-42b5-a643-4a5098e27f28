package com.watchrx.watchrxhealth;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.adapter.ContactsListAdapter;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.PhoneNumbers;
import com.watchrx.watchrxhealth.models.ContactsData;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public class PhoneCallsActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_contacts_list);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        RecyclerView recyclerView = findViewById(R.id.rvContacts);
        List<ContactsData> contactsData = new ArrayList<>();
        List<PhoneNumbers> phoneNumbersList = PhoneNumbers.getFromDB();
        for (PhoneNumbers phoneNumbers : phoneNumbersList) {
            ContactsData data = new ContactsData();
            data.setContactName(phoneNumbers.getContactName());
            data.setContactNumber(phoneNumbers.getPhonenumber());
            contactsData.add(data);
        }
        Collections.sort(contactsData, new Comparator<ContactsData>() {
            @Override
            public int compare(ContactsData lhs, ContactsData rhs) {
                return lhs.getContactName().compareTo(rhs.getContactName());
            }
        });
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(new ContactsListAdapter(PhoneCallsActivity.this, contactsData, new ContactsListAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(ContactsData item) {
                Toast.makeText(PhoneCallsActivity.this, "Calling to " + item.getContactName(), Toast.LENGTH_LONG).show();
                String phNum = item.getContactNumber();
                String number = "tel:" + phNum;
                String[] first_name = item.getContactName().split(" ");
                if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                    GeneralUtils.speak("Calling " + first_name[0]);
                } else {
                    GeneralUtils.speak("Llamando .  " + first_name[0]);
                }
                LogUtils.debug("Calling  " + item.getContactName() + "This number =" + phNum);
                try {
                    Thread.sleep(2000);
                    Intent callIntent = new Intent(Intent.ACTION_CALL, Uri.parse(number));
                    startActivity(callIntent);
                    finish();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }));

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }
}