<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".VitalDetailsActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/progressBarView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="10dp">

        <ProgressBar
            android:id="@+id/progressbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/nodata"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_bold"
            android:padding="@dimen/d_10_d"
            android:text="@string/no_data_found"
            android:textColor="@color/black"
            android:textSize="@dimen/d_30_s"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/d_10_d"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- Left Button -->
        <ImageView
            android:id="@+id/before_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_corner_img"
            android:padding="@dimen/d_10_d"
            android:rotation="180"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Center TextView -->
        <TextView
            android:id="@+id/date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_background"
            android:clickable="true"
            android:drawablePadding="8dp"
            android:focusable="true"
            android:gravity="center"
            android:fontFamily="@font/lato_bold"
            android:paddingHorizontal="16dp"
            android:paddingVertical="8dp"
            android:text="31 Dec 2024"
            android:textSize="18sp"
            app:drawableEndCompat="@drawable/down_arrow"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/after_date"
            app:layout_constraintStart_toEndOf="@id/before_date"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Right Button -->
        <ImageView
            android:id="@+id/after_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/rounded_corner_img"
            android:padding="@dimen/d_10_d"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/vitalRv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="@dimen/d_10_d"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/buttonGroup"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clDate"
        tools:layout_editor_absoluteX="10dp" />

    <com.github.mikephil.charting.charts.LineChart
        android:id="@+id/lineChart"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="1dp"
        android:visibility="visible"
        app:layout_constraintBottom_toTopOf="@+id/buttonGroup"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clDate" />

    <LinearLayout
        android:id="@+id/buttonGroup"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintStart_toStartOf="parent">

        <RadioGroup
            android:id="@+id/radioGroupToggle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/radioGraph"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/toggle_selector_left"
                android:button="@android:color/transparent"
                android:checked="true"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp"
                android:text="Graph"
                android:textColor="@color/toggle_text_selector"
                android:textSize="16sp" />

            <RadioButton
                android:id="@+id/radioTable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/toggle_selector_right"
                android:button="@android:color/transparent"
                android:paddingHorizontal="20dp"
                android:paddingVertical="10dp"
                android:text="Table"
                android:textColor="@color/toggle_text_selector"
                android:textSize="16sp" />
        </RadioGroup>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>