{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.0\\com.watchrx.watchrxhealth.app-mergeDebugResources-58:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed01b5b8f9cea297e728a0087469107c\\transformed\\jetified-video-android-7.0.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "715,716,717", "startColumns": "4,4,4", "startOffsets": "34889,34935,34989", "endLines": "715,716,721", "endColumns": "45,53,11", "endOffsets": "34930,34984,35158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b87b3f2d7275f8fdc70256821a83cc8d\\transformed\\work-runtime-2.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "759,760,761,763", "startColumns": "4,4,4,4", "startOffsets": "36662,36727,36797,36923", "endColumns": "64,69,63,60", "endOffsets": "36722,36792,36856,36979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72ede0399808c17d9b7f9da6bb0e818d\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2248", "startColumns": "4", "startOffsets": "142818", "endColumns": "82", "endOffsets": "142896"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ace4240242d7e30916f29befa3ff2d25\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2120,2325", "startColumns": "4,4", "startOffsets": "134669,148956", "endColumns": "67,166", "endOffsets": "134732,149118"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dfc6b2dc1b1e8d7695a962a47974e93a\\transformed\\jetified-activity-1.8.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2066,2107", "startColumns": "4,4", "startOffsets": "131822,133892", "endColumns": "41,59", "endOffsets": "131859,133947"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eefc612161d90b6bd0031304794c24b9\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2106", "startColumns": "4", "startOffsets": "133849", "endColumns": "42", "endOffsets": "133887"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "764,774,779,780,781,792,797,798,799,800,810,857,858,859,864,865,866,867,1109,1119,1120,1121,1122,1123,1130,1131,1132,1137,1138,1139,1142,1143,1144,1145,1146", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "36984,37689,38053,38095,38145,38956,39249,39295,39342,39393,40165,43338,43378,43417,43701,43743,43788,43833,62123,62821,62868,62913,62960,63006,63455,63498,63544,63876,63921,63964,64133,64181,64222,64262,64308", "endColumns": "43,44,41,49,38,43,45,46,50,43,43,39,38,40,41,44,44,43,40,46,44,46,45,37,42,45,45,44,42,47,47,40,39,45,40", "endOffsets": "37023,37729,38090,38140,38179,38995,39290,39337,39388,39432,40204,43373,43412,43453,43738,43783,43828,43872,62159,62863,62908,62955,63001,63039,63493,63539,63585,63916,63959,64007,64176,64217,64257,64303,64344"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c496671a68b8e9628512bef2587c7af2\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2108", "startColumns": "4", "startOffsets": "133952", "endColumns": "53", "endOffsets": "134001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965e73c60b960f9618dcd798b95c371f\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "234,237,1266", "startColumns": "4,4,4", "startOffsets": "10428,10592,71754", "endColumns": "55,47,51", "endOffsets": "10479,10635,71801"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\42fcd71a3362c3d3c804d610cf3f2c72\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "233,332,339,691,756,757,765,766,767,768,769,770,771,775,776,777,778,782,783,784,785,786,787,788,789,848,849,850,851,853,854,855,856,860,861,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1111,1112,1113,1114,1115,1116,1117,1118,1124,1125,1126,1127,1128,1129,1133,1134,1135,1136,1140,1141,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1316,1317,1322,1323,1324,1325,1326,1327,1328,1987,1988,1989,1990,1991,1992,1993,1994,2032,2033,2034,2035,2040,2064,2065,2074,2103,2112,2113,2116,2117,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2680,2770,2771,2772,2773,2774,2807,2815,2816,2820,2824,2835,2840,2869,2876,2880,2884,2889,2893,2897,2901,2905,2909,2913,2919,2923,2929,2933,2939,2943,2948,2952,2955,2959,2965,2969,2975,2979,2985,2988,2992,2996,3000,3004,3008,3009,3010,3011,3014,3017,3020,3023,3027,3028,3029,3030,3071,3074,3076,3078,3080,3085,3086,3090,3096,3100,3101,3103,3115,3116,3120,3126,3130,3207,3208,3212,3239,3243,3244,3248,5045,5217,5243,5414,5440,5471,5479,5485,5501,5523,5528,5533,5543,5552,5561,5565,5572,5591,5598,5599,5608,5611,5614,5618,5622,5626,5629,5630,5635,5640,5650,5655,5662,5668,5669,5672,5676,5681,5683,5685,5688,5691,5693,5697,5700,5707,5710,5713,5717,5719,5723,5725,5727,5729,5733,5741,5749,5761,5767,5776,5779,5790,5793,5794,5799,5800,6323,6392,6466,6467,6477,6486,6638,6640,6644,6647,6650,6653,6656,6659,6662,6665,6669,6672,6675,6678,6682,6685,6689,6840,6841,6842,6843,6844,6845,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6858,6859,6860,6862,6864,6865,6866,6867,6868,6869,6870,6871,6873,6874,6876,6877,6879,6881,6882,6884,6885,6886,6887,6888,6889,6891,6892,6893,6894,6895,7185,7187,7189,7191,7192,7193,7194,7195,7196,7197,7198,7199,7200,7201,7202,7203,7205,7206,7207,7208,7209,7210,7211,7213,7217,7394,7395,7396,7397,7398,7399,7403,7404,7405,7955,7957,7959,7961,7963,7965,7966,7967,7968,7970,7972,7974,7975,7976,7977,7978,7979,7980,7981,7982,7983,7984,7985,7988,7989,7990,7991,7993,7995,7996,7998,7999,8001,8003,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8016,8018,8019,8020,8021,8023,8024,8025,8026,8027,8029,8031,8033,8035,8036,8037,8038,8039,8040,8041,8042,8043,8044,8045,8046,8047,8048,8049", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10373,15265,15550,33842,36472,36527,37028,37092,37162,37223,37298,37374,37451,37734,37819,37901,37977,38184,38261,38339,38445,38551,38630,38710,38767,42730,42804,42879,42944,43072,43132,43193,43265,43458,43525,56970,57029,57088,57147,57206,57265,57319,57373,57426,57480,57534,57588,62233,62307,62386,62459,62533,62604,62676,62748,63044,63101,63159,63232,63306,63380,63590,63662,63735,63805,64012,64072,64407,64476,64545,64615,64689,64765,64829,64906,64982,65059,65124,65193,65270,65345,65414,65482,65559,65625,65686,65783,65848,65917,66016,66087,66146,66204,66261,66320,66384,66455,66527,66599,66671,66743,66810,66878,66946,67005,67068,67132,67222,67313,67373,67439,67506,67572,67642,67706,67759,67826,67887,67954,68067,68125,68188,68253,68318,68393,68466,68538,68582,68629,68675,68724,68785,68846,68907,68969,69033,69097,69161,69226,69289,69349,69410,69476,69535,69595,69657,69728,69788,74949,75035,75327,75417,75504,75592,75674,75757,75847,127179,127231,127289,127334,127400,127464,127521,127578,130032,130089,130137,130186,130441,131726,131773,132206,133724,134163,134227,134417,134477,139447,139521,139591,139669,139723,139793,139878,139926,139972,140033,140096,140162,140226,140297,140360,140425,140489,140550,140611,140663,140736,140810,140879,140954,141028,141102,141243,181726,187444,187522,187612,187700,187796,189869,190451,190540,190787,191068,191734,192019,193828,194305,194527,194749,195025,195252,195482,195712,195942,196172,196399,196818,197044,197469,197699,198127,198346,198629,198837,198968,199195,199621,199846,200273,200494,200919,201039,201315,201616,201940,202231,202545,202682,202813,202918,203160,203327,203531,203739,204010,204122,204234,204339,206432,206646,206792,206932,207018,207366,207454,207700,208118,208367,208449,208547,209204,209304,209556,209980,210235,216125,216214,216451,218475,218717,218819,219072,354152,364833,366349,377044,378572,380329,380955,381375,382636,383901,384157,384393,384940,385434,386039,386237,386817,388185,388560,388678,389216,389373,389569,389842,390098,390268,390409,390473,390838,391205,391881,392145,392483,392836,392930,393116,393422,393684,393809,393936,394175,394386,394505,394698,394875,395330,395511,395633,395892,396005,396192,396294,396401,396530,396805,397313,397809,398686,398980,399550,399699,400431,400603,400687,401023,401115,434614,439845,445560,445622,446200,446784,454731,454844,455073,455233,455385,455556,455722,455891,456058,456221,456464,456634,456807,456978,457252,457451,457656,467142,467226,467322,467418,467516,467616,467718,467820,467922,468024,468126,468226,468322,468434,468563,468686,468817,468948,469046,469160,469254,469394,469528,469624,469736,469836,469952,470048,470160,470260,470400,470536,470700,470830,470988,471138,471279,471423,471558,471670,471820,471948,472076,472212,472344,472474,472604,472716,490756,490902,491046,491184,491250,491340,491416,491520,491610,491712,491820,491928,492028,492108,492200,492298,492408,492460,492538,492644,492736,492840,492950,493072,493235,506704,506784,506884,506974,507084,507174,507415,507509,507615,547847,547947,548059,548173,548289,548405,548499,548613,548725,548827,548947,549069,549151,549255,549375,549501,549599,549693,549781,549893,550009,550131,550243,550418,550534,550620,550712,550824,550948,551015,551141,551209,551337,551481,551609,551678,551773,551888,552001,552100,552209,552320,552431,552532,552637,552737,552867,552958,553081,553175,553287,553373,553477,553573,553661,553779,553883,553987,554113,554201,554309,554409,554499,554609,554693,554795,554879,554933,554997,555103,555189,555299,555383", "endLines": "233,332,339,691,756,757,765,766,767,768,769,770,771,775,776,777,778,782,783,784,785,786,787,788,789,848,849,850,851,853,854,855,856,860,861,1039,1040,1041,1042,1043,1044,1045,1046,1047,1048,1049,1050,1111,1112,1113,1114,1115,1116,1117,1118,1124,1125,1126,1127,1128,1129,1133,1134,1135,1136,1140,1141,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1316,1317,1322,1323,1324,1325,1326,1327,1328,1987,1988,1989,1990,1991,1992,1993,1994,2032,2033,2034,2035,2040,2064,2065,2074,2103,2112,2113,2116,2117,2194,2195,2196,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2680,2770,2771,2772,2773,2774,2814,2815,2819,2823,2827,2839,2845,2875,2879,2883,2888,2892,2896,2900,2904,2908,2912,2918,2922,2928,2932,2938,2942,2947,2951,2954,2958,2964,2968,2974,2978,2984,2987,2991,2995,2999,3003,3007,3008,3009,3010,3013,3016,3019,3022,3026,3027,3028,3029,3030,3073,3075,3077,3079,3084,3085,3089,3095,3099,3100,3102,3114,3115,3119,3125,3129,3130,3207,3211,3238,3242,3243,3247,3275,5216,5242,5413,5439,5470,5478,5484,5500,5522,5527,5532,5542,5551,5560,5564,5571,5590,5597,5598,5607,5610,5613,5617,5621,5625,5628,5629,5634,5639,5649,5654,5661,5667,5668,5671,5675,5680,5682,5684,5687,5690,5692,5696,5699,5706,5709,5712,5716,5718,5722,5724,5726,5728,5732,5740,5748,5760,5766,5775,5778,5789,5792,5793,5798,5799,5804,6391,6461,6466,6476,6485,6486,6639,6643,6646,6649,6652,6655,6658,6661,6664,6668,6671,6674,6677,6681,6684,6688,6692,6840,6841,6842,6843,6844,6845,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6858,6859,6861,6863,6864,6865,6866,6867,6868,6869,6870,6872,6873,6875,6876,6878,6880,6881,6883,6884,6885,6886,6887,6888,6890,6891,6892,6893,6894,6895,7186,7188,7190,7191,7192,7193,7194,7195,7196,7197,7198,7199,7200,7201,7202,7204,7205,7206,7207,7208,7209,7210,7212,7216,7220,7394,7395,7396,7397,7398,7402,7403,7404,7405,7956,7958,7960,7962,7964,7965,7966,7967,7969,7971,7973,7974,7975,7976,7977,7978,7979,7980,7981,7982,7983,7984,7987,7988,7989,7990,7992,7994,7995,7997,7998,8000,8002,8004,8005,8006,8007,8008,8009,8010,8011,8012,8013,8014,8015,8017,8018,8019,8020,8022,8023,8024,8025,8026,8028,8030,8032,8034,8035,8036,8037,8038,8039,8040,8041,8042,8043,8044,8045,8046,8047,8048,8049", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "10423,15305,15594,33878,36522,36584,37087,37157,37218,37293,37369,37446,37524,37814,37896,37972,38048,38256,38334,38440,38546,38625,38705,38762,38820,42799,42874,42939,43005,43127,43188,43260,43333,43520,43588,57024,57083,57142,57201,57260,57314,57368,57421,57475,57529,57583,57637,62302,62381,62454,62528,62599,62671,62743,62816,63096,63154,63227,63301,63375,63450,63657,63730,63800,63871,64067,64128,64471,64540,64610,64684,64760,64824,64901,64977,65054,65119,65188,65265,65340,65409,65477,65554,65620,65681,65778,65843,65912,66011,66082,66141,66199,66256,66315,66379,66450,66522,66594,66666,66738,66805,66873,66941,67000,67063,67127,67217,67308,67368,67434,67501,67567,67637,67701,67754,67821,67882,67949,68062,68120,68183,68248,68313,68388,68461,68533,68577,68624,68670,68719,68780,68841,68902,68964,69028,69092,69156,69221,69284,69344,69405,69471,69530,69590,69652,69723,69783,69851,75030,75117,75412,75499,75587,75669,75752,75842,75933,127226,127284,127329,127395,127459,127516,127573,127627,130084,130132,130181,130232,130470,131768,131817,132247,133751,134222,134284,134472,134529,139516,139586,139664,139718,139788,139873,139921,139967,140028,140091,140157,140221,140292,140355,140420,140484,140545,140606,140658,140731,140805,140874,140949,141023,141097,141238,141308,181774,187517,187607,187695,187791,187881,190446,190535,190782,191063,191315,192014,192407,194300,194522,194744,195020,195247,195477,195707,195937,196167,196394,196813,197039,197464,197694,198122,198341,198624,198832,198963,199190,199616,199841,200268,200489,200914,201034,201310,201611,201935,202226,202540,202677,202808,202913,203155,203322,203526,203734,204005,204117,204229,204334,204451,206641,206787,206927,207013,207361,207449,207695,208113,208362,208444,208542,209199,209299,209551,209975,210230,210324,216209,216446,218470,218712,218814,219067,221223,364828,366344,377039,378567,380324,380950,381370,382631,383896,384152,384388,384935,385429,386034,386232,386812,388180,388555,388673,389211,389368,389564,389837,390093,390263,390404,390468,390833,391200,391876,392140,392478,392831,392925,393111,393417,393679,393804,393931,394170,394381,394500,394693,394870,395325,395506,395628,395887,396000,396187,396289,396396,396525,396800,397308,397804,398681,398975,399545,399694,400426,400598,400682,401018,401110,401388,439840,445211,445617,446195,446779,446870,454839,455068,455228,455380,455551,455717,455886,456053,456216,456459,456629,456802,456973,457247,457446,457651,457981,467221,467317,467413,467511,467611,467713,467815,467917,468019,468121,468221,468317,468429,468558,468681,468812,468943,469041,469155,469249,469389,469523,469619,469731,469831,469947,470043,470155,470255,470395,470531,470695,470825,470983,471133,471274,471418,471553,471665,471815,471943,472071,472207,472339,472469,472599,472711,472851,490897,491041,491179,491245,491335,491411,491515,491605,491707,491815,491923,492023,492103,492195,492293,492403,492455,492533,492639,492731,492835,492945,493067,493230,493387,506779,506879,506969,507079,507169,507410,507504,507610,507702,547942,548054,548168,548284,548400,548494,548608,548720,548822,548942,549064,549146,549250,549370,549496,549594,549688,549776,549888,550004,550126,550238,550413,550529,550615,550707,550819,550943,551010,551136,551204,551332,551476,551604,551673,551768,551883,551996,552095,552204,552315,552426,552527,552632,552732,552862,552953,553076,553170,553282,553368,553472,553568,553656,553774,553878,553982,554108,554196,554304,554404,554494,554604,554688,554790,554874,554928,554992,555098,555184,555294,555378,555498"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2187,2188,2189,2190,2191,2192,2193,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2249,2250,2251,2252,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2277,2278,2279,2280,2288,2289,2290,2291,2295,2299,2300,2301,2302,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2332,2333,2334,2335,2336,2337,2338,2340,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2355,2356,2357,2358,2359,2360,2361,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2398,2399,2400,2401,2406,2407,2409,2411,2412,2413,2414,2415,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2431,2432,2433,2434,2435,2436,2438,2439,2441,2442,2443,2444,2446,2447,2448,2449,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2464,2465,2466,2467,2468,2485,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2635,2636,2638,2639,2645,2646,2647,2648,2649,2650,2651,2655,2656,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2686,2687,2688,2689,2691,2692,2693,2694,2695,2696,2697,2698,2702,2703,2704,2705,2706,2707,2708,2709,2710,2712,2713,2714,2715,2717,2718,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2751,2752,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "139193,139227,139261,139299,139332,139373,139408,141313,141366,141416,141457,141509,141558,141620,141683,141737,141803,141860,141912,141970,142032,142088,142130,142297,142350,142386,142442,142494,142570,142624,142667,142723,142759,142901,142943,142989,143041,143247,143300,143350,143404,143468,143524,143582,143628,143664,143742,143783,143831,143889,143937,144068,144187,144230,145362,145410,145452,145500,146058,146102,146144,146198,146539,146727,146795,146862,146910,147109,147163,147206,147420,147460,147530,147575,147624,147675,147771,147820,147867,150045,150089,150140,150184,150225,150268,150309,150413,150620,150671,150714,150758,150802,150858,150911,150967,151081,151218,151267,151424,151478,151546,151594,151675,151713,151768,152013,152055,152108,152175,152228,152283,152325,152398,152477,152538,152580,152634,152691,152747,152808,152872,152944,153028,153085,153161,153266,153326,153441,153556,153620,153685,153739,153826,153895,153954,154024,154319,154437,154609,154690,155188,155240,155391,155554,155632,155691,155727,155850,156075,156170,156219,156283,156345,156411,156481,156555,156617,156681,157174,157224,157307,157376,157433,157493,157695,157765,157986,158029,158115,158198,158306,158350,158398,158542,158686,158726,158768,158819,158871,158917,158963,159015,159063,159114,159156,159202,159322,159369,159409,159451,159517,161474,165268,165350,165432,165508,165571,165634,165685,165745,165805,165865,165917,165971,166015,166069,166115,166196,166244,166303,166391,166495,166531,177034,177072,177120,177166,177216,177262,177314,177367,177416,177478,177512,177602,177659,177707,177764,177842,177966,178025,178093,178170,178244,178278,178324,178366,178427,178500,178603,178675,178799,178851,179812,179860,180008,180053,180101,180147,180210,180413,180471,180599,180655,180704,180754,180809,180855,180903,180945,180999,181059,181105,181153,181203,181252,181292,181373,181441,181488,181524,181581,181619,181666,182287,182339,182395,182457,182543,182698,182755,182829,182898,182967,183036,183088,183573,183633,183697,183743,183788,183832,183878,183968,184008,184131,184188,184230,184309,184492,184528,184623,184663,184717,184765,184807,184845,184898,184950,185002,185054,185122,185160,185201,185274,185349,185410,185468,185522,185568,185634,185704,185740,185780,185832,185890,185965,186025,186111,186207,186429,186482,186562,186614,186665,186715,186765,186807,186863,186931,187086,187130,187174,187210,187252,187302,187354,187408", "endLines": "2187,2188,2189,2190,2191,2192,2193,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2231,2232,2233,2234,2235,2237,2238,2239,2240,2241,2242,2243,2244,2245,2246,2247,2249,2250,2251,2252,2254,2255,2256,2257,2258,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2277,2278,2279,2280,2288,2289,2290,2291,2295,2299,2300,2301,2302,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2332,2333,2334,2335,2336,2337,2338,2340,2343,2344,2345,2346,2347,2348,2349,2350,2351,2352,2353,2355,2356,2357,2358,2359,2360,2361,2363,2364,2365,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2398,2399,2400,2401,2406,2407,2409,2411,2412,2413,2414,2415,2417,2418,2419,2420,2421,2422,2423,2424,2425,2426,2431,2432,2433,2434,2435,2436,2438,2439,2441,2442,2443,2444,2446,2447,2448,2449,2451,2452,2453,2454,2455,2456,2457,2458,2459,2460,2461,2462,2464,2465,2466,2467,2468,2485,2518,2519,2520,2521,2522,2523,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2607,2608,2609,2610,2611,2612,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2624,2625,2626,2627,2628,2629,2630,2631,2632,2633,2635,2636,2638,2639,2645,2646,2647,2648,2649,2650,2651,2655,2656,2658,2659,2660,2661,2662,2663,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2686,2687,2688,2689,2691,2692,2693,2694,2695,2696,2697,2698,2702,2703,2704,2705,2706,2707,2708,2709,2710,2712,2713,2714,2715,2717,2718,2720,2721,2722,2723,2724,2725,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2751,2752,2754,2755,2756,2757,2758,2759,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769", "endColumns": "33,33,37,32,40,34,38,52,49,40,51,48,61,62,53,65,56,51,57,61,55,41,112,52,35,55,51,75,53,42,55,35,58,41,45,51,55,52,49,53,63,55,57,45,35,77,40,47,57,47,130,118,42,42,47,41,47,46,43,41,53,42,48,67,66,47,69,53,42,213,39,69,44,48,50,95,48,46,48,43,50,43,40,42,40,40,65,50,42,43,43,55,52,55,113,136,48,118,53,67,47,80,37,54,100,41,52,66,52,54,41,72,78,60,41,53,56,55,60,63,71,83,56,75,104,59,114,114,63,64,53,86,68,58,69,69,117,171,80,37,51,68,55,77,58,35,122,142,94,48,63,61,65,69,73,61,63,63,49,82,68,56,59,48,69,66,42,85,82,55,43,47,143,84,39,41,50,51,45,45,51,47,50,41,45,69,46,39,41,65,39,69,81,81,75,62,62,50,59,59,59,51,53,43,53,45,80,47,58,87,103,35,38,37,47,45,49,45,51,52,48,61,33,89,56,47,56,77,79,58,67,76,73,33,45,41,60,72,47,71,58,51,158,47,147,44,47,45,62,62,57,54,55,48,49,54,45,47,41,53,59,45,47,49,48,39,80,67,46,35,56,37,46,59,51,55,61,37,154,56,73,68,68,68,51,45,59,63,45,44,43,45,89,39,51,56,41,78,43,35,38,39,53,47,41,37,52,51,51,51,67,37,40,72,74,60,57,53,45,65,69,35,39,51,57,74,59,85,95,123,52,39,51,50,49,49,41,55,67,154,43,43,35,41,49,51,53,35", "endOffsets": "139222,139256,139294,139327,139368,139403,139442,141361,141411,141452,141504,141553,141615,141678,141732,141798,141855,141907,141965,142027,142083,142125,142292,142345,142381,142437,142489,142565,142619,142662,142718,142754,142813,142938,142984,143036,143092,143295,143345,143399,143463,143519,143577,143623,143659,143737,143778,143826,143884,143932,144063,144182,144225,144268,145405,145447,145495,145542,146097,146139,146193,146236,146583,146790,146857,146905,146975,147158,147201,147415,147455,147525,147570,147619,147670,147766,147815,147862,147911,150084,150135,150179,150220,150263,150304,150345,150474,150666,150709,150753,150797,150853,150906,150962,151076,151213,151262,151381,151473,151541,151589,151670,151708,151763,151864,152050,152103,152170,152223,152278,152320,152393,152472,152533,152575,152629,152686,152742,152803,152867,152939,153023,153080,153156,153261,153321,153436,153551,153615,153680,153734,153821,153890,153949,154019,154089,154432,154604,154685,154723,155235,155304,155442,155627,155686,155722,155845,155988,156165,156214,156278,156340,156406,156476,156550,156612,156676,156740,157219,157302,157371,157428,157488,157537,157760,157827,158024,158110,158193,158249,158345,158393,158537,158622,158721,158763,158814,158866,158912,158958,159010,159058,159109,159151,159197,159267,159364,159404,159446,159512,159552,161539,165345,165427,165503,165566,165629,165680,165740,165800,165860,165912,165966,166010,166064,166110,166191,166239,166298,166386,166490,166526,166565,177067,177115,177161,177211,177257,177309,177362,177411,177473,177507,177597,177654,177702,177759,177837,177917,178020,178088,178165,178239,178273,178319,178361,178422,178495,178543,178670,178729,178846,179005,179855,180003,180048,180096,180142,180205,180268,180466,180521,180650,180699,180749,180804,180850,180898,180940,180994,181054,181100,181148,181198,181247,181287,181368,181436,181483,181519,181576,181614,181661,181721,182334,182390,182452,182490,182693,182750,182824,182893,182962,183031,183083,183129,183628,183692,183738,183783,183827,183873,183963,184003,184055,184183,184225,184304,184348,184523,184562,184658,184712,184760,184802,184840,184893,184945,184997,185049,185117,185155,185196,185269,185344,185405,185463,185517,185563,185629,185699,185735,185775,185827,185885,185960,186020,186106,186202,186326,186477,186517,186609,186660,186710,186760,186802,186858,186926,187081,187125,187169,187205,187247,187297,187349,187403,187439"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1229,1230,1231,1233,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1318,1329,1715,1716,1754,1755,1968,1969,1970,1971,1972,1985,1986", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "69856,69914,69970,70082,70843,70881,70919,70957,70995,71033,71071,71109,71147,71185,71223,71261,71297,71335,71371,71408,71460,75122,75938,109456,109501,112104,112143,126180,126227,126279,126324,126369,127100,127143", "endColumns": "57,55,46,47,37,37,37,37,37,37,37,37,37,37,37,35,37,35,36,51,35,41,41,44,42,38,43,46,51,44,44,43,42,35", "endOffsets": "69909,69965,70012,70125,70876,70914,70952,70990,71028,71066,71104,71142,71180,71218,71256,71292,71330,71366,71403,71455,71491,75159,75975,109496,109539,112138,112182,126222,126274,126319,126364,126408,127138,127174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\87f5b861fcf03439e943ebcc6db5de8c\\transformed\\jetified-window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "639,644,645,646,2036", "startColumns": "4,4,4,4,4", "startOffsets": "30815,30994,31054,31106,130237", "endLines": "643,644,645,646,2036", "endColumns": "11,59,51,44,59", "endOffsets": "30989,31049,31101,31146,130292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b86d17340e149f32b42d8e4b4155161e\\transformed\\jetified-Pinview-v1.4\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "335", "startColumns": "4", "startOffsets": "15418", "endLines": "338", "endColumns": "11", "endOffsets": "15545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\98720b29c7f34fbfef5d30a8961d4610\\transformed\\preference-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "758,1110,1973,1974,1975,1976,1977,1978,1979,2061,2062,2063,2179,2180,2354,2402,2623,2652,2716,2749,2750,6178,6487,6490,6496,6502,6505,6511,6515,6518,6525,6531,6534,6540,6545,6550,6557,6559,6565,6571,6579,6584,6591,6596,6602,6606,6613,6617,6623,6629,6632,6636,6637", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "36589,62164,126413,126477,126532,126600,126667,126732,126789,131569,131617,131665,138620,138683,151386,154728,177922,180273,184353,186331,186381,425111,446875,446980,447225,447563,447709,448049,448261,448424,448831,449169,449292,449631,449870,450127,450498,450558,450896,451182,451631,451923,452311,452616,452960,453205,453535,453742,454010,454283,454427,454628,454675", "endLines": "758,1110,1973,1974,1975,1976,1977,1978,1979,2061,2062,2063,2179,2180,2354,2402,2623,2654,2716,2749,2750,6194,6489,6495,6501,6504,6510,6514,6517,6524,6530,6533,6539,6544,6549,6556,6558,6564,6570,6578,6583,6590,6595,6601,6605,6612,6616,6622,6628,6631,6635,6636,6637", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "36657,62228,126472,126527,126595,126662,126727,126784,126841,131612,131660,131721,138678,138741,151419,154780,177961,180408,184487,186376,186424,426544,446975,447220,447558,447704,448044,448256,448419,448826,449164,449287,449626,449865,450122,450493,450553,450891,451177,451626,451918,452306,452611,452955,453200,453530,453737,454005,454278,454422,454623,454670,454726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29c63916f894d15cc031166656a2aa2e\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2408", "startColumns": "4", "startOffsets": "155309", "endColumns": "81", "endOffsets": "155386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\267985dc04d40e215387f1f013ce0e5f\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "205,10037", "startColumns": "4,4", "startOffsets": "9197,685186", "endLines": "205,10039", "endColumns": "60,12", "endOffsets": "9253,685326"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "7388,7389,7393", "startColumns": "4,4,4", "startOffsets": "506369,506464,506619", "endLines": "7388,7392,7393", "endColumns": "94,12,84", "endOffsets": "506459,506614,506699"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2362,2410,2416,2427,2428,2429,2430,2657", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "151869,155447,155993,156745,156849,156958,157078,180526", "endColumns": "143,106,81,103,108,119,95,72", "endOffsets": "152008,155549,156070,156844,156953,157073,157169,180594"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dc65449114092406ba100a0c99207fb8\\transformed\\gridlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1267", "startColumns": "4", "startOffsets": "71806", "endColumns": "42", "endOffsets": "71844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f533fcbc46a57fb9ff7758295e66825\\transformed\\constraintlayout-2.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "98,105,107,113,114,122,124,125,132,138,140,141,142,143,144,201,202,203,204,206,210,211,212,215,225,235,272,273,278,279,284,289,290,291,296,297,302,303,308,309,310,316,317,318,323,329,330,348,349,355,356,357,358,361,364,367,368,371,374,375,376,377,378,381,384,385,386,387,393,398,401,404,405,406,411,412,413,416,419,420,423,426,429,432,433,434,437,440,441,446,447,453,458,461,464,465,466,467,468,469,470,471,472,473,474,475,491,572,573,574,575,580,587,595,596,597,600,605,607,615,616,637,652,689,690,694,695,705,706,707,713,729,735,739,740,741,742,743,752,2047,2105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3582,3866,3978,4213,4274,4565,4672,4722,5124,5431,5551,5606,5666,5731,5790,8976,9028,9089,9151,9258,9391,9443,9493,9654,10061,10484,12773,12832,13029,13086,13281,13462,13516,13573,13765,13823,14019,14075,14269,14326,14377,14599,14651,14706,14896,15112,15162,16043,16099,16305,16366,16426,16496,16629,16760,16888,16956,17085,17211,17273,17336,17404,17471,17594,17719,17786,17851,17916,18205,18386,18507,18628,18694,18761,18971,19040,19106,19231,19357,19424,19550,19677,19802,19929,19985,20050,20176,20299,20364,20572,20639,20927,21107,21227,21347,21412,21474,21536,21600,21662,21721,21781,21842,21903,21962,22022,22682,27625,27676,27725,27773,28060,28352,28660,28707,28767,28873,29053,29165,29500,29554,30719,31402,33732,33783,33992,34044,34419,34478,34532,34770,35456,35658,35797,35843,35898,35943,35987,36335,130760,133804", "endLines": "104,105,111,113,121,122,124,125,132,138,140,141,142,143,144,201,202,203,204,209,210,211,212,224,232,235,272,277,278,283,288,289,290,295,296,301,302,307,308,309,315,316,317,322,328,329,330,348,354,355,356,357,360,363,366,367,370,373,374,375,376,377,380,383,384,385,386,392,397,400,403,404,405,410,411,412,415,418,419,422,425,428,431,432,433,436,439,440,445,446,452,457,460,463,464,465,466,467,468,469,470,471,472,473,474,490,496,572,573,574,575,586,594,595,596,599,604,605,614,615,616,637,652,689,690,694,703,705,706,712,713,734,738,739,740,741,742,751,755,2047,2105", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "3861,3917,4159,4269,4560,4612,4717,4770,5167,5477,5601,5661,5726,5785,5847,9023,9084,9146,9192,9386,9438,9488,9539,10056,10368,10524,12827,13024,13081,13276,13457,13511,13568,13760,13818,14014,14070,14264,14321,14372,14594,14646,14701,14891,15107,15157,15209,16094,16300,16361,16421,16491,16624,16755,16883,16951,17080,17206,17268,17331,17399,17466,17589,17714,17781,17846,17911,18200,18381,18502,18623,18689,18756,18966,19035,19101,19226,19352,19419,19545,19672,19797,19924,19980,20045,20171,20294,20359,20567,20634,20922,21102,21222,21342,21407,21469,21531,21595,21657,21716,21776,21837,21898,21957,22017,22677,22928,27671,27720,27768,27826,28347,28655,28702,28762,28868,29048,29102,29495,29549,29605,30760,31444,33778,33837,34039,34369,34473,34527,34765,34820,35653,35792,35838,35893,35938,35982,36330,36467,130796,133844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bab69c72513635a63b5299e261d854f\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2109", "startColumns": "4", "startOffsets": "134006", "endColumns": "49", "endOffsets": "134051"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a5bf5e5c6ef9e8b277937fda46aabee7\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "131,793,794,795,796,1234,1235,1236,2828,6195,6197,6200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5072,39000,39061,39123,39185,70130,70189,70246,191320,426549,426613,426739", "endLines": "131,793,794,795,796,1234,1235,1236,2834,6196,6199,6202", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "5119,39056,39118,39180,39244,70184,70241,70295,191729,426608,426734,426862"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7ed42c12cb279249327ac70e76bb72eb\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2038,2039,2060,2068,2069,2098,2099,2100,2101,2102", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "130354,130394,131526,131907,131962,133458,133512,133564,133613,133674", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "130389,130436,131564,131957,132004,133507,133559,133608,133669,133719"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ee139caef85bfed62fe92dd3a70d103a\\transformed\\material-1.11.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "106,112,123,126,127,128,129,130,133,134,135,136,137,139,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,213,214,236,238,239,240,241,242,243,244,245,246,247,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,331,333,334,340,341,342,343,344,345,346,347,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,576,577,578,606,618,619,620,621,622,623,624,625,626,627,628,629,633,634,635,636,638,647,648,649,650,651,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,692,693,704,714,728,762,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1232,1237,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1995,1996,2044,2045,2046,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2067,2070,2071,2072,2090,2091,2092,2093,2094,2095,2096,2104,2114,2115,2118,2119,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2181,2183,2253,2271,2272,2273,2274,2275,2276,2292,2293,2294,2303,2394,2397,2403,2404,2405,2437,2440,2450,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2551,2554,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2640,2641,2642,2643,2644,2681,2684,2685,2699,2700,2775,2779,2783,2787,2791,2792,2846,2854,2861,3031,3034,3044,3053,3062,3131,3132,3133,3134,3140,3141,3142,3143,3144,3145,3151,3152,3153,3154,3155,3160,3161,3165,3166,3172,3176,3177,3178,3179,3189,3190,3191,3195,3196,3202,3206,3276,3279,3280,3284,3285,3288,3289,3290,3291,3554,3561,3821,3827,4090,4097,4357,4363,4426,4508,4560,4642,4704,4786,4850,4902,4984,4992,4998,5009,5013,5017,5030,5805,5821,5828,5834,5851,5864,5884,5901,5910,5915,5922,5942,5955,5972,5978,5984,5991,5995,6001,6015,6018,6028,6029,6030,6078,6082,6086,6090,6091,6092,6095,6111,6118,6132,6177,6221,6227,6231,6235,6240,6247,6253,6254,6257,6261,6266,6279,6283,6288,6293,6298,6301,6304,6307,6311,6462,6463,6464,6465,6693,6694,6695,6696,6697,6698,6699,6700,6701,6702,6703,6704,6705,6706,6707,6708,6709,6710,6711,6715,6719,6723,6727,6731,6735,6739,6740,6741,6742,6743,6744,6745,6746,6750,6754,6755,6759,6760,6763,6767,6770,6773,6776,6783,6786,6789,6793,6797,6801,6805,6808,6809,6810,6811,6814,6818,6821,6824,6827,6830,6833,6836,6912,6915,6916,6919,6922,6923,6926,6927,6928,6932,6933,6938,6945,6952,6959,6966,6973,6980,6987,6994,7001,7010,7019,7028,7035,7044,7053,7056,7059,7060,7061,7062,7063,7064,7065,7066,7067,7068,7069,7070,7071,7075,7080,7085,7088,7089,7090,7091,7092,7100,7108,7109,7117,7121,7129,7137,7145,7153,7161,7162,7170,7178,7179,7182,7221,7223,7228,7230,7235,7239,7243,7244,7245,7246,7250,7254,7255,7259,7260,7261,7262,7263,7264,7265,7266,7267,7268,7269,7270,7271,7272,7273,7274,7278,7282,7283,7287,7288,7289,7294,7295,7296,7297,7298,7299,7300,7301,7302,7303,7304,7305,7306,7307,7308,7309,7310,7311,7312,7313,7314,7318,7319,7320,7326,7327,7331,7333,7334,7339,7340,7341,7342,7343,7344,7348,7349,7350,7356,7357,7361,7363,7367,7371,7375,7406,7407,7408,7409,7412,7415,7418,7421,7424,7429,7433,7436,7437,7442,7446,7451,7457,7463,7468,7472,7477,7481,7485,7526,7527,7528,7529,7530,7534,7535,7536,7537,7541,7545,7549,7553,7557,7561,7565,7569,7575,7576,7617,7631,7636,7662,7669,7672,7683,7688,7691,7694,7749,7755,7756,7759,7762,7765,7768,7771,7774,7777,7781,7784,7785,7786,7794,7802,7805,7810,7815,7820,7825,7829,7833,7834,7842,7843,7844,7845,7846,7854,7859,7864,7865,7866,7867,7892,7898,7903,7906,7910,7913,7917,7927,7930,7935,7938,7942,8052,8060,8074,8087,8091,8106,8117,8120,8131,8136,8140,8175,8176,8177,8189,8197,8205,8213,8221,8241,8244,8271,8276,8296,8299,8302,8309,8322,8331,8334,8354,8364,8368,8372,8385,8389,8393,8397,8403,8407,8424,8432,8436,8440,8444,8447,8451,8455,8459,8469,8476,8483,8487,8513,8523,8548,8557,8577,8587,8591,8601,8626,8636,8639,8643,8644,8645,8646,8650,8656,8662,8663,8676,8677,8678,8681,8684,8687,8690,8693,8696,8699,8702,8705,8708,8711,8714,8717,8720,8723,8726,8729,8732,8735,8738,8741,8742,8747,8748,8761,8771,8775,8780,8785,8789,8792,8796,8800,8803,8807,8810,8814,8819,8824,8827,8834,8838,8842,8851,8856,8861,8862,8866,8869,8873,8886,8891,8899,8903,8907,8924,8928,8933,8951,8958,8962,8992,8995,8998,9001,9004,9007,9010,9029,9035,9043,9050,9062,9070,9075,9080,9084,9095,9099,9107,9110,9115,9116,9117,9118,9122,9126,9130,9134,9169,9172,9176,9180,9214,9217,9221,9225,9234,9240,9243,9253,9257,9258,9265,9269,9276,9277,9278,9281,9286,9291,9292,9296,9311,9330,9334,9335,9347,9357,9358,9370,9375,9399,9402,9408,9411,9420,9428,9432,9435,9438,9441,9445,9448,9465,9469,9472,9487,9490,9498,9503,9510,9515,9516,9521,9522,9528,9534,9540,9572,9583,9600,9607,9611,9614,9627,9636,9640,9645,9649,9653,9657,9661,9665,9669,9673,9678,9681,9693,9698,9707,9710,9717,9718,9722,9731,9737,9741,9742,9746,9767,9773,9777,9781,9782,9800,9801,9802,9803,9804,9809,9812,9813,9819,9820,9832,9844,9851,9852,9857,9862,9863,9867,9881,9886,9892,9898,9904,9909,9915,9921,9922,9928,9943,9948,9957,9966,9969,9983,9988,9999,10003,10012,10021,10022,10029,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3922,4164,4617,4775,4831,4891,4952,5017,5172,5222,5272,5325,5383,5482,5852,5900,5971,6043,6115,6188,6255,6304,6358,6395,6446,6506,6553,6609,6658,6716,6770,6831,6887,6938,6998,7054,7117,7166,7222,7278,7328,7387,7442,7504,7551,7605,7661,7713,7768,7822,7876,7930,7979,8037,8091,8148,8204,8251,8304,8360,8420,8483,8542,8604,8654,8708,8762,8810,8867,8920,9544,9598,10529,10640,10702,10758,10818,10871,10932,11011,11092,11164,11243,11670,11746,11824,11893,11969,12046,12117,12190,12266,12344,12413,12489,12566,12630,12701,15214,15310,15363,15599,15666,15719,15771,15821,15879,15944,15992,22933,23000,23066,23124,23193,23251,23320,23390,23463,23537,23605,23672,23742,23808,23881,23941,24017,24077,24137,24212,24280,24346,24414,24474,24533,24590,24656,24718,24775,24843,24916,24986,25048,25109,25177,25239,25309,25378,25434,25493,25555,25617,25684,25741,25802,25863,25924,25985,26041,26097,26153,26209,26267,26325,26383,26441,26498,26555,26612,26669,26728,26787,26845,26928,27011,27084,27138,27207,27263,27344,27425,27496,27831,27884,27942,29107,29666,29712,29772,29826,29896,29966,30031,30097,30162,30230,30299,30367,30497,30550,30609,30667,30765,31151,31203,31249,31299,31355,31449,31507,31565,31627,31690,31752,31811,31871,31936,32002,32067,32129,32191,32253,32315,32377,32439,32505,32572,32638,32701,32765,32828,32896,32957,33019,33081,33144,33208,33271,33335,33413,33472,33538,33618,33679,33883,33941,34374,34825,35397,36861,40307,40381,40452,40518,40592,40661,40732,40805,40876,40944,41017,41093,41163,41241,41309,41375,41436,41505,41569,41635,41703,41769,41832,41900,41971,42036,42109,42172,42253,42317,42383,42453,42523,42593,42663,43877,43934,43992,44051,44111,44170,44229,44288,44347,44406,44465,44524,44583,44642,44701,44761,44822,44884,44945,45006,45067,45128,45189,45250,45310,45371,45432,45492,45553,45614,45675,45736,45797,45858,45919,45980,46041,46102,46163,46231,46300,46370,46439,46508,46577,46646,46715,46784,46853,46922,46991,47060,47120,47181,47243,47304,47365,47426,47487,47548,47609,47670,47731,47792,47853,47915,47978,48042,48105,48168,48231,48294,48357,48420,48483,48546,48609,48672,48733,48795,48858,48920,48982,49044,49106,49168,49230,49292,49354,49416,49478,49535,49621,49701,49791,49886,49978,50070,50160,50243,50336,50423,50520,50611,50712,50799,50902,50991,51090,51182,51282,51366,51460,51548,51646,51729,51820,51914,52013,52115,52213,52313,52400,52500,52586,52682,52770,52851,52942,53038,53131,53224,53315,53400,53494,53583,53681,53774,53876,53964,54068,54159,54259,54352,54453,54538,54633,54722,54821,54906,54998,55093,55193,55296,55395,55498,55587,55688,55775,55872,55960,56056,56148,56248,56338,56436,56521,56610,56699,56792,56879,57642,57708,57784,57853,57932,58005,58085,58165,58242,58310,58388,58464,58535,58616,58689,58772,58847,58932,59005,59086,59167,59241,59325,59395,59473,59543,59623,59701,59773,59855,59925,60002,60082,60167,60255,60339,60426,60500,60578,60656,60727,60808,60899,60982,61078,61176,61283,61348,61414,61467,61543,61609,61696,61772,70017,70300,71849,71903,71982,72060,72133,72198,72261,72327,72398,72469,72539,72601,72670,72736,72796,72863,72930,72986,73037,73090,73142,73196,73267,73330,73389,73451,73510,73583,73650,73720,73780,73843,73918,73990,74086,74157,74213,74284,74341,74398,74464,74528,74599,74656,74709,74772,74824,74882,76203,76272,76338,76397,76480,76539,76596,76663,76733,76807,76869,76938,77008,77107,77204,77303,77389,77475,77556,77631,77720,77811,77895,77954,78000,78066,78123,78190,78247,78329,78394,78460,78583,78667,78788,78853,78915,79013,79087,79170,79259,79323,79402,79476,79538,79634,79699,79758,79814,79870,79930,80037,80084,80144,80205,80269,80330,80390,80448,80491,80540,80592,80643,80695,80744,80793,80858,80924,80984,81045,81101,81160,81209,81257,81315,81372,81474,81531,81606,81654,81705,81767,81832,81884,81958,82021,82084,82152,82202,82264,82324,82381,82441,82490,82558,82664,82766,82835,82906,82962,83011,83111,83182,83292,83381,83472,83554,83652,83708,83809,83919,84018,84081,84187,84264,84376,84503,84615,84742,84812,84926,85057,85154,85222,85340,85443,85561,85622,85696,85763,85868,85990,86064,86131,86241,86340,86413,86510,86632,86750,86868,86929,87051,87168,87236,87342,87444,87524,87595,87691,87758,87832,87906,87992,88080,88170,88248,88325,88425,88496,88617,88738,88802,88927,89001,89125,89249,89316,89425,89553,89665,89744,89822,89923,89994,90116,90238,90303,90429,90541,90647,90715,90814,90918,90981,91047,91131,91244,91357,91475,91553,91625,91761,91897,91982,92122,92260,92398,92540,92622,92731,92842,92970,93098,93230,93360,93490,93624,93686,93782,93849,93966,94087,94184,94266,94353,94440,94571,94702,94837,94914,94991,95102,95216,95290,95399,95511,95613,95709,95813,95880,95974,96046,96156,96262,96335,96426,96528,96631,96726,96833,96938,97060,97182,97308,97367,97425,97549,97673,97801,97919,98037,98159,98245,98342,98476,98610,98690,98828,98960,99092,99228,99303,99379,99482,99556,99669,99750,99807,99868,99927,99987,100045,100106,100164,100214,100263,100330,100389,100448,100497,100568,100652,100722,100793,100873,100942,101005,101073,101139,101207,101272,101338,101415,101493,101599,101705,101801,101930,102019,102146,102212,102281,102367,102433,102516,102614,102710,102806,102904,103013,103108,103197,103259,103319,103384,103441,103522,103576,103633,103730,103840,103901,104016,104137,104232,104324,104417,104519,104575,104634,104683,104775,104824,104878,104932,104986,105040,105094,105149,105259,105369,105477,105587,105697,105807,105917,106025,106131,106235,106339,106443,106538,106633,106726,106819,106923,107029,107133,107237,107330,107423,107516,107609,107717,107823,107929,108035,108132,108227,108322,108417,108523,108629,108735,108841,108939,109035,109131,109229,109294,109398,109544,109608,109669,109731,109791,109856,109918,109986,110044,110107,110170,110237,110312,110385,110451,110503,110556,110608,110665,110749,110844,110929,111010,111090,111167,111246,111323,111397,111471,111542,111622,111694,111769,111834,111895,111955,112030,112187,112260,112330,112402,112472,112545,112609,112679,112725,112794,112846,112931,113014,113071,113137,113204,113270,113351,113426,113482,113535,113596,113654,113704,113753,113802,113851,113913,113965,114010,114091,114142,114196,114249,114303,114354,114403,114469,114520,114581,114642,114704,114754,114795,114872,114931,114990,115049,115110,115166,115222,115289,115350,115415,115470,115535,115604,115672,115750,115819,115879,115950,116024,116089,116161,116231,116298,116382,116451,116518,116588,116651,116718,116786,116869,116948,117038,117115,117183,117250,117328,117385,117442,117510,117576,117632,117692,117751,117805,117855,117905,117953,118015,118066,118139,118219,118299,118363,118430,118501,118559,118620,118686,118745,118812,118872,118932,118995,119063,119124,119191,119269,119339,119388,119445,119514,119575,119663,119751,119839,119927,120014,120101,120188,120275,120333,120407,120477,120533,120604,120669,120731,120806,120879,120969,121035,121101,121162,121226,121288,121346,121417,121500,121559,121630,121696,121761,121822,121881,121952,122018,122083,122166,122242,122317,122398,122458,122527,122597,122666,122721,122777,122833,122894,122952,123008,123062,123117,123179,123236,123330,123399,123500,123551,123621,123684,123740,123798,123857,123911,123997,124081,124151,124220,124290,124405,124526,124593,124660,124735,124802,124861,124915,124969,125023,125076,125128,127632,127769,130611,130660,130710,130801,130849,130905,130963,131025,131080,131138,131209,131273,131332,131394,131460,131864,132009,132053,132098,133076,133127,133174,133219,133270,133321,133372,133756,134289,134355,134534,134597,134737,134794,134848,134903,134961,135016,135075,135131,135200,135269,135338,135408,135471,135534,135597,135660,135725,135790,135855,135920,135983,136047,136111,136175,136226,136304,136382,136453,136525,136598,136670,136736,136802,136870,136938,137004,137071,137145,137208,137265,137325,137390,137457,137522,137579,137640,137698,137802,137912,138021,138125,138203,138268,138335,138401,138471,138518,138570,138746,138873,143097,144273,144404,144588,144766,145004,145193,146241,146339,146454,146980,154094,154254,154785,154874,155031,157542,157832,158627,159557,159744,159840,159930,160026,160116,160282,160405,160528,160698,160804,160919,161034,161136,161242,161359,162136,162218,162391,162559,162707,162866,163021,163194,163311,163428,163596,163708,163822,163994,164170,164328,164461,164573,164719,164871,165003,165146,166570,166748,166884,166980,167116,167211,167378,167471,167563,167750,167906,168084,168248,168430,168747,168929,169111,169301,169533,169723,169900,170062,170219,170329,170512,170649,170853,171037,171221,171381,171539,171723,171950,172153,172324,172544,172766,172921,173121,173305,173408,173598,173739,173904,174075,174275,174479,174681,174846,175051,175250,175449,175646,175737,175886,176036,176120,176269,176414,176566,176707,176873,179010,179088,179389,179555,179710,181779,181937,182101,183134,183357,187886,188163,188435,188713,188958,189020,192412,192863,193319,204456,204604,205118,205555,205989,210329,210414,210535,210634,211039,211136,211253,211340,211463,211564,211970,212069,212188,212281,212388,212731,212838,213083,213204,213613,213861,213961,214066,214185,214694,214841,214960,215211,215344,215759,216013,221228,221475,221600,221917,222038,222266,222387,222520,222667,243298,243790,264170,264594,285270,285764,306189,306615,311456,316873,320964,326395,331137,336514,340498,344490,349881,350428,350861,351617,351847,352090,353223,401393,402297,402881,403354,404784,405528,406721,407775,408253,408546,408929,410444,411209,412352,412793,413234,413830,414104,414515,415531,415709,416462,416599,416690,418884,419150,419472,419682,419791,419910,420094,421212,421682,422433,425016,427792,428168,428396,428652,428911,429487,429841,429963,430102,430394,430654,431582,431868,432271,432673,433016,433228,433429,433642,433931,445216,445289,445376,445461,457986,458098,458204,458327,458459,458582,458712,458836,458969,459100,459225,459342,459462,459594,459722,459836,459954,460067,460188,460376,460563,460744,460927,461111,461276,461458,461578,461698,461806,461916,462028,462136,462246,462411,462577,462729,462894,462995,463115,463286,463447,463610,463771,464058,464177,464294,464474,464656,464837,465020,465175,465320,465442,465577,465740,465933,466059,466211,466353,466523,466679,466851,473996,474191,474283,474456,474618,474713,474882,474976,475065,475308,475397,475690,476106,476526,476947,477373,477790,478206,478623,479041,479455,479925,480398,480870,481281,481752,482224,482414,482620,482726,482834,482940,483052,483166,483278,483392,483508,483622,483730,483840,483948,484210,484589,484993,485140,485248,485358,485466,485580,485989,486403,486519,486937,487178,487608,488043,488453,488875,489285,489407,489816,490232,490354,490572,493392,493460,493804,493884,494240,494390,494534,494610,494722,494812,495074,495339,495447,495599,495707,495783,495895,495985,496087,496195,496303,496403,496511,496596,496700,496787,496865,496979,497071,497335,497602,497712,497865,497975,498059,498448,498546,498654,498748,498878,498986,499108,499244,499352,499472,499606,499728,499856,499998,500124,500264,500390,500508,500640,500738,500848,501148,501260,501378,501842,501958,502261,502387,502483,502884,502994,503118,503256,503366,503488,503800,503924,504054,504530,504658,504973,505111,505273,505489,505645,507707,507775,507859,507963,508166,508355,508556,508749,508954,509267,509479,509645,509761,510007,510223,510536,510962,511424,511661,511813,512073,512217,512359,515591,515705,515825,515941,516035,516356,516455,516573,516674,516953,517238,517517,517799,518052,518311,518564,518820,519244,519320,522570,523925,524369,526223,526798,527006,528016,528396,528562,528703,533723,534149,534261,534396,534549,534746,534917,535100,535275,535462,535734,535892,535976,536080,536567,537123,537281,537500,537731,537954,538189,538411,538677,538815,539414,539528,539666,539778,539902,540473,540968,541514,541659,541752,541844,543771,544341,544639,544828,545034,545227,545437,546321,546466,546858,547016,547233,555642,556074,556949,557569,557766,558714,559479,559602,560375,560596,560796,562773,562873,562963,563649,564402,565167,565930,566705,567918,568083,569696,570017,571080,571290,571460,572030,572925,573558,573724,575210,575826,576062,576283,577241,577506,577771,578018,578432,578668,579953,580402,580589,580838,581080,581256,581497,581730,581955,582550,583025,583549,583810,585161,585636,586862,587332,588380,588832,589076,589533,590778,591261,591411,591755,591901,592039,592175,592463,592967,593476,593592,594494,594616,594728,594905,595171,595441,595707,595975,596231,596491,596747,597005,597257,597513,597765,598019,598251,598487,598739,598995,599247,599501,599733,599967,600079,600504,600628,601720,602535,602731,603055,603444,603796,604037,604251,604550,604742,605057,605264,605610,605910,606311,606530,606943,607180,607550,608274,608629,608898,609038,609292,609436,609713,610705,611114,611746,612092,612460,613534,613897,614297,615805,616390,616708,619243,619437,619655,619881,620093,620292,620499,621703,621998,622555,622945,623577,624054,624299,624650,624896,625656,625920,626343,626534,626913,627001,627109,627217,627530,627855,628174,628505,631208,631396,631657,631906,634490,634682,634947,635200,635732,636140,636339,636923,637158,637282,637694,637908,638310,638413,638543,638718,638970,639166,639306,639500,640511,641580,641868,641998,642775,643432,643578,644284,644522,646062,646212,646629,646794,647480,647950,648146,648237,648321,648465,648699,648866,649794,650080,650240,650855,651014,651342,651569,652081,652443,652522,652861,652966,653331,653702,654063,655937,656566,657642,658066,658319,658471,659519,660256,660459,660705,660952,661170,661412,661733,661997,662302,662525,662836,663025,663740,664009,664503,664729,665169,665328,665612,666357,666722,667027,667185,667423,668742,669140,669368,669588,669730,671020,671126,671256,671394,671518,671806,671975,672075,672360,672474,673357,674112,674551,674675,674921,675114,675248,675439,676218,676436,676727,677006,677323,677545,677840,678123,678227,678568,679384,679700,680261,680767,680972,681758,682163,682824,683013,683564,684130,684250,684652,836853,836948,837041,837104,837186,837279,837372,837459,837557,837648,837739,837827,837911,838007,838111,838211,838317,838420,838521,838625,838731,838830,838936,839038,839145,839254,839365,839496,839616,839732,839850,839949,840056,840172,840291,840419,840508,840603,840680,840769,840860,840953,841027,841124,841219,841317,841416,841520,841616,841718,841821,841921,842024,842109,842210,842308,842398,842493,842580,842686,842788,842882,842973,843067,843143,843235,843324,843427,843538,843621,843707,843802,843899,843995,844083,844184,844285,844388,844494,844592,844689,844784,844882,844985,845085,845188,845293,845411,845527,845622,845715,845800,845896,845990,846082,846184,846291,846374,846478,846583,846683,846784,846889,846989,847090,847189,847291,847385,847492,847594,847697,847790,847886,847988,848091,848187,848289,848392,848489,848592,848690,848794,848899,848996,849104,849218,849333,849441,849555,849670,849772,849877,849985,850095,850211,850328,850423,850520,850619,850724,850830,850929,851034,851140,851240,851346,851447,851554,851673,851772,851877,851979,852081,852181,852284,852379,852483,852568,852672,852776,852874,852978,853084,853182,853287,853385,853498,853592,853681,853770,853853,853944,854027,854125,854215,854311,854400,854494,854582,854678,854763,854871,854972,855073,855171,855277,855368,855467,855564,855662,855758,855851,855961,856059,856154,856264,856356,856456,856555,856642,856746,856851,856950,857057,857164,857263,857372,857464,857575,857686,857797,857901,858016,858132,858259,858379,858476,858575,858667,858766,858858,858957,859043,859137,859240,859336,859439,859535,859638,859735,859833,859936,860029,860119,860220,860303,860394,860479,860571,860674,860769,860865,860958,861052,861131,861238,861329,861428,861521,861624,861728,861829,861930,862034,862128,862232,862336,862449,862555,862661,862769,862886,862987,863095,863195,863298,863403,863510,863606,863685,863775,863859,863951,864024,864121,864203,864288,864373,864470,864563,864658,864757,864854,864945,865036,865128,865223,865330,865438,865540,865637,865734,865827,865914,865998,866095,866192,866285,866372,866463,866562,866661,866756,866845,866926,867025,867129,867226,867331,867428,867512,867611,867715,867812,867917,868014,868112,868213,868319,868418,868525,868624,868723,868814,868903,868992,869074,869167,869258,869369,869470,869570,869682,869795,869893,870001,870095,870195,870284,870376,870487,870597,870692,870808,870934,871060,871179,871307,871432,871557,871675,871802,871911,872020,872133,872256,872379,872495,872620,872717,872825,872947,873063,873179,873288,873376,873477,873566,873667,873754,873842,873939,874031,874137,874237,874313", "endLines": "106,112,123,126,127,128,129,130,133,134,135,136,137,139,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,213,214,236,238,239,240,241,242,243,244,245,246,247,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,331,333,334,340,341,342,343,344,345,346,347,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,571,576,577,578,606,618,619,620,621,622,623,624,625,626,627,628,632,633,634,635,636,638,647,648,649,650,651,653,654,655,656,657,658,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,692,693,704,714,728,762,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1051,1052,1053,1054,1055,1056,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1232,1237,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1995,1996,2044,2045,2046,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2067,2070,2071,2072,2090,2091,2092,2093,2094,2095,2096,2104,2114,2115,2118,2119,2121,2122,2123,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2181,2186,2253,2271,2272,2273,2274,2275,2276,2292,2293,2294,2303,2396,2397,2403,2404,2405,2437,2440,2450,2469,2470,2471,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2496,2497,2498,2499,2500,2501,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2539,2540,2541,2542,2543,2544,2545,2546,2547,2550,2553,2556,2557,2558,2559,2560,2561,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2640,2641,2642,2643,2644,2683,2684,2685,2699,2700,2778,2782,2786,2790,2791,2795,2853,2860,2868,3033,3043,3052,3061,3070,3131,3132,3133,3139,3140,3141,3142,3143,3144,3150,3151,3152,3153,3154,3159,3160,3164,3165,3171,3175,3176,3177,3178,3188,3189,3190,3194,3195,3201,3205,3206,3278,3279,3283,3284,3287,3288,3289,3290,3553,3560,3820,3826,4089,4096,4356,4362,4425,4507,4559,4641,4703,4785,4849,4901,4983,4991,4997,5008,5012,5016,5029,5044,5820,5827,5833,5850,5863,5883,5900,5909,5914,5921,5941,5954,5971,5977,5983,5990,5994,6000,6014,6017,6027,6028,6029,6077,6081,6085,6089,6090,6091,6094,6110,6117,6131,6176,6177,6226,6230,6234,6239,6246,6252,6253,6256,6260,6265,6278,6282,6287,6292,6297,6300,6303,6306,6310,6314,6462,6463,6464,6465,6693,6694,6695,6696,6697,6698,6699,6700,6701,6702,6703,6704,6705,6706,6707,6708,6709,6710,6714,6718,6722,6726,6730,6734,6738,6739,6740,6741,6742,6743,6744,6745,6749,6753,6754,6758,6759,6762,6766,6769,6772,6775,6779,6785,6788,6792,6796,6800,6804,6807,6808,6809,6810,6813,6817,6820,6823,6826,6829,6832,6835,6839,6914,6915,6918,6921,6922,6925,6926,6927,6931,6932,6937,6944,6951,6958,6965,6972,6979,6986,6993,7000,7009,7018,7027,7034,7043,7052,7055,7058,7059,7060,7061,7062,7063,7064,7065,7066,7067,7068,7069,7070,7074,7079,7084,7087,7088,7089,7090,7091,7099,7107,7108,7116,7120,7128,7136,7144,7152,7160,7161,7169,7177,7178,7181,7184,7222,7227,7229,7234,7238,7242,7243,7244,7245,7249,7253,7254,7258,7259,7260,7261,7262,7263,7264,7265,7266,7267,7268,7269,7270,7271,7272,7273,7277,7281,7282,7286,7287,7288,7293,7294,7295,7296,7297,7298,7299,7300,7301,7302,7303,7304,7305,7306,7307,7308,7309,7310,7311,7312,7313,7317,7318,7319,7325,7326,7330,7332,7333,7338,7339,7340,7341,7342,7343,7347,7348,7349,7355,7356,7360,7362,7366,7370,7374,7378,7406,7407,7408,7411,7414,7417,7420,7423,7428,7432,7435,7436,7441,7445,7450,7456,7462,7467,7471,7476,7480,7484,7525,7526,7527,7528,7529,7533,7534,7535,7536,7540,7544,7548,7552,7556,7560,7564,7568,7574,7575,7616,7630,7635,7661,7668,7671,7682,7687,7690,7693,7748,7754,7755,7758,7761,7764,7767,7770,7773,7776,7780,7783,7784,7785,7793,7801,7804,7809,7814,7819,7824,7828,7832,7833,7841,7842,7843,7844,7845,7853,7858,7863,7864,7865,7866,7891,7897,7902,7905,7909,7912,7916,7926,7929,7934,7937,7941,7945,8059,8073,8086,8090,8105,8116,8119,8130,8135,8139,8174,8175,8176,8188,8196,8204,8212,8220,8240,8243,8270,8275,8295,8298,8301,8308,8321,8330,8333,8353,8363,8367,8371,8384,8388,8392,8396,8402,8406,8423,8431,8435,8439,8443,8446,8450,8454,8458,8468,8475,8482,8486,8512,8522,8547,8556,8576,8586,8590,8600,8625,8635,8638,8642,8643,8644,8645,8649,8655,8661,8662,8675,8676,8677,8680,8683,8686,8689,8692,8695,8698,8701,8704,8707,8710,8713,8716,8719,8722,8725,8728,8731,8734,8737,8740,8741,8746,8747,8760,8770,8774,8779,8784,8788,8791,8795,8799,8802,8806,8809,8813,8818,8823,8826,8833,8837,8841,8850,8855,8860,8861,8865,8868,8872,8885,8890,8898,8902,8906,8923,8927,8932,8950,8957,8961,8991,8994,8997,9000,9003,9006,9009,9028,9034,9042,9049,9061,9069,9074,9079,9083,9094,9098,9106,9109,9114,9115,9116,9117,9121,9125,9129,9133,9168,9171,9175,9179,9213,9216,9220,9224,9233,9239,9242,9252,9256,9257,9264,9268,9275,9276,9277,9280,9285,9290,9291,9295,9310,9329,9333,9334,9346,9356,9357,9369,9374,9398,9401,9407,9410,9419,9427,9431,9434,9437,9440,9444,9447,9464,9468,9471,9486,9489,9497,9502,9509,9514,9515,9520,9521,9527,9533,9539,9571,9582,9599,9606,9610,9613,9626,9635,9639,9644,9648,9652,9656,9660,9664,9668,9672,9677,9680,9692,9697,9706,9709,9716,9717,9721,9730,9736,9740,9741,9745,9766,9772,9776,9780,9781,9799,9800,9801,9802,9803,9808,9811,9812,9818,9819,9831,9843,9850,9851,9856,9861,9862,9866,9880,9885,9891,9897,9903,9908,9914,9920,9921,9927,9942,9947,9956,9965,9968,9982,9987,9998,10002,10011,10020,10021,10028,10036,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,88,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,87,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,108,110,127,127,131,129,129,133,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,101,95,103,66,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,68,85,65,82,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,101,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,95,95,97,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,103,86,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,145,137,135,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,103,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,101,106,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,96,81,84,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "3973,4208,4667,4826,4886,4947,5012,5067,5217,5267,5320,5378,5426,5546,5895,5966,6038,6110,6183,6250,6299,6353,6390,6441,6501,6548,6604,6653,6711,6765,6826,6882,6933,6993,7049,7112,7161,7217,7273,7323,7382,7437,7499,7546,7600,7656,7708,7763,7817,7871,7925,7974,8032,8086,8143,8199,8246,8299,8355,8415,8478,8537,8599,8649,8703,8757,8805,8862,8915,8971,9593,9649,10587,10697,10753,10813,10866,10927,11006,11087,11159,11238,11318,11741,11819,11888,11964,12041,12112,12185,12261,12339,12408,12484,12561,12625,12696,12768,15260,15358,15413,15661,15714,15766,15816,15874,15939,15987,16038,22995,23061,23119,23188,23246,23315,23385,23458,23532,23600,23667,23737,23803,23876,23936,24012,24072,24132,24207,24275,24341,24409,24469,24528,24585,24651,24713,24770,24838,24911,24981,25043,25104,25172,25234,25304,25373,25429,25488,25550,25612,25679,25736,25797,25858,25919,25980,26036,26092,26148,26204,26262,26320,26378,26436,26493,26550,26607,26664,26723,26782,26840,26923,27006,27079,27133,27202,27258,27339,27420,27491,27620,27879,27937,27995,29160,29707,29767,29821,29891,29961,30026,30092,30157,30225,30294,30362,30492,30545,30604,30662,30714,30810,31198,31244,31294,31350,31397,31502,31560,31622,31685,31747,31806,31866,31931,31997,32062,32124,32186,32248,32310,32372,32434,32500,32567,32633,32696,32760,32823,32891,32952,33014,33076,33139,33203,33266,33330,33408,33467,33533,33613,33674,33727,33936,33987,34414,34884,35451,36918,40376,40447,40513,40587,40656,40727,40800,40871,40939,41012,41088,41158,41236,41304,41370,41431,41500,41564,41630,41698,41764,41827,41895,41966,42031,42104,42167,42248,42312,42378,42448,42518,42588,42658,42725,43929,43987,44046,44106,44165,44224,44283,44342,44401,44460,44519,44578,44637,44696,44756,44817,44879,44940,45001,45062,45123,45184,45245,45305,45366,45427,45487,45548,45609,45670,45731,45792,45853,45914,45975,46036,46097,46158,46226,46295,46365,46434,46503,46572,46641,46710,46779,46848,46917,46986,47055,47115,47176,47238,47299,47360,47421,47482,47543,47604,47665,47726,47787,47848,47910,47973,48037,48100,48163,48226,48289,48352,48415,48478,48541,48604,48667,48728,48790,48853,48915,48977,49039,49101,49163,49225,49287,49349,49411,49473,49530,49616,49696,49786,49881,49973,50065,50155,50238,50331,50418,50515,50606,50707,50794,50897,50986,51085,51177,51277,51361,51455,51543,51641,51724,51815,51909,52008,52110,52208,52308,52395,52495,52581,52677,52765,52846,52937,53033,53126,53219,53310,53395,53489,53578,53676,53769,53871,53959,54063,54154,54254,54347,54448,54533,54628,54717,54816,54901,54993,55088,55188,55291,55390,55493,55582,55683,55770,55867,55955,56051,56143,56243,56333,56431,56516,56605,56694,56787,56874,56965,57703,57779,57848,57927,58000,58080,58160,58237,58305,58383,58459,58530,58611,58684,58767,58842,58927,59000,59081,59162,59236,59320,59390,59468,59538,59618,59696,59768,59850,59920,59997,60077,60162,60250,60334,60421,60495,60573,60651,60722,60803,60894,60977,61073,61171,61278,61343,61409,61462,61538,61604,61691,61767,61843,70077,70350,71898,71977,72055,72128,72193,72256,72322,72393,72464,72534,72596,72665,72731,72791,72858,72925,72981,73032,73085,73137,73191,73262,73325,73384,73446,73505,73578,73645,73715,73775,73838,73913,73985,74081,74152,74208,74279,74336,74393,74459,74523,74594,74651,74704,74767,74819,74877,74944,76267,76333,76392,76475,76534,76591,76658,76728,76802,76864,76933,77003,77102,77199,77298,77384,77470,77551,77626,77715,77806,77890,77949,77995,78061,78118,78185,78242,78324,78389,78455,78578,78662,78783,78848,78910,79008,79082,79165,79254,79318,79397,79471,79533,79629,79694,79753,79809,79865,79925,80032,80079,80139,80200,80264,80325,80385,80443,80486,80535,80587,80638,80690,80739,80788,80853,80919,80979,81040,81096,81155,81204,81252,81310,81367,81469,81526,81601,81649,81700,81762,81827,81879,81953,82016,82079,82147,82197,82259,82319,82376,82436,82485,82553,82659,82761,82830,82901,82957,83006,83106,83177,83287,83376,83467,83549,83647,83703,83804,83914,84013,84076,84182,84259,84371,84498,84610,84737,84807,84921,85052,85149,85217,85335,85438,85556,85617,85691,85758,85863,85985,86059,86126,86236,86335,86408,86505,86627,86745,86863,86924,87046,87163,87231,87337,87439,87519,87590,87686,87753,87827,87901,87987,88075,88165,88243,88320,88420,88491,88612,88733,88797,88922,88996,89120,89244,89311,89420,89548,89660,89739,89817,89918,89989,90111,90233,90298,90424,90536,90642,90710,90809,90913,90976,91042,91126,91239,91352,91470,91548,91620,91756,91892,91977,92117,92255,92393,92535,92617,92726,92837,92965,93093,93225,93355,93485,93619,93681,93777,93844,93961,94082,94179,94261,94348,94435,94566,94697,94832,94909,94986,95097,95211,95285,95394,95506,95608,95704,95808,95875,95969,96041,96151,96257,96330,96421,96523,96626,96721,96828,96933,97055,97177,97303,97362,97420,97544,97668,97796,97914,98032,98154,98240,98337,98471,98605,98685,98823,98955,99087,99223,99298,99374,99477,99551,99664,99745,99802,99863,99922,99982,100040,100101,100159,100209,100258,100325,100384,100443,100492,100563,100647,100717,100788,100868,100937,101000,101068,101134,101202,101267,101333,101410,101488,101594,101700,101796,101925,102014,102141,102207,102276,102362,102428,102511,102609,102705,102801,102899,103008,103103,103192,103254,103314,103379,103436,103517,103571,103628,103725,103835,103896,104011,104132,104227,104319,104412,104514,104570,104629,104678,104770,104819,104873,104927,104981,105035,105089,105144,105254,105364,105472,105582,105692,105802,105912,106020,106126,106230,106334,106438,106533,106628,106721,106814,106918,107024,107128,107232,107325,107418,107511,107604,107712,107818,107924,108030,108127,108222,108317,108412,108518,108624,108730,108836,108934,109030,109126,109224,109289,109393,109451,109603,109664,109726,109786,109851,109913,109981,110039,110102,110165,110232,110307,110380,110446,110498,110551,110603,110660,110744,110839,110924,111005,111085,111162,111241,111318,111392,111466,111537,111617,111689,111764,111829,111890,111950,112025,112099,112255,112325,112397,112467,112540,112604,112674,112720,112789,112841,112926,113009,113066,113132,113199,113265,113346,113421,113477,113530,113591,113649,113699,113748,113797,113846,113908,113960,114005,114086,114137,114191,114244,114298,114349,114398,114464,114515,114576,114637,114699,114749,114790,114867,114926,114985,115044,115105,115161,115217,115284,115345,115410,115465,115530,115599,115667,115745,115814,115874,115945,116019,116084,116156,116226,116293,116377,116446,116513,116583,116646,116713,116781,116864,116943,117033,117110,117178,117245,117323,117380,117437,117505,117571,117627,117687,117746,117800,117850,117900,117948,118010,118061,118134,118214,118294,118358,118425,118496,118554,118615,118681,118740,118807,118867,118927,118990,119058,119119,119186,119264,119334,119383,119440,119509,119570,119658,119746,119834,119922,120009,120096,120183,120270,120328,120402,120472,120528,120599,120664,120726,120801,120874,120964,121030,121096,121157,121221,121283,121341,121412,121495,121554,121625,121691,121756,121817,121876,121947,122013,122078,122161,122237,122312,122393,122453,122522,122592,122661,122716,122772,122828,122889,122947,123003,123057,123112,123174,123231,123325,123394,123495,123546,123616,123679,123735,123793,123852,123906,123992,124076,124146,124215,124285,124400,124521,124588,124655,124730,124797,124856,124910,124964,125018,125071,125123,125197,127764,127904,130655,130705,130755,130844,130900,130958,131020,131075,131133,131204,131268,131327,131389,131455,131521,131902,132048,132093,132136,133122,133169,133214,133265,133316,133367,133418,133799,134350,134412,134592,134664,134789,134843,134898,134956,135011,135070,135126,135195,135264,135333,135403,135466,135529,135592,135655,135720,135785,135850,135915,135978,136042,136106,136170,136221,136299,136377,136448,136520,136593,136665,136731,136797,136865,136933,136999,137066,137140,137203,137260,137320,137385,137452,137517,137574,137635,137693,137797,137907,138016,138120,138198,138263,138330,138396,138466,138513,138565,138615,138798,139188,143242,144399,144583,144761,144999,145188,145357,146334,146449,146534,147054,154249,154314,154869,155026,155183,157690,157981,158681,159739,159835,159925,160021,160111,160277,160400,160523,160693,160799,160914,161029,161131,161237,161354,161469,162213,162386,162554,162702,162861,163016,163189,163306,163423,163591,163703,163817,163989,164165,164323,164456,164568,164714,164866,164998,165141,165263,166743,166879,166975,167111,167206,167373,167466,167558,167745,167901,168079,168243,168425,168742,168924,169106,169296,169528,169718,169895,170057,170214,170324,170507,170644,170848,171032,171216,171376,171534,171718,171945,172148,172319,172539,172761,172916,173116,173300,173403,173593,173734,173899,174070,174270,174474,174676,174841,175046,175245,175444,175641,175732,175881,176031,176115,176264,176409,176561,176702,176868,177029,179083,179384,179550,179705,179807,181932,182096,182282,183352,183477,188158,188430,188708,188953,189015,189300,192858,193314,193823,204599,205113,205550,205984,206427,210409,210530,210629,211034,211131,211248,211335,211458,211559,211965,212064,212183,212276,212383,212726,212833,213078,213199,213608,213856,213956,214061,214180,214689,214836,214955,215206,215339,215754,216008,216120,221470,221595,221912,222033,222261,222382,222515,222662,243293,243785,264165,264589,285265,285759,306184,306610,311451,316868,320959,326390,331132,336509,340493,344485,349876,350423,350856,351612,351842,352085,353218,354147,402292,402876,403349,404779,405523,406716,407770,408248,408541,408924,410439,411204,412347,412788,413229,413825,414099,414510,415526,415704,416457,416594,416685,418879,419145,419467,419677,419786,419905,420089,421207,421677,422428,425011,425106,428163,428391,428647,428906,429482,429836,429958,430097,430389,430649,431577,431863,432266,432668,433011,433223,433424,433637,433926,434211,445284,445371,445456,445555,458093,458199,458322,458454,458577,458707,458831,458964,459095,459220,459337,459457,459589,459717,459831,459949,460062,460183,460371,460558,460739,460922,461106,461271,461453,461573,461693,461801,461911,462023,462131,462241,462406,462572,462724,462889,462990,463110,463281,463442,463605,463766,463933,464172,464289,464469,464651,464832,465015,465170,465315,465437,465572,465735,465928,466054,466206,466348,466518,466674,466846,467137,474186,474278,474451,474613,474708,474877,474971,475060,475303,475392,475685,476101,476521,476942,477368,477785,478201,478618,479036,479450,479920,480393,480865,481276,481747,482219,482409,482615,482721,482829,482935,483047,483161,483273,483387,483503,483617,483725,483835,483943,484205,484584,484988,485135,485243,485353,485461,485575,485984,486398,486514,486932,487173,487603,488038,488448,488870,489280,489402,489811,490227,490349,490567,490751,493455,493799,493879,494235,494385,494529,494605,494717,494807,495069,495334,495442,495594,495702,495778,495890,495980,496082,496190,496298,496398,496506,496591,496695,496782,496860,496974,497066,497330,497597,497707,497860,497970,498054,498443,498541,498649,498743,498873,498981,499103,499239,499347,499467,499601,499723,499851,499993,500119,500259,500385,500503,500635,500733,500843,501143,501255,501373,501837,501953,502256,502382,502478,502879,502989,503113,503251,503361,503483,503795,503919,504049,504525,504653,504968,505106,505268,505484,505640,505844,507770,507854,507958,508161,508350,508551,508744,508949,509262,509474,509640,509756,510002,510218,510531,510957,511419,511656,511808,512068,512212,512354,515586,515700,515820,515936,516030,516351,516450,516568,516669,516948,517233,517512,517794,518047,518306,518559,518815,519239,519315,522565,523920,524364,526218,526793,527001,528011,528391,528557,528698,533718,534144,534256,534391,534544,534741,534912,535095,535270,535457,535729,535887,535971,536075,536562,537118,537276,537495,537726,537949,538184,538406,538672,538810,539409,539523,539661,539773,539897,540468,540963,541509,541654,541747,541839,543766,544336,544634,544823,545029,545222,545432,546316,546461,546853,547011,547228,547489,556069,556944,557564,557761,558709,559474,559597,560370,560591,560791,562768,562868,562958,563644,564397,565162,565925,566700,567913,568078,569691,570012,571075,571285,571455,572025,572920,573553,573719,575205,575821,576057,576278,577236,577501,577766,578013,578427,578663,579948,580397,580584,580833,581075,581251,581492,581725,581950,582545,583020,583544,583805,585156,585631,586857,587327,588375,588827,589071,589528,590773,591256,591406,591750,591896,592034,592170,592458,592962,593471,593587,594489,594611,594723,594900,595166,595436,595702,595970,596226,596486,596742,597000,597252,597508,597760,598014,598246,598482,598734,598990,599242,599496,599728,599962,600074,600499,600623,601715,602530,602726,603050,603439,603791,604032,604246,604545,604737,605052,605259,605605,605905,606306,606525,606938,607175,607545,608269,608624,608893,609033,609287,609431,609708,610700,611109,611741,612087,612455,613529,613892,614292,615800,616385,616703,619238,619432,619650,619876,620088,620287,620494,621698,621993,622550,622940,623572,624049,624294,624645,624891,625651,625915,626338,626529,626908,626996,627104,627212,627525,627850,628169,628500,631203,631391,631652,631901,634485,634677,634942,635195,635727,636135,636334,636918,637153,637277,637689,637903,638305,638408,638538,638713,638965,639161,639301,639495,640506,641575,641863,641993,642770,643427,643573,644279,644517,646057,646207,646624,646789,647475,647945,648141,648232,648316,648460,648694,648861,649789,650075,650235,650850,651009,651337,651564,652076,652438,652517,652856,652961,653326,653697,654058,655932,656561,657637,658061,658314,658466,659514,660251,660454,660700,660947,661165,661407,661728,661992,662297,662520,662831,663020,663735,664004,664498,664724,665164,665323,665607,666352,666717,667022,667180,667418,668737,669135,669363,669583,669725,671015,671121,671251,671389,671513,671801,671970,672070,672355,672469,673352,674107,674546,674670,674916,675109,675243,675434,676213,676431,676722,677001,677318,677540,677835,678118,678222,678563,679379,679695,680256,680762,680967,681753,682158,682819,683008,683559,684125,684245,684647,685181,836943,837036,837099,837181,837274,837367,837454,837552,837643,837734,837822,837906,838002,838106,838206,838312,838415,838516,838620,838726,838825,838931,839033,839140,839249,839360,839491,839611,839727,839845,839944,840051,840167,840286,840414,840503,840598,840675,840764,840855,840948,841022,841119,841214,841312,841411,841515,841611,841713,841816,841916,842019,842104,842205,842303,842393,842488,842575,842681,842783,842877,842968,843062,843138,843230,843319,843422,843533,843616,843702,843797,843894,843990,844078,844179,844280,844383,844489,844587,844684,844779,844877,844980,845080,845183,845288,845406,845522,845617,845710,845795,845891,845985,846077,846179,846286,846369,846473,846578,846678,846779,846884,846984,847085,847184,847286,847380,847487,847589,847692,847785,847881,847983,848086,848182,848284,848387,848484,848587,848685,848789,848894,848991,849099,849213,849328,849436,849550,849665,849767,849872,849980,850090,850206,850323,850418,850515,850614,850719,850825,850924,851029,851135,851235,851341,851442,851549,851668,851767,851872,851974,852076,852176,852279,852374,852478,852563,852667,852771,852869,852973,853079,853177,853282,853380,853493,853587,853676,853765,853848,853939,854022,854120,854210,854306,854395,854489,854577,854673,854758,854866,854967,855068,855166,855272,855363,855462,855559,855657,855753,855846,855956,856054,856149,856259,856351,856451,856550,856637,856741,856846,856945,857052,857159,857258,857367,857459,857570,857681,857792,857896,858011,858127,858254,858374,858471,858570,858662,858761,858853,858952,859038,859132,859235,859331,859434,859530,859633,859730,859828,859931,860024,860114,860215,860298,860389,860474,860566,860669,860764,860860,860953,861047,861126,861233,861324,861423,861516,861619,861723,861824,861925,862029,862123,862227,862331,862444,862550,862656,862764,862881,862982,863090,863190,863293,863398,863505,863601,863680,863770,863854,863946,864019,864116,864198,864283,864368,864465,864558,864653,864752,864849,864940,865031,865123,865218,865325,865433,865535,865632,865729,865822,865909,865993,866090,866187,866280,866367,866458,866557,866656,866751,866840,866921,867020,867124,867221,867326,867423,867507,867606,867710,867807,867912,868009,868107,868208,868314,868413,868520,868619,868718,868809,868898,868987,869069,869162,869253,869364,869465,869565,869677,869790,869888,869996,870090,870190,870279,870371,870482,870592,870687,870803,870929,871055,871174,871302,871427,871552,871670,871797,871906,872015,872128,872251,872374,872490,872615,872712,872820,872942,873058,873174,873283,873371,873472,873561,873662,873749,873837,873934,874026,874132,874232,874308,874385"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\07a96c4913de9523900a40f3d6eddd2c\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "579,772,773,790,791,1106,1107,1238,1239,1240,1241,1242,1243,1244,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2042,2043,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2097,2182,2281,2282,2283,2284,2285,2286,2287,2711,6896,6897,6902,6905,6910,8050,8051", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28000,37529,37601,38825,38890,61902,61971,70355,70425,70493,70565,70635,70696,70770,125202,125263,125324,125386,125450,125512,125573,125641,125741,125801,125867,125940,126009,126066,126118,127909,127981,128057,128122,128181,128240,128300,128360,128420,128480,128540,128600,128660,128720,128780,128840,128899,128959,129019,129079,129139,129199,129259,129319,129379,129439,129499,129558,129618,129678,129737,129796,129855,129914,129973,130541,130576,132252,132307,132370,132425,132483,132541,132602,132665,132722,132773,132823,132884,132941,133007,133041,133423,138803,145547,145614,145686,145755,145824,145898,145970,184060,472856,472973,473240,473533,473800,555503,555575", "endLines": "579,772,773,790,791,1106,1107,1238,1239,1240,1241,1242,1243,1244,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2042,2043,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2097,2182,2281,2282,2283,2284,2285,2286,2287,2711,6896,6900,6902,6908,6910,8050,8051", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "28055,37596,37684,38885,38951,61966,62029,70420,70488,70560,70630,70691,70765,70838,125258,125319,125381,125445,125507,125568,125636,125736,125796,125862,125935,126004,126061,126113,126175,127976,128052,128117,128176,128235,128295,128355,128415,128475,128535,128595,128655,128715,128775,128835,128894,128954,129014,129074,129134,129194,129254,129314,129374,129434,129494,129553,129613,129673,129732,129791,129850,129909,129968,130027,130571,130606,132302,132365,132420,132478,132536,132597,132660,132717,132768,132818,132879,132936,133002,133036,133071,133453,138868,145609,145681,145750,145819,145893,145965,146053,184126,472968,473169,473345,473729,473924,555570,555637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e2e54c84504d4b5d4bbcd5b5a658108\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "617,1319,1320,1321,1330,1331,1332,2041", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "29610,75164,75223,75271,75980,76055,76131,130475", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "29661,75218,75266,75322,76050,76126,76198,130536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5057ec80a63cbdf1b3c7fa6aaf5d6a71\\transformed\\jetified-uitoolkit-1.11.2-1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2296,2297,2298,2304,2445,2463,2637,2690,2719,2753", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "146588,146626,146683,147059,158254,159272,178734,182495,184567,186522", "endColumns": "37,56,43,49,51,49,64,47,55,39", "endOffsets": "146621,146678,146722,147104,158301,159317,178794,182538,184618,186557"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4c345bdfac77b4b56aa7e015904465a4\\transformed\\jetified-aws-android-sdk-auth-core-2.16.13\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2701", "startColumns": "4", "startOffsets": "183482", "endColumns": "90", "endOffsets": "183568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\95dc6764ddc16f8cca6dc25b8b4f1d65\\transformed\\jetified-aws-android-sdk-mobile-client-2.16.13\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2634", "startColumns": "4", "startOffsets": "178548", "endColumns": "54", "endOffsets": "178598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a44e8efa791fec526e44d72b1cad3c4\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "801,802,803,804,805,806,807,808,2317,2318,2319,2320,2321,2322,2323,2324,2326,2327,2328,2329,2330,2331,2339,2341,2342", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39437,39527,39607,39697,39787,39867,39948,40028,147916,148021,148202,148327,148434,148614,148737,148853,149123,149311,149416,149597,149722,149897,150350,150479,150541", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "39522,39602,39692,39782,39862,39943,40023,40103,148016,148197,148322,148429,148609,148732,148848,148951,149306,149411,149592,149717,149892,150040,150408,150536,150615"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d6b000a9a8e720e86578baf4ac1ca24\\transformed\\jetified-zoomvideosdk-core-1.11.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2111", "startColumns": "4", "startOffsets": "134120", "endColumns": "42", "endOffsets": "134158"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,7,14,21,35,49", "startColumns": "4,4,4,4,4,4", "startOffsets": "150,290,538,813,1166,1657", "endLines": "6,13,20,34,48,54", "endColumns": "19,19,19,20,19,19", "endOffsets": "285,533,808,1161,1652,1823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b0edbd2979ae96216d8e5e558dae44e\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1108,1981,1982,1983,1984,6901,6903,6904,6909,6911", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "62034,126888,126941,126994,127047,473174,473350,473472,473734,473929", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "62118,126936,126989,127042,127095,473235,473467,473528,473795,473991"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2796,2801,6203,6206,6211,6214,6315,6780,7379,7946,10040,10043", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189305,189587,426867,426964,427195,427356,434216,463938,505849,547494,685331,685469", "endLines": "2800,2806,6205,6210,6213,6220,6322,6782,7387,7954,10042,10045", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "189582,189864,426959,427190,427351,427787,434609,464053,506364,547842,685464,685601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e81b820efb681ef4f53988c1afa8d787\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2037,2073,2110", "startColumns": "4,4,4", "startOffsets": "130297,132141,134056", "endColumns": "56,64,63", "endOffsets": "130349,132201,134115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c94defda84e4aea81c759dab29875d1f\\transformed\\jetified-material-calendar-view-1.9.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "75,84,248,722,809,811,812,852,862,863,1105,1147,1262,1263,1264,1265,1980,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2721,3181,11323,35163,40108,40209,40260,43010,43593,43646,61848,64349,71496,71564,71631,71693,126846,161544,161599,161654,161723,161788,161845,161900,161957,162022,162078", "endLines": "83,97,256,727,809,811,812,852,862,863,1105,1147,1262,1263,1264,1265,1980,2486,2487,2488,2489,2490,2491,2492,2493,2494,2495", "endColumns": "12,12,11,11,56,50,46,61,52,54,53,57,67,66,61,60,41,54,54,68,64,56,54,56,64,55,57", "endOffsets": "3176,3577,11665,35392,40160,40255,40302,43067,43641,43696,61897,64402,71559,71626,71688,71749,126883,161594,161649,161718,161783,161840,161895,161952,162017,162073,162131"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\health_permissions.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "21", "endColumns": "12", "endOffsets": "1066"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "1828", "endLines": "74", "endColumns": "12", "endOffsets": "2716"}}]}, {"outputFile": "com.watchrx.watchrxhealth.app-mergeDebugResources-58:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ed01b5b8f9cea297e728a0087469107c\\transformed\\jetified-video-android-7.0.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "721,722,723", "startColumns": "4,4,4", "startOffsets": "35167,35213,35267", "endLines": "721,722,727", "endColumns": "45,53,11", "endOffsets": "35208,35262,35436"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b87b3f2d7275f8fdc70256821a83cc8d\\transformed\\work-runtime-2.7.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "765,766,767,769", "startColumns": "4,4,4,4", "startOffsets": "36940,37005,37075,37201", "endColumns": "64,69,63,60", "endOffsets": "37000,37070,37134,37257"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\72ede0399808c17d9b7f9da6bb0e818d\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2254", "startColumns": "4", "startOffsets": "143096", "endColumns": "82", "endOffsets": "143174"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ace4240242d7e30916f29befa3ff2d25\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2126,2331", "startColumns": "4,4", "startOffsets": "134947,149234", "endColumns": "67,166", "endOffsets": "135010,149396"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dfc6b2dc1b1e8d7695a962a47974e93a\\transformed\\jetified-activity-1.8.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2072,2113", "startColumns": "4,4", "startOffsets": "132100,134170", "endColumns": "41,59", "endOffsets": "132137,134225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\eefc612161d90b6bd0031304794c24b9\\transformed\\lifecycle-runtime-2.6.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2112", "startColumns": "4", "startOffsets": "134127", "endColumns": "42", "endOffsets": "134165"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "770,780,785,786,787,798,803,804,805,806,816,863,864,865,870,871,872,873,1115,1125,1126,1127,1128,1129,1136,1137,1138,1143,1144,1145,1148,1149,1150,1151,1152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "37262,37967,38331,38373,38423,39234,39527,39573,39620,39671,40443,43616,43656,43695,43979,44021,44066,44111,62401,63099,63146,63191,63238,63284,63733,63776,63822,64154,64199,64242,64411,64459,64500,64540,64586", "endColumns": "43,44,41,49,38,43,45,46,50,43,43,39,38,40,41,44,44,43,40,46,44,46,45,37,42,45,45,44,42,47,47,40,39,45,40", "endOffsets": "37301,38007,38368,38418,38457,39273,39568,39615,39666,39710,40482,43651,43690,43731,44016,44061,44106,44150,62437,63141,63186,63233,63279,63317,63771,63817,63863,64194,64237,64285,64454,64495,64535,64581,64622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c496671a68b8e9628512bef2587c7af2\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2114", "startColumns": "4", "startOffsets": "134230", "endColumns": "53", "endOffsets": "134279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965e73c60b960f9618dcd798b95c371f\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "240,243,1272", "startColumns": "4,4,4", "startOffsets": "10706,10870,72032", "endColumns": "55,47,51", "endOffsets": "10757,10913,72079"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\42fcd71a3362c3d3c804d610cf3f2c72\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "239,338,345,697,762,763,771,772,773,774,775,776,777,781,782,783,784,788,789,790,791,792,793,794,795,854,855,856,857,859,860,861,862,866,867,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1117,1118,1119,1120,1121,1122,1123,1124,1130,1131,1132,1133,1134,1135,1139,1140,1141,1142,1146,1147,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1322,1323,1328,1329,1330,1331,1332,1333,1334,1993,1994,1995,1996,1997,1998,1999,2000,2038,2039,2040,2041,2046,2070,2071,2080,2109,2118,2119,2122,2123,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2686,2776,2777,2778,2779,2780,2813,2821,2822,2826,2830,2841,2846,2875,2882,2886,2890,2895,2899,2903,2907,2911,2915,2919,2925,2929,2935,2939,2945,2949,2954,2958,2961,2965,2971,2975,2981,2985,2991,2994,2998,3002,3006,3010,3014,3015,3016,3017,3020,3023,3026,3029,3033,3034,3035,3036,3077,3080,3082,3084,3086,3091,3092,3096,3102,3106,3107,3109,3121,3122,3126,3132,3136,3213,3214,3218,3245,3249,3250,3254,5051,5223,5249,5420,5446,5477,5485,5491,5507,5529,5534,5539,5549,5558,5567,5571,5578,5597,5604,5605,5614,5617,5620,5624,5628,5632,5635,5636,5641,5646,5656,5661,5668,5674,5675,5678,5682,5687,5689,5691,5694,5697,5699,5703,5706,5713,5716,5719,5723,5725,5729,5731,5733,5735,5739,5747,5755,5767,5773,5782,5785,5796,5799,5800,5805,5806,6329,6398,6472,6473,6483,6492,6644,6646,6650,6653,6656,6659,6662,6665,6668,6671,6675,6678,6681,6684,6688,6691,6695,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6858,6859,6860,6861,6862,6863,6864,6865,6866,6868,6870,6871,6872,6873,6874,6875,6876,6877,6879,6880,6882,6883,6885,6887,6888,6890,6891,6892,6893,6894,6895,6897,6898,6899,6900,6901,7191,7193,7195,7197,7198,7199,7200,7201,7202,7203,7204,7205,7206,7207,7208,7209,7211,7212,7213,7214,7215,7216,7217,7219,7223,7400,7401,7402,7403,7404,7405,7409,7410,7411,7961,7963,7965,7967,7969,7971,7972,7973,7974,7976,7978,7980,7981,7982,7983,7984,7985,7986,7987,7988,7989,7990,7991,7994,7995,7996,7997,7999,8001,8002,8004,8005,8007,8009,8011,8012,8013,8014,8015,8016,8017,8018,8019,8020,8021,8022,8024,8025,8026,8027,8029,8030,8031,8032,8033,8035,8037,8039,8041,8042,8043,8044,8045,8046,8047,8048,8049,8050,8051,8052,8053,8054,8055", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "10651,15543,15828,34120,36750,36805,37306,37370,37440,37501,37576,37652,37729,38012,38097,38179,38255,38462,38539,38617,38723,38829,38908,38988,39045,43008,43082,43157,43222,43350,43410,43471,43543,43736,43803,57248,57307,57366,57425,57484,57543,57597,57651,57704,57758,57812,57866,62511,62585,62664,62737,62811,62882,62954,63026,63322,63379,63437,63510,63584,63658,63868,63940,64013,64083,64290,64350,64685,64754,64823,64893,64967,65043,65107,65184,65260,65337,65402,65471,65548,65623,65692,65760,65837,65903,65964,66061,66126,66195,66294,66365,66424,66482,66539,66598,66662,66733,66805,66877,66949,67021,67088,67156,67224,67283,67346,67410,67500,67591,67651,67717,67784,67850,67920,67984,68037,68104,68165,68232,68345,68403,68466,68531,68596,68671,68744,68816,68860,68907,68953,69002,69063,69124,69185,69247,69311,69375,69439,69504,69567,69627,69688,69754,69813,69873,69935,70006,70066,75227,75313,75605,75695,75782,75870,75952,76035,76125,127457,127509,127567,127612,127678,127742,127799,127856,130310,130367,130415,130464,130719,132004,132051,132484,134002,134441,134505,134695,134755,139725,139799,139869,139947,140001,140071,140156,140204,140250,140311,140374,140440,140504,140575,140638,140703,140767,140828,140889,140941,141014,141088,141157,141232,141306,141380,141521,182004,187722,187800,187890,187978,188074,190147,190729,190818,191065,191346,192012,192297,194106,194583,194805,195027,195303,195530,195760,195990,196220,196450,196677,197096,197322,197747,197977,198405,198624,198907,199115,199246,199473,199899,200124,200551,200772,201197,201317,201593,201894,202218,202509,202823,202960,203091,203196,203438,203605,203809,204017,204288,204400,204512,204617,206710,206924,207070,207210,207296,207644,207732,207978,208396,208645,208727,208825,209482,209582,209834,210258,210513,216403,216492,216729,218753,218995,219097,219350,354430,365111,366627,377322,378850,380607,381233,381653,382914,384179,384435,384671,385218,385712,386317,386515,387095,388463,388838,388956,389494,389651,389847,390120,390376,390546,390687,390751,391116,391483,392159,392423,392761,393114,393208,393394,393700,393962,394087,394214,394453,394664,394783,394976,395153,395608,395789,395911,396170,396283,396470,396572,396679,396808,397083,397591,398087,398964,399258,399828,399977,400709,400881,400965,401301,401393,434892,440123,445838,445900,446478,447062,455009,455122,455351,455511,455663,455834,456000,456169,456336,456499,456742,456912,457085,457256,457530,457729,457934,467420,467504,467600,467696,467794,467894,467996,468098,468200,468302,468404,468504,468600,468712,468841,468964,469095,469226,469324,469438,469532,469672,469806,469902,470014,470114,470230,470326,470438,470538,470678,470814,470978,471108,471266,471416,471557,471701,471836,471948,472098,472226,472354,472490,472622,472752,472882,472994,491034,491180,491324,491462,491528,491618,491694,491798,491888,491990,492098,492206,492306,492386,492478,492576,492686,492738,492816,492922,493014,493118,493228,493350,493513,506982,507062,507162,507252,507362,507452,507693,507787,507893,548125,548225,548337,548451,548567,548683,548777,548891,549003,549105,549225,549347,549429,549533,549653,549779,549877,549971,550059,550171,550287,550409,550521,550696,550812,550898,550990,551102,551226,551293,551419,551487,551615,551759,551887,551956,552051,552166,552279,552378,552487,552598,552709,552810,552915,553015,553145,553236,553359,553453,553565,553651,553755,553851,553939,554057,554161,554265,554391,554479,554587,554687,554777,554887,554971,555073,555157,555211,555275,555381,555467,555577,555661", "endLines": "239,338,345,697,762,763,771,772,773,774,775,776,777,781,782,783,784,788,789,790,791,792,793,794,795,854,855,856,857,859,860,861,862,866,867,1045,1046,1047,1048,1049,1050,1051,1052,1053,1054,1055,1056,1117,1118,1119,1120,1121,1122,1123,1124,1130,1131,1132,1133,1134,1135,1139,1140,1141,1142,1146,1147,1154,1155,1156,1157,1158,1159,1160,1161,1162,1163,1164,1165,1166,1167,1168,1169,1170,1171,1172,1173,1174,1175,1176,1177,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1322,1323,1328,1329,1330,1331,1332,1333,1334,1993,1994,1995,1996,1997,1998,1999,2000,2038,2039,2040,2041,2046,2070,2071,2080,2109,2118,2119,2122,2123,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2213,2214,2215,2216,2217,2218,2219,2220,2221,2222,2223,2224,2225,2226,2686,2776,2777,2778,2779,2780,2820,2821,2825,2829,2833,2845,2851,2881,2885,2889,2894,2898,2902,2906,2910,2914,2918,2924,2928,2934,2938,2944,2948,2953,2957,2960,2964,2970,2974,2980,2984,2990,2993,2997,3001,3005,3009,3013,3014,3015,3016,3019,3022,3025,3028,3032,3033,3034,3035,3036,3079,3081,3083,3085,3090,3091,3095,3101,3105,3106,3108,3120,3121,3125,3131,3135,3136,3213,3217,3244,3248,3249,3253,3281,5222,5248,5419,5445,5476,5484,5490,5506,5528,5533,5538,5548,5557,5566,5570,5577,5596,5603,5604,5613,5616,5619,5623,5627,5631,5634,5635,5640,5645,5655,5660,5667,5673,5674,5677,5681,5686,5688,5690,5693,5696,5698,5702,5705,5712,5715,5718,5722,5724,5728,5730,5732,5734,5738,5746,5754,5766,5772,5781,5784,5795,5798,5799,5804,5805,5810,6397,6467,6472,6482,6491,6492,6645,6649,6652,6655,6658,6661,6664,6667,6670,6674,6677,6680,6683,6687,6690,6694,6698,6846,6847,6848,6849,6850,6851,6852,6853,6854,6855,6856,6857,6858,6859,6860,6861,6862,6863,6864,6865,6867,6869,6870,6871,6872,6873,6874,6875,6876,6878,6879,6881,6882,6884,6886,6887,6889,6890,6891,6892,6893,6894,6896,6897,6898,6899,6900,6901,7192,7194,7196,7197,7198,7199,7200,7201,7202,7203,7204,7205,7206,7207,7208,7210,7211,7212,7213,7214,7215,7216,7218,7222,7226,7400,7401,7402,7403,7404,7408,7409,7410,7411,7962,7964,7966,7968,7970,7971,7972,7973,7975,7977,7979,7980,7981,7982,7983,7984,7985,7986,7987,7988,7989,7990,7993,7994,7995,7996,7998,8000,8001,8003,8004,8006,8008,8010,8011,8012,8013,8014,8015,8016,8017,8018,8019,8020,8021,8023,8024,8025,8026,8028,8029,8030,8031,8032,8034,8036,8038,8040,8041,8042,8043,8044,8045,8046,8047,8048,8049,8050,8051,8052,8053,8054,8055", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "10701,15583,15872,34156,36800,36862,37365,37435,37496,37571,37647,37724,37802,38092,38174,38250,38326,38534,38612,38718,38824,38903,38983,39040,39098,43077,43152,43217,43283,43405,43466,43538,43611,43798,43866,57302,57361,57420,57479,57538,57592,57646,57699,57753,57807,57861,57915,62580,62659,62732,62806,62877,62949,63021,63094,63374,63432,63505,63579,63653,63728,63935,64008,64078,64149,64345,64406,64749,64818,64888,64962,65038,65102,65179,65255,65332,65397,65466,65543,65618,65687,65755,65832,65898,65959,66056,66121,66190,66289,66360,66419,66477,66534,66593,66657,66728,66800,66872,66944,67016,67083,67151,67219,67278,67341,67405,67495,67586,67646,67712,67779,67845,67915,67979,68032,68099,68160,68227,68340,68398,68461,68526,68591,68666,68739,68811,68855,68902,68948,68997,69058,69119,69180,69242,69306,69370,69434,69499,69562,69622,69683,69749,69808,69868,69930,70001,70061,70129,75308,75395,75690,75777,75865,75947,76030,76120,76211,127504,127562,127607,127673,127737,127794,127851,127905,130362,130410,130459,130510,130748,132046,132095,132525,134029,134500,134562,134750,134807,139794,139864,139942,139996,140066,140151,140199,140245,140306,140369,140435,140499,140570,140633,140698,140762,140823,140884,140936,141009,141083,141152,141227,141301,141375,141516,141586,182052,187795,187885,187973,188069,188159,190724,190813,191060,191341,191593,192292,192685,194578,194800,195022,195298,195525,195755,195985,196215,196445,196672,197091,197317,197742,197972,198400,198619,198902,199110,199241,199468,199894,200119,200546,200767,201192,201312,201588,201889,202213,202504,202818,202955,203086,203191,203433,203600,203804,204012,204283,204395,204507,204612,204729,206919,207065,207205,207291,207639,207727,207973,208391,208640,208722,208820,209477,209577,209829,210253,210508,210602,216487,216724,218748,218990,219092,219345,221501,365106,366622,377317,378845,380602,381228,381648,382909,384174,384430,384666,385213,385707,386312,386510,387090,388458,388833,388951,389489,389646,389842,390115,390371,390541,390682,390746,391111,391478,392154,392418,392756,393109,393203,393389,393695,393957,394082,394209,394448,394659,394778,394971,395148,395603,395784,395906,396165,396278,396465,396567,396674,396803,397078,397586,398082,398959,399253,399823,399972,400704,400876,400960,401296,401388,401666,440118,445489,445895,446473,447057,447148,455117,455346,455506,455658,455829,455995,456164,456331,456494,456737,456907,457080,457251,457525,457724,457929,458259,467499,467595,467691,467789,467889,467991,468093,468195,468297,468399,468499,468595,468707,468836,468959,469090,469221,469319,469433,469527,469667,469801,469897,470009,470109,470225,470321,470433,470533,470673,470809,470973,471103,471261,471411,471552,471696,471831,471943,472093,472221,472349,472485,472617,472747,472877,472989,473129,491175,491319,491457,491523,491613,491689,491793,491883,491985,492093,492201,492301,492381,492473,492571,492681,492733,492811,492917,493009,493113,493223,493345,493508,493665,507057,507157,507247,507357,507447,507688,507782,507888,507980,548220,548332,548446,548562,548678,548772,548886,548998,549100,549220,549342,549424,549528,549648,549774,549872,549966,550054,550166,550282,550404,550516,550691,550807,550893,550985,551097,551221,551288,551414,551482,551610,551754,551882,551951,552046,552161,552274,552373,552482,552593,552704,552805,552910,553010,553140,553231,553354,553448,553560,553646,553750,553846,553934,554052,554156,554260,554386,554474,554582,554682,554772,554882,554966,555068,555152,555206,555270,555376,555462,555572,555656,555776"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2193,2194,2195,2196,2197,2198,2199,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2242,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2255,2256,2257,2258,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2283,2284,2285,2286,2294,2295,2296,2297,2301,2305,2306,2307,2308,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2338,2339,2340,2341,2342,2343,2344,2346,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2361,2362,2363,2364,2365,2366,2367,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2404,2405,2406,2407,2412,2413,2415,2417,2418,2419,2420,2421,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2437,2438,2439,2440,2441,2442,2444,2445,2447,2448,2449,2450,2452,2453,2454,2455,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2470,2471,2472,2473,2474,2491,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2641,2642,2644,2645,2651,2652,2653,2654,2655,2656,2657,2661,2662,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2692,2693,2694,2695,2697,2698,2699,2700,2701,2702,2703,2704,2708,2709,2710,2711,2712,2713,2714,2715,2716,2718,2719,2720,2721,2723,2724,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2757,2758,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "139471,139505,139539,139577,139610,139651,139686,141591,141644,141694,141735,141787,141836,141898,141961,142015,142081,142138,142190,142248,142310,142366,142408,142575,142628,142664,142720,142772,142848,142902,142945,143001,143037,143179,143221,143267,143319,143525,143578,143628,143682,143746,143802,143860,143906,143942,144020,144061,144109,144167,144215,144346,144465,144508,145640,145688,145730,145778,146336,146380,146422,146476,146817,147005,147073,147140,147188,147387,147441,147484,147698,147738,147808,147853,147902,147953,148049,148098,148145,150323,150367,150418,150462,150503,150546,150587,150691,150898,150949,150992,151036,151080,151136,151189,151245,151359,151496,151545,151702,151756,151824,151872,151953,151991,152046,152291,152333,152386,152453,152506,152561,152603,152676,152755,152816,152858,152912,152969,153025,153086,153150,153222,153306,153363,153439,153544,153604,153719,153834,153898,153963,154017,154104,154173,154232,154302,154597,154715,154887,154968,155466,155518,155669,155832,155910,155969,156005,156128,156353,156448,156497,156561,156623,156689,156759,156833,156895,156959,157452,157502,157585,157654,157711,157771,157973,158043,158264,158307,158393,158476,158584,158628,158676,158820,158964,159004,159046,159097,159149,159195,159241,159293,159341,159392,159434,159480,159600,159647,159687,159729,159795,161752,165546,165628,165710,165786,165849,165912,165963,166023,166083,166143,166195,166249,166293,166347,166393,166474,166522,166581,166669,166773,166809,177312,177350,177398,177444,177494,177540,177592,177645,177694,177756,177790,177880,177937,177985,178042,178120,178244,178303,178371,178448,178522,178556,178602,178644,178705,178778,178881,178953,179077,179129,180090,180138,180286,180331,180379,180425,180488,180691,180749,180877,180933,180982,181032,181087,181133,181181,181223,181277,181337,181383,181431,181481,181530,181570,181651,181719,181766,181802,181859,181897,181944,182565,182617,182673,182735,182821,182976,183033,183107,183176,183245,183314,183366,183851,183911,183975,184021,184066,184110,184156,184246,184286,184409,184466,184508,184587,184770,184806,184901,184941,184995,185043,185085,185123,185176,185228,185280,185332,185400,185438,185479,185552,185627,185688,185746,185800,185846,185912,185982,186018,186058,186110,186168,186243,186303,186389,186485,186707,186760,186840,186892,186943,186993,187043,187085,187141,187209,187364,187408,187452,187488,187530,187580,187632,187686", "endLines": "2193,2194,2195,2196,2197,2198,2199,2227,2228,2229,2230,2231,2232,2233,2234,2235,2236,2237,2238,2239,2240,2241,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2255,2256,2257,2258,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2283,2284,2285,2286,2294,2295,2296,2297,2301,2305,2306,2307,2308,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2338,2339,2340,2341,2342,2343,2344,2346,2349,2350,2351,2352,2353,2354,2355,2356,2357,2358,2359,2361,2362,2363,2364,2365,2366,2367,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2393,2394,2395,2396,2397,2398,2399,2404,2405,2406,2407,2412,2413,2415,2417,2418,2419,2420,2421,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2437,2438,2439,2440,2441,2442,2444,2445,2447,2448,2449,2450,2452,2453,2454,2455,2457,2458,2459,2460,2461,2462,2463,2464,2465,2466,2467,2468,2470,2471,2472,2473,2474,2491,2524,2525,2526,2527,2528,2529,2530,2531,2532,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2613,2614,2615,2616,2617,2618,2619,2620,2621,2622,2623,2624,2625,2626,2627,2628,2630,2631,2632,2633,2634,2635,2636,2637,2638,2639,2641,2642,2644,2645,2651,2652,2653,2654,2655,2656,2657,2661,2662,2664,2665,2666,2667,2668,2669,2670,2671,2672,2673,2674,2675,2676,2677,2678,2679,2680,2681,2682,2683,2684,2685,2692,2693,2694,2695,2697,2698,2699,2700,2701,2702,2703,2704,2708,2709,2710,2711,2712,2713,2714,2715,2716,2718,2719,2720,2721,2723,2724,2726,2727,2728,2729,2730,2731,2732,2733,2734,2735,2736,2737,2738,2739,2740,2741,2742,2743,2744,2745,2746,2747,2748,2749,2750,2751,2752,2753,2754,2757,2758,2760,2761,2762,2763,2764,2765,2766,2767,2768,2769,2770,2771,2772,2773,2774,2775", "endColumns": "33,33,37,32,40,34,38,52,49,40,51,48,61,62,53,65,56,51,57,61,55,41,112,52,35,55,51,75,53,42,55,35,58,41,45,51,55,52,49,53,63,55,57,45,35,77,40,47,57,47,130,118,42,42,47,41,47,46,43,41,53,42,48,67,66,47,69,53,42,213,39,69,44,48,50,95,48,46,48,43,50,43,40,42,40,40,65,50,42,43,43,55,52,55,113,136,48,118,53,67,47,80,37,54,100,41,52,66,52,54,41,72,78,60,41,53,56,55,60,63,71,83,56,75,104,59,114,114,63,64,53,86,68,58,69,69,117,171,80,37,51,68,55,77,58,35,122,142,94,48,63,61,65,69,73,61,63,63,49,82,68,56,59,48,69,66,42,85,82,55,43,47,143,84,39,41,50,51,45,45,51,47,50,41,45,69,46,39,41,65,39,69,81,81,75,62,62,50,59,59,59,51,53,43,53,45,80,47,58,87,103,35,38,37,47,45,49,45,51,52,48,61,33,89,56,47,56,77,79,58,67,76,73,33,45,41,60,72,47,71,58,51,158,47,147,44,47,45,62,62,57,54,55,48,49,54,45,47,41,53,59,45,47,49,48,39,80,67,46,35,56,37,46,59,51,55,61,37,154,56,73,68,68,68,51,45,59,63,45,44,43,45,89,39,51,56,41,78,43,35,38,39,53,47,41,37,52,51,51,51,67,37,40,72,74,60,57,53,45,65,69,35,39,51,57,74,59,85,95,123,52,39,51,50,49,49,41,55,67,154,43,43,35,41,49,51,53,35", "endOffsets": "139500,139534,139572,139605,139646,139681,139720,141639,141689,141730,141782,141831,141893,141956,142010,142076,142133,142185,142243,142305,142361,142403,142570,142623,142659,142715,142767,142843,142897,142940,142996,143032,143091,143216,143262,143314,143370,143573,143623,143677,143741,143797,143855,143901,143937,144015,144056,144104,144162,144210,144341,144460,144503,144546,145683,145725,145773,145820,146375,146417,146471,146514,146861,147068,147135,147183,147253,147436,147479,147693,147733,147803,147848,147897,147948,148044,148093,148140,148189,150362,150413,150457,150498,150541,150582,150623,150752,150944,150987,151031,151075,151131,151184,151240,151354,151491,151540,151659,151751,151819,151867,151948,151986,152041,152142,152328,152381,152448,152501,152556,152598,152671,152750,152811,152853,152907,152964,153020,153081,153145,153217,153301,153358,153434,153539,153599,153714,153829,153893,153958,154012,154099,154168,154227,154297,154367,154710,154882,154963,155001,155513,155582,155720,155905,155964,156000,156123,156266,156443,156492,156556,156618,156684,156754,156828,156890,156954,157018,157497,157580,157649,157706,157766,157815,158038,158105,158302,158388,158471,158527,158623,158671,158815,158900,158999,159041,159092,159144,159190,159236,159288,159336,159387,159429,159475,159545,159642,159682,159724,159790,159830,161817,165623,165705,165781,165844,165907,165958,166018,166078,166138,166190,166244,166288,166342,166388,166469,166517,166576,166664,166768,166804,166843,177345,177393,177439,177489,177535,177587,177640,177689,177751,177785,177875,177932,177980,178037,178115,178195,178298,178366,178443,178517,178551,178597,178639,178700,178773,178821,178948,179007,179124,179283,180133,180281,180326,180374,180420,180483,180546,180744,180799,180928,180977,181027,181082,181128,181176,181218,181272,181332,181378,181426,181476,181525,181565,181646,181714,181761,181797,181854,181892,181939,181999,182612,182668,182730,182768,182971,183028,183102,183171,183240,183309,183361,183407,183906,183970,184016,184061,184105,184151,184241,184281,184333,184461,184503,184582,184626,184801,184840,184936,184990,185038,185080,185118,185171,185223,185275,185327,185395,185433,185474,185547,185622,185683,185741,185795,185841,185907,185977,186013,186053,186105,186163,186238,186298,186384,186480,186604,186755,186795,186887,186938,186988,187038,187080,187136,187204,187359,187403,187447,187483,187525,187575,187627,187681,187717"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1235,1236,1237,1239,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1324,1335,1721,1722,1760,1761,1974,1975,1976,1977,1978,1991,1992", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "70134,70192,70248,70360,71121,71159,71197,71235,71273,71311,71349,71387,71425,71463,71501,71539,71575,71613,71649,71686,71738,75400,76216,109734,109779,112382,112421,126458,126505,126557,126602,126647,127378,127421", "endColumns": "57,55,46,47,37,37,37,37,37,37,37,37,37,37,37,35,37,35,36,51,35,41,41,44,42,38,43,46,51,44,44,43,42,35", "endOffsets": "70187,70243,70290,70403,71154,71192,71230,71268,71306,71344,71382,71420,71458,71496,71534,71570,71608,71644,71681,71733,71769,75437,76253,109774,109817,112416,112460,126500,126552,126597,126642,126686,127416,127452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\87f5b861fcf03439e943ebcc6db5de8c\\transformed\\jetified-window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "645,650,651,652,2042", "startColumns": "4,4,4,4,4", "startOffsets": "31093,31272,31332,31384,130515", "endLines": "649,650,651,652,2042", "endColumns": "11,59,51,44,59", "endOffsets": "31267,31327,31379,31424,130570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b86d17340e149f32b42d8e4b4155161e\\transformed\\jetified-Pinview-v1.4\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "341", "startColumns": "4", "startOffsets": "15696", "endLines": "344", "endColumns": "11", "endOffsets": "15823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\98720b29c7f34fbfef5d30a8961d4610\\transformed\\preference-1.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "764,1116,1979,1980,1981,1982,1983,1984,1985,2067,2068,2069,2185,2186,2360,2408,2629,2658,2722,2755,2756,6184,6493,6496,6502,6508,6511,6517,6521,6524,6531,6537,6540,6546,6551,6556,6563,6565,6571,6577,6585,6590,6597,6602,6608,6612,6619,6623,6629,6635,6638,6642,6643", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "36867,62442,126691,126755,126810,126878,126945,127010,127067,131847,131895,131943,138898,138961,151664,155006,178200,180551,184631,186609,186659,425389,447153,447258,447503,447841,447987,448327,448539,448702,449109,449447,449570,449909,450148,450405,450776,450836,451174,451460,451909,452201,452589,452894,453238,453483,453813,454020,454288,454561,454705,454906,454953", "endLines": "764,1116,1979,1980,1981,1982,1983,1984,1985,2067,2068,2069,2185,2186,2360,2408,2629,2660,2722,2755,2756,6200,6495,6501,6507,6510,6516,6520,6523,6530,6536,6539,6545,6550,6555,6562,6564,6570,6576,6584,6589,6596,6601,6607,6611,6618,6622,6628,6634,6637,6641,6642,6643", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "36935,62506,126750,126805,126873,126940,127005,127062,127119,131890,131938,131999,138956,139019,151697,155058,178239,180686,184765,186654,186702,426822,447253,447498,447836,447982,448322,448534,448697,449104,449442,449565,449904,450143,450400,450771,450831,451169,451455,451904,452196,452584,452889,453233,453478,453808,454015,454283,454556,454700,454901,454948,455004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\29c63916f894d15cc031166656a2aa2e\\transformed\\jetified-firebase-messaging-24.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2414", "startColumns": "4", "startOffsets": "155587", "endColumns": "81", "endOffsets": "155664"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\267985dc04d40e215387f1f013ce0e5f\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "211,10043", "startColumns": "4,4", "startOffsets": "9475,685464", "endLines": "211,10045", "endColumns": "60,12", "endOffsets": "9531,685604"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "7394,7395,7399", "startColumns": "4,4,4", "startOffsets": "506647,506742,506897", "endLines": "7394,7398,7399", "endColumns": "94,12,84", "endOffsets": "506737,506892,506977"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2368,2416,2422,2433,2434,2435,2436,2663", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "152147,155725,156271,157023,157127,157236,157356,180804", "endColumns": "143,106,81,103,108,119,95,72", "endOffsets": "152286,155827,156348,157122,157231,157351,157447,180872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\dc65449114092406ba100a0c99207fb8\\transformed\\gridlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1273", "startColumns": "4", "startOffsets": "72084", "endColumns": "42", "endOffsets": "72122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0f533fcbc46a57fb9ff7758295e66825\\transformed\\constraintlayout-2.2.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "104,111,113,119,120,128,130,131,138,144,146,147,148,149,150,207,208,209,210,212,216,217,218,221,231,241,278,279,284,285,290,295,296,297,302,303,308,309,314,315,316,322,323,324,329,335,336,354,355,361,362,363,364,367,370,373,374,377,380,381,382,383,384,387,390,391,392,393,399,404,407,410,411,412,417,418,419,422,425,426,429,432,435,438,439,440,443,446,447,452,453,459,464,467,470,471,472,473,474,475,476,477,478,479,480,481,497,578,579,580,581,586,593,601,602,603,606,611,613,621,622,643,658,695,696,700,701,711,712,713,719,735,741,745,746,747,748,749,758,2053,2111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3860,4144,4256,4491,4552,4843,4950,5000,5402,5709,5829,5884,5944,6009,6068,9254,9306,9367,9429,9536,9669,9721,9771,9932,10339,10762,13051,13110,13307,13364,13559,13740,13794,13851,14043,14101,14297,14353,14547,14604,14655,14877,14929,14984,15174,15390,15440,16321,16377,16583,16644,16704,16774,16907,17038,17166,17234,17363,17489,17551,17614,17682,17749,17872,17997,18064,18129,18194,18483,18664,18785,18906,18972,19039,19249,19318,19384,19509,19635,19702,19828,19955,20080,20207,20263,20328,20454,20577,20642,20850,20917,21205,21385,21505,21625,21690,21752,21814,21878,21940,21999,22059,22120,22181,22240,22300,22960,27903,27954,28003,28051,28338,28630,28938,28985,29045,29151,29331,29443,29778,29832,30997,31680,34010,34061,34270,34322,34697,34756,34810,35048,35734,35936,36075,36121,36176,36221,36265,36613,131038,134082", "endLines": "110,111,117,119,127,128,130,131,138,144,146,147,148,149,150,207,208,209,210,215,216,217,218,230,238,241,278,283,284,289,294,295,296,301,302,307,308,313,314,315,321,322,323,328,334,335,336,354,360,361,362,363,366,369,372,373,376,379,380,381,382,383,386,389,390,391,392,398,403,406,409,410,411,416,417,418,421,424,425,428,431,434,437,438,439,442,445,446,451,452,458,463,466,469,470,471,472,473,474,475,476,477,478,479,480,496,502,578,579,580,581,592,600,601,602,605,610,611,620,621,622,643,658,695,696,700,709,711,712,718,719,740,744,745,746,747,748,757,761,2053,2111", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "4139,4195,4437,4547,4838,4890,4995,5048,5445,5755,5879,5939,6004,6063,6125,9301,9362,9424,9470,9664,9716,9766,9817,10334,10646,10802,13105,13302,13359,13554,13735,13789,13846,14038,14096,14292,14348,14542,14599,14650,14872,14924,14979,15169,15385,15435,15487,16372,16578,16639,16699,16769,16902,17033,17161,17229,17358,17484,17546,17609,17677,17744,17867,17992,18059,18124,18189,18478,18659,18780,18901,18967,19034,19244,19313,19379,19504,19630,19697,19823,19950,20075,20202,20258,20323,20449,20572,20637,20845,20912,21200,21380,21500,21620,21685,21747,21809,21873,21935,21994,22054,22115,22176,22235,22295,22955,23206,27949,27998,28046,28104,28625,28933,28980,29040,29146,29326,29380,29773,29827,29883,31038,31722,34056,34115,34317,34647,34751,34805,35043,35098,35931,36070,36116,36171,36216,36260,36608,36745,131074,134122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8bab69c72513635a63b5299e261d854f\\transformed\\lifecycle-viewmodel-2.6.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2115", "startColumns": "4", "startOffsets": "134284", "endColumns": "49", "endOffsets": "134329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a5bf5e5c6ef9e8b277937fda46aabee7\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "137,799,800,801,802,1240,1241,1242,2834,6201,6203,6206", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5350,39278,39339,39401,39463,70408,70467,70524,191598,426827,426891,427017", "endLines": "137,799,800,801,802,1240,1241,1242,2840,6202,6205,6208", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "5397,39334,39396,39458,39522,70462,70519,70573,192007,426886,427012,427140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7ed42c12cb279249327ac70e76bb72eb\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2044,2045,2066,2074,2075,2104,2105,2106,2107,2108", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "130632,130672,131804,132185,132240,133736,133790,133842,133891,133952", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "130667,130714,131842,132235,132282,133785,133837,133886,133947,133997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ee139caef85bfed62fe92dd3a70d103a\\transformed\\material-1.11.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "112,118,129,132,133,134,135,136,139,140,141,142,143,145,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,219,220,242,244,245,246,247,248,249,250,251,252,253,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,337,339,340,346,347,348,349,350,351,352,353,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,582,583,584,612,624,625,626,627,628,629,630,631,632,633,634,635,639,640,641,642,644,653,654,655,656,657,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,698,699,710,720,734,768,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1238,1243,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,2001,2002,2050,2051,2052,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2073,2076,2077,2078,2096,2097,2098,2099,2100,2101,2102,2110,2120,2121,2124,2125,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2187,2189,2259,2277,2278,2279,2280,2281,2282,2298,2299,2300,2309,2400,2403,2409,2410,2411,2443,2446,2456,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2545,2546,2547,2548,2549,2550,2551,2552,2553,2554,2557,2560,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2646,2647,2648,2649,2650,2687,2690,2691,2705,2706,2781,2785,2789,2793,2797,2798,2852,2860,2867,3037,3040,3050,3059,3068,3137,3138,3139,3140,3146,3147,3148,3149,3150,3151,3157,3158,3159,3160,3161,3166,3167,3171,3172,3178,3182,3183,3184,3185,3195,3196,3197,3201,3202,3208,3212,3282,3285,3286,3290,3291,3294,3295,3296,3297,3560,3567,3827,3833,4096,4103,4363,4369,4432,4514,4566,4648,4710,4792,4856,4908,4990,4998,5004,5015,5019,5023,5036,5811,5827,5834,5840,5857,5870,5890,5907,5916,5921,5928,5948,5961,5978,5984,5990,5997,6001,6007,6021,6024,6034,6035,6036,6084,6088,6092,6096,6097,6098,6101,6117,6124,6138,6183,6227,6233,6237,6241,6246,6253,6259,6260,6263,6267,6272,6285,6289,6294,6299,6304,6307,6310,6313,6317,6468,6469,6470,6471,6699,6700,6701,6702,6703,6704,6705,6706,6707,6708,6709,6710,6711,6712,6713,6714,6715,6716,6717,6721,6725,6729,6733,6737,6741,6745,6746,6747,6748,6749,6750,6751,6752,6756,6760,6761,6765,6766,6769,6773,6776,6779,6782,6789,6792,6795,6799,6803,6807,6811,6814,6815,6816,6817,6820,6824,6827,6830,6833,6836,6839,6842,6918,6921,6922,6925,6928,6929,6932,6933,6934,6938,6939,6944,6951,6958,6965,6972,6979,6986,6993,7000,7007,7016,7025,7034,7041,7050,7059,7062,7065,7066,7067,7068,7069,7070,7071,7072,7073,7074,7075,7076,7077,7081,7086,7091,7094,7095,7096,7097,7098,7106,7114,7115,7123,7127,7135,7143,7151,7159,7167,7168,7176,7184,7185,7188,7227,7229,7234,7236,7241,7245,7249,7250,7251,7252,7256,7260,7261,7265,7266,7267,7268,7269,7270,7271,7272,7273,7274,7275,7276,7277,7278,7279,7280,7284,7288,7289,7293,7294,7295,7300,7301,7302,7303,7304,7305,7306,7307,7308,7309,7310,7311,7312,7313,7314,7315,7316,7317,7318,7319,7320,7324,7325,7326,7332,7333,7337,7339,7340,7345,7346,7347,7348,7349,7350,7354,7355,7356,7362,7363,7367,7369,7373,7377,7381,7412,7413,7414,7415,7418,7421,7424,7427,7430,7435,7439,7442,7443,7448,7452,7457,7463,7469,7474,7478,7483,7487,7491,7532,7533,7534,7535,7536,7540,7541,7542,7543,7547,7551,7555,7559,7563,7567,7571,7575,7581,7582,7623,7637,7642,7668,7675,7678,7689,7694,7697,7700,7755,7761,7762,7765,7768,7771,7774,7777,7780,7783,7787,7790,7791,7792,7800,7808,7811,7816,7821,7826,7831,7835,7839,7840,7848,7849,7850,7851,7852,7860,7865,7870,7871,7872,7873,7898,7904,7909,7912,7916,7919,7923,7933,7936,7941,7944,7948,8058,8066,8080,8093,8097,8112,8123,8126,8137,8142,8146,8181,8182,8183,8195,8203,8211,8219,8227,8247,8250,8277,8282,8302,8305,8308,8315,8328,8337,8340,8360,8370,8374,8378,8391,8395,8399,8403,8409,8413,8430,8438,8442,8446,8450,8453,8457,8461,8465,8475,8482,8489,8493,8519,8529,8554,8563,8583,8593,8597,8607,8632,8642,8645,8649,8650,8651,8652,8656,8662,8668,8669,8682,8683,8684,8687,8690,8693,8696,8699,8702,8705,8708,8711,8714,8717,8720,8723,8726,8729,8732,8735,8738,8741,8744,8747,8748,8753,8754,8767,8777,8781,8786,8791,8795,8798,8802,8806,8809,8813,8816,8820,8825,8830,8833,8840,8844,8848,8857,8862,8867,8868,8872,8875,8879,8892,8897,8905,8909,8913,8930,8934,8939,8957,8964,8968,8998,9001,9004,9007,9010,9013,9016,9035,9041,9049,9056,9068,9076,9081,9086,9090,9101,9105,9113,9116,9121,9122,9123,9124,9128,9132,9136,9140,9175,9178,9182,9186,9220,9223,9227,9231,9240,9246,9249,9259,9263,9264,9271,9275,9282,9283,9284,9287,9292,9297,9298,9302,9317,9336,9340,9341,9353,9363,9364,9376,9381,9405,9408,9414,9417,9426,9434,9438,9441,9444,9447,9451,9454,9471,9475,9478,9493,9496,9504,9509,9516,9521,9522,9527,9528,9534,9540,9546,9578,9589,9606,9613,9617,9620,9633,9642,9646,9651,9655,9659,9663,9667,9671,9675,9679,9684,9687,9699,9704,9713,9716,9723,9724,9728,9737,9743,9747,9748,9752,9773,9779,9783,9787,9788,9806,9807,9808,9809,9810,9815,9818,9819,9825,9826,9838,9850,9857,9858,9863,9868,9869,9873,9887,9892,9898,9904,9910,9915,9921,9927,9928,9934,9949,9954,9963,9972,9975,9989,9994,10005,10009,10018,10027,10028,10035,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4200,4442,4895,5053,5109,5169,5230,5295,5450,5500,5550,5603,5661,5760,6130,6178,6249,6321,6393,6466,6533,6582,6636,6673,6724,6784,6831,6887,6936,6994,7048,7109,7165,7216,7276,7332,7395,7444,7500,7556,7606,7665,7720,7782,7829,7883,7939,7991,8046,8100,8154,8208,8257,8315,8369,8426,8482,8529,8582,8638,8698,8761,8820,8882,8932,8986,9040,9088,9145,9198,9822,9876,10807,10918,10980,11036,11096,11149,11210,11289,11370,11442,11521,11948,12024,12102,12171,12247,12324,12395,12468,12544,12622,12691,12767,12844,12908,12979,15492,15588,15641,15877,15944,15997,16049,16099,16157,16222,16270,23211,23278,23344,23402,23471,23529,23598,23668,23741,23815,23883,23950,24020,24086,24159,24219,24295,24355,24415,24490,24558,24624,24692,24752,24811,24868,24934,24996,25053,25121,25194,25264,25326,25387,25455,25517,25587,25656,25712,25771,25833,25895,25962,26019,26080,26141,26202,26263,26319,26375,26431,26487,26545,26603,26661,26719,26776,26833,26890,26947,27006,27065,27123,27206,27289,27362,27416,27485,27541,27622,27703,27774,28109,28162,28220,29385,29944,29990,30050,30104,30174,30244,30309,30375,30440,30508,30577,30645,30775,30828,30887,30945,31043,31429,31481,31527,31577,31633,31727,31785,31843,31905,31968,32030,32089,32149,32214,32280,32345,32407,32469,32531,32593,32655,32717,32783,32850,32916,32979,33043,33106,33174,33235,33297,33359,33422,33486,33549,33613,33691,33750,33816,33896,33957,34161,34219,34652,35103,35675,37139,40585,40659,40730,40796,40870,40939,41010,41083,41154,41222,41295,41371,41441,41519,41587,41653,41714,41783,41847,41913,41981,42047,42110,42178,42249,42314,42387,42450,42531,42595,42661,42731,42801,42871,42941,44155,44212,44270,44329,44389,44448,44507,44566,44625,44684,44743,44802,44861,44920,44979,45039,45100,45162,45223,45284,45345,45406,45467,45528,45588,45649,45710,45770,45831,45892,45953,46014,46075,46136,46197,46258,46319,46380,46441,46509,46578,46648,46717,46786,46855,46924,46993,47062,47131,47200,47269,47338,47398,47459,47521,47582,47643,47704,47765,47826,47887,47948,48009,48070,48131,48193,48256,48320,48383,48446,48509,48572,48635,48698,48761,48824,48887,48950,49011,49073,49136,49198,49260,49322,49384,49446,49508,49570,49632,49694,49756,49813,49899,49979,50069,50164,50256,50348,50438,50521,50614,50701,50798,50889,50990,51077,51180,51269,51368,51460,51560,51644,51738,51826,51924,52007,52098,52192,52291,52393,52491,52591,52678,52778,52864,52960,53048,53129,53220,53316,53409,53502,53593,53678,53772,53861,53959,54052,54154,54242,54346,54437,54537,54630,54731,54816,54911,55000,55099,55184,55276,55371,55471,55574,55673,55776,55865,55966,56053,56150,56238,56334,56426,56526,56616,56714,56799,56888,56977,57070,57157,57920,57986,58062,58131,58210,58283,58363,58443,58520,58588,58666,58742,58813,58894,58967,59050,59125,59210,59283,59364,59445,59519,59603,59673,59751,59821,59901,59979,60051,60133,60203,60280,60360,60445,60533,60617,60704,60778,60856,60934,61005,61086,61177,61260,61356,61454,61561,61626,61692,61745,61821,61887,61974,62050,70295,70578,72127,72181,72260,72338,72411,72476,72539,72605,72676,72747,72817,72879,72948,73014,73074,73141,73208,73264,73315,73368,73420,73474,73545,73608,73667,73729,73788,73861,73928,73998,74058,74121,74196,74268,74364,74435,74491,74562,74619,74676,74742,74806,74877,74934,74987,75050,75102,75160,76481,76550,76616,76675,76758,76817,76874,76941,77011,77085,77147,77216,77286,77385,77482,77581,77667,77753,77834,77909,77998,78089,78173,78232,78278,78344,78401,78468,78525,78607,78672,78738,78861,78945,79066,79131,79193,79291,79365,79448,79537,79601,79680,79754,79816,79912,79977,80036,80092,80148,80208,80315,80362,80422,80483,80547,80608,80668,80726,80769,80818,80870,80921,80973,81022,81071,81136,81202,81262,81323,81379,81438,81487,81535,81593,81650,81752,81809,81884,81932,81983,82045,82110,82162,82236,82299,82362,82430,82480,82542,82602,82659,82719,82768,82836,82942,83044,83113,83184,83240,83289,83389,83460,83570,83659,83750,83832,83930,83986,84087,84197,84296,84359,84465,84542,84654,84781,84893,85020,85090,85204,85335,85432,85500,85618,85721,85839,85900,85974,86041,86146,86268,86342,86409,86519,86618,86691,86788,86910,87028,87146,87207,87329,87446,87514,87620,87722,87802,87873,87969,88036,88110,88184,88270,88358,88448,88526,88603,88703,88774,88895,89016,89080,89205,89279,89403,89527,89594,89703,89831,89943,90022,90100,90201,90272,90394,90516,90581,90707,90819,90925,90993,91092,91196,91259,91325,91409,91522,91635,91753,91831,91903,92039,92175,92260,92400,92538,92676,92818,92900,93009,93120,93248,93376,93508,93638,93768,93902,93964,94060,94127,94244,94365,94462,94544,94631,94718,94849,94980,95115,95192,95269,95380,95494,95568,95677,95789,95891,95987,96091,96158,96252,96324,96434,96540,96613,96704,96806,96909,97004,97111,97216,97338,97460,97586,97645,97703,97827,97951,98079,98197,98315,98437,98523,98620,98754,98888,98968,99106,99238,99370,99506,99581,99657,99760,99834,99947,100028,100085,100146,100205,100265,100323,100384,100442,100492,100541,100608,100667,100726,100775,100846,100930,101000,101071,101151,101220,101283,101351,101417,101485,101550,101616,101693,101771,101877,101983,102079,102208,102297,102424,102490,102559,102645,102711,102794,102892,102988,103084,103182,103291,103386,103475,103537,103597,103662,103719,103800,103854,103911,104008,104118,104179,104294,104415,104510,104602,104695,104797,104853,104912,104961,105053,105102,105156,105210,105264,105318,105372,105427,105537,105647,105755,105865,105975,106085,106195,106303,106409,106513,106617,106721,106816,106911,107004,107097,107201,107307,107411,107515,107608,107701,107794,107887,107995,108101,108207,108313,108410,108505,108600,108695,108801,108907,109013,109119,109217,109313,109409,109507,109572,109676,109822,109886,109947,110009,110069,110134,110196,110264,110322,110385,110448,110515,110590,110663,110729,110781,110834,110886,110943,111027,111122,111207,111288,111368,111445,111524,111601,111675,111749,111820,111900,111972,112047,112112,112173,112233,112308,112465,112538,112608,112680,112750,112823,112887,112957,113003,113072,113124,113209,113292,113349,113415,113482,113548,113629,113704,113760,113813,113874,113932,113982,114031,114080,114129,114191,114243,114288,114369,114420,114474,114527,114581,114632,114681,114747,114798,114859,114920,114982,115032,115073,115150,115209,115268,115327,115388,115444,115500,115567,115628,115693,115748,115813,115882,115950,116028,116097,116157,116228,116302,116367,116439,116509,116576,116660,116729,116796,116866,116929,116996,117064,117147,117226,117316,117393,117461,117528,117606,117663,117720,117788,117854,117910,117970,118029,118083,118133,118183,118231,118293,118344,118417,118497,118577,118641,118708,118779,118837,118898,118964,119023,119090,119150,119210,119273,119341,119402,119469,119547,119617,119666,119723,119792,119853,119941,120029,120117,120205,120292,120379,120466,120553,120611,120685,120755,120811,120882,120947,121009,121084,121157,121247,121313,121379,121440,121504,121566,121624,121695,121778,121837,121908,121974,122039,122100,122159,122230,122296,122361,122444,122520,122595,122676,122736,122805,122875,122944,122999,123055,123111,123172,123230,123286,123340,123395,123457,123514,123608,123677,123778,123829,123899,123962,124018,124076,124135,124189,124275,124359,124429,124498,124568,124683,124804,124871,124938,125013,125080,125139,125193,125247,125301,125354,125406,127910,128047,130889,130938,130988,131079,131127,131183,131241,131303,131358,131416,131487,131551,131610,131672,131738,132142,132287,132331,132376,133354,133405,133452,133497,133548,133599,133650,134034,134567,134633,134812,134875,135015,135072,135126,135181,135239,135294,135353,135409,135478,135547,135616,135686,135749,135812,135875,135938,136003,136068,136133,136198,136261,136325,136389,136453,136504,136582,136660,136731,136803,136876,136948,137014,137080,137148,137216,137282,137349,137423,137486,137543,137603,137668,137735,137800,137857,137918,137976,138080,138190,138299,138403,138481,138546,138613,138679,138749,138796,138848,139024,139151,143375,144551,144682,144866,145044,145282,145471,146519,146617,146732,147258,154372,154532,155063,155152,155309,157820,158110,158905,159835,160022,160118,160208,160304,160394,160560,160683,160806,160976,161082,161197,161312,161414,161520,161637,162414,162496,162669,162837,162985,163144,163299,163472,163589,163706,163874,163986,164100,164272,164448,164606,164739,164851,164997,165149,165281,165424,166848,167026,167162,167258,167394,167489,167656,167749,167841,168028,168184,168362,168526,168708,169025,169207,169389,169579,169811,170001,170178,170340,170497,170607,170790,170927,171131,171315,171499,171659,171817,172001,172228,172431,172602,172822,173044,173199,173399,173583,173686,173876,174017,174182,174353,174553,174757,174959,175124,175329,175528,175727,175924,176015,176164,176314,176398,176547,176692,176844,176985,177151,179288,179366,179667,179833,179988,182057,182215,182379,183412,183635,188164,188441,188713,188991,189236,189298,192690,193141,193597,204734,204882,205396,205833,206267,210607,210692,210813,210912,211317,211414,211531,211618,211741,211842,212248,212347,212466,212559,212666,213009,213116,213361,213482,213891,214139,214239,214344,214463,214972,215119,215238,215489,215622,216037,216291,221506,221753,221878,222195,222316,222544,222665,222798,222945,243576,244068,264448,264872,285548,286042,306467,306893,311734,317151,321242,326673,331415,336792,340776,344768,350159,350706,351139,351895,352125,352368,353501,401671,402575,403159,403632,405062,405806,406999,408053,408531,408824,409207,410722,411487,412630,413071,413512,414108,414382,414793,415809,415987,416740,416877,416968,419162,419428,419750,419960,420069,420188,420372,421490,421960,422711,425294,428070,428446,428674,428930,429189,429765,430119,430241,430380,430672,430932,431860,432146,432549,432951,433294,433506,433707,433920,434209,445494,445567,445654,445739,458264,458376,458482,458605,458737,458860,458990,459114,459247,459378,459503,459620,459740,459872,460000,460114,460232,460345,460466,460654,460841,461022,461205,461389,461554,461736,461856,461976,462084,462194,462306,462414,462524,462689,462855,463007,463172,463273,463393,463564,463725,463888,464049,464336,464455,464572,464752,464934,465115,465298,465453,465598,465720,465855,466018,466211,466337,466489,466631,466801,466957,467129,474274,474469,474561,474734,474896,474991,475160,475254,475343,475586,475675,475968,476384,476804,477225,477651,478068,478484,478901,479319,479733,480203,480676,481148,481559,482030,482502,482692,482898,483004,483112,483218,483330,483444,483556,483670,483786,483900,484008,484118,484226,484488,484867,485271,485418,485526,485636,485744,485858,486267,486681,486797,487215,487456,487886,488321,488731,489153,489563,489685,490094,490510,490632,490850,493670,493738,494082,494162,494518,494668,494812,494888,495000,495090,495352,495617,495725,495877,495985,496061,496173,496263,496365,496473,496581,496681,496789,496874,496978,497065,497143,497257,497349,497613,497880,497990,498143,498253,498337,498726,498824,498932,499026,499156,499264,499386,499522,499630,499750,499884,500006,500134,500276,500402,500542,500668,500786,500918,501016,501126,501426,501538,501656,502120,502236,502539,502665,502761,503162,503272,503396,503534,503644,503766,504078,504202,504332,504808,504936,505251,505389,505551,505767,505923,507985,508053,508137,508241,508444,508633,508834,509027,509232,509545,509757,509923,510039,510285,510501,510814,511240,511702,511939,512091,512351,512495,512637,515869,515983,516103,516219,516313,516634,516733,516851,516952,517231,517516,517795,518077,518330,518589,518842,519098,519522,519598,522848,524203,524647,526501,527076,527284,528294,528674,528840,528981,534001,534427,534539,534674,534827,535024,535195,535378,535553,535740,536012,536170,536254,536358,536845,537401,537559,537778,538009,538232,538467,538689,538955,539093,539692,539806,539944,540056,540180,540751,541246,541792,541937,542030,542122,544049,544619,544917,545106,545312,545505,545715,546599,546744,547136,547294,547511,555920,556352,557227,557847,558044,558992,559757,559880,560653,560874,561074,563051,563151,563241,563927,564680,565445,566208,566983,568196,568361,569974,570295,571358,571568,571738,572308,573203,573836,574002,575488,576104,576340,576561,577519,577784,578049,578296,578710,578946,580231,580680,580867,581116,581358,581534,581775,582008,582233,582828,583303,583827,584088,585439,585914,587140,587610,588658,589110,589354,589811,591056,591539,591689,592033,592179,592317,592453,592741,593245,593754,593870,594772,594894,595006,595183,595449,595719,595985,596253,596509,596769,597025,597283,597535,597791,598043,598297,598529,598765,599017,599273,599525,599779,600011,600245,600357,600782,600906,601998,602813,603009,603333,603722,604074,604315,604529,604828,605020,605335,605542,605888,606188,606589,606808,607221,607458,607828,608552,608907,609176,609316,609570,609714,609991,610983,611392,612024,612370,612738,613812,614175,614575,616083,616668,616986,619521,619715,619933,620159,620371,620570,620777,621981,622276,622833,623223,623855,624332,624577,624928,625174,625934,626198,626621,626812,627191,627279,627387,627495,627808,628133,628452,628783,631486,631674,631935,632184,634768,634960,635225,635478,636010,636418,636617,637201,637436,637560,637972,638186,638588,638691,638821,638996,639248,639444,639584,639778,640789,641858,642146,642276,643053,643710,643856,644562,644800,646340,646490,646907,647072,647758,648228,648424,648515,648599,648743,648977,649144,650072,650358,650518,651133,651292,651620,651847,652359,652721,652800,653139,653244,653609,653980,654341,656215,656844,657920,658344,658597,658749,659797,660534,660737,660983,661230,661448,661690,662011,662275,662580,662803,663114,663303,664018,664287,664781,665007,665447,665606,665890,666635,667000,667305,667463,667701,669020,669418,669646,669866,670008,671298,671404,671534,671672,671796,672084,672253,672353,672638,672752,673635,674390,674829,674953,675199,675392,675526,675717,676496,676714,677005,677284,677601,677823,678118,678401,678505,678846,679662,679978,680539,681045,681250,682036,682441,683102,683291,683842,684408,684528,684930,837131,837226,837319,837382,837464,837557,837650,837737,837835,837926,838017,838105,838189,838285,838389,838489,838595,838698,838799,838903,839009,839108,839214,839316,839423,839532,839643,839774,839894,840010,840128,840227,840334,840450,840569,840697,840786,840881,840958,841047,841138,841231,841305,841402,841497,841595,841694,841798,841894,841996,842099,842199,842302,842387,842488,842586,842676,842771,842858,842964,843066,843160,843251,843345,843421,843513,843602,843705,843816,843899,843985,844080,844177,844273,844361,844462,844563,844666,844772,844870,844967,845062,845160,845263,845363,845466,845571,845689,845805,845900,845993,846078,846174,846268,846360,846462,846569,846652,846756,846861,846961,847062,847167,847267,847368,847467,847569,847663,847770,847872,847975,848068,848164,848266,848369,848465,848567,848670,848767,848870,848968,849072,849177,849274,849382,849496,849611,849719,849833,849948,850050,850155,850263,850373,850489,850606,850701,850798,850897,851002,851108,851207,851312,851418,851518,851624,851725,851832,851951,852050,852155,852257,852359,852459,852562,852657,852761,852846,852950,853054,853152,853256,853362,853460,853565,853663,853776,853870,853959,854048,854131,854222,854305,854403,854493,854589,854678,854772,854860,854956,855041,855149,855250,855351,855449,855555,855646,855745,855842,855940,856036,856129,856239,856337,856432,856542,856634,856734,856833,856920,857024,857129,857228,857335,857442,857541,857650,857742,857853,857964,858075,858179,858294,858410,858537,858657,858754,858853,858945,859044,859136,859235,859321,859415,859518,859614,859717,859813,859916,860013,860111,860214,860307,860397,860498,860581,860672,860757,860849,860952,861047,861143,861236,861330,861409,861516,861607,861706,861799,861902,862006,862107,862208,862312,862406,862510,862614,862727,862833,862939,863047,863164,863265,863373,863473,863576,863681,863788,863884,863963,864053,864137,864229,864302,864399,864481,864566,864651,864748,864841,864936,865035,865132,865223,865314,865406,865501,865608,865716,865818,865915,866012,866105,866192,866276,866373,866470,866563,866650,866741,866840,866939,867034,867123,867204,867303,867407,867504,867609,867706,867790,867889,867993,868090,868195,868292,868390,868491,868597,868696,868803,868902,869001,869092,869181,869270,869352,869445,869536,869647,869748,869848,869960,870073,870171,870279,870373,870473,870562,870654,870765,870875,870970,871086,871212,871338,871457,871585,871710,871835,871953,872080,872189,872298,872411,872534,872657,872773,872898,872995,873103,873225,873341,873457,873566,873654,873755,873844,873945,874032,874120,874217,874309,874415,874515,874591", "endLines": "112,118,129,132,133,134,135,136,139,140,141,142,143,145,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,219,220,242,244,245,246,247,248,249,250,251,252,253,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,337,339,340,346,347,348,349,350,351,352,353,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,577,582,583,584,612,624,625,626,627,628,629,630,631,632,633,634,638,639,640,641,642,644,653,654,655,656,657,659,660,661,662,663,664,665,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,698,699,710,720,734,768,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,944,945,946,947,948,949,950,951,952,953,954,955,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1011,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1027,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1042,1043,1044,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1077,1078,1079,1080,1081,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1238,1243,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,2001,2002,2050,2051,2052,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2073,2076,2077,2078,2096,2097,2098,2099,2100,2101,2102,2110,2120,2121,2124,2125,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2149,2150,2151,2152,2153,2154,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2180,2181,2182,2183,2184,2187,2192,2259,2277,2278,2279,2280,2281,2282,2298,2299,2300,2309,2402,2403,2409,2410,2411,2443,2446,2456,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2485,2486,2487,2488,2489,2490,2502,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2516,2517,2518,2519,2520,2521,2522,2523,2545,2546,2547,2548,2549,2550,2551,2552,2553,2556,2559,2562,2563,2564,2565,2566,2567,2568,2569,2570,2571,2572,2573,2574,2575,2576,2577,2578,2579,2580,2581,2582,2583,2584,2585,2586,2587,2588,2589,2590,2591,2592,2593,2594,2595,2596,2597,2598,2599,2600,2601,2602,2603,2604,2605,2606,2607,2608,2609,2610,2611,2612,2646,2647,2648,2649,2650,2689,2690,2691,2705,2706,2784,2788,2792,2796,2797,2801,2859,2866,2874,3039,3049,3058,3067,3076,3137,3138,3139,3145,3146,3147,3148,3149,3150,3156,3157,3158,3159,3160,3165,3166,3170,3171,3177,3181,3182,3183,3184,3194,3195,3196,3200,3201,3207,3211,3212,3284,3285,3289,3290,3293,3294,3295,3296,3559,3566,3826,3832,4095,4102,4362,4368,4431,4513,4565,4647,4709,4791,4855,4907,4989,4997,5003,5014,5018,5022,5035,5050,5826,5833,5839,5856,5869,5889,5906,5915,5920,5927,5947,5960,5977,5983,5989,5996,6000,6006,6020,6023,6033,6034,6035,6083,6087,6091,6095,6096,6097,6100,6116,6123,6137,6182,6183,6232,6236,6240,6245,6252,6258,6259,6262,6266,6271,6284,6288,6293,6298,6303,6306,6309,6312,6316,6320,6468,6469,6470,6471,6699,6700,6701,6702,6703,6704,6705,6706,6707,6708,6709,6710,6711,6712,6713,6714,6715,6716,6720,6724,6728,6732,6736,6740,6744,6745,6746,6747,6748,6749,6750,6751,6755,6759,6760,6764,6765,6768,6772,6775,6778,6781,6785,6791,6794,6798,6802,6806,6810,6813,6814,6815,6816,6819,6823,6826,6829,6832,6835,6838,6841,6845,6920,6921,6924,6927,6928,6931,6932,6933,6937,6938,6943,6950,6957,6964,6971,6978,6985,6992,6999,7006,7015,7024,7033,7040,7049,7058,7061,7064,7065,7066,7067,7068,7069,7070,7071,7072,7073,7074,7075,7076,7080,7085,7090,7093,7094,7095,7096,7097,7105,7113,7114,7122,7126,7134,7142,7150,7158,7166,7167,7175,7183,7184,7187,7190,7228,7233,7235,7240,7244,7248,7249,7250,7251,7255,7259,7260,7264,7265,7266,7267,7268,7269,7270,7271,7272,7273,7274,7275,7276,7277,7278,7279,7283,7287,7288,7292,7293,7294,7299,7300,7301,7302,7303,7304,7305,7306,7307,7308,7309,7310,7311,7312,7313,7314,7315,7316,7317,7318,7319,7323,7324,7325,7331,7332,7336,7338,7339,7344,7345,7346,7347,7348,7349,7353,7354,7355,7361,7362,7366,7368,7372,7376,7380,7384,7412,7413,7414,7417,7420,7423,7426,7429,7434,7438,7441,7442,7447,7451,7456,7462,7468,7473,7477,7482,7486,7490,7531,7532,7533,7534,7535,7539,7540,7541,7542,7546,7550,7554,7558,7562,7566,7570,7574,7580,7581,7622,7636,7641,7667,7674,7677,7688,7693,7696,7699,7754,7760,7761,7764,7767,7770,7773,7776,7779,7782,7786,7789,7790,7791,7799,7807,7810,7815,7820,7825,7830,7834,7838,7839,7847,7848,7849,7850,7851,7859,7864,7869,7870,7871,7872,7897,7903,7908,7911,7915,7918,7922,7932,7935,7940,7943,7947,7951,8065,8079,8092,8096,8111,8122,8125,8136,8141,8145,8180,8181,8182,8194,8202,8210,8218,8226,8246,8249,8276,8281,8301,8304,8307,8314,8327,8336,8339,8359,8369,8373,8377,8390,8394,8398,8402,8408,8412,8429,8437,8441,8445,8449,8452,8456,8460,8464,8474,8481,8488,8492,8518,8528,8553,8562,8582,8592,8596,8606,8631,8641,8644,8648,8649,8650,8651,8655,8661,8667,8668,8681,8682,8683,8686,8689,8692,8695,8698,8701,8704,8707,8710,8713,8716,8719,8722,8725,8728,8731,8734,8737,8740,8743,8746,8747,8752,8753,8766,8776,8780,8785,8790,8794,8797,8801,8805,8808,8812,8815,8819,8824,8829,8832,8839,8843,8847,8856,8861,8866,8867,8871,8874,8878,8891,8896,8904,8908,8912,8929,8933,8938,8956,8963,8967,8997,9000,9003,9006,9009,9012,9015,9034,9040,9048,9055,9067,9075,9080,9085,9089,9100,9104,9112,9115,9120,9121,9122,9123,9127,9131,9135,9139,9174,9177,9181,9185,9219,9222,9226,9230,9239,9245,9248,9258,9262,9263,9270,9274,9281,9282,9283,9286,9291,9296,9297,9301,9316,9335,9339,9340,9352,9362,9363,9375,9380,9404,9407,9413,9416,9425,9433,9437,9440,9443,9446,9450,9453,9470,9474,9477,9492,9495,9503,9508,9515,9520,9521,9526,9527,9533,9539,9545,9577,9588,9605,9612,9616,9619,9632,9641,9645,9650,9654,9658,9662,9666,9670,9674,9678,9683,9686,9698,9703,9712,9715,9722,9723,9727,9736,9742,9746,9747,9751,9772,9778,9782,9786,9787,9805,9806,9807,9808,9809,9814,9817,9818,9824,9825,9837,9849,9856,9857,9862,9867,9868,9872,9886,9891,9897,9903,9909,9914,9920,9926,9927,9933,9948,9953,9962,9971,9974,9988,9993,10004,10008,10017,10026,10027,10034,10042,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895,14896,14897,14898,14899,14900,14901,14902,14903,14904,14905,14906,14907,14908,14909,14910,14911,14912,14913,14914,14915,14916,14917,14918,14919,14920,14921,14922,14923,14924,14925,14926,14927,14928,14929,14930,14931,14932,14933,14934,14935,14936,14937,14938,14939,14940,14941,14942,14943,14944,14945,14946,14947,14948,14949,14950,14951,14952,14953,14954,14955,14956,14957,14958,14959,14960,14961,14962,14963,14964,14965,14966,14967,14968,14969,14970,14971,14972,14973,14974,14975,14976,14977,14978,14979,14980,14981,14982,14983,14984,14985,14986,14987,14988,14989,14990,14991,14992,14993,14994,14995,14996,14997,14998,14999,15000,15001,15002,15003,15004,15005,15006,15007,15008,15009,15010,15011,15012,15013,15014,15015,15016,15017,15018,15019,15020,15021,15022,15023,15024,15025,15026,15027,15028,15029,15030,15031,15032,15033,15034,15035,15036,15037,15038,15039,15040,15041,15042,15043,15044,15045,15046,15047,15048,15049,15050,15051,15052,15053,15054,15055,15056,15057,15058,15059,15060,15061,15062,15063,15064,15065,15066,15067,15068,15069,15070,15071,15072,15073,15074,15075,15076,15077,15078,15079,15080,15081,15082,15083,15084,15085,15086,15087,15088,15089,15090,15091,15092,15093,15094,15095,15096,15097,15098,15099,15100,15101,15102,15103,15104,15105,15106,15107,15108,15109,15110,15111,15112,15113,15114,15115,15116,15117,15118,15119,15120,15121,15122,15123,15124,15125,15126,15127,15128,15129,15130,15131,15132,15133,15134,15135,15136,15137,15138,15139,15140,15141,15142,15143,15144,15145,15146,15147,15148,15149,15150,15151,15152,15153,15154,15155,15156,15157,15158,15159,15160,15161,15162,15163,15164,15165,15166,15167,15168,15169,15170,15171,15172,15173,15174,15175,15176,15177,15178,15179,15180,15181,15182,15183,15184,15185,15186,15187,15188,15189,15190,15191", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,85,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,82,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,88,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,87,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,108,110,127,127,131,129,129,133,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,101,95,103,66,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,69,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,68,85,65,82,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,101,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,95,95,97,64,103,57,63,60,61,59,64,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,203,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,103,86,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,145,137,135,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,92,86,97,90,90,87,83,95,103,99,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,88,94,76,88,90,92,73,96,94,97,98,103,95,101,102,99,102,84,100,97,89,94,86,105,101,93,90,93,75,91,88,102,110,82,85,94,96,95,87,100,100,102,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,101,106,82,103,104,99,100,104,99,100,98,101,93,106,101,102,92,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,94,96,98,104,105,98,104,105,99,105,100,106,118,98,104,101,101,99,102,94,103,84,103,103,97,103,105,97,104,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,92,89,100,82,90,84,91,102,94,95,92,93,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,100,107,99,102,104,106,95,78,89,83,91,72,96,81,84,84,96,92,94,98,96,90,90,91,94,106,107,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,103,96,104,96,83,98,103,96,104,96,97,100,105,98,106,98,98,90,88,88,81,92,90,110,100,99,111,112,97,107,93,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,121,115,115,108,87,100,88,100,86,87,96,91,105,99,75,76", "endOffsets": "4251,4486,4945,5104,5164,5225,5290,5345,5495,5545,5598,5656,5704,5824,6173,6244,6316,6388,6461,6528,6577,6631,6668,6719,6779,6826,6882,6931,6989,7043,7104,7160,7211,7271,7327,7390,7439,7495,7551,7601,7660,7715,7777,7824,7878,7934,7986,8041,8095,8149,8203,8252,8310,8364,8421,8477,8524,8577,8633,8693,8756,8815,8877,8927,8981,9035,9083,9140,9193,9249,9871,9927,10865,10975,11031,11091,11144,11205,11284,11365,11437,11516,11596,12019,12097,12166,12242,12319,12390,12463,12539,12617,12686,12762,12839,12903,12974,13046,15538,15636,15691,15939,15992,16044,16094,16152,16217,16265,16316,23273,23339,23397,23466,23524,23593,23663,23736,23810,23878,23945,24015,24081,24154,24214,24290,24350,24410,24485,24553,24619,24687,24747,24806,24863,24929,24991,25048,25116,25189,25259,25321,25382,25450,25512,25582,25651,25707,25766,25828,25890,25957,26014,26075,26136,26197,26258,26314,26370,26426,26482,26540,26598,26656,26714,26771,26828,26885,26942,27001,27060,27118,27201,27284,27357,27411,27480,27536,27617,27698,27769,27898,28157,28215,28273,29438,29985,30045,30099,30169,30239,30304,30370,30435,30503,30572,30640,30770,30823,30882,30940,30992,31088,31476,31522,31572,31628,31675,31780,31838,31900,31963,32025,32084,32144,32209,32275,32340,32402,32464,32526,32588,32650,32712,32778,32845,32911,32974,33038,33101,33169,33230,33292,33354,33417,33481,33544,33608,33686,33745,33811,33891,33952,34005,34214,34265,34692,35162,35729,37196,40654,40725,40791,40865,40934,41005,41078,41149,41217,41290,41366,41436,41514,41582,41648,41709,41778,41842,41908,41976,42042,42105,42173,42244,42309,42382,42445,42526,42590,42656,42726,42796,42866,42936,43003,44207,44265,44324,44384,44443,44502,44561,44620,44679,44738,44797,44856,44915,44974,45034,45095,45157,45218,45279,45340,45401,45462,45523,45583,45644,45705,45765,45826,45887,45948,46009,46070,46131,46192,46253,46314,46375,46436,46504,46573,46643,46712,46781,46850,46919,46988,47057,47126,47195,47264,47333,47393,47454,47516,47577,47638,47699,47760,47821,47882,47943,48004,48065,48126,48188,48251,48315,48378,48441,48504,48567,48630,48693,48756,48819,48882,48945,49006,49068,49131,49193,49255,49317,49379,49441,49503,49565,49627,49689,49751,49808,49894,49974,50064,50159,50251,50343,50433,50516,50609,50696,50793,50884,50985,51072,51175,51264,51363,51455,51555,51639,51733,51821,51919,52002,52093,52187,52286,52388,52486,52586,52673,52773,52859,52955,53043,53124,53215,53311,53404,53497,53588,53673,53767,53856,53954,54047,54149,54237,54341,54432,54532,54625,54726,54811,54906,54995,55094,55179,55271,55366,55466,55569,55668,55771,55860,55961,56048,56145,56233,56329,56421,56521,56611,56709,56794,56883,56972,57065,57152,57243,57981,58057,58126,58205,58278,58358,58438,58515,58583,58661,58737,58808,58889,58962,59045,59120,59205,59278,59359,59440,59514,59598,59668,59746,59816,59896,59974,60046,60128,60198,60275,60355,60440,60528,60612,60699,60773,60851,60929,61000,61081,61172,61255,61351,61449,61556,61621,61687,61740,61816,61882,61969,62045,62121,70355,70628,72176,72255,72333,72406,72471,72534,72600,72671,72742,72812,72874,72943,73009,73069,73136,73203,73259,73310,73363,73415,73469,73540,73603,73662,73724,73783,73856,73923,73993,74053,74116,74191,74263,74359,74430,74486,74557,74614,74671,74737,74801,74872,74929,74982,75045,75097,75155,75222,76545,76611,76670,76753,76812,76869,76936,77006,77080,77142,77211,77281,77380,77477,77576,77662,77748,77829,77904,77993,78084,78168,78227,78273,78339,78396,78463,78520,78602,78667,78733,78856,78940,79061,79126,79188,79286,79360,79443,79532,79596,79675,79749,79811,79907,79972,80031,80087,80143,80203,80310,80357,80417,80478,80542,80603,80663,80721,80764,80813,80865,80916,80968,81017,81066,81131,81197,81257,81318,81374,81433,81482,81530,81588,81645,81747,81804,81879,81927,81978,82040,82105,82157,82231,82294,82357,82425,82475,82537,82597,82654,82714,82763,82831,82937,83039,83108,83179,83235,83284,83384,83455,83565,83654,83745,83827,83925,83981,84082,84192,84291,84354,84460,84537,84649,84776,84888,85015,85085,85199,85330,85427,85495,85613,85716,85834,85895,85969,86036,86141,86263,86337,86404,86514,86613,86686,86783,86905,87023,87141,87202,87324,87441,87509,87615,87717,87797,87868,87964,88031,88105,88179,88265,88353,88443,88521,88598,88698,88769,88890,89011,89075,89200,89274,89398,89522,89589,89698,89826,89938,90017,90095,90196,90267,90389,90511,90576,90702,90814,90920,90988,91087,91191,91254,91320,91404,91517,91630,91748,91826,91898,92034,92170,92255,92395,92533,92671,92813,92895,93004,93115,93243,93371,93503,93633,93763,93897,93959,94055,94122,94239,94360,94457,94539,94626,94713,94844,94975,95110,95187,95264,95375,95489,95563,95672,95784,95886,95982,96086,96153,96247,96319,96429,96535,96608,96699,96801,96904,96999,97106,97211,97333,97455,97581,97640,97698,97822,97946,98074,98192,98310,98432,98518,98615,98749,98883,98963,99101,99233,99365,99501,99576,99652,99755,99829,99942,100023,100080,100141,100200,100260,100318,100379,100437,100487,100536,100603,100662,100721,100770,100841,100925,100995,101066,101146,101215,101278,101346,101412,101480,101545,101611,101688,101766,101872,101978,102074,102203,102292,102419,102485,102554,102640,102706,102789,102887,102983,103079,103177,103286,103381,103470,103532,103592,103657,103714,103795,103849,103906,104003,104113,104174,104289,104410,104505,104597,104690,104792,104848,104907,104956,105048,105097,105151,105205,105259,105313,105367,105422,105532,105642,105750,105860,105970,106080,106190,106298,106404,106508,106612,106716,106811,106906,106999,107092,107196,107302,107406,107510,107603,107696,107789,107882,107990,108096,108202,108308,108405,108500,108595,108690,108796,108902,109008,109114,109212,109308,109404,109502,109567,109671,109729,109881,109942,110004,110064,110129,110191,110259,110317,110380,110443,110510,110585,110658,110724,110776,110829,110881,110938,111022,111117,111202,111283,111363,111440,111519,111596,111670,111744,111815,111895,111967,112042,112107,112168,112228,112303,112377,112533,112603,112675,112745,112818,112882,112952,112998,113067,113119,113204,113287,113344,113410,113477,113543,113624,113699,113755,113808,113869,113927,113977,114026,114075,114124,114186,114238,114283,114364,114415,114469,114522,114576,114627,114676,114742,114793,114854,114915,114977,115027,115068,115145,115204,115263,115322,115383,115439,115495,115562,115623,115688,115743,115808,115877,115945,116023,116092,116152,116223,116297,116362,116434,116504,116571,116655,116724,116791,116861,116924,116991,117059,117142,117221,117311,117388,117456,117523,117601,117658,117715,117783,117849,117905,117965,118024,118078,118128,118178,118226,118288,118339,118412,118492,118572,118636,118703,118774,118832,118893,118959,119018,119085,119145,119205,119268,119336,119397,119464,119542,119612,119661,119718,119787,119848,119936,120024,120112,120200,120287,120374,120461,120548,120606,120680,120750,120806,120877,120942,121004,121079,121152,121242,121308,121374,121435,121499,121561,121619,121690,121773,121832,121903,121969,122034,122095,122154,122225,122291,122356,122439,122515,122590,122671,122731,122800,122870,122939,122994,123050,123106,123167,123225,123281,123335,123390,123452,123509,123603,123672,123773,123824,123894,123957,124013,124071,124130,124184,124270,124354,124424,124493,124563,124678,124799,124866,124933,125008,125075,125134,125188,125242,125296,125349,125401,125475,128042,128182,130933,130983,131033,131122,131178,131236,131298,131353,131411,131482,131546,131605,131667,131733,131799,132180,132326,132371,132414,133400,133447,133492,133543,133594,133645,133696,134077,134628,134690,134870,134942,135067,135121,135176,135234,135289,135348,135404,135473,135542,135611,135681,135744,135807,135870,135933,135998,136063,136128,136193,136256,136320,136384,136448,136499,136577,136655,136726,136798,136871,136943,137009,137075,137143,137211,137277,137344,137418,137481,137538,137598,137663,137730,137795,137852,137913,137971,138075,138185,138294,138398,138476,138541,138608,138674,138744,138791,138843,138893,139076,139466,143520,144677,144861,145039,145277,145466,145635,146612,146727,146812,147332,154527,154592,155147,155304,155461,157968,158259,158959,160017,160113,160203,160299,160389,160555,160678,160801,160971,161077,161192,161307,161409,161515,161632,161747,162491,162664,162832,162980,163139,163294,163467,163584,163701,163869,163981,164095,164267,164443,164601,164734,164846,164992,165144,165276,165419,165541,167021,167157,167253,167389,167484,167651,167744,167836,168023,168179,168357,168521,168703,169020,169202,169384,169574,169806,169996,170173,170335,170492,170602,170785,170922,171126,171310,171494,171654,171812,171996,172223,172426,172597,172817,173039,173194,173394,173578,173681,173871,174012,174177,174348,174548,174752,174954,175119,175324,175523,175722,175919,176010,176159,176309,176393,176542,176687,176839,176980,177146,177307,179361,179662,179828,179983,180085,182210,182374,182560,183630,183755,188436,188708,188986,189231,189293,189578,193136,193592,194101,204877,205391,205828,206262,206705,210687,210808,210907,211312,211409,211526,211613,211736,211837,212243,212342,212461,212554,212661,213004,213111,213356,213477,213886,214134,214234,214339,214458,214967,215114,215233,215484,215617,216032,216286,216398,221748,221873,222190,222311,222539,222660,222793,222940,243571,244063,264443,264867,285543,286037,306462,306888,311729,317146,321237,326668,331410,336787,340771,344763,350154,350701,351134,351890,352120,352363,353496,354425,402570,403154,403627,405057,405801,406994,408048,408526,408819,409202,410717,411482,412625,413066,413507,414103,414377,414788,415804,415982,416735,416872,416963,419157,419423,419745,419955,420064,420183,420367,421485,421955,422706,425289,425384,428441,428669,428925,429184,429760,430114,430236,430375,430667,430927,431855,432141,432544,432946,433289,433501,433702,433915,434204,434489,445562,445649,445734,445833,458371,458477,458600,458732,458855,458985,459109,459242,459373,459498,459615,459735,459867,459995,460109,460227,460340,460461,460649,460836,461017,461200,461384,461549,461731,461851,461971,462079,462189,462301,462409,462519,462684,462850,463002,463167,463268,463388,463559,463720,463883,464044,464211,464450,464567,464747,464929,465110,465293,465448,465593,465715,465850,466013,466206,466332,466484,466626,466796,466952,467124,467415,474464,474556,474729,474891,474986,475155,475249,475338,475581,475670,475963,476379,476799,477220,477646,478063,478479,478896,479314,479728,480198,480671,481143,481554,482025,482497,482687,482893,482999,483107,483213,483325,483439,483551,483665,483781,483895,484003,484113,484221,484483,484862,485266,485413,485521,485631,485739,485853,486262,486676,486792,487210,487451,487881,488316,488726,489148,489558,489680,490089,490505,490627,490845,491029,493733,494077,494157,494513,494663,494807,494883,494995,495085,495347,495612,495720,495872,495980,496056,496168,496258,496360,496468,496576,496676,496784,496869,496973,497060,497138,497252,497344,497608,497875,497985,498138,498248,498332,498721,498819,498927,499021,499151,499259,499381,499517,499625,499745,499879,500001,500129,500271,500397,500537,500663,500781,500913,501011,501121,501421,501533,501651,502115,502231,502534,502660,502756,503157,503267,503391,503529,503639,503761,504073,504197,504327,504803,504931,505246,505384,505546,505762,505918,506122,508048,508132,508236,508439,508628,508829,509022,509227,509540,509752,509918,510034,510280,510496,510809,511235,511697,511934,512086,512346,512490,512632,515864,515978,516098,516214,516308,516629,516728,516846,516947,517226,517511,517790,518072,518325,518584,518837,519093,519517,519593,522843,524198,524642,526496,527071,527279,528289,528669,528835,528976,533996,534422,534534,534669,534822,535019,535190,535373,535548,535735,536007,536165,536249,536353,536840,537396,537554,537773,538004,538227,538462,538684,538950,539088,539687,539801,539939,540051,540175,540746,541241,541787,541932,542025,542117,544044,544614,544912,545101,545307,545500,545710,546594,546739,547131,547289,547506,547767,556347,557222,557842,558039,558987,559752,559875,560648,560869,561069,563046,563146,563236,563922,564675,565440,566203,566978,568191,568356,569969,570290,571353,571563,571733,572303,573198,573831,573997,575483,576099,576335,576556,577514,577779,578044,578291,578705,578941,580226,580675,580862,581111,581353,581529,581770,582003,582228,582823,583298,583822,584083,585434,585909,587135,587605,588653,589105,589349,589806,591051,591534,591684,592028,592174,592312,592448,592736,593240,593749,593865,594767,594889,595001,595178,595444,595714,595980,596248,596504,596764,597020,597278,597530,597786,598038,598292,598524,598760,599012,599268,599520,599774,600006,600240,600352,600777,600901,601993,602808,603004,603328,603717,604069,604310,604524,604823,605015,605330,605537,605883,606183,606584,606803,607216,607453,607823,608547,608902,609171,609311,609565,609709,609986,610978,611387,612019,612365,612733,613807,614170,614570,616078,616663,616981,619516,619710,619928,620154,620366,620565,620772,621976,622271,622828,623218,623850,624327,624572,624923,625169,625929,626193,626616,626807,627186,627274,627382,627490,627803,628128,628447,628778,631481,631669,631930,632179,634763,634955,635220,635473,636005,636413,636612,637196,637431,637555,637967,638181,638583,638686,638816,638991,639243,639439,639579,639773,640784,641853,642141,642271,643048,643705,643851,644557,644795,646335,646485,646902,647067,647753,648223,648419,648510,648594,648738,648972,649139,650067,650353,650513,651128,651287,651615,651842,652354,652716,652795,653134,653239,653604,653975,654336,656210,656839,657915,658339,658592,658744,659792,660529,660732,660978,661225,661443,661685,662006,662270,662575,662798,663109,663298,664013,664282,664776,665002,665442,665601,665885,666630,666995,667300,667458,667696,669015,669413,669641,669861,670003,671293,671399,671529,671667,671791,672079,672248,672348,672633,672747,673630,674385,674824,674948,675194,675387,675521,675712,676491,676709,677000,677279,677596,677818,678113,678396,678500,678841,679657,679973,680534,681040,681245,682031,682436,683097,683286,683837,684403,684523,684925,685459,837221,837314,837377,837459,837552,837645,837732,837830,837921,838012,838100,838184,838280,838384,838484,838590,838693,838794,838898,839004,839103,839209,839311,839418,839527,839638,839769,839889,840005,840123,840222,840329,840445,840564,840692,840781,840876,840953,841042,841133,841226,841300,841397,841492,841590,841689,841793,841889,841991,842094,842194,842297,842382,842483,842581,842671,842766,842853,842959,843061,843155,843246,843340,843416,843508,843597,843700,843811,843894,843980,844075,844172,844268,844356,844457,844558,844661,844767,844865,844962,845057,845155,845258,845358,845461,845566,845684,845800,845895,845988,846073,846169,846263,846355,846457,846564,846647,846751,846856,846956,847057,847162,847262,847363,847462,847564,847658,847765,847867,847970,848063,848159,848261,848364,848460,848562,848665,848762,848865,848963,849067,849172,849269,849377,849491,849606,849714,849828,849943,850045,850150,850258,850368,850484,850601,850696,850793,850892,850997,851103,851202,851307,851413,851513,851619,851720,851827,851946,852045,852150,852252,852354,852454,852557,852652,852756,852841,852945,853049,853147,853251,853357,853455,853560,853658,853771,853865,853954,854043,854126,854217,854300,854398,854488,854584,854673,854767,854855,854951,855036,855144,855245,855346,855444,855550,855641,855740,855837,855935,856031,856124,856234,856332,856427,856537,856629,856729,856828,856915,857019,857124,857223,857330,857437,857536,857645,857737,857848,857959,858070,858174,858289,858405,858532,858652,858749,858848,858940,859039,859131,859230,859316,859410,859513,859609,859712,859808,859911,860008,860106,860209,860302,860392,860493,860576,860667,860752,860844,860947,861042,861138,861231,861325,861404,861511,861602,861701,861794,861897,862001,862102,862203,862307,862401,862505,862609,862722,862828,862934,863042,863159,863260,863368,863468,863571,863676,863783,863879,863958,864048,864132,864224,864297,864394,864476,864561,864646,864743,864836,864931,865030,865127,865218,865309,865401,865496,865603,865711,865813,865910,866007,866100,866187,866271,866368,866465,866558,866645,866736,866835,866934,867029,867118,867199,867298,867402,867499,867604,867701,867785,867884,867988,868085,868190,868287,868385,868486,868592,868691,868798,868897,868996,869087,869176,869265,869347,869440,869531,869642,869743,869843,869955,870068,870166,870274,870368,870468,870557,870649,870760,870870,870965,871081,871207,871333,871452,871580,871705,871830,871948,872075,872184,872293,872406,872529,872652,872768,872893,872990,873098,873220,873336,873452,873561,873649,873750,873839,873940,874027,874115,874212,874304,874410,874510,874586,874663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\07a96c4913de9523900a40f3d6eddd2c\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "585,778,779,796,797,1112,1113,1244,1245,1246,1247,1248,1249,1250,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2048,2049,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2103,2188,2287,2288,2289,2290,2291,2292,2293,2717,6902,6903,6908,6911,6916,8056,8057", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "28278,37807,37879,39103,39168,62180,62249,70633,70703,70771,70843,70913,70974,71048,125480,125541,125602,125664,125728,125790,125851,125919,126019,126079,126145,126218,126287,126344,126396,128187,128259,128335,128400,128459,128518,128578,128638,128698,128758,128818,128878,128938,128998,129058,129118,129177,129237,129297,129357,129417,129477,129537,129597,129657,129717,129777,129836,129896,129956,130015,130074,130133,130192,130251,130819,130854,132530,132585,132648,132703,132761,132819,132880,132943,133000,133051,133101,133162,133219,133285,133319,133701,139081,145825,145892,145964,146033,146102,146176,146248,184338,473134,473251,473518,473811,474078,555781,555853", "endLines": "585,778,779,796,797,1112,1113,1244,1245,1246,1247,1248,1249,1250,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,1972,1973,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2020,2021,2022,2023,2024,2025,2026,2027,2028,2029,2030,2031,2032,2033,2034,2035,2036,2037,2048,2049,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2091,2092,2093,2094,2095,2103,2188,2287,2288,2289,2290,2291,2292,2293,2717,6902,6906,6908,6914,6916,8056,8057", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "28333,37874,37962,39163,39229,62244,62307,70698,70766,70838,70908,70969,71043,71116,125536,125597,125659,125723,125785,125846,125914,126014,126074,126140,126213,126282,126339,126391,126453,128254,128330,128395,128454,128513,128573,128633,128693,128753,128813,128873,128933,128993,129053,129113,129172,129232,129292,129352,129412,129472,129532,129592,129652,129712,129772,129831,129891,129951,130010,130069,130128,130187,130246,130305,130849,130884,132580,132643,132698,132756,132814,132875,132938,132995,133046,133096,133157,133214,133280,133314,133349,133731,139146,145887,145959,146028,146097,146171,146243,146331,184404,473246,473447,473623,474007,474202,555848,555915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1e2e54c84504d4b5d4bbcd5b5a658108\\transformed\\recyclerview-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "623,1325,1326,1327,1336,1337,1338,2047", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "29888,75442,75501,75549,76258,76333,76409,130753", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "29939,75496,75544,75600,76328,76404,76476,130814"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5057ec80a63cbdf1b3c7fa6aaf5d6a71\\transformed\\jetified-uitoolkit-1.11.2-1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2302,2303,2304,2310,2451,2469,2643,2696,2725,2759", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "146866,146904,146961,147337,158532,159550,179012,182773,184845,186800", "endColumns": "37,56,43,49,51,49,64,47,55,39", "endOffsets": "146899,146956,147000,147382,158579,159595,179072,182816,184896,186835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4c345bdfac77b4b56aa7e015904465a4\\transformed\\jetified-aws-android-sdk-auth-core-2.16.13\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2707", "startColumns": "4", "startOffsets": "183760", "endColumns": "90", "endOffsets": "183846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\95dc6764ddc16f8cca6dc25b8b4f1d65\\transformed\\jetified-aws-android-sdk-mobile-client-2.16.13\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2640", "startColumns": "4", "startOffsets": "178826", "endColumns": "54", "endOffsets": "178876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a44e8efa791fec526e44d72b1cad3c4\\transformed\\jetified-play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "807,808,809,810,811,812,813,814,2323,2324,2325,2326,2327,2328,2329,2330,2332,2333,2334,2335,2336,2337,2345,2347,2348", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "39715,39805,39885,39975,40065,40145,40226,40306,148194,148299,148480,148605,148712,148892,149015,149131,149401,149589,149694,149875,150000,150175,150628,150757,150819", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "39800,39880,39970,40060,40140,40221,40301,40381,148294,148475,148600,148707,148887,149010,149126,149229,149584,149689,149870,149995,150170,150318,150686,150814,150893"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d6b000a9a8e720e86578baf4ac1ca24\\transformed\\jetified-zoomvideosdk-core-1.11.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2117", "startColumns": "4", "startOffsets": "134398", "endColumns": "42", "endOffsets": "134436"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\arrays.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,7,14,21,35,49", "startColumns": "4,4,4,4,4,4", "startOffsets": "150,290,538,813,1166,1657", "endLines": "6,13,20,34,48,54", "endColumns": "19,19,19,20,19,19", "endOffsets": "285,533,808,1161,1652,1823"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7b0edbd2979ae96216d8e5e558dae44e\\transformed\\media-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1114,1987,1988,1989,1990,6907,6909,6910,6915,6917", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "62312,127166,127219,127272,127325,473452,473628,473750,474012,474207", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "62396,127214,127267,127320,127373,473513,473745,473806,474073,474269"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2802,2807,6209,6212,6217,6220,6321,6786,7385,7952,10046,10049", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "189583,189865,427145,427242,427473,427634,434494,464216,506127,547772,685609,685747", "endLines": "2806,2812,6211,6216,6219,6226,6328,6788,7393,7960,10048,10051", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "189860,190142,427237,427468,427629,428065,434887,464331,506642,548120,685742,685879"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e81b820efb681ef4f53988c1afa8d787\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "2043,2079,2116", "startColumns": "4,4,4", "startOffsets": "130575,132419,134334", "endColumns": "56,64,63", "endOffsets": "130627,132479,134393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c94defda84e4aea81c759dab29875d1f\\transformed\\jetified-material-calendar-view-1.9.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "81,90,254,728,815,817,818,858,868,869,1111,1153,1268,1269,1270,1271,1986,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2999,3459,11601,35441,40386,40487,40538,43288,43871,43924,62126,64627,71774,71842,71909,71971,127124,161822,161877,161932,162001,162066,162123,162178,162235,162300,162356", "endLines": "89,103,262,733,815,817,818,858,868,869,1111,1153,1268,1269,1270,1271,1986,2492,2493,2494,2495,2496,2497,2498,2499,2500,2501", "endColumns": "12,12,11,11,56,50,46,61,52,54,53,57,67,66,61,60,41,54,54,68,64,56,54,56,64,55,57", "endOffsets": "3454,3855,11943,35670,40438,40533,40580,43345,43919,43974,62175,64680,71837,71904,71966,72027,127161,161872,161927,161996,162061,162118,162173,162230,162295,162351,162409"}}, {"source": "C:\\Users\\<USER>\\Documents\\GitHub\\watchrxwatch\\app\\src\\main\\res\\values\\health_permissions.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "27", "endColumns": "12", "endOffsets": "1384"}, "to": {"startLines": "55", "startColumns": "4", "startOffsets": "1828", "endLines": "80", "endColumns": "12", "endOffsets": "2994"}}]}]}