package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.utils.LogUtils;

public class DBAdaptor {

    private static final String DB_NAME = "WatchRx.db";
    private static final int DB_VERSION = 1;

    private static SQLiteDatabase sqliteDB;
    private static DBHelper dbHelper;
    private static boolean isDbNeedToCreate;

    private static DBAdaptor dbAdaptor;

    private DBAdaptor() {
        // do nothing
    }

    public static DBAdaptor getDbAdaptorInstance() {
        if (null == dbAdaptor) {
            dbAdaptor = new DBAdaptor();
        }
        return dbAdaptor;
    }

    @SuppressWarnings("unused")
    public boolean isDBOpen() {
        return (sqliteDB != null) && sqliteDB.isOpen();
    }

    @SuppressWarnings("unused")
    public DBAdaptor open(Context context) {
        dbHelper = new DBHelper(context, DB_NAME, DB_VERSION);
        sqliteDB = dbHelper.getWritableDatabase();

        return this;
    }

    public void createDBIfNeeded(boolean status) {
        isDbNeedToCreate = status;
        if (sqliteDB != null)
            dbHelper.onCreate(sqliteDB);
    }

    @SuppressWarnings("unused")
    public void close() {
        if (dbHelper != null) {
            dbHelper.close();
        }
    }

    long insert(String tableName, ContentValues contentValues) {
        reOpenDB();
        return sqliteDB.insert(tableName, null, contentValues);
    }

    @SuppressWarnings("unused")
    Cursor getResultSet(String query) {
        reOpenDB();
        if (sqliteDB != null)
            return sqliteDB.rawQuery(query, null);
        return null;
    }

    @SuppressWarnings("unused")
    Cursor getResultSet(String tableName, String[] columns, String orderBy) throws SQLException {
        reOpenDB();
        return sqliteDB.query(true, tableName, columns, null, null, null, null, orderBy, null);
    }

    Cursor getResultSet(String tableName, String[] columns, String whereClause, String groupBy, String orderBy) {
        reOpenDB();
        return sqliteDB.query(true, tableName, columns, whereClause, null, groupBy, null, orderBy, null);
    }

    void delete(String tableName) {
        reOpenDB();
        if (sqliteDB != null) {
            sqliteDB.delete(tableName, null, null);
        }
    }

    @SuppressWarnings("unused")
    public int delete(String tableName, String whereClause, String[] whereArgs) {
        reOpenDB();
        return sqliteDB.delete(tableName, whereClause, whereArgs);
    }

    public void dropTable(String tableName) {
        reOpenDB();
        sqliteDB.execSQL("DROP TABLE IF EXISTS " + tableName);
    }

    @SuppressWarnings("unused")
    public int update(String tableName, ContentValues contentValues, String whereClause, String[] whereArgs) {
        reOpenDB();
        return sqliteDB.update(tableName, contentValues, whereClause, whereArgs);
    }

    public void reOpenDB() {
        if (sqliteDB == null) {
            LogUtils.debug("Sqlite database object=" + sqliteDB);
            if (dbHelper == null) {
                dbHelper = new DBHelper(WatchApp.getContext(), DB_NAME, DB_VERSION);
            }
            sqliteDB = dbHelper.getWritableDatabase();
        }
    }

    public void execSQL(String sql) {
        sqliteDB.execSQL(sql);
    }

    public boolean validDbExists(Context context) {
        DBHelper dbHelper = new DBHelper(context, DB_NAME, DB_VERSION);
        return dbHelper.isTableExists(sqliteDB, PatientDetails.TABLE_PATIENT_DETAILS) &&
                dbHelper.isTableExists(sqliteDB, MedicationScheduleMaster.TABLE_MEDICATION_SCHEDULE_MASTER) &&
                dbHelper.isTableExists(sqliteDB, MedicationScheduleInstance.TABLE_MEDICATION_SCHEDULE_INSTANCE) &&
                dbHelper.isTableExists(sqliteDB, ServerQueue.TABLE_PUSH_TO_SERVER_QUEUE) &&
                dbHelper.isTableExists(sqliteDB, PhoneNumbers.TABLE_PHONE_NUMBERS_DETAILS) &&
                dbHelper.isTableExists(sqliteDB, Alerts.TABLE_ALERTS_DETAILS) &&
                dbHelper.isTableExists(sqliteDB, CustomerAlertsDB.TABLE_CUSTOM_ALTER_DETAILS) &&
                dbHelper.isTableExists(sqliteDB, ScheduleMessagesDB.TABLE_SCHEDULE_MESSAGE) &&
                dbHelper.isTableExists(sqliteDB, VitalConfiguration.TABLE_VITAL_DETAILS) &&
                dbHelper.isTableExists(sqliteDB, VitalStatusDetails.TABLE_VITAL_STATUS_DETAILS);
    }

    private static class DBHelper extends SQLiteOpenHelper {

        DBHelper(Context context, String dbName, int dbVersion) {
            super(context, dbName, null, dbVersion);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {


            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + PatientDetails.TABLE_PATIENT_DETAILS);
                db.execSQL(PatientDetails.CREATE_PATIENT_DETAILS_TABLE);
            } else if (!isTableExists(db, PatientDetails.TABLE_PATIENT_DETAILS)) {
                // db.execSQL(PatientDetails.DELETE_PATIENT_DETAILS_TABLE);
                db.execSQL(PatientDetails.CREATE_PATIENT_DETAILS_TABLE);
            }
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + CustomerAlertsDB.TABLE_CUSTOM_ALTER_DETAILS);
                db.execSQL(CustomerAlertsDB.CREATE_CUSTOM_ALTER_DETAIL_TABLE);
            } else if (!isTableExists(db, CustomerAlertsDB.TABLE_CUSTOM_ALTER_DETAILS)) {
                // db.execSQL(PatientDetails.DELETE_PATIENT_DETAILS_TABLE);
                db.execSQL(CustomerAlertsDB.CREATE_CUSTOM_ALTER_DETAIL_TABLE);
            }

            // Create the Medication Schedule Master table
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + MedicationScheduleMaster.TABLE_MEDICATION_SCHEDULE_MASTER);
                db.execSQL(MedicationScheduleMaster.CREATE_MEDSCHED_MASTER_TABLE);
            } else if (!isTableExists(db, MedicationScheduleMaster.TABLE_MEDICATION_SCHEDULE_MASTER)) {
                // db.execSQL(MedicationScheduleMaster.DELETE_MEDSCHED_MASTER_TABLE);
                db.execSQL(MedicationScheduleMaster.CREATE_MEDSCHED_MASTER_TABLE);
            }

            // Create the Medication Schedule Instance table
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + MedicationScheduleInstance.TABLE_MEDICATION_SCHEDULE_INSTANCE);
                db.execSQL(MedicationScheduleInstance.CREATE_MEDSCHED_INSTANCE_TABLE);
            } else if (!isTableExists(db, MedicationScheduleInstance.TABLE_MEDICATION_SCHEDULE_INSTANCE)) {
                // db.execSQL(MedicationScheduleInstance.DELETE_MEDSCHED_INSTANCE_TABLE);
                db.execSQL(MedicationScheduleInstance.CREATE_MEDSCHED_INSTANCE_TABLE);
            }

            // Create the Medication Adherence Log table
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + ServerQueue.TABLE_PUSH_TO_SERVER_QUEUE);
                db.execSQL(ServerQueue.CREATE_TABLE_PUSH_TO_SERVER_QUEUE);
            } else if (!isTableExists(db, ServerQueue.TABLE_PUSH_TO_SERVER_QUEUE)) {
                //  db.execSQL(ServerQueue.DELETE_TABLE_PUSH_TO_SERVER_QUEUE);
                db.execSQL(ServerQueue.CREATE_TABLE_PUSH_TO_SERVER_QUEUE);
            }
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + PhoneNumbers.TABLE_PHONE_NUMBERS_DETAILS);
                db.execSQL(PhoneNumbers.CREATE_PHONE_NUMBERS_DETAILS_TABLE);
            } else if (!isTableExists(db, PhoneNumbers.TABLE_PHONE_NUMBERS_DETAILS)) {
                db.execSQL(PhoneNumbers.CREATE_PHONE_NUMBERS_DETAILS_TABLE);
            }
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + Alerts.TABLE_ALERTS_DETAILS);
                db.execSQL(Alerts.CREATE_ALERTS_DETAILS_TABLE);
            } else if (!isTableExists(db, Alerts.TABLE_ALERTS_DETAILS)) {
                db.execSQL(Alerts.CREATE_ALERTS_DETAILS_TABLE);
            }
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + ScheduleMessagesDB.TABLE_SCHEDULE_MESSAGE);
                db.execSQL(ScheduleMessagesDB.CREATE_SCHEDULE_MESSAGE_TABLE);
            } else if (!isTableExists(db, ScheduleMessagesDB.TABLE_SCHEDULE_MESSAGE)) {
                db.execSQL(ScheduleMessagesDB.CREATE_SCHEDULE_MESSAGE_TABLE);
            }

            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + VitalConfiguration.TABLE_VITAL_DETAILS);
                db.execSQL(VitalConfiguration.CREATE_TABLE_VITAL_DETAILS);
            } else if (!isTableExists(db, VitalConfiguration.TABLE_VITAL_DETAILS)) {
                db.execSQL(VitalConfiguration.CREATE_TABLE_VITAL_DETAILS);
            }
            if (isDbNeedToCreate) {
                db.execSQL("DROP TABLE IF EXISTS " + VitalStatusDetails.TABLE_VITAL_STATUS_DETAILS);
                db.execSQL(VitalStatusDetails.CREATE_TABLE_STATUS_DETAILS);
            } else if (!isTableExists(db, VitalStatusDetails.TABLE_VITAL_STATUS_DETAILS)) {
                db.execSQL(VitalStatusDetails.CREATE_TABLE_STATUS_DETAILS);
            }
            isDbNeedToCreate = false;
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {

        }

        boolean isTableExists(SQLiteDatabase db, String tableName) {
            if (tableName == null || db == null || !db.isOpen()) {
                return false;
            }
            Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM sqlite_master WHERE type = ? AND name = ?", new String[]{"table", tableName});
            if (!cursor.moveToFirst()) {
                cursor.close();
                return false;
            }
            int count = cursor.getInt(0);
            cursor.close();
            return count > 0;
        }
    }
}
