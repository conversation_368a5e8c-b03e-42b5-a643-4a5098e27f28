<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:weightSum="3"
    android:background="@color/App_color"
    tools:context=".MainActivity">


    <TextView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.2"
        android:text="WATHCRX"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textAllCaps="true"
        android:textSize="30sp"
        android:textStyle="bold" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.0" />

    <Button
        android:id="@+id/configWifi"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.8"
        android:text="@string/connect_wifi" />


</LinearLayout>