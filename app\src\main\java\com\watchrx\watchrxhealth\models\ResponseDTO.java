package com.watchrx.watchrxhealth.models;

import java.util.List;

public class ResponseDTO {
    private boolean success;
    private List<String> messages;
    private List<String> measuredDates;
    private List<String> period;
    private List<VitalsCountGraphVO> vitalsCountGraphVOs;
    private ThresholdConfig thresholdConfig;
    private ThresholdConfig thresholdConfigVO;

    public ThresholdConfig getThresholdConfigVO() {
        return thresholdConfigVO;
    }

    public void setThresholdConfigVO(ThresholdConfig thresholdConfigVO) {
        this.thresholdConfigVO = thresholdConfigVO;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public List<String> getMessages() {
        return messages;
    }

    public void setMessages(List<String> messages) {
        this.messages = messages;
    }

    public List<String> getMeasuredDates() {
        return measuredDates;
    }

    public void setMeasuredDates(List<String> measuredDates) {
        this.measuredDates = measuredDates;
    }

    public List<VitalsCountGraphVO> getVitalsCountGraphVOs() {
        return vitalsCountGraphVOs;
    }

    public void setVitalsCountGraphVOs(List<VitalsCountGraphVO> vitalsCountGraphVOs) {
        this.vitalsCountGraphVOs = vitalsCountGraphVOs;
    }

    public ThresholdConfig getThresholdConfig() {
        return thresholdConfig;
    }

    public void setThresholdConfig(ThresholdConfig thresholdConfig) {
        this.thresholdConfig = thresholdConfig;
    }

    public List<String> getPeriod() {
        return period;
    }

    public void setPeriod(List<String> period) {
        this.period = period;
    }

    public static class VitalsCountGraphVO {
        private String vitalTypeName;
        private List<Double> counts;

        public String getVitalTypeName() {
            return vitalTypeName;
        }

        public void setVitalTypeName(String vitalTypeName) {
            this.vitalTypeName = vitalTypeName;
        }

        public List<Double> getCounts() {
            return counts;
        }

        public void setCounts(List<Double> counts) {
            this.counts = counts;
        }
    }

    public static class ThresholdConfig {
        private boolean success;
        private List<String> messages;
        private List<Threshold> eList;

        private Long pedometerStepCountMin;
        private Long pedometerStepCountMax;
        private Long pedometerStepCountCriticalMin;
        private Long pedometerStepCountCriticalMax;

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public List<String> getMessages() {
            return messages;
        }

        public void setMessages(List<String> messages) {
            this.messages = messages;
        }

        public List<Threshold> geteList() {
            return eList;
        }

        public void seteList(List<Threshold> eList) {
            this.eList = eList;
        }

        public Long getPedometerStepCountMin() {
            return pedometerStepCountMin;
        }

        public void setPedometerStepCountMin(Long pedometerStepCountMin) {
            this.pedometerStepCountMin = pedometerStepCountMin;
        }

        public Long getPedometerStepCountMax() {
            return pedometerStepCountMax;
        }

        public void setPedometerStepCountMax(Long pedometerStepCountMax) {
            this.pedometerStepCountMax = pedometerStepCountMax;
        }

        public Long getPedometerStepCountCriticalMin() {
            return pedometerStepCountCriticalMin;
        }

        public void setPedometerStepCountCriticalMin(Long pedometerStepCountCriticalMin) {
            this.pedometerStepCountCriticalMin = pedometerStepCountCriticalMin;
        }

        public Long getPedometerStepCountCriticalMax() {
            return pedometerStepCountCriticalMax;
        }

        public void setPedometerStepCountCriticalMax(Long pedometerStepCountCriticalMax) {
            this.pedometerStepCountCriticalMax = pedometerStepCountCriticalMax;
        }

        public static class Threshold {
            private boolean success;
            private String thresholdId;
            private float vitalMin;
            private float vitalMax;
            private float vitalCriticalMin;
            private float vitalCriticalMax;

            public boolean isSuccess() {
                return success;
            }

            public void setSuccess(boolean success) {
                this.success = success;
            }

            public String getThresholdId() {
                return thresholdId;
            }

            public void setThresholdId(String thresholdId) {
                this.thresholdId = thresholdId;
            }

            public float getVitalMin() {
                return vitalMin;
            }

            public void setVitalMin(float vitalMin) {
                this.vitalMin = vitalMin;
            }

            public float getVitalMax() {
                return vitalMax;
            }

            public void setVitalMax(float vitalMax) {
                this.vitalMax = vitalMax;
            }

            public float getVitalCriticalMin() {
                return vitalCriticalMin;
            }

            public void setVitalCriticalMin(float vitalCriticalMin) {
                this.vitalCriticalMin = vitalCriticalMin;
            }

            public float getVitalCriticalMax() {
                return vitalCriticalMax;
            }

            public void setVitalCriticalMax(float vitalCriticalMax) {
                this.vitalCriticalMax = vitalCriticalMax;
            }
        }
    }
}
