package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.SoftwareUpdateUtil;


/**
 * Created by mukes on 2/28/2017.
 */

public class NewPackageInstalled extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String status = SoftwareUpdateUtil.getSWUpgradeStatus();
        if (status != null && status.equalsIgnoreCase("INTALLATION_STARTED")) {
            LogUtils.debug("package installation completed, resgtart app now");
            throw new RuntimeException("restarting application intentionly to get on the new version.");
        }
    }

}
