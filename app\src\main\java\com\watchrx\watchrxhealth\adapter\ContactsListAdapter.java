package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.ContactsData;

import java.util.List;

public class ContactsListAdapter extends RecyclerView.Adapter<ContactsListAdapter.MyViewHolder> {
    Context mContext;
    List<ContactsData> contactsData;

    public interface OnItemClickListener {
        void onItemClick(ContactsData item);
    }

    private final OnItemClickListener listener;

    public ContactsListAdapter(Context context, List<ContactsData> contactsData, OnItemClickListener listener) {
        this.mContext = context;
        this.contactsData = contactsData;
        this.listener = listener;
    }

    @NonNull
    @Override
    public MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.contact_list_item, parent, false);
        return new MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull MyViewHolder holder, int position) {
        ContactsData contact = contactsData.get(position);
        holder.tvName.setText(contact.getContactName());
        holder.tvMobileNo.setText(contact.getContactNumber());
        holder.materialButton.setText(contact.getContactName().substring(0, 1));
        holder.bind(contact, listener);
    }

    @Override
    public int getItemCount() {
        return contactsData.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView tvName;
        TextView tvMobileNo;
        MaterialButton materialButton;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            tvName = itemView.findViewById(R.id.tvName);
            tvMobileNo = itemView.findViewById(R.id.tvNumber);
            materialButton = itemView.findViewById(R.id.image);
        }

        public void bind(final ContactsData item, final OnItemClickListener listener) {
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(item);
                }
            });
        }
    }
}
