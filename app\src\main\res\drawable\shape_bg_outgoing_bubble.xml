<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!--Shadow Layer-->

    <item>
        <rotate
            android:fromDegrees="40"
            android:pivotX="100%"
            android:pivotY="0%"
            android:toDegrees="0">
            <shape android:shape="rectangle">
                <corners android:radius="4dp" />
                <padding
                    android:bottom="1px"
                    android:left="1px"
                    android:right="1px" />
                <solid android:color="#01000000" />
            </shape>
        </rotate>
    </item>
    <item android:right="10dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp" />
            <padding
                android:bottom="1px"
                android:left="1px"
                android:right="1px" />
            <solid android:color="#01000000" />
        </shape>
    </item>

    <!--===============-->

    <item>
        <rotate
            android:fromDegrees="40"
            android:pivotX="100%"
            android:pivotY="0%"
            android:toDegrees="0">
            <shape android:shape="rectangle">
                <corners android:radius="4dp" />
                <padding android:bottom="1px" />
                <solid android:color="#09000000" />
            </shape>
        </rotate>
    </item>
    <item android:right="10dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp" />
            <padding android:bottom="1px" />
            <solid android:color="#09000000" />
        </shape>
    </item>

    <!--===============-->

    <item>
        <rotate
            android:fromDegrees="40"
            android:pivotX="100%"
            android:pivotY="0%"
            android:toDegrees="0">
            <shape android:shape="rectangle">
                <corners android:radius="4dp" />
                <padding
                    android:bottom="1px"
                    android:left="1px"
                    android:right="1px" />
                <solid android:color="#10000000" />
            </shape>
        </rotate>
    </item>
    <item android:right="10dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp" />
            <padding
                android:bottom="1px"
                android:left="1px"
                android:right="1px" />
            <solid android:color="#10000000" />
        </shape>
    </item>

    <!--===============-->


    <!--ForeGround-->

    <item>
        <rotate
            android:fromDegrees="40"
            android:pivotX="100%"
            android:pivotY="0%"
            android:toDegrees="0">
            <shape android:shape="rectangle">
                <solid android:color="#CBEBFC" />
            </shape>
        </rotate>
    </item>
    <item android:right="10dp">
        <shape android:shape="rectangle">
            <corners android:radius="4dp" />
            <solid android:color="#CBEBFC" />
        </shape>
    </item>

</layer-list>