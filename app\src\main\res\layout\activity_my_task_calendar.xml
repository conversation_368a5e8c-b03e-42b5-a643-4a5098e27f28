<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- ProgressBar to show while fetching events -->
    <ProgressBar
        android:id="@+id/loader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:indeterminate="true"
        android:visibility="gone" />

    <!-- CalendarView that displays the calendar -->
    <com.applandeo.materialcalendarview.CalendarView
        android:id="@+id/calendarView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:padding="4dp"
        app:headerColor="#FFA726"
        app:highlightedDaysLabelsColor="#EF5350" />

    <!-- TextView to show if there are no appointments -->
    <TextView
        android:id="@+id/noAppointmentsTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/calendarView"
        android:gravity="center"
        android:padding="16dp"
        android:text="No appointments"
        android:textColor="@color/grey"
        android:textSize="16sp"
        android:textStyle="italic"
        android:visibility="gone" />

    <!-- RecyclerView to display event data -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/eventsRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/calendarView"
        android:padding="8dp" />

</RelativeLayout>
