package com.watchrx.watchrxhealth.constants;

public class VoicePrompts {
    public static final String SILENCE = "$";
    public static final String TIME_SLOT = "TIME_SLOT";
    public static final String MED_COUNT = "MED_COUNT";
    public static final String BEFORE_FOOD = "BEFORE_FOOD";
    public static final String NURSE_NAME = "NURSE_NAME";
    public static final String DOSAGE = "DOSAGE_NAME";
    public static final String MED_NAME = "MED_NAME";

    public static final String REGULAR_REMINDER =
            //"Time for your early morning meds you have 2 meds to take now before food"
            "Time for your " + TIME_SLOT +
                    " meds" + "" + " You have " + MED_COUNT + " meds to take now " +
                    BEFORE_FOOD + SILENCE + " Tap ANYWHERE, when you are ready";

    public static final String REGULAR_REMINDER_SPANISH =
            //"Time for your early morning meds you have 2 meds to take now before food"
            "Es la hora de su medicina a las " + TIME_SLOT +
                    "le toca tomar" + MED_COUNT + " medicinas " +
                    BEFORE_FOOD + SILENCE + " toque su reloj dondequiera cuando este listo";


    public static final String VOICE_PROMPT_NURSE_REMINDER =
            " You MISSED " + MED_COUNT + " of your " +
                    TIME_SLOT + "meds" + SILENCE +
                    "and now " + NURSE_NAME + " is reminding you to take them " +
                    BEFORE_FOOD + SILENCE + " Tap ANYWHERE, and I'll tell you which one's you missed";
    public static final String VOICE_PROMPT_NURSE_REMINDER_SPANISH =
            "¿Oye?" + SILENCE + " faltó " + MED_COUNT + " de la medicina que toca a las " + TIME_SLOT + SILENCE
                    + " y la enfermera " + NURSE_NAME + " le está recordando tomarlas " + BEFORE_FOOD + SILENCE + " Toque su reloj dondequiera, y le dire cual medicina le faltó.";


    public static final String INJECTION_REMINDER =
            "Time for your " + TIME_SLOT + "injection" + SILENCE + " You need to inject " + DOSAGE + "  of " +
                    MED_NAME + "  Tap ANYWHERE, when you are ready";
}
