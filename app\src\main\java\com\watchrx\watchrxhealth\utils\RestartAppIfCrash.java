package com.watchrx.watchrxhealth.utils;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.watchrx.watchrxhealth.SplashActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.pedometer.Database;
import com.watchrx.watchrxhealth.pedometer.SensorListener;

import java.io.PrintWriter;
import java.io.StringWriter;

public class RestartAppIfCrash implements Thread.UncaughtExceptionHandler {
    private final Activity activity;

    public RestartAppIfCrash(Activity activity) {
        this.activity = activity;
    }

    @Override
    public void uncaughtException(@NonNull Thread thread, Throwable ex) {

        StringWriter sw = new StringWriter();
        ex.printStackTrace(new PrintWriter(sw));
        String stackTrace = sw.toString(); // stack trace as a string

        LogUpdateToServer.setLogUpdateRequired();

        LogUtils.debug("Got an Exception: " + ex.getMessage() + " Complete StackTrace is:\n" + stackTrace);

        LogUtils.debug("App get crashed And It's restarting....... ");

        ReminderUtils.clearExistingReminderAlarms(activity);
        ReminderUtils.clearExistingDailyUpdaterAlarm(activity);
        Globals.isWatchRegistered = false;

        activity.stopService(new Intent(activity, SensorListener.class));
        ReminderUtils.clearExistingPedoMeterAlarm(activity);
        Database db = Database.getInstance(WatchApp.getContext());
        db.reCreateDB(activity);


        CommUtils.disableBroadcastReceiver(WatchApp.getContext());

        Intent intent = new Intent(activity, SplashActivity.class);

        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_CLEAR_TASK
                | Intent.FLAG_ACTIVITY_NEW_TASK);


        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            pendingIntent = PendingIntent.getActivity(
                    activity.getApplicationContext(), Globals.reminderAlarmCount, intent, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(
                    activity.getApplicationContext(), Globals.reminderAlarmCount, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }

        AlarmManager mgr = (AlarmManager) activity.getBaseContext()
                .getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 5000,
                pendingIntent);

        //This will finish your activity manually
        activity.finish();

        //This will stop your application and take out from it.
        System.exit(2);

    }
}
