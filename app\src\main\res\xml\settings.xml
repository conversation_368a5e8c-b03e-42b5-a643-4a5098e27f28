<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <PreferenceCategory android:title="General">
        <CheckBoxPreference
            android:defaultValue="true"
            android:key="enable_automatic_subscription"
            android:title="Enable Automatic Track Subscription" />
    </PreferenceCategory>
    <PreferenceCategory android:title="@string/codec_title">
        <CheckBoxPreference
            android:defaultValue="false"
            android:key="vp8_simulcast"
            android:title="@string/vp8_simulcast" />
        <ListPreference
            android:key="video_codec"
            android:negativeButtonText="@null"
            android:positiveButtonText="@null"
            android:title="@string/pref_title_video_codec" />
        <ListPreference
            android:key="audio_codec"
            android:negativeButtonText="@null"
            android:positiveButtonText="@null"
            android:title="@string/pref_title_audio_codec" />
    </PreferenceCategory>
    <PreferenceCategory android:title="@string/sender_bandwidth_constraints">
        <EditTextPreference
            android:defaultValue="0"
            android:key="sender_max_audio_bitrate"
            android:title="@string/max_audio_bitrate" />
        <EditTextPreference
            android:defaultValue="0"
            android:key="sender_max_video_bitrate"
            android:title="@string/max_video_bitrate" />
    </PreferenceCategory>
</androidx.preference.PreferenceScreen>
