<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/video_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true"
    tools:showIn="@layout/activity_video">

    <LinearLayout
        android:id="@+id/status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_gravity="bottom"
        android:orientation="vertical">

        <TextView
            android:id="@+id/video_status_textview"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:text="Welcome to WatchRx"
            android:textColor="@color/black"
            android:visibility="gone"
            android:textSize="20sp" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/reconnecting_progress_bar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true"
        android:indeterminateTint="@android:color/black"
        android:visibility="visible" />

    <com.twilio.video.VideoView
        android:id="@+id/thumbnail_video_view"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_gravity="bottom|start"
        android:layout_margin="16dp"
        android:visibility="gone"
        app:layout_constraintCircleRadius="@dimen/d_10_d"
        app:tviMirror="false"
        app:tviOverlaySurface="true" />

    <com.twilio.video.VideoView
        android:id="@+id/primary_video_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_margin="5dp" />

</merge>
