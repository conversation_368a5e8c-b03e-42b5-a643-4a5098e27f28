package com.watchrx.watchrxhealth.auth;

import android.Manifest;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.style.UnderlineSpan;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.SplashActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.EmailHelper;
import com.watchrx.watchrxhealth.utils.ExactAlarmHelper;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONObject;

import java.net.URL;
import java.util.Objects;

@RequiresApi(api = Build.VERSION_CODES.TIRAMISU)
public class LoginScreen extends AppCompatActivity {

    String[] ANDROID_12_PERMISSIONS_REQUIRED = new String[]{
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.SCHEDULE_EXACT_ALARM,
            Manifest.permission.POST_NOTIFICATIONS,
            Manifest.permission.BLUETOOTH_CONNECT
    };
    private static final int REQUEST_PERMISSIONS = 100;
    private static final int REQUEST_CODE_FOREGROUND_SERVICE_MEDIA_PLAYBACK = 200;

    private EditText userNameET;
    private Dialog loadingDialog;

    public SharedPreferences sharedpreferences;
    public static final String mypreference = "WatchRx";
    public static final String userName = "username";
    public static final String password = "password";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        LogUtils.setLogLevel(LogUtils.DEBUG_LEVEL);
        LogUtils.debug("********************** Application Started ********************");
        super.onCreate(savedInstanceState);
        setEdges();
        sharedpreferences = WatchApp.getContext().getSharedPreferences(mypreference, Context.MODE_PRIVATE);
        if (checkPermission(ANDROID_12_PERMISSIONS_REQUIRED)) {
            ActivityCompat.requestPermissions(this, ANDROID_12_PERMISSIONS_REQUIRED, REQUEST_PERMISSIONS);
        } else {
            initView();
        }
    }

    private void initView() {
        if (checkSelfPermission(Manifest.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK) != PackageManager.PERMISSION_GRANTED) {
            if (Build.VERSION.SDK_INT >= 34) {
                requestPermissions(
                        new String[]{Manifest.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK},
                        REQUEST_CODE_FOREGROUND_SERVICE_MEDIA_PLAYBACK
                );
            }
        }
        if (!ExactAlarmHelper.hasExactAlarmPermission(this)) {
            ExactAlarmHelper.requestExactAlarmPermission(this);
        }

        userNameET = findViewById(R.id.userName);
        Button nextButton = findViewById(R.id.nextButton);
        setupLoadingDialog();
        nextButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                checkOtpValidation();
            }
        });

        TextView forgotPassword = findViewById(R.id.forgotPassword);
        forgotPassword.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(LoginScreen.this, ResendOTPScreen.class);
                startActivity(intent);
            }
        });

        TextView privacyPolicy = findViewById(R.id.privacyPolicy);

        SpannableString content = new SpannableString("Privacy Policy");
        content.setSpan(new UnderlineSpan(), 0, content.length(), 0);
        privacyPolicy.setText(content);
        privacyPolicy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String privacyPolicyUrl = "https://watchrx.io/privacy-policy/";
                Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(privacyPolicyUrl));
                startActivity(browserIntent);
            }
        });

        if (sharedpreferences != null && sharedpreferences.contains(userName) && sharedpreferences.contains(password)) {
            LogUtils.debug("LoginActivity User Details Found");
            SharedPreferences prefs = WatchApp.getContext().getSharedPreferences("WatchRx", MODE_PRIVATE);
            String username = prefs.getString("username", null);
            String password = prefs.getString("password", null);
            loginMethod(username, password);
        } else {
            userNameET.setText("+1");
        }
    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    private void loginMethod(String enteredUserName, String enteredPassword) {
        Log.e("Login Method", "Login Method called \n\n");
        try {
            showLoadingDialog();
            closeKeyboard();
            LogUtils.debug("Going call AUTHENTICATE API with User Name:" + enteredUserName + " Password: " + enteredPassword);
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.AUTHENTICATE_URL);
            loginModel.username = enteredUserName.trim();
            loginModel.password = enteredPassword.trim();
            userNameET.setText(enteredUserName);
            String json = new Gson().toJson(loginModel);
            SharedPreferences.Editor editor = sharedpreferences.edit();
            editor.putString(userName, enteredUserName);
            editor.putString(password, enteredPassword);
            editor.apply();
            new RestAsyncTask(url, json, null, new AuthTokenResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private class AuthTokenResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            LogUtils.debug("Received the response from AuthTokenResponseHandler");
            loadingDialog.dismiss();
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("Invalid Credentials.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginScreen.this)
                                        .setTitle("WatchRx")
                                        .setMessage("OTP is not validate, Click resend OTP link below ")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("OTP is already validated. Invalid Credentials.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginScreen.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Invalid user name or password")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").trim().equalsIgnoreCase("OTP not validated.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginScreen.this)
                                        .setTitle("WatchRx")
                                        .setMessage("You have not validated your OTP yet, Please validate OTP along with set your new password.")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        Globals.authToken = jsonObject.optString("token");
                        Toast.makeText(LoginScreen.this, "Login successful", Toast.LENGTH_SHORT).show();
                        Intent intent = new Intent(LoginScreen.this, SplashActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }


    private void setupLoadingDialog() {
        loadingDialog = new Dialog(this);
        loadingDialog.setContentView(R.layout.dialog_loading);
        loadingDialog.setCancelable(false);

        loadingDialog.setOnShowListener(dialog -> {
            ImageView loaderImage = loadingDialog.findViewById(R.id.loaderImage);
            Animation rotateAnimation = AnimationUtils.loadAnimation(this, R.anim.rotate);
            loaderImage.startAnimation(rotateAnimation);
        });
    }

    private void showLoadingDialog() {
        if (loadingDialog == null) {
            setupLoadingDialog();
        }

        if (!loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private void checkOtpValidation() {
        String input = userNameET.getText().toString().trim();

        if (input.isEmpty()) {
            Toast.makeText(this, "Please enter email or phone number", Toast.LENGTH_SHORT).show();
            return;
        }

        if (!EmailHelper.isValidEmail(input) && !isValidPhoneNumber(input)) {
            Toast.makeText(this, "Enter a valid email or phone number (with country code)", Toast.LENGTH_SHORT).show();
            return;
        }
        checkOTPValidated(input);
    }

    private boolean isValidPhoneNumber(String phone) {
        return phone.matches("^\\+[1-9]\\d{9,14}$");
    }


    private void checkOTPValidated(String email) {
        try {
            showLoadingDialog();
            LogUtils.debug("Going check email is validated with OTP or not: " + email);
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.OTP_VALIDATION_CHECK_URL);
            loginModel.emailId = email.trim();
            String json = new Gson().toJson(loginModel);
            new RestAsyncTask(url, json, null, new OTPValidationResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class OTPValidationResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            LogUtils.debug("Received the response from OTPValidationResponseHandler");
            loadingDialog.dismiss();

            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error", "Failed to get authenticate token");
                showDialogPopup("Server Error", "Unable to validate giver user name with server please try again");
                return;
            }

            try {
                JSONObject jsonObject = new JSONObject(result.getResult().toString());
                String responseCode = jsonObject.optString("responseCode");
                String responseMessage = jsonObject.optString("responseMessage");

                if (responseCode.equalsIgnoreCase("500")
                        && responseMessage.equalsIgnoreCase("More than one patient found for given phone or email.")) {
                    showDialogPopup("User Exists", "Already email or phone exists, Please contact case manager for details");
                    return;
                }

                if (responseCode.equalsIgnoreCase("500")
                        && responseMessage.equalsIgnoreCase("No patient registered with for given phone or email.")) {
                    showDialogPopup("User Not Found", "No patient registered with for given phone or email.");
                    return;
                }
                boolean isOtpValidated = !("200".equalsIgnoreCase(responseCode)
                        && "OTP_NOT_VALIDATED".equalsIgnoreCase(responseMessage));
                showOtpResultAndNavigate(isOtpValidated);
            } catch (Exception e) {
                Log.e("OTPHandler", "JSON Parsing Error: " + e.getMessage(), e);
                showOtpResultAndNavigate(false);
            }
        }

        private void showOtpResultAndNavigate(boolean isOtpValidated) {
            Intent intent = new Intent(LoginScreen.this, EnterPasswordActivity.class);
            intent.putExtra("userName", userNameET.getText().toString().trim());
            intent.putExtra("isOtpValidated", isOtpValidated);
            startActivity(intent);
        }
    }

    private void showDialogPopup(String title, String desc) {
        new android.app.AlertDialog.Builder(LoginScreen.this)
                .setTitle(title)
                .setMessage(desc)
                .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int which) {
                        dialog.dismiss();
                    }
                }).show();
    }

    private boolean checkPermission(String[] permissions) {
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(getApplicationContext(), permission) != PackageManager.PERMISSION_GRANTED) {
                return true;
            }
        }
        return false;
    }

    private void setEdges() {
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_login_screen);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.layout), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        Objects.requireNonNull(getSupportActionBar()).hide();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CODE_FOREGROUND_SERVICE_MEDIA_PLAYBACK) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initView();
            } else {
                new android.app.AlertDialog.Builder(LoginScreen.this)
                        .setTitle("Permission Denied")
                        .setMessage("Without this permission the app will be not work !!")
                        .setPositiveButton("RE-TRY", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                ActivityCompat.requestPermissions(LoginScreen.this, ANDROID_12_PERMISSIONS_REQUIRED, REQUEST_PERMISSIONS);
                            }
                        })
                        .setNegativeButton("I'M SURE", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        }).show();
            }
        }

        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            initView();
        } else {
            new android.app.AlertDialog.Builder(LoginScreen.this)
                    .setTitle("Permission Denied")
                    .setMessage("Without this permission the app will be not work !!")
                    .setPositiveButton("RE-TRY", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            ActivityCompat.requestPermissions(LoginScreen.this, ANDROID_12_PERMISSIONS_REQUIRED, REQUEST_PERMISSIONS);
                        }
                    })
                    .setNegativeButton("I'M SURE", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                        }
                    }).show();
        }
    }
}