package com.watchrx.watchrxhealth.receivers;

import static com.watchrx.watchrxhealth.constants.CommonConstants.MAX_VITAL_TRIGGER_TIME_DIFFERENCE;
import static java.lang.Math.abs;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.VitalConfiguration;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

public class VitalReminderReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        LogUtils.debug("Vital Reminder alarm to be triggered at: '" + intent.getLongExtra("triggerAt", -1) +
                "  time was received at: '" +
                System.currentTimeMillis() + "' for :'" + intent.getStringExtra("day") + "-" + intent.getStringExtra("time")
                + "-" + intent.getStringExtra("vitalScheduleId") + "'");

        long triggerTimeMillis = intent.getLongExtra("triggerAt", -1);
        long currentTimeMillis = System.currentTimeMillis();

        if (abs(currentTimeMillis - triggerTimeMillis) > MAX_VITAL_TRIGGER_TIME_DIFFERENCE) {
            Log.e("Outside Alarm", "This alarm is  outside the time window");
            LogUtils.debug("This alarm is  outside the time window");
            return;
        }

        String key = (intent.getStringExtra("day") + "-" + intent.getStringExtra("time")
                + "-" + intent.getStringExtra("vitalScheduleId") + intent.getStringExtra("collectMode")).toLowerCase();

        Log.e("Alarm Key", key);
        LogUtils.debug("Key need to check :" + key);
        Globals.vitalsIntentMap.remove(key);
        Globals.timersCurrentlySetMap.remove(key);
        String collectMode = intent.getStringExtra("collectMode");
        VitalConfiguration configuration = VitalConfiguration.getVitalDetailsForId(intent.getStringExtra("vitalScheduleId"));
        if (configuration != null) {
            String vitalTypeName = configuration.getVitalTypeName();
            VitalStatusDetails vitalStatusDetails = VitalStatusDetails.getVitalStatusByVitalName(vitalTypeName);
            if (vitalStatusDetails != null && vitalStatusDetails.getVitalStatus().equalsIgnoreCase("enable")) {
                Log.e("Vital Reminder :", vitalTypeName + " Checking status :" + vitalStatusDetails.getVitalStatus());
                if (collectMode != null && collectMode.equalsIgnoreCase("frequency")) {
                    ReminderUtils.setUpVitalIdUsingFrequency(context,
                            intent.getStringExtra("vitalScheduleId"), intent.getStringExtra("day"), configuration.getFrequency(), collectMode);
                }
                if (configuration.getVitalTypeName().equalsIgnoreCase("Heart Rate")
                        && configuration.getDeviceName().equalsIgnoreCase("Watch")) {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("HeartRateActivity");
                    info.setAlertId(intent.getStringExtra("vitalScheduleId"));
                    Globals.priorityQueue.add(info);
                    if (WatchApp.isInForeground()) {
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    } else {
                        final NotificationHelper notificationHelper = new NotificationHelper(context);
                        notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                    }
                } else {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setAlertId(intent.getStringExtra("vitalScheduleId"));
                    info.setBeforeOrAfterFood(configuration.getDeviceName());
                    info.setCaregiverName(configuration.getVitalTypeName());
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("NewVitalsActivity");
                    Globals.priorityQueue.add(info);
                    if (WatchApp.isInForeground()) {
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    } else {
                        final NotificationHelper notificationHelper = new NotificationHelper(context);
                        notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                    }
                }
            } else {
                assert vitalStatusDetails != null;
                Log.e("Vital Reminder :", vitalTypeName + " Checking status :" + vitalStatusDetails.getVitalStatus());
                LogUtils.debug(vitalTypeName + " Checking status :" + vitalStatusDetails.getVitalStatus() + " Alarm didn't proceed.");
            }
        } else {
            Log.e("Outside Alarm", "This alarm is  outside the time window : No details found in DB");
            LogUtils.debug("This alarm is  outside the time window : No details found in DB");
        }
    }
}