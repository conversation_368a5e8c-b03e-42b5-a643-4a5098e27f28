//package com.watchrx.watch.utils;
//
//import android.app.Activity;
//import android.content.Context;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.ViewGroup;
//import android.widget.ArrayAdapter;
//import android.widget.ImageView;
//import android.widget.TextView;
//
//import com.watchrx.watch.R;
//
//import java.util.List;
//
//public class AlertListAdapter extends ArrayAdapter<AlertsListDTO> {
//
//
//    private List<AlertsListDTO> lists;
//    private LayoutInflater inflater;
//    private int resource;
//
//    public AlertListAdapter(Context context, int resource, List<AlertsListDTO> objects) {
//        super(context, resource, objects);
//        this.lists = objects;
//        this.resource = resource;
//        inflater = (LayoutInflater) context.getSystemService(Activity.LAYOUT_INFLATER_SERVICE);
//
//    }
//
//    @Override
//    public View getView(int position, View convertView, ViewGroup parent) {
//        if (convertView == null) {
//            convertView = inflater.inflate(resource, null);
//        }
//        TextView title;
//        ImageView imageView;
//
//        //title = (TextView) convertView.findViewById(R.id.textHeading);
//        imageView = (ImageView) convertView.findViewById(R.id.icon);
//        if (lists.get(position).getTitle().equalsIgnoreCase("Missed call")) {
//            imageView.setImageResource(R.drawable.missedcal);
//            //title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 27.0f);
//            String newString = lists.get(position).getDescription().replaceAll("[^0-9]", "");
//            //title.setText(newString);
//        } else {
//            imageView.setImageResource(R.drawable.medicine);
//
//            if (lists.get(position).getDescription().equalsIgnoreCase("AfternoonSnack") || lists.get(position).getDescription().equalsIgnoreCase("EarlyMorning")) {
//                // title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 23.0f);
//                // title.setText(lists.get(position).getDescription() + " Meds");
//            } else {
//                // title.setTextSize(TypedValue.COMPLEX_UNIT_PX, 30.0f);
//                // title.setText(lists.get(position).getDescription() + " Meds");
//            }
//        }
//        return convertView;
//    }
//}
