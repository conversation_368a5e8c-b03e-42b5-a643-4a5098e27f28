<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="#FFFFFF">

    <TextView
        android:id="@+id/tv_rationale_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Health Data Access"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="#333333"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <TextView
        android:id="@+id/tv_rationale_description"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:text="WatchRx Health needs access to your Blood Sugar data from Health Connect to provide you with personalized health insights and tracking. Your data is used only within this app and is not shared with third parties."
        android:textSize="16sp"
        android:textColor="#666666"
        android:layout_marginBottom="32dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Cancel"
            android:textColor="#666666"
            android:background="@drawable/blue_outline"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_continue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Continue"
            android:textColor="#FFFFFF"
            android:background="@drawable/button_shape_green"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>