<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:padding="16dp"
    tools:context=".LoginActivity">

    <!-- Vertical Guideline -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/verticalGuide"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.50" />

    <ImageView
        android:id="@+id/appLogo"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:layout_marginTop="32dp"
        android:src="@drawable/app_logo_1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/userName"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/rounded_edittext"
        android:hint="@string/email_id"
        android:paddingStart="16dp"
        android:textColor="@color/colorText"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/appLogo" />

    <EditText
        android:id="@+id/password"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/rounded_edittext"
        android:hint="@string/password"
        android:inputType="textPassword"
        android:paddingStart="16dp"
        android:textColor="@color/colorText"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/userName" />

    <EditText
        android:id="@+id/otpET"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="8dp"
        android:background="@drawable/rounded_edittext"
        android:hint="@string/otp"
        android:inputType="number"
        android:paddingStart="16dp"
        android:textColor="@color/colorText"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/password" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/loginButton"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_gradient"
        android:text="@string/login"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:fontFamily="@font/lato_regular"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otpET" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/validateOTP"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_gradient"
        android:text="@string/validateotp"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/otpET" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/resendOTPButton"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:layout_marginTop="16dp"
        android:background="@drawable/button_gradient"
        android:fontFamily="@font/lato_regular"
        android:text="@string/resendOTP"
        android:textColor="@color/white"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/password" />

    <ProgressBar
        android:id="@+id/loginProgressBar"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/forgotPassword"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/forgot_your_password"
        android:textColor="@color/red"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@id/verticalGuide"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginButton" />

    <TextView
        android:id="@+id/privacyPolicy"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/privacy_policy"
        android:textColor="@color/blue"
        android:textSize="18sp"
        app:layout_constraintEnd_toStartOf="@id/verticalGuide"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/forgotPassword" />

    <TextView
        android:id="@+id/validateText"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/otp_validate"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/verticalGuide"
        app:layout_constraintTop_toBottomOf="@id/loginButton" />

    <TextView
        android:id="@+id/loginText"
        android:layout_width="0dp"
        android:layout_height="40dp"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="@string/already_validated"
        android:textSize="18sp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/verticalGuide"
        app:layout_constraintTop_toBottomOf="@id/validateOTP" />

</androidx.constraintlayout.widget.ConstraintLayout>
