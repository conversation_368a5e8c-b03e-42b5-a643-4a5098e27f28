<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_reminder"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/App_color"
    android:keepScreenOn="true"
    android:orientation="vertical"
    android:weightSum="3"
    tools:context=".ble.NewVitalsActivity">

    <LinearLayout
        android:id="@+id/imagelayout2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1.25"
        android:orientation="vertical"
        android:paddingStart="3dp"
        android:paddingEnd="5dp"
        android:paddingBottom="2dp"
        android:weightSum="2">

        <TextView
            android:id="@+id/device_name"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center|center_horizontal"
            android:layout_weight="1"
            android:text="@string/no_device"
            android:textAlignment="center"
            android:textColor="#4defe2"
            android:textSize="30sp"
            android:textStyle="bold"
            android:visibility="gone" />

        <TextView
            android:id="@+id/vital_type"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center|center_horizontal"
            android:layout_weight="1"
            android:text="@string/vital_name"
            android:textAlignment="center"
            android:textColor="@color/white"
            android:textSize="25sp"
            android:textStyle="bold" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/imagelayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_weight="1.0"
        android:orientation="horizontal"
        android:paddingStart="3dp"
        android:paddingEnd="5dp"
        android:paddingBottom="2dp"
        android:weightSum="2">

        <Button
            android:id="@+id/scan"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/button_shape_green"
            android:gravity="center_vertical|center_horizontal"
            android:text="@string/scan"
            android:textColor="@color/white"
            android:visibility="gone" />

        <Button
            android:id="@+id/connect_to_device"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/button_shape_green"
            android:gravity="center_vertical|center_horizontal"
            android:text="@string/connect"
            android:textColor="@color/white"
            android:visibility="gone" />

        <Button
            android:id="@+id/disconnect_to_device"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/button_shape_red"
            android:gravity="center_vertical|center_horizontal"
            android:text="@string/exit"
            android:textColor="@color/white"
            android:visibility="gone" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.75"
        android:orientation="horizontal"
        android:paddingStart="3dp"
        android:paddingEnd="5dp"
        android:paddingBottom="2dp"
        android:weightSum="3">

        <ImageButton
            android:id="@+id/imageView_setting"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_weight="0.84"
            android:background="@drawable/phn"
            android:contentDescription="@string/todo" />

        <ImageView
            android:id="@+id/warning"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.26"
            android:background="@drawable/warning"
            android:visibility="invisible" />

        <ToggleButton
            android:id="@+id/toggleButton1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.87"
            android:background="@drawable/check"
            android:checked="true"
            android:textOff=""
            android:textOn=""
            android:textSize="19sp" />

    </LinearLayout>
</LinearLayout>