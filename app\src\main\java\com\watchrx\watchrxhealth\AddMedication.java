package com.watchrx.watchrxhealth;

import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.syncup.SyncUpWithServer;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ProgressDialogUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class AddMedication extends AppCompatActivity implements View.OnClickListener {

    private TextInputEditText medicationName, dosage, color;
    private CheckBox all, sun, mon, tue, wed, thur, fri, sat;
    private CheckBox earlyMorningCheck, breakfastCheck, lunchCheck, afternoonCheck, dinnerCheck, bedCheck;
    private TextInputEditText earlyMorningQnty, breakfastQnty, lunchQnty, afternoonQnty, dinnerQnty, bedQnty;
    private Spinner earlyMorningSpn, breakfastSpn, lunchSpn, afternoonSpn, dinnerSpn, bedSpn;
    private LinearLayout earlyMorningED, breakfastED, lunchED, afternoonED, dinnerED, bedED;
    private RadioGroup medsTypeRadio;
    private final String[] dropDownValues = {"BeforeFood", "AnyTime", "AfterFood"};
    private ImageView chooseImage;
    private File medicationImageFile = null;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        setContentView(R.layout.activity_add_medication);

        medicationName = findViewById(R.id.medicationName);
        dosage = findViewById(R.id.dosage);
        color = findViewById(R.id.color);

        all = findViewById(R.id.all);
        sun = findViewById(R.id.sun);
        mon = findViewById(R.id.mon);
        tue = findViewById(R.id.tue);
        wed = findViewById(R.id.wed);
        thur = findViewById(R.id.thur);
        fri = findViewById(R.id.fri);
        sat = findViewById(R.id.sat);

        earlyMorningCheck = findViewById(R.id.earlyMorningCheck);
        breakfastCheck = findViewById(R.id.breakfastCheck);
        lunchCheck = findViewById(R.id.lunchCheck);
        afternoonCheck = findViewById(R.id.afternoonCheck);
        dinnerCheck = findViewById(R.id.dinnerCheck);
        bedCheck = findViewById(R.id.bedCheck);

        earlyMorningQnty = findViewById(R.id.earlyMorningQanty);
        breakfastQnty = findViewById(R.id.breakfastQanty);
        lunchQnty = findViewById(R.id.lunchQanty);
        afternoonQnty = findViewById(R.id.afternoonQanty);
        dinnerQnty = findViewById(R.id.dinnerQanty);
        bedQnty = findViewById(R.id.bedQanty);

        earlyMorningSpn = findViewById(R.id.earlyMorningSpn);
        breakfastSpn = findViewById(R.id.breakfastSpn);
        lunchSpn = findViewById(R.id.lunchSpn);
        afternoonSpn = findViewById(R.id.afternoonSpn);
        dinnerSpn = findViewById(R.id.dinnerSpn);
        bedSpn = findViewById(R.id.bedSpn);

        earlyMorningED = findViewById(R.id.earlyMorningED);
        breakfastED = findViewById(R.id.breakfastED);
        lunchED = findViewById(R.id.lunchED);
        afternoonED = findViewById(R.id.afternoonED);
        dinnerED = findViewById(R.id.dinnerED);
        bedED = findViewById(R.id.bedED);


        all.setOnClickListener(this);
        sun.setOnClickListener(this);
        mon.setOnClickListener(this);
        tue.setOnClickListener(this);
        wed.setOnClickListener(this);
        thur.setOnClickListener(this);
        fri.setOnClickListener(this);
        sat.setOnClickListener(this);

        earlyMorningCheck.setOnClickListener(this);
        breakfastCheck.setOnClickListener(this);
        lunchCheck.setOnClickListener(this);
        afternoonCheck.setOnClickListener(this);
        dinnerCheck.setOnClickListener(this);
        bedCheck.setOnClickListener(this);

        medsTypeRadio = findViewById(R.id.medsTypeRadio);

        ArrayAdapter<String> timeSlotsValues = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, dropDownValues);
        timeSlotsValues.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);

        earlyMorningSpn.setAdapter(timeSlotsValues);
        breakfastSpn.setAdapter(timeSlotsValues);
        lunchSpn.setAdapter(timeSlotsValues);
        afternoonSpn.setAdapter(timeSlotsValues);
        dinnerSpn.setAdapter(timeSlotsValues);
        bedSpn.setAdapter(timeSlotsValues);

        Button submit = findViewById(R.id.submit);
        chooseImage = findViewById(R.id.chooseImage);
        chooseImage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
                startActivityForResult(intent, 2);
            }
        });

        submit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (medicationName == null || Objects.requireNonNull(medicationName.getText()).toString().isEmpty()) {
                    Objects.requireNonNull(medicationName).setError("Please enter Medication name");
                    return;
                }
                if (dosage == null || Objects.requireNonNull(dosage.getText()).toString().isEmpty()) {
                    Objects.requireNonNull(dosage).setError("Please enter dosage");
                    return;
                }
                if (color == null || Objects.requireNonNull(color.getText()).toString().isEmpty()) {
                    Objects.requireNonNull(color).setError("Please enter color");
                    return;
                }

                if (!all.isChecked() && !sun.isChecked() && !mon.isChecked() && !tue.isChecked() && !wed.isChecked()
                        && !thur.isChecked() && !fri.isChecked() && !sat.isChecked()) {
                    Toast.makeText(AddMedication.this, "Select at least one day", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (earlyMorningCheck.isChecked()) {
                    if (earlyMorningQnty == null || Objects.requireNonNull(earlyMorningQnty.getText()).toString().isEmpty()) {
                        Objects.requireNonNull(earlyMorningQnty).setError("Please enter quantity");
                        return;
                    }
                }
                if (breakfastCheck.isChecked()) {
                    if (breakfastQnty == null || Objects.requireNonNull(breakfastQnty.getText()).toString().isEmpty()) {
                        Objects.requireNonNull(breakfastQnty).setError("Please enter quantity");
                        return;
                    }
                }
                if (lunchCheck.isChecked()) {
                    if (lunchQnty == null || Objects.requireNonNull(lunchQnty.getText()).toString().isEmpty()) {
                        Objects.requireNonNull(lunchQnty).setError("Please enter quantity");
                        return;
                    }
                }
                if (afternoonCheck.isChecked()) {
                    if (afternoonQnty == null || Objects.requireNonNull(afternoonQnty.getText()).toString().isEmpty()) {
                        Objects.requireNonNull(afternoonQnty).setError("Please enter quantity");
                        return;
                    }
                }

                if (dinnerCheck.isChecked()) {
                    if (dinnerQnty == null || Objects.requireNonNull(dinnerQnty.getText()).toString().isEmpty()) {
                        Objects.requireNonNull(dinnerQnty).setError("Please enter quantity");
                        return;
                    }
                }
                if (bedCheck.isChecked()) {
                    if (bedQnty == null || Objects.requireNonNull(bedQnty.getText()).toString().isEmpty()) {
                        Objects.requireNonNull(bedQnty).setError("Please enter quantity");
                        return;
                    }
                }
                if (!earlyMorningCheck.isChecked() && !breakfastCheck.isChecked() && !lunchCheck.isChecked() &&
                        !afternoonCheck.isChecked() && !dinnerCheck.isChecked() && !bedCheck.isChecked()) {
                    Toast.makeText(AddMedication.this, "Select at least one time slot", Toast.LENGTH_LONG).show();
                }
                sendMedicationDetailsToServer();
            }
        });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == RESULT_OK) {
            Uri selectedImage = data.getData();
            String[] filePath = {MediaStore.Images.Media.DATA};
            Cursor c = null;
            if (selectedImage != null) {
                c = getContentResolver().query(selectedImage, filePath, null, null, null);
            }
            if (c != null) {
                c.moveToFirst();
            }
            int columnIndex = 0;
            if (c != null) {
                columnIndex = c.getColumnIndex(filePath[0]);
            }
            String picturePath = c.getString(columnIndex);
            c.close();
            Bitmap thumbnail = (BitmapFactory.decodeFile(picturePath));
            thumbnail = getResizedBitmap(thumbnail, 400);
            chooseImage.setImageBitmap(thumbnail);
            medicationImageFile = new File(Objects.requireNonNull(picturePath));
            Log.w("File Path", medicationImageFile.getPath());
        }
    }

    public Bitmap getResizedBitmap(Bitmap image, int maxSize) {
        int width = image.getWidth();
        int height = image.getHeight();

        float bitmapRatio = (float) width / (float) height;
        if (bitmapRatio > 1) {
            width = maxSize;
            height = (int) (width / bitmapRatio);
        } else {
            height = maxSize;
            width = (int) (height * bitmapRatio);
        }
        return Bitmap.createScaledBitmap(image, width, height, true);
    }

    private void sendMedicationDetailsToServer() {
        ProgressDialogUtil.showProgressDialog(AddMedication.this, "Loading...");
        String medicationNameV = Objects.requireNonNull(medicationName.getText()).toString();
        String dosageV = Objects.requireNonNull(dosage.getText()).toString();
        String colorV = Objects.requireNonNull(color.getText()).toString();

        int medicationTypeId = medsTypeRadio.getCheckedRadioButtonId();
        RadioButton radioButton = (RadioButton) findViewById(medicationTypeId);
        String medicationTypeName = radioButton.getText().toString();

        String selectedMedsTypeId = getSelectedMedsTypeByName(medicationTypeName);
        String daysOfWeek = getSelectedDays();

        List<String> timeSlots = new ArrayList<>();
        List<String> quantities = new ArrayList<>();
        List<String> medTimeRelativeToFood = new ArrayList<>();
        if (earlyMorningCheck.isChecked()) {
            timeSlots.add("EarlyMorning");
            quantities.add(Objects.requireNonNull(earlyMorningQnty.getText()).toString());
            medTimeRelativeToFood.add(earlyMorningSpn.getSelectedItem().toString());
        }
        if (breakfastCheck.isChecked()) {
            timeSlots.add("Breakfast");
            quantities.add(Objects.requireNonNull(breakfastQnty.getText()).toString());
            medTimeRelativeToFood.add(breakfastSpn.getSelectedItem().toString());
        }
        if (lunchCheck.isChecked()) {
            timeSlots.add("Lunch");
            quantities.add(Objects.requireNonNull(lunchQnty.getText()).toString());
            medTimeRelativeToFood.add(lunchSpn.getSelectedItem().toString());
        }
        if (afternoonCheck.isChecked()) {
            timeSlots.add("AfternoonSnack");
            quantities.add(Objects.requireNonNull(afternoonQnty.getText()).toString());
            medTimeRelativeToFood.add(afternoonSpn.getSelectedItem().toString());
        }
        if (dinnerCheck.isChecked()) {
            timeSlots.add("Dinner");
            quantities.add(Objects.requireNonNull(dinnerQnty.getText()).toString());
            medTimeRelativeToFood.add(dinnerSpn.getSelectedItem().toString());
        }
        if (bedCheck.isChecked()) {
            timeSlots.add("Bed");
            quantities.add(Objects.requireNonNull(bedQnty.getText()).toString());
            medTimeRelativeToFood.add(bedSpn.getSelectedItem().toString());
        }

        String timeSlotsStr = concatList(timeSlots);
        String quantitiesStr = concatList(quantities);
        String medTimeRelativeToFoodStr = concatList(medTimeRelativeToFood);

        JSONObject mainObj = new JSONObject();
        try {
            mainObj.accumulate("prescriptionId", null);
            mainObj.accumulate("imageExists", true);
            mainObj.accumulate("name", medicationNameV);
            mainObj.accumulate("dosage", dosageV);
            mainObj.accumulate("color", colorV);
            mainObj.accumulate("type", selectedMedsTypeId);
            mainObj.accumulate("existingOrNewschedule", "Existing");
            mainObj.accumulate("beforeOrAfterFood", "Before");

            JSONObject timeOfMedicine = new JSONObject();
            timeOfMedicine.accumulate("earlymorning", earlyMorningCheck.isChecked());
            timeOfMedicine.accumulate("breakfast", breakfastCheck.isChecked());
            timeOfMedicine.accumulate("lunch", lunchCheck.isChecked());
            timeOfMedicine.accumulate("afternoonsnack", afternoonCheck.isChecked());
            timeOfMedicine.accumulate("dinner", dinnerCheck.isChecked());
            timeOfMedicine.accumulate("bed", bedCheck.isChecked());

            mainObj.accumulate("timeOfMedicine", timeOfMedicine);
            mainObj.accumulate("timeSlots", timeSlotsStr);
            mainObj.accumulate("quantities", quantitiesStr);
            mainObj.accumulate("medTimeRelativeToFood", medTimeRelativeToFoodStr);
            mainObj.accumulate("daysOfWeek", daysOfWeek);

            JSONObject day = new JSONObject();
            day.accumulate("sunday", sun.isChecked());
            day.accumulate("monday", mon.isChecked());
            day.accumulate("tuesday", tue.isChecked());
            day.accumulate("wednesday", wed.isChecked());
            day.accumulate("thursday", thur.isChecked());
            day.accumulate("friday", fri.isChecked());
            day.accumulate("saturday", sat.isChecked());
            mainObj.accumulate("day", day);
            mainObj.accumulate("patientId", PatientDetails.getFromDB().getPatientId());

            URL url = new URL(URLConstants.ADD_MEDS);
            Log.w("Request", mainObj.toString());
            new RestAsyncTask(url, mainObj.toString(), null, new MedicationResponseHandler(), null).execute();
        } catch (JSONException | MalformedURLException e) {
            e.printStackTrace();
        }
    }

    private String getSelectedDays() {
        String days = "";
        List<String> daysList = new ArrayList<>();
        if (all.isChecked()) {
            return "Su|Mo|Tu|We|Th|Fr|Sa";
        }
        if (sun.isChecked()) {
            daysList.add("Su");
        }
        if (mon.isChecked()) {
            daysList.add("Mo");
        }
        if (tue.isChecked()) {
            daysList.add("Tu");
        }
        if (wed.isChecked()) {
            daysList.add("We");
        }
        if (thur.isChecked()) {
            daysList.add("Th");
        }
        if (fri.isChecked()) {
            daysList.add("Fr");
        }
        if (sat.isChecked()) {
            daysList.add("Sa");
        }
        if (daysList.size() > 0) {
            days = concatList(daysList);
        }
        return days;
    }

    String concatList(List<String> sList) {
        Iterator<String> iter = sList.iterator();
        StringBuilder sb = new StringBuilder();
        while (iter.hasNext()) {
            sb.append(iter.next()).append(iter.hasNext() ? "|" : "");
        }
        return sb.toString();
    }

    private String getSelectedMedsTypeByName(String medicationTypeName) {
        switch (medicationTypeName) {
            case "Tablet":
                return "1";
            case "Injection":
                return "2";
            case "Inhaler":
                return "3";
            case "Nasal Spray":
                return "4";
            case "Syrup":
                return "5";
            default:
                return "";
        }
    }


    @Override
    public void onClick(View v) {
        if (v.getId() == R.id.sun || v.getId() == R.id.mon ||
                v.getId() == R.id.tue || v.getId() == R.id.wed ||
                v.getId() == R.id.thur || v.getId() == R.id.fri ||
                v.getId() == R.id.sat) {
            all.setChecked(isAllDaysChecked());
        } else if (v.getId() == R.id.all) {
            checkUnCheckAllDays(all.isChecked());
        } else if (v.getId() == R.id.earlyMorningCheck) {
            earlyMorningED.setVisibility(earlyMorningCheck.isChecked() ? View.VISIBLE : View.GONE);
        } else if (v.getId() == R.id.breakfastCheck) {
            breakfastED.setVisibility(breakfastCheck.isChecked() ? View.VISIBLE : View.GONE);
        } else if (v.getId() == R.id.lunchCheck) {
            lunchED.setVisibility(lunchCheck.isChecked() ? View.VISIBLE : View.GONE);
        } else if (v.getId() == R.id.afternoonCheck) {
            afternoonED.setVisibility(afternoonCheck.isChecked() ? View.VISIBLE : View.GONE);
        } else if (v.getId() == R.id.dinnerCheck) {
            dinnerED.setVisibility(dinnerCheck.isChecked() ? View.VISIBLE : View.GONE);
        } else if (v.getId() == R.id.bedCheck) {
            bedED.setVisibility(bedCheck.isChecked() ? View.VISIBLE : View.GONE);
        }
    }

    private void checkUnCheckAllDays(boolean b) {
        sun.setChecked(b);
        mon.setChecked(b);
        tue.setChecked(b);
        wed.setChecked(b);
        thur.setChecked(b);
        fri.setChecked(b);
        sat.setChecked(b);
    }

    private boolean isAllDaysChecked() {
        return sun.isChecked() && mon.isChecked() && tue.isChecked() &&
                wed.isChecked() && thur.isChecked() && fri.isChecked() && sat.isChecked();
    }

    private class MedicationResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult result) {
            ProgressDialogUtil.dismiss();
            if (result.getResult() == null) {
                LogUtils.debug("Failed to add to medication.");

                Toast.makeText(WatchApp.getContext(), "Failed to add medication.", Toast.LENGTH_LONG).show();
            } else {
                try {
                    JSONObject jsonObject = new JSONObject(result.getResult().toString());

                    if (jsonObject.optBoolean("success") && jsonObject.optInt("createdPrescription") > 0) {
                        Toast.makeText(WatchApp.getContext(), "Medication added successfully.", Toast.LENGTH_LONG).show();
                        new UploadFileAsync().execute(jsonObject.optString("createdPrescription"));
                    } else {
                        Toast.makeText(WatchApp.getContext(), "Failed to add medication.", Toast.LENGTH_LONG).show();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private class UploadFileAsync extends AsyncTask<String, Void, String> {

        @Override
        protected String doInBackground(String... params) {
            try {
                URL url = new URL(URLConstants.MEDICATION_IMAGE_UPLOAD);
                return uploadFile(url, params[0]);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return null;
        }

        @Override
        protected void onPostExecute(String result) {
            ProgressDialogUtil.dismiss();
            if (result != null) {

                JSONObject jsonObject = null;
                try {
                    jsonObject = new JSONObject(result);

                    if (jsonObject.optBoolean("success")) {
                        Toast.makeText(WatchApp.getContext(), "Medication image uploaded successfully.", Toast.LENGTH_LONG).show();
                        SyncUpWithServer.registerWatchAtNight(AddMedication.this);
                        finish();
                    } else {
                        Toast.makeText(WatchApp.getContext(), "Medication image uploaded failed.", Toast.LENGTH_LONG).show();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            ProgressDialogUtil.showProgressDialog(AddMedication.this);
        }
    }

    public String uploadFile(URL url, String medicationId) {
        try {
            if (medicationImageFile != null) {
                OkHttpClient client = new OkHttpClient().newBuilder()
                        .build();
                MediaType mediaType = MediaType.parse("text/plain");
                RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                        .addFormDataPart("imageFile", medicationImageFile.getName(),
                                RequestBody.create(MediaType.parse("application/octet-stream"),
                                        medicationImageFile))
                        .addFormDataPart("medicationId", medicationId)
                        .build();
                Request request = new Request.Builder()
                        .url(url)
                        .method("POST", body)
                        .build();
                Response response = client.newCall(request).execute();
                if (response.isSuccessful()) {
                    assert response.body() != null;
                    return response.body().string();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}