<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:padding="16dp">

    <!--    <TextView-->
    <!--        android:id="@+id/patient_name"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:fontFamily="@font/lato_regular"-->
    <!--        android:text="Hi John 👋"-->
    <!--        android:textSize="22sp"-->
    <!--        app:layout_constraintEnd_toStartOf="@+id/avatar"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/text_subtitle"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:fontFamily="@font/lato_regular"-->
    <!--        android:text="@string/how_is_your_health"-->
    <!--        android:textColor="#888888"-->
    <!--        android:textSize="14sp"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/patient_name" />-->

    <!--    <com.google.android.material.card.MaterialCardView-->
    <!--        android:id="@+id/avatar"-->
    <!--        android:layout_width="40dp"-->
    <!--        android:layout_height="40dp"-->
    <!--        android:layout_marginStart="8dp"-->
    <!--        android:visibility="gone"-->
    <!--        app:cardBackgroundColor="#9C27B0"-->
    <!--        app:cardCornerRadius="20dp"-->
    <!--        app:cardElevation="2dp"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent">-->

    <!--        <TextView-->
    <!--            android:id="@+id/patient_first_two_letters"-->
    <!--            android:layout_width="match_parent"-->
    <!--            android:layout_height="match_parent"-->
    <!--            android:gravity="center"-->
    <!--            android:textColor="@android:color/white"-->
    <!--            android:textStyle="bold" />-->

    <!--    </com.google.android.material.card.MaterialCardView>-->

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_care_manager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="0dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_purple_gradient"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <FrameLayout
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_icon_semitrans_white">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/dummy_content"
                    android:src="@drawable/ic_person_icon"
                    app:tint="@android:color/white" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/care_manager"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/cm_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_regular"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/chat_button"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/bg_icon_white">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/calling"
                    android:src="@drawable/dash_chat"
                    app:tint="@color/purple_700" />
            </FrameLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_clinic"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/card_care_manager"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_green_gradient"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <FrameLayout
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_icon_semitrans_white">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/dummy_content"
                    android:src="@drawable/ic_medical"
                    app:tint="@android:color/white" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/clinic_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_regular"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/physician_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_regular"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/clinic_phone"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/bg_icon_white">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/calling"
                    android:src="@drawable/ic_call"
                    app:tint="@color/purple_700" />
            </FrameLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_emergency"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="8dp"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardCornerRadius="24dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/card_clinic"
        app:strokeWidth="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_red_gradient"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="16dp">

            <FrameLayout
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/bg_icon_semitrans_white">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/dummy_content"
                    android:src="@drawable/ic_emergency"
                    app:tint="@android:color/white" />
            </FrameLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/emergency_contact"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/family_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_regular"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <FrameLayout
                android:id="@+id/family_phone"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/bg_icon_white">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="18dp"
                    android:layout_gravity="center"
                    android:contentDescription="@string/calling"
                    android:src="@drawable/ic_call"
                    app:tint="@color/purple_700" />
            </FrameLayout>
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <androidx.gridlayout.widget.GridLayout
        android:id="@+id/grid_dashboard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        app:columnCount="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_emergency">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_my_vitals"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_margin="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_columnWeight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/bg_vitals_icon"
                    android:clipToOutline="true">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_heart_pulse"
                        app:tint="@android:color/white" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:text="@string/my_vitals"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="center"
                    android:text="Track health metrics"
                    android:textColor="#666666"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_messages"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_margin="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_columnWeight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/bg_messages_icon"
                    android:clipToOutline="true">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/dash_chat"
                        app:tint="@android:color/white" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:text="@string/messages"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="center"
                    android:text="@string/chat_with_care_team"
                    android:textColor="#666666"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_medications"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_margin="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_columnWeight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/bg_medications_icon"
                    android:clipToOutline="true">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_meds_filled"
                        app:tint="@android:color/white" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:text="@string/medications"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="center"
                    android:text="@string/manage_prescriptions"
                    android:textColor="#666666"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/reminder_card"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_margin="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_columnWeight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/bg_reminders_icon"
                    android:clipToOutline="true">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_alarm_icon"
                        app:tint="@android:color/white" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:text="@string/reminders"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="center"
                    android:text="@string/daily_notifications"
                    android:textColor="#666666"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/appointment_card"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_margin="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_columnWeight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/bg_appointment_icon"
                    android:clipToOutline="true">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:contentDescription="@string/appointment"
                        android:src="@drawable/ic_appointment_icon"
                        app:tint="@android:color/white" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:text="@string/appointment"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="center"
                    android:text="@string/schedule_visits"
                    android:textColor="#666666"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/summary_card"
            android:layout_width="0dp"
            android:layout_height="100dp"
            android:layout_margin="8dp"
            app:cardBackgroundColor="@android:color/white"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_columnWeight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="12dp">

                <FrameLayout
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_marginBottom="6dp"
                    android:background="@drawable/bg_summary_icon"
                    android:clipToOutline="true"
                    tools:ignore="UnusedAttribute">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="center"
                        android:contentDescription="@string/summary"
                        android:src="@drawable/dash_doc"
                        app:tint="@android:color/white" />
                </FrameLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/lato_bold"
                    android:gravity="center"
                    android:text="@string/summary"
                    android:textColor="#000000"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:fontFamily="@font/lato_regular"
                    android:gravity="center"
                    android:text="@string/health_overview"
                    android:textColor="#666666"
                    android:textSize="12sp" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </androidx.gridlayout.widget.GridLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
