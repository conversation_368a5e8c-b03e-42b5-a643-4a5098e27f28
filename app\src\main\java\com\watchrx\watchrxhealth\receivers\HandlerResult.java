package com.watchrx.watchrxhealth.receivers;

public class HandlerResult {
    private Object result;
    private Object param;

    public Object getResult() {
        return result;
    }

    public HandlerResult(Object result, Object param) {
        this.result = result;
        this.param = param;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public Object getParam() {
        return param;
    }

    public void setParam(Object param) {
        this.param = param;
    }
}
