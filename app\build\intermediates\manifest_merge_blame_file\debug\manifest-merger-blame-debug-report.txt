1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.watchrx.watchrxhealth"
4    android:versionCode="44"
5    android:versionName="v1.0.44" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera" />
11-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:6:5-8:40
11-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:7:9-47
12    <uses-feature android:name="android.hardware.camera.autofocus" />
12-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:9:5-70
12-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:9:19-67
13    <uses-feature
13-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:10:5-12:35
14        android:glEsVersion="0x00020000"
14-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:11:9-41
15        android:required="true" />
15-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:12:9-32
16
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:14:5-77
17-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:14:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
18-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:15:5-92
18-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:15:22-89
19    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
19-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:16:5-79
19-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:16:22-76
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:17:5-79
20-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:17:22-76
21    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
21-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:18:5-81
21-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:18:22-78
22    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
22-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:19:5-81
22-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:19:22-78
23    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
23-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:20:5-79
23-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:20:22-76
24    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
24-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:21:5-76
24-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:21:22-73
25    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
25-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:22:5-76
25-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:22:22-73
26    <uses-permission android:name="android.permission.INTERNET" />
26-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:23:5-67
26-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:23:22-64
27    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
27-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:24:5-79
27-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:24:22-76
28    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
28-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:25:5-86
28-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:25:22-83
29    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
29-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:26:5-75
29-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:26:22-72
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:27:5-66
30-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:27:22-63
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:28:5-68
31-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:28:22-65
32    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
32-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:29:5-80
32-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:29:22-77
33    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
33-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:30:5-81
33-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:30:22-78
34    <uses-permission android:name="android.permission.CALL_PHONE" />
34-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:31:5-69
34-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:31:22-66
35    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
35-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:32:5-81
35-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:32:22-78
36    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
36-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:33:5-80
36-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:33:22-77
37    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
37-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:34:5-85
37-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:34:22-82
38    <uses-permission android:name="android.permission.BODY_SENSORS" />
38-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:35:5-71
38-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:35:22-68
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:36:5-77
39-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:36:22-74
40    <uses-permission android:name="android.permission.CAMERA" />
40-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:37:5-65
40-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:37:22-62
41    <uses-permission android:name="android.permission.RECORD_AUDIO" />
41-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:38:5-71
41-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:38:22-68
42    <uses-permission
42-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:39:5-41:38
43        android:name="android.permission.BLUETOOTH"
43-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:40:9-52
44        android:maxSdkVersion="30" />
44-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:41:9-35
45    <uses-permission
45-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:42:5-44:38
46        android:name="android.permission.BLUETOOTH_ADMIN"
46-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:43:9-58
47        android:maxSdkVersion="30" />
47-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:44:9-35
48    <uses-permission
48-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:45:5-47:58
49        android:name="android.permission.BLUETOOTH_SCAN"
49-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:46:9-57
50        android:usesPermissionFlags="neverForLocation" />
50-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:47:9-55
51    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
51-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:48:5-76
51-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:48:22-73
52
53    <!-- Health Connect permissions for all enabled vitals -->
54    <uses-permission android:name="android.permission.health.READ_BLOOD_GLUCOSE" />
54-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:51:5-84
54-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:51:22-81
55    <uses-permission android:name="android.permission.health.READ_HEART_RATE" />
55-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:52:5-81
55-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:52:22-78
56    <uses-permission android:name="android.permission.health.READ_BLOOD_PRESSURE" />
56-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:53:5-85
56-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:53:22-82
57    <uses-permission android:name="android.permission.health.READ_OXYGEN_SATURATION" />
57-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:54:5-88
57-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:54:22-85
58    <uses-permission android:name="android.permission.health.READ_STEPS" />
58-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:55:5-76
58-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:55:22-73
59    <uses-permission android:name="android.permission.health.READ_SLEEP" />
59-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:56:5-76
59-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:56:22-73
60    <uses-permission android:name="android.permission.health.READ_WEIGHT" />
60-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:57:5-77
60-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:57:22-74
61    <uses-permission android:name="android.permission.health.READ_BODY_TEMPERATURE" />
61-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:58:5-87
61-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:58:22-84
62
63    <queries>
63-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:60:5-62:15
64        <package android:name="com.google.android.apps.healthdata" />
64-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:61:9-70
64-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:61:18-67
65        <!-- Needs to be explicitly declared on Android R+ -->
66        <package android:name="com.google.android.apps.maps" />
66-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
66-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
67    </queries>
68
69    <uses-feature
69-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:64:5-66:36
70        android:name="android.hardware.bluetooth_le"
70-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:65:9-53
71        android:required="false" />
71-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:66:9-33
72    <uses-feature
72-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:16:5-18:36
73        android:name="android.hardware.telephony"
73-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:17:9-50
74        android:required="false" />
74-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:18:9-33
75    <uses-feature
75-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:19:5-21:36
76        android:name="android.hardware.bluetooth"
76-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:20:9-50
77        android:required="false" />
77-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:21:9-33
78    <uses-feature
78-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:22:5-24:36
79        android:name="android.hardware.microphone"
79-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:23:9-51
80        android:required="false" />
80-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:24:9-33
81
82    <uses-permission android:name="android.permission.BROADCAST_STICKY" /> <!-- In Meeting "share screen" will need the following Permissions -->
82-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:44:5-75
82-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:44:22-72
83    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
83-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:50:5-78
83-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:50:22-75
84    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
84-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:55:5-94
84-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:55:22-91
85    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
85-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:5-88
85-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:22-85
86    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
86-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:57:5-88
86-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:57:22-85
87    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
87-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:5-88
87-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:22-85
88    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
88-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
89
90    <permission
90-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
91        android:name="com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
91-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
92        android:protectionLevel="signature" />
92-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
93
94    <uses-permission android:name="com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
94-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
94-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
95
96    <application
96-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:72:5-404:19
97        android:name="com.watchrx.watchrxhealth.WatchApp"
97-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:73:9-33
98        android:allowBackup="true"
98-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:74:9-35
99        android:appComponentFactory="@string/app_name"
99-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:75:9-55
100        android:debuggable="true"
101        android:enabled="true"
101-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:76:9-31
102        android:extractNativeLibs="true"
102-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:77:9-41
103        android:fullBackupContent="false"
103-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:78:9-42
104        android:icon="@mipmap/watchrx_app_icon_round"
104-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:79:9-54
105        android:label="@string/app_name"
105-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:80:9-41
106        android:persistent="true"
106-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:81:9-34
107        android:requestLegacyExternalStorage="true"
107-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:82:9-52
108        android:supportsRtl="true"
108-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:83:9-35
109        android:testOnly="true"
110        android:theme="@style/Theme.WatchRx"
110-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:84:9-45
111        android:usesCleartextTraffic="true"
111-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:85:9-44
112        android:windowSoftInputMode="adjustResize" >
112-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:86:9-51
113        <activity
113-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:90:9-92:40
114            android:name="com.watchrx.watchrxhealth.LatestAlertsActivity"
114-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:91:13-49
115            android:exported="false" />
115-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:92:13-37
116        <activity
116-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:93:9-95:40
117            android:name="com.watchrx.watchrxhealth.ZoomVideoCallScreen"
117-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:94:13-48
118            android:exported="false" />
118-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:95:13-37
119        <activity
119-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:96:9-98:40
120            android:name="com.watchrx.watchrxhealth.auth.EnterOTPActivity"
120-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:97:13-50
121            android:exported="false" />
121-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:98:13-37
122        <activity
122-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:99:9-101:40
123            android:name="com.watchrx.watchrxhealth.auth.EnterPasswordActivity"
123-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:100:13-55
124            android:exported="false" />
124-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:101:13-37
125        <activity
125-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:103:9-105:40
126            android:name="com.watchrx.watchrxhealth.auth.ResendOTPScreen"
126-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:104:13-49
127            android:exported="false" />
127-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:105:13-37
128        <activity
128-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:106:9-118:20
129            android:name="com.watchrx.watchrxhealth.auth.LoginScreen"
129-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:107:13-45
130            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
130-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:108:13-81
131            android:exported="true"
131-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:109:13-36
132            android:launchMode="standard"
132-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:110:13-42
133            android:singleUser="true" >
133-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:111:13-38
134            <intent-filter>
134-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:113:13-117:29
135                <action android:name="android.intent.action.MAIN" />
135-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:114:17-69
135-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:114:25-66
136
137                <category android:name="android.intent.category.LAUNCHER" />
137-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:116:17-77
137-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:116:27-74
138            </intent-filter>
139        </activity>
140        <activity android:name="com.watchrx.watchrxhealth.LoginActivity" />
140-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:119:9-121:37
140-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:120:13-42
141        <activity
141-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:122:9-124:40
142            android:name="com.watchrx.watchrxhealth.ChatActivity"
142-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:123:13-41
143            android:exported="false" />
143-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:124:13-37
144        <activity
144-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:125:9-127:40
145            android:name="com.watchrx.watchrxhealth.VitalsGraphActivity"
145-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:126:13-48
146            android:exported="false" />
146-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:127:13-37
147        <activity
147-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:128:9-130:40
148            android:name="com.watchrx.watchrxhealth.MyTaskCalendar"
148-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:129:13-43
149            android:exported="false" />
149-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:130:13-37
150        <activity
150-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:131:9-133:40
151            android:name="com.watchrx.watchrxhealth.WebViewActivity"
151-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:132:13-44
152            android:exported="false" />
152-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:133:13-37
153        <activity
153-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:134:9-136:40
154            android:name="com.watchrx.watchrxhealth.ReminderDetailsActivity"
154-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:135:13-52
155            android:exported="false" />
155-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:136:13-37
156        <activity
156-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:137:9-139:40
157            android:name="com.watchrx.watchrxhealth.VitalDashboard"
157-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:138:13-43
158            android:exported="false" />
158-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:139:13-37
159        <activity
159-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:140:9-142:40
160            android:name="com.watchrx.watchrxhealth.InteractiveVoiceActivity"
160-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:141:13-53
161            android:exported="false" />
161-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:142:13-37
162        <activity
162-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:143:9-145:54
163            android:name="com.watchrx.watchrxhealth.PatientDiaryActivity"
163-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:144:13-49
164            android:screenOrientation="fullSensor" />
164-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:145:13-51
165        <activity android:name="com.watchrx.watchrxhealth.SleepMonitorActivity" />
165-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:146:9-58
165-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:146:19-55
166        <activity android:name="com.watchrx.watchrxhealth.SplashActivity" />
166-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:147:9-52
166-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:147:19-49
167        <activity
167-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:148:9-150:52
168            android:name="com.watchrx.watchrxhealth.VitalDetailsActivity"
168-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:149:13-49
169            android:screenOrientation="portrait" />
169-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:150:13-49
170        <activity
170-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:151:9-153:52
171            android:name="com.watchrx.watchrxhealth.ViewAllTextMessageActivity"
171-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:152:13-55
172            android:screenOrientation="portrait" />
172-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:153:13-49
173        <activity
173-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:154:9-156:52
174            android:name="com.watchrx.watchrxhealth.MedicationDetailsActivity"
174-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:155:13-54
175            android:screenOrientation="portrait" />
175-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:156:13-49
176        <activity
176-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:157:9-159:52
177            android:name="com.watchrx.watchrxhealth.GPSActivity"
177-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:158:13-40
178            android:screenOrientation="portrait" />
178-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:159:13-49
179        <activity
179-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:160:9-163:70
180            android:name="com.watchrx.watchrxhealth.TextMessageActivity"
180-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:161:13-48
181            android:screenOrientation="portrait"
181-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:162:13-49
182            android:windowSoftInputMode="adjustResize|stateHidden" />
182-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:163:13-67
183        <activity
183-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:164:9-166:52
184            android:name="com.watchrx.watchrxhealth.PhoneCallsActivity"
184-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:165:13-47
185            android:screenOrientation="portrait" />
185-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:166:13-49
186        <activity android:name="com.watchrx.watchrxhealth.WifiConfig" />
186-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:167:9-48
186-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:167:19-45
187        <activity
187-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:168:9-170:52
188            android:name="com.watchrx.watchrxhealth.CustomAlertActivity"
188-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:169:13-48
189            android:screenOrientation="portrait" />
189-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:170:13-49
190        <activity
190-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:171:9-174:52
191            android:name="com.watchrx.watchrxhealth.MainActivity"
191-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:172:13-41
192            android:launchMode="singleTask"
192-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:173:13-44
193            android:screenOrientation="portrait" />
193-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:174:13-49
194        <activity
194-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:175:9-177:52
195            android:name="com.watchrx.watchrxhealth.ReminderActivity"
195-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:176:13-45
196            android:screenOrientation="portrait" />
196-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:177:13-49
197        <activity
197-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:178:9-181:58
198            android:name="com.watchrx.watchrxhealth.AddMedication"
198-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:179:13-42
199            android:screenOrientation="portrait"
199-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:180:13-49
200            android:windowSoftInputMode="adjustResize" />
200-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:181:13-55
201        <activity
201-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:182:9-184:52
202            android:name="com.watchrx.watchrxhealth.MedicationActivity"
202-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:183:13-47
203            android:screenOrientation="portrait" />
203-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:184:13-49
204        <activity
204-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:185:9-187:52
205            android:name="com.watchrx.watchrxhealth.NurseOnTheWayActivity"
205-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:186:13-50
206            android:screenOrientation="portrait" />
206-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:187:13-49
207        <activity
207-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:188:9-190:52
208            android:name="com.watchrx.watchrxhealth.VisitVerificationActivity"
208-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:189:13-54
209            android:screenOrientation="portrait" />
209-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:190:13-49
210        <activity
210-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:191:9-193:52
211            android:name="com.watchrx.watchrxhealth.BatteryActivity"
211-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:192:13-44
212            android:screenOrientation="portrait" />
212-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:193:13-49
213        <activity
213-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:194:9-196:52
214            android:name="com.watchrx.watchrxhealth.AlertsActivity"
214-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:195:13-43
215            android:screenOrientation="portrait" />
215-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:196:13-49
216        <activity
216-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:197:9-199:52
217            android:name="com.watchrx.watchrxhealth.HeartRateActivity"
217-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:198:13-46
218            android:screenOrientation="portrait" />
218-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:199:13-49
219        <activity
219-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:200:9-202:52
220            android:name="com.watchrx.watchrxhealth.PedoMeterActivity"
220-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:201:13-46
221            android:screenOrientation="portrait" /> <!-- <activity -->
221-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:202:13-49
222        <!-- android:name=".ble.NewVitalsActivity" -->
223        <!-- android:screenOrientation="portrait" /> -->
224        <activity
224-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:205:9-207:52
225            android:name="com.watchrx.watchrxhealth.ScheduleTextMessageActivity"
225-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:206:13-56
226            android:screenOrientation="portrait" />
226-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:207:13-49
227        <activity
227-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:208:9-211:52
228            android:name="com.watchrx.watchrxhealth.twilio.VideoActivity"
228-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:209:13-49
229            android:launchMode="singleTask"
229-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:210:13-44
230            android:screenOrientation="portrait" />
230-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:211:13-49
231        <activity
231-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:212:9-215:52
232            android:name="com.watchrx.watchrxhealth.twilio.SettingsActivity"
232-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:213:13-52
233            android:launchMode="singleTask"
233-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:214:13-44
234            android:screenOrientation="portrait" />
234-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:215:13-49
235        <activity android:name="com.watchrx.watchrxhealth.ble.NewVitalsActivity" />
235-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:216:9-59
235-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:216:19-56
236        <activity
236-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:217:9-219:47
237            android:name="com.watchrx.watchrxhealth.voip.IncomingCallActivity"
237-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:218:13-54
238            android:launchMode="singleTask" />
238-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:219:13-44
239        <activity
239-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:221:9-228:20
240            android:name="com.watchrx.watchrxhealth.PermissionsRationaleActivity"
240-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:222:13-57
241            android:exported="true"
241-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:223:13-36
242            android:screenOrientation="portrait" >
242-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:224:13-49
243            <intent-filter>
243-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:225:13-227:29
244                <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
244-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:226:17-92
244-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:226:25-89
245            </intent-filter>
246        </activity>
247
248        <activity-alias
248-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:230:9-239:26
249            android:name="com.watchrx.watchrxhealth.ViewPermissionUsageActivity"
249-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:231:13-55
250            android:exported="true"
250-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:232:13-36
251            android:permission="android.permission.START_VIEW_PERMISSION_USAGE"
251-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:234:13-80
252            android:targetActivity="com.watchrx.watchrxhealth.PermissionsRationaleActivity" >
252-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:233:13-67
253            <intent-filter>
253-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:235:13-238:29
254                <action android:name="android.intent.action.VIEW_PERMISSION_USAGE" />
254-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:236:17-86
254-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:236:25-83
255
256                <category android:name="android.intent.category.HEALTH_PERMISSIONS" />
256-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:237:17-87
256-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:237:27-84
257            </intent-filter>
258        </activity-alias>
259
260        <meta-data
260-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:241:9-243:69
261            android:name="com.google.android.gms.version"
261-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:242:13-58
262            android:value="@integer/google_play_services_version" />
262-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:243:13-66
263        <meta-data
263-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:244:9-246:37
264            android:name="firebase_messaging_auto_init_enabled"
264-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:245:13-64
265            android:value="false" />
265-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:246:13-34
266        <meta-data
266-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:247:9-249:37
267            android:name="firebase_analytics_collection_enabled"
267-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:248:13-65
268            android:value="false" />
268-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:249:13-34
269        <meta-data
269-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:250:9-252:71
270            android:name="com.google.android.geo.API_KEY"
270-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:251:13-58
271            android:value="AIzaSyDhGEgJCRCYBnheNQ9TVFY3h7Byn0oq6R4" />
271-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:252:13-68
272        <meta-data
272-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:253:9-255:71
273            android:name="com.google.firebase.messaging.default_notification_channel_id"
273-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:254:13-89
274            android:value="@string/default_notification_channel_id" />
274-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:255:13-68
275
276        <provider
276-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:257:9-265:20
277            android:name="androidx.core.content.FileProvider"
277-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:258:13-62
278            android:authorities="com.watchrx.watchrxhealth.fileprovider"
278-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:259:13-73
279            android:exported="false"
279-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:260:13-37
280            android:grantUriPermissions="true" >
280-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:261:13-47
281            <meta-data
281-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:262:13-264:53
282                android:name="android.support.FILE_PROVIDER_PATHS"
282-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:263:17-67
283                android:resource="@xml/filepaths" />
283-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:264:17-50
284        </provider>
285
286        <receiver
286-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:267:9-270:39
287            android:name="com.watchrx.watchrxhealth.receivers.CheckQueueReceiver"
287-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:268:13-57
288            android:enabled="true"
288-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:269:13-35
289            android:exported="true" />
289-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:270:13-36
290        <receiver
290-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:271:9-274:39
291            android:name="com.watchrx.watchrxhealth.receivers.ReminderReceiver"
291-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:272:13-55
292            android:enabled="true"
292-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:273:13-35
293            android:exported="true" />
293-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:274:13-36
294        <receiver
294-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:275:9-278:39
295            android:name="com.watchrx.watchrxhealth.receivers.CustomAlertReceiver"
295-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:276:13-58
296            android:enabled="true"
296-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:277:13-35
297            android:exported="true" />
297-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:278:13-36
298        <receiver
298-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:279:9-282:39
299            android:name="com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver"
299-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:280:13-67
300            android:enabled="true"
300-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:281:13-35
301            android:exported="true" />
301-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:282:13-36
302        <receiver
302-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:283:9-286:39
303            android:name="com.watchrx.watchrxhealth.syncup.SyncupReceiver"
303-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:284:13-50
304            android:enabled="true"
304-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:285:13-35
305            android:exported="true" />
305-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:286:13-36
306        <receiver
306-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:287:9-299:20
307            android:name="com.watchrx.watchrxhealth.receivers.RebootComplete"
307-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:288:13-53
308            android:enabled="true"
308-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:289:13-35
309            android:exported="true"
309-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:290:13-36
310            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
310-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:291:13-75
311            <intent-filter>
311-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:292:13-298:29
312                <action android:name="android.intent.action.BOOT_COMPLETED" />
312-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:293:17-79
312-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:293:25-76
313                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
313-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:294:17-82
313-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:294:25-79
314                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
314-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:295:17-80
314-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:295:25-77
315
316                <category android:name="android.intent.category.DEFAULT" />
316-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:297:17-76
316-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:297:27-73
317            </intent-filter>
318        </receiver>
319        <receiver
319-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:300:9-307:20
320            android:name="com.watchrx.watchrxhealth.receivers.TimeZoneChangedReceiver"
320-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:301:13-62
321            android:enabled="true"
321-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:302:13-35
322            android:exported="true" >
322-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:303:13-36
323            <intent-filter>
323-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:304:13-306:29
324                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
324-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:17-81
324-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:25-78
325            </intent-filter>
326        </receiver>
327        <receiver
327-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:308:9-322:20
328            android:name="com.watchrx.watchrxhealth.receivers.NewPackageInstalled"
328-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:309:13-58
329            android:enabled="true"
329-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:310:13-35
330            android:exported="true" >
330-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:311:13-36
331            <intent-filter>
331-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:312:13-321:29
332                <action android:name="android.intent.action.PACKAGE_ADDED" />
332-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:17-78
332-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:25-75
333                <action android:name="android.intent.action.PACKAGE_INSTALL" />
333-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:314:17-80
333-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:314:25-77
334                <action android:name="android.intent.action.PACKAGE_REMOVED" />
334-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:315:17-80
334-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:315:25-77
335                <action android:name="android.intent.action.PACKAGE_REPLACED" />
335-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:316:17-81
335-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:316:25-78
336
337                <category android:name="android.intent.category.DEFAULT" />
337-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:297:17-76
337-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:297:27-73
338
339                <data android:scheme="package" />
339-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:320:17-50
339-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:320:23-47
340            </intent-filter>
341        </receiver>
342        <receiver
342-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:323:9-330:20
343            android:name="com.watchrx.watchrxhealth.receivers.InternetStatusChangeReceiver"
343-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:324:13-67
344            android:exported="true" >
344-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:325:13-36
345            <intent-filter>
345-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:326:13-329:29
346                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
346-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:327:17-79
346-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:327:25-76
347                <!-- <action android:name="android.net.wifi.WIFI_STATE_CHANGED" /> -->
348            </intent-filter>
349        </receiver>
350        <receiver
350-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:331:9-334:39
351            android:name="com.watchrx.watchrxhealth.receivers.HrtBroadcastReceiver"
351-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:332:13-59
352            android:enabled="true"
352-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:333:13-35
353            android:exported="true" />
353-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:334:13-36
354        <receiver
354-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:335:9-338:39
355            android:name="com.watchrx.watchrxhealth.receivers.NetworkHeartBeat"
355-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:336:13-55
356            android:enabled="true"
356-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:337:13-35
357            android:exported="true" />
357-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:338:13-36
358        <receiver
358-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:339:9-342:39
359            android:name="com.watchrx.watchrxhealth.receivers.ScheduleMessageReceiver"
359-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:340:13-62
360            android:enabled="true"
360-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:341:13-35
361            android:exported="true" />
361-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:342:13-36
362        <receiver
362-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:343:9-346:39
363            android:name="com.watchrx.watchrxhealth.receivers.SendPedoMeterReceiver"
363-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:344:13-60
364            android:enabled="true"
364-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:345:13-35
365            android:exported="true" />
365-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:346:13-36
366        <receiver
366-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:347:9-350:39
367            android:name="com.watchrx.watchrxhealth.receivers.SendHealthConnectDataReceiver"
367-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:348:13-68
368            android:enabled="true"
368-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:349:13-35
369            android:exported="true" />
369-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:350:13-36
370        <receiver
370-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:351:9-354:39
371            android:name="com.watchrx.watchrxhealth.receivers.MidnightPedoMeterResetReceiver"
371-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:352:13-69
372            android:enabled="true"
372-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:353:13-35
373            android:exported="true" />
373-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:354:13-36
374        <receiver
374-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:355:9-358:39
375            android:name="com.watchrx.watchrxhealth.receivers.VitalReminderReceiver"
375-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:356:13-60
376            android:enabled="true"
376-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:357:13-35
377            android:exported="true" />
377-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:358:13-36
378        <receiver
378-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:359:9-362:39
379            android:name="com.watchrx.watchrxhealth.receivers.MidnightLogFileUploadReceiver"
379-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:360:13-68
380            android:enabled="true"
380-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:361:13-35
381            android:exported="true" />
381-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:362:13-36
382        <receiver
382-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:363:9-366:39
383            android:name="com.watchrx.watchrxhealth.receivers.BleBroadCastReceiver"
383-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:364:13-59
384            android:enabled="true"
384-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:365:13-35
385            android:exported="true" />
385-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:366:13-36
386
387        <service
387-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:368:9-378:19
388            android:name="com.watchrx.watchrxhealth.gcm.GCMPushReceiverService"
388-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:369:13-55
389            android:directBootAware="true"
389-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:370:13-43
390            android:exported="true"
390-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:371:13-36
391            android:permission="true" >
391-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:372:13-38
392            <intent-filter>
392-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:374:13-377:29
393                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
393-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:375:17-80
393-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:375:25-77
394                <action android:name="com.google.firebase.MESSAGING_EVENT" />
394-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:17-78
394-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:25-75
395            </intent-filter>
396        </service>
397        <service
397-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:379:9-381:40
398            android:name="com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService"
398-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:380:13-61
399            android:exported="false" />
399-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:381:13-37
400        <service
400-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:382:9-384:40
401            android:name="com.watchrx.watchrxhealth.gps.GeoFenceIntentServices"
401-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:383:13-55
402            android:exported="false" />
402-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:384:13-37
403        <service
403-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:385:9-387:40
404            android:name="com.watchrx.watchrxhealth.pedometer.SensorListener"
404-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:386:13-53
405            android:exported="false" />
405-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:387:13-37
406        <service
406-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:388:9-392:74
407            android:name="com.watchrx.watchrxhealth.WatchRxForegroundService"
407-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:389:13-53
408            android:exported="false"
408-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:390:13-37
409            android:foregroundServiceType="mediaPlayback"
409-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:391:13-58
410            android:permission="android.permission.FOREGROUND_SERVICE" />
410-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:392:13-71
411        <service
411-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:393:9-399:61
412            android:name="com.zipow.videobox.share.ScreenShareServiceForSDK"
412-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:394:13-77
413            android:exported="false"
413-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:395:13-37
414            android:foregroundServiceType="microphone|connectedDevice"
414-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:396:13-71
415            android:label="Zoom" />
415-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:397:13-33
416
417        <uses-library
417-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:401:9-403:40
418            android:name="org.apache.http.legacy"
418-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:402:13-50
419            android:required="false" />
419-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:403:13-37
420
421        <receiver
421-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
422            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
422-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
423            android:exported="true"
423-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
424            android:permission="com.google.android.c2dm.permission.SEND" >
424-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
425            <intent-filter>
425-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
426                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
426-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
426-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
427            </intent-filter>
428
429            <meta-data
429-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
430                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
430-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
431                android:value="true" />
431-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
432        </receiver>
433        <!--
434             FirebaseMessagingService performs security checks at runtime,
435             but set to not exported to explicitly avoid allowing another app to call it.
436        -->
437        <service
437-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
438            android:name="com.google.firebase.messaging.FirebaseMessagingService"
438-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
439            android:directBootAware="true"
439-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
440            android:exported="false" >
440-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
441            <intent-filter android:priority="-500" >
441-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
441-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
442                <action android:name="com.google.firebase.MESSAGING_EVENT" />
442-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:17-78
442-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:25-75
443            </intent-filter>
444        </service>
445        <service
445-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
446            android:name="com.google.firebase.components.ComponentDiscoveryService"
446-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
447            android:directBootAware="true"
447-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
448            android:exported="false" >
448-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
449            <meta-data
449-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
450                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
450-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
451                android:value="com.google.firebase.components.ComponentRegistrar" />
451-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
452            <meta-data
452-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
453                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
453-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
454                android:value="com.google.firebase.components.ComponentRegistrar" />
454-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
455            <meta-data
455-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
456                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
456-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
457                android:value="com.google.firebase.components.ComponentRegistrar" />
457-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
458            <meta-data
458-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
459                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
459-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
460                android:value="com.google.firebase.components.ComponentRegistrar" />
460-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
461            <meta-data
461-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
462                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
462-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
463                android:value="com.google.firebase.components.ComponentRegistrar" />
463-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
464            <meta-data
464-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
465                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
465-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
466                android:value="com.google.firebase.components.ComponentRegistrar" />
466-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
467            <meta-data
467-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
468                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
468-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
469                android:value="com.google.firebase.components.ComponentRegistrar" />
469-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
470            <meta-data
470-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
471                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
471-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
472                android:value="com.google.firebase.components.ComponentRegistrar" />
472-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
473        </service>
474
475        <receiver
475-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:32:9-40:20
476            android:name="com.google.firebase.messaging.directboot.FirebaseMessagingDirectBootReceiver"
476-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:33:13-104
477            android:directBootAware="true"
477-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:34:13-43
478            android:exported="true"
478-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:35:13-36
479            android:permission="com.google.android.c2dm.permission.SEND" >
479-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:36:13-73
480            <intent-filter>
480-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:37:13-39:29
481                <action android:name="com.google.firebase.messaging.RECEIVE_DIRECT_BOOT" />
481-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:38:17-92
481-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:38:25-89
482            </intent-filter>
483        </receiver>
484
485        <activity
485-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
486            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
486-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
487            android:excludeFromRecents="true"
487-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
488            android:exported="false"
488-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
489            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
489-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
490        <!--
491            Service handling Google Sign-In user revocation. For apps that do not integrate with
492            Google Sign-In, this service will never be started.
493        -->
494        <service
494-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
495            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
495-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
496            android:exported="true"
496-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
497            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
497-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
498            android:visibleToInstantApps="true" />
498-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
499        <service
499-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:24:9-32:19
500            android:name="androidx.health.platform.client.impl.sdkservice.HealthDataSdkService"
500-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:25:13-96
501            android:enabled="true"
501-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:26:13-35
502            android:exported="true" >
502-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:27:13-36
503            <intent-filter>
503-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:29:13-31:29
504                <action android:name="androidx.health.platform.client.ACTION_BIND_SDK_SERVICE" />
504-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:30:17-98
504-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:30:25-95
505            </intent-filter>
506        </service>
507
508        <activity
508-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
509            android:name="com.google.android.gms.common.api.GoogleApiActivity"
509-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
510            android:exported="false"
510-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
511            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
511-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
512
513        <provider
513-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
514            android:name="com.google.firebase.provider.FirebaseInitProvider"
514-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
515            android:authorities="com.watchrx.watchrxhealth.firebaseinitprovider"
515-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
516            android:directBootAware="true"
516-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
517            android:exported="false"
517-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
518            android:initOrder="100" />
518-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
519        <provider
519-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
520            android:name="androidx.startup.InitializationProvider"
520-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
521            android:authorities="com.watchrx.watchrxhealth.androidx-startup"
521-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
522            android:exported="false" >
522-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
523            <meta-data
523-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
524                android:name="androidx.work.WorkManagerInitializer"
524-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
525                android:value="androidx.startup" />
525-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
526            <meta-data
526-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
527                android:name="androidx.emoji2.text.EmojiCompatInitializer"
527-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
528                android:value="androidx.startup" />
528-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
529            <meta-data
529-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
530                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
530-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
531                android:value="androidx.startup" />
531-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
532            <meta-data
532-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
533                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
533-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
534                android:value="androidx.startup" />
534-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
535        </provider>
536
537        <service
537-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
538            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
538-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
539            android:directBootAware="false"
539-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
540            android:enabled="@bool/enable_system_alarm_service_default"
540-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
541            android:exported="false" />
541-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
542        <service
542-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
543            android:name="androidx.work.impl.background.systemjob.SystemJobService"
543-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
544            android:directBootAware="false"
544-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
545            android:enabled="@bool/enable_system_job_service_default"
545-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
546            android:exported="true"
546-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
547            android:permission="android.permission.BIND_JOB_SERVICE" />
547-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
548        <service
548-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
549            android:name="androidx.work.impl.foreground.SystemForegroundService"
549-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
550            android:directBootAware="false"
550-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
551            android:enabled="@bool/enable_system_foreground_service_default"
551-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
552            android:exported="false" />
552-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
553
554        <receiver
554-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
555            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
555-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
556            android:directBootAware="false"
556-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
557            android:enabled="true"
557-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
558            android:exported="false" />
558-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
559        <receiver
559-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
560            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
560-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
561            android:directBootAware="false"
561-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
562            android:enabled="false"
562-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
563            android:exported="false" >
563-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
564            <intent-filter>
564-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
565                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
565-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
565-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
566                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
566-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
566-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
567            </intent-filter>
568        </receiver>
569        <receiver
569-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
570            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
570-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
571            android:directBootAware="false"
571-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
572            android:enabled="false"
572-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
573            android:exported="false" >
573-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
574            <intent-filter>
574-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
575                <action android:name="android.intent.action.BATTERY_OKAY" />
575-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
575-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
576                <action android:name="android.intent.action.BATTERY_LOW" />
576-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
576-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
577            </intent-filter>
578        </receiver>
579        <receiver
579-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
580            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
580-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
581            android:directBootAware="false"
581-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
582            android:enabled="false"
582-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
583            android:exported="false" >
583-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
584            <intent-filter>
584-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
585                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
585-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
585-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
586                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
586-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
586-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
587            </intent-filter>
588        </receiver>
589        <receiver
589-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
590            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
590-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
591            android:directBootAware="false"
591-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
592            android:enabled="false"
592-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
593            android:exported="false" >
593-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
594            <intent-filter>
594-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:326:13-329:29
595                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
595-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:327:17-79
595-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:327:25-76
596            </intent-filter>
597        </receiver>
598        <receiver
598-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
599            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
599-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
600            android:directBootAware="false"
600-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
601            android:enabled="false"
601-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
602            android:exported="false" >
602-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
603            <intent-filter>
603-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
604                <action android:name="android.intent.action.BOOT_COMPLETED" />
604-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:293:17-79
604-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:293:25-76
605                <action android:name="android.intent.action.TIME_SET" />
605-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
605-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
606                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
606-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:17-81
606-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:25-78
607            </intent-filter>
608        </receiver>
609        <receiver
609-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
610            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
610-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
611            android:directBootAware="false"
611-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
612            android:enabled="@bool/enable_system_alarm_service_default"
612-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
613            android:exported="false" >
613-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
614            <intent-filter>
614-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
615                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
615-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
615-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
616            </intent-filter>
617        </receiver>
618        <receiver
618-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
619            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
619-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
620            android:directBootAware="false"
620-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
621            android:enabled="true"
621-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
622            android:exported="true"
622-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
623            android:permission="android.permission.DUMP" >
623-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
624            <intent-filter>
624-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
625                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
625-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
625-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
626            </intent-filter>
627        </receiver>
628
629        <uses-library
629-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
630            android:name="androidx.window.extensions"
630-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
631            android:required="false" />
631-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
632        <uses-library
632-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
633            android:name="androidx.window.sidecar"
633-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
634            android:required="false" />
634-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
635
636        <receiver
636-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
637            android:name="androidx.profileinstaller.ProfileInstallReceiver"
637-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
638            android:directBootAware="false"
638-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
639            android:enabled="true"
639-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
640            android:exported="true"
640-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
641            android:permission="android.permission.DUMP" >
641-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
642            <intent-filter>
642-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
643                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
643-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
643-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
644            </intent-filter>
645            <intent-filter>
645-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
646                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
646-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
646-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
647            </intent-filter>
648            <intent-filter>
648-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
649                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
649-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
649-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
650            </intent-filter>
651            <intent-filter>
651-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
652                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
652-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
652-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
653            </intent-filter>
654        </receiver>
655
656        <service
656-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
657            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
657-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
658            android:exported="false" >
658-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
659            <meta-data
659-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
660                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
660-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
661                android:value="cct" />
661-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
662        </service>
663        <service
663-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
664            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
664-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
665            android:exported="false"
665-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
666            android:permission="android.permission.BIND_JOB_SERVICE" >
666-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
667        </service>
668
669        <receiver
669-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
670            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
670-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
671            android:exported="false" />
671-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
672
673        <service
673-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
674            android:name="androidx.room.MultiInstanceInvalidationService"
674-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
675            android:directBootAware="true"
675-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
676            android:exported="false" />
676-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
677    </application>
678
679</manifest>
