1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.watchrx.watchrxhealth"
4    android:versionCode="44"
5    android:versionName="v1.0.44" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera" />
11-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:6:5-8:40
11-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:7:9-47
12    <uses-feature android:name="android.hardware.camera.autofocus" />
12-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:9:5-70
12-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:9:19-67
13    <uses-feature
13-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:10:5-12:35
14        android:glEsVersion="0x00020000"
14-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:11:9-41
15        android:required="true" />
15-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:12:9-32
16
17    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
17-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:14:5-77
17-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:14:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
18-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:15:5-92
18-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:15:22-89
19    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
19-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:16:5-79
19-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:16:22-76
20    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
20-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:17:5-79
20-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:17:22-76
21    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
21-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:18:5-81
21-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:18:22-78
22    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
22-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:19:5-81
22-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:19:22-78
23    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
23-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:20:5-79
23-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:20:22-76
24    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
24-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:21:5-76
24-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:21:22-73
25    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
25-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:22:5-76
25-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:22:22-73
26    <uses-permission android:name="android.permission.INTERNET" />
26-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:23:5-67
26-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:23:22-64
27    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
27-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:24:5-79
27-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:24:22-76
28    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
28-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:25:5-86
28-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:25:22-83
29    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
29-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:26:5-75
29-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:26:22-72
30    <uses-permission android:name="android.permission.VIBRATE" />
30-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:27:5-66
30-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:27:22-63
31    <uses-permission android:name="android.permission.WAKE_LOCK" />
31-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:28:5-68
31-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:28:22-65
32    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
32-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:29:5-80
32-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:29:22-77
33    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
33-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:30:5-81
33-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:30:22-78
34    <uses-permission android:name="android.permission.CALL_PHONE" />
34-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:31:5-69
34-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:31:22-66
35    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
35-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:32:5-81
35-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:32:22-78
36    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
36-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:33:5-80
36-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:33:22-77
37    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
37-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:34:5-85
37-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:34:22-82
38    <uses-permission android:name="android.permission.BODY_SENSORS" />
38-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:35:5-71
38-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:35:22-68
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:36:5-77
39-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:36:22-74
40    <uses-permission android:name="android.permission.CAMERA" />
40-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:37:5-65
40-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:37:22-62
41    <uses-permission android:name="android.permission.RECORD_AUDIO" />
41-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:38:5-71
41-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:38:22-68
42    <uses-permission
42-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:39:5-41:38
43        android:name="android.permission.BLUETOOTH"
43-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:40:9-52
44        android:maxSdkVersion="30" />
44-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:41:9-35
45    <uses-permission
45-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:42:5-44:38
46        android:name="android.permission.BLUETOOTH_ADMIN"
46-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:43:9-58
47        android:maxSdkVersion="30" />
47-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:44:9-35
48    <uses-permission
48-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:45:5-47:58
49        android:name="android.permission.BLUETOOTH_SCAN"
49-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:46:9-57
50        android:usesPermissionFlags="neverForLocation" />
50-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:47:9-55
51    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
51-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:48:5-76
51-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:48:22-73
52
53    <!-- Health Connect permissions for all enabled vitals -->
54    <uses-permission android:name="android.permission.health.READ_BLOOD_GLUCOSE" />
54-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:51:5-84
54-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:51:22-81
55    <uses-permission android:name="android.permission.health.WRITE_BLOOD_GLUCOSE" />
55-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:52:5-85
55-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:52:22-82
56    <uses-permission android:name="android.permission.health.READ_HEART_RATE" />
56-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:53:5-81
56-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:53:22-78
57    <uses-permission android:name="android.permission.health.WRITE_HEART_RATE" />
57-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:54:5-82
57-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:54:22-79
58    <uses-permission android:name="android.permission.health.READ_BLOOD_PRESSURE" />
58-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:55:5-85
58-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:55:22-82
59    <uses-permission android:name="android.permission.health.WRITE_BLOOD_PRESSURE" />
59-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:56:5-86
59-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:56:22-83
60    <uses-permission android:name="android.permission.health.READ_OXYGEN_SATURATION" />
60-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:57:5-88
60-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:57:22-85
61    <uses-permission android:name="android.permission.health.WRITE_OXYGEN_SATURATION" />
61-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:58:5-89
61-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:58:22-86
62    <uses-permission android:name="android.permission.health.READ_STEPS" />
62-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:59:5-76
62-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:59:22-73
63    <uses-permission android:name="android.permission.health.WRITE_STEPS" />
63-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:60:5-77
63-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:60:22-74
64    <uses-permission android:name="android.permission.health.READ_SLEEP" />
64-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:61:5-76
64-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:61:22-73
65    <uses-permission android:name="android.permission.health.WRITE_SLEEP" />
65-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:62:5-77
65-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:62:22-74
66    <uses-permission android:name="android.permission.health.READ_WEIGHT" />
66-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:63:5-77
66-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:63:22-74
67    <uses-permission android:name="android.permission.health.WRITE_WEIGHT" />
67-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:64:5-78
67-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:64:22-75
68    <uses-permission android:name="android.permission.health.READ_BODY_TEMPERATURE" />
68-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:65:5-87
68-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:65:22-84
69    <uses-permission android:name="android.permission.health.WRITE_BODY_TEMPERATURE" />
69-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:66:5-88
69-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:66:22-85
70
71    <queries>
71-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:68:5-70:15
72        <package android:name="com.google.android.apps.healthdata" />
72-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:69:9-70
72-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:69:18-67
73        <!-- Needs to be explicitly declared on Android R+ -->
74        <package android:name="com.google.android.apps.maps" />
74-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:9-64
74-->[com.google.android.gms:play-services-maps:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f3151dc0f6c2d78d350067360cc3d3a0\transformed\jetified-play-services-maps-18.1.0\AndroidManifest.xml:33:18-61
75    </queries>
76
77    <uses-feature
77-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:72:5-74:36
78        android:name="android.hardware.bluetooth_le"
78-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:73:9-53
79        android:required="false" />
79-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:74:9-33
80    <uses-feature
80-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:16:5-18:36
81        android:name="android.hardware.telephony"
81-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:17:9-50
82        android:required="false" />
82-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:18:9-33
83    <uses-feature
83-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:19:5-21:36
84        android:name="android.hardware.bluetooth"
84-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:20:9-50
85        android:required="false" />
85-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:21:9-33
86    <uses-feature
86-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:22:5-24:36
87        android:name="android.hardware.microphone"
87-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:23:9-51
88        android:required="false" />
88-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:24:9-33
89
90    <uses-permission android:name="android.permission.BROADCAST_STICKY" /> <!-- In Meeting "share screen" will need the following Permissions -->
90-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:44:5-75
90-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:44:22-72
91    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
91-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:50:5-78
91-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:50:22-75
92    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
92-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:55:5-94
92-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:55:22-91
93    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" />
93-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:5-88
93-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:22-85
94    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
94-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:57:5-88
94-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:57:22-85
95    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
95-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:5-88
95-->[us.zoom.videosdk:zoomvideosdk-core:1.11.2] C:\Users\<USER>\.gradle\caches\transforms-3\8d6b000a9a8e720e86578baf4ac1ca24\transformed\jetified-zoomvideosdk-core-1.11.2\AndroidManifest.xml:56:22-85
96    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
96-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
96-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
97
98    <permission
98-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
99        android:name="com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
99-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
100        android:protectionLevel="signature" />
100-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
101
102    <uses-permission android:name="com.watchrx.watchrxhealth.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
102-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
102-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\07a96c4913de9523900a40f3d6eddd2c\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
103
104    <application
104-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:80:5-412:19
105        android:name="com.watchrx.watchrxhealth.WatchApp"
105-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:81:9-33
106        android:allowBackup="true"
106-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:82:9-35
107        android:appComponentFactory="@string/app_name"
107-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:83:9-55
108        android:debuggable="true"
109        android:enabled="true"
109-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:84:9-31
110        android:extractNativeLibs="true"
110-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:85:9-41
111        android:fullBackupContent="false"
111-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:86:9-42
112        android:icon="@mipmap/watchrx_app_icon_round"
112-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:87:9-54
113        android:label="@string/app_name"
113-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:88:9-41
114        android:persistent="true"
114-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:89:9-34
115        android:requestLegacyExternalStorage="true"
115-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:90:9-52
116        android:supportsRtl="true"
116-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:91:9-35
117        android:testOnly="true"
118        android:theme="@style/Theme.WatchRx"
118-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:92:9-45
119        android:usesCleartextTraffic="true"
119-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:93:9-44
120        android:windowSoftInputMode="adjustResize" >
120-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:94:9-51
121        <activity
121-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:98:9-100:40
122            android:name="com.watchrx.watchrxhealth.LatestAlertsActivity"
122-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:99:13-49
123            android:exported="false" />
123-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:100:13-37
124        <activity
124-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:101:9-103:40
125            android:name="com.watchrx.watchrxhealth.ZoomVideoCallScreen"
125-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:102:13-48
126            android:exported="false" />
126-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:103:13-37
127        <activity
127-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:104:9-106:40
128            android:name="com.watchrx.watchrxhealth.auth.EnterOTPActivity"
128-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:105:13-50
129            android:exported="false" />
129-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:106:13-37
130        <activity
130-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:107:9-109:40
131            android:name="com.watchrx.watchrxhealth.auth.EnterPasswordActivity"
131-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:108:13-55
132            android:exported="false" />
132-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:109:13-37
133        <activity
133-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:111:9-113:40
134            android:name="com.watchrx.watchrxhealth.auth.ResendOTPScreen"
134-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:112:13-49
135            android:exported="false" />
135-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:113:13-37
136        <activity
136-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:114:9-126:20
137            android:name="com.watchrx.watchrxhealth.auth.LoginScreen"
137-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:115:13-45
138            android:configChanges="orientation|screenSize|keyboardHidden|uiMode"
138-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:116:13-81
139            android:exported="true"
139-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:117:13-36
140            android:launchMode="standard"
140-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:118:13-42
141            android:singleUser="true" >
141-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:119:13-38
142            <intent-filter>
142-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:121:13-125:29
143                <action android:name="android.intent.action.MAIN" />
143-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:122:17-69
143-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:122:25-66
144
145                <category android:name="android.intent.category.LAUNCHER" />
145-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:124:17-77
145-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:124:27-74
146            </intent-filter>
147        </activity>
148        <activity android:name="com.watchrx.watchrxhealth.LoginActivity" />
148-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:127:9-129:37
148-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:128:13-42
149        <activity
149-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:130:9-132:40
150            android:name="com.watchrx.watchrxhealth.ChatActivity"
150-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:131:13-41
151            android:exported="false" />
151-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:132:13-37
152        <activity
152-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:133:9-135:40
153            android:name="com.watchrx.watchrxhealth.VitalsGraphActivity"
153-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:134:13-48
154            android:exported="false" />
154-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:135:13-37
155        <activity
155-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:136:9-138:40
156            android:name="com.watchrx.watchrxhealth.MyTaskCalendar"
156-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:137:13-43
157            android:exported="false" />
157-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:138:13-37
158        <activity
158-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:139:9-141:40
159            android:name="com.watchrx.watchrxhealth.WebViewActivity"
159-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:140:13-44
160            android:exported="false" />
160-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:141:13-37
161        <activity
161-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:142:9-144:40
162            android:name="com.watchrx.watchrxhealth.ReminderDetailsActivity"
162-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:143:13-52
163            android:exported="false" />
163-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:144:13-37
164        <activity
164-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:145:9-147:40
165            android:name="com.watchrx.watchrxhealth.VitalDashboard"
165-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:146:13-43
166            android:exported="false" />
166-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:147:13-37
167        <activity
167-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:148:9-150:40
168            android:name="com.watchrx.watchrxhealth.InteractiveVoiceActivity"
168-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:149:13-53
169            android:exported="false" />
169-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:150:13-37
170        <activity
170-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:151:9-153:54
171            android:name="com.watchrx.watchrxhealth.PatientDiaryActivity"
171-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:152:13-49
172            android:screenOrientation="fullSensor" />
172-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:153:13-51
173        <activity android:name="com.watchrx.watchrxhealth.SleepMonitorActivity" />
173-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:154:9-58
173-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:154:19-55
174        <activity android:name="com.watchrx.watchrxhealth.SplashActivity" />
174-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:155:9-52
174-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:155:19-49
175        <activity
175-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:156:9-158:52
176            android:name="com.watchrx.watchrxhealth.VitalDetailsActivity"
176-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:157:13-49
177            android:screenOrientation="portrait" />
177-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:158:13-49
178        <activity
178-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:159:9-161:52
179            android:name="com.watchrx.watchrxhealth.ViewAllTextMessageActivity"
179-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:160:13-55
180            android:screenOrientation="portrait" />
180-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:161:13-49
181        <activity
181-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:162:9-164:52
182            android:name="com.watchrx.watchrxhealth.MedicationDetailsActivity"
182-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:163:13-54
183            android:screenOrientation="portrait" />
183-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:164:13-49
184        <activity
184-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:165:9-167:52
185            android:name="com.watchrx.watchrxhealth.GPSActivity"
185-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:166:13-40
186            android:screenOrientation="portrait" />
186-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:167:13-49
187        <activity
187-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:168:9-171:70
188            android:name="com.watchrx.watchrxhealth.TextMessageActivity"
188-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:169:13-48
189            android:screenOrientation="portrait"
189-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:170:13-49
190            android:windowSoftInputMode="adjustResize|stateHidden" />
190-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:171:13-67
191        <activity
191-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:172:9-174:52
192            android:name="com.watchrx.watchrxhealth.PhoneCallsActivity"
192-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:173:13-47
193            android:screenOrientation="portrait" />
193-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:174:13-49
194        <activity android:name="com.watchrx.watchrxhealth.WifiConfig" />
194-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:175:9-48
194-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:175:19-45
195        <activity
195-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:176:9-178:52
196            android:name="com.watchrx.watchrxhealth.CustomAlertActivity"
196-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:177:13-48
197            android:screenOrientation="portrait" />
197-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:178:13-49
198        <activity
198-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:179:9-182:52
199            android:name="com.watchrx.watchrxhealth.MainActivity"
199-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:180:13-41
200            android:launchMode="singleTask"
200-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:181:13-44
201            android:screenOrientation="portrait" />
201-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:182:13-49
202        <activity
202-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:183:9-185:52
203            android:name="com.watchrx.watchrxhealth.ReminderActivity"
203-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:184:13-45
204            android:screenOrientation="portrait" />
204-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:185:13-49
205        <activity
205-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:186:9-189:58
206            android:name="com.watchrx.watchrxhealth.AddMedication"
206-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:187:13-42
207            android:screenOrientation="portrait"
207-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:188:13-49
208            android:windowSoftInputMode="adjustResize" />
208-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:189:13-55
209        <activity
209-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:190:9-192:52
210            android:name="com.watchrx.watchrxhealth.MedicationActivity"
210-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:191:13-47
211            android:screenOrientation="portrait" />
211-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:192:13-49
212        <activity
212-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:193:9-195:52
213            android:name="com.watchrx.watchrxhealth.NurseOnTheWayActivity"
213-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:194:13-50
214            android:screenOrientation="portrait" />
214-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:195:13-49
215        <activity
215-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:196:9-198:52
216            android:name="com.watchrx.watchrxhealth.VisitVerificationActivity"
216-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:197:13-54
217            android:screenOrientation="portrait" />
217-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:198:13-49
218        <activity
218-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:199:9-201:52
219            android:name="com.watchrx.watchrxhealth.BatteryActivity"
219-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:200:13-44
220            android:screenOrientation="portrait" />
220-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:201:13-49
221        <activity
221-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:202:9-204:52
222            android:name="com.watchrx.watchrxhealth.AlertsActivity"
222-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:203:13-43
223            android:screenOrientation="portrait" />
223-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:204:13-49
224        <activity
224-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:205:9-207:52
225            android:name="com.watchrx.watchrxhealth.HeartRateActivity"
225-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:206:13-46
226            android:screenOrientation="portrait" />
226-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:207:13-49
227        <activity
227-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:208:9-210:52
228            android:name="com.watchrx.watchrxhealth.PedoMeterActivity"
228-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:209:13-46
229            android:screenOrientation="portrait" /> <!-- <activity -->
229-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:210:13-49
230        <!-- android:name=".ble.NewVitalsActivity" -->
231        <!-- android:screenOrientation="portrait" /> -->
232        <activity
232-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:213:9-215:52
233            android:name="com.watchrx.watchrxhealth.ScheduleTextMessageActivity"
233-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:214:13-56
234            android:screenOrientation="portrait" />
234-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:215:13-49
235        <activity
235-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:216:9-219:52
236            android:name="com.watchrx.watchrxhealth.twilio.VideoActivity"
236-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:217:13-49
237            android:launchMode="singleTask"
237-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:218:13-44
238            android:screenOrientation="portrait" />
238-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:219:13-49
239        <activity
239-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:220:9-223:52
240            android:name="com.watchrx.watchrxhealth.twilio.SettingsActivity"
240-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:221:13-52
241            android:launchMode="singleTask"
241-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:222:13-44
242            android:screenOrientation="portrait" />
242-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:223:13-49
243        <activity android:name="com.watchrx.watchrxhealth.ble.NewVitalsActivity" />
243-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:224:9-59
243-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:224:19-56
244        <activity
244-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:225:9-227:47
245            android:name="com.watchrx.watchrxhealth.voip.IncomingCallActivity"
245-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:226:13-54
246            android:launchMode="singleTask" />
246-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:227:13-44
247        <activity
247-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:229:9-236:20
248            android:name="com.watchrx.watchrxhealth.PermissionsRationaleActivity"
248-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:230:13-57
249            android:exported="true"
249-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:231:13-36
250            android:screenOrientation="portrait" >
250-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:232:13-49
251            <intent-filter>
251-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:233:13-235:29
252                <action android:name="androidx.health.ACTION_SHOW_PERMISSIONS_RATIONALE" />
252-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:234:17-92
252-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:234:25-89
253            </intent-filter>
254        </activity>
255
256        <activity-alias
256-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:238:9-247:26
257            android:name="com.watchrx.watchrxhealth.ViewPermissionUsageActivity"
257-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:239:13-55
258            android:exported="true"
258-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:240:13-36
259            android:permission="android.permission.START_VIEW_PERMISSION_USAGE"
259-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:242:13-80
260            android:targetActivity="com.watchrx.watchrxhealth.PermissionsRationaleActivity" >
260-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:241:13-67
261            <intent-filter>
261-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:243:13-246:29
262                <action android:name="android.intent.action.VIEW_PERMISSION_USAGE" />
262-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:244:17-86
262-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:244:25-83
263
264                <category android:name="android.intent.category.HEALTH_PERMISSIONS" />
264-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:245:17-87
264-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:245:27-84
265            </intent-filter>
266        </activity-alias>
267
268        <meta-data
268-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:249:9-251:69
269            android:name="com.google.android.gms.version"
269-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:250:13-58
270            android:value="@integer/google_play_services_version" />
270-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:251:13-66
271        <meta-data
271-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:252:9-254:37
272            android:name="firebase_messaging_auto_init_enabled"
272-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:253:13-64
273            android:value="false" />
273-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:254:13-34
274        <meta-data
274-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:255:9-257:37
275            android:name="firebase_analytics_collection_enabled"
275-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:256:13-65
276            android:value="false" />
276-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:257:13-34
277        <meta-data
277-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:258:9-260:71
278            android:name="com.google.android.geo.API_KEY"
278-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:259:13-58
279            android:value="AIzaSyDhGEgJCRCYBnheNQ9TVFY3h7Byn0oq6R4" />
279-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:260:13-68
280        <meta-data
280-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:261:9-263:71
281            android:name="com.google.firebase.messaging.default_notification_channel_id"
281-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:262:13-89
282            android:value="@string/default_notification_channel_id" />
282-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:263:13-68
283
284        <provider
284-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:265:9-273:20
285            android:name="androidx.core.content.FileProvider"
285-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:266:13-62
286            android:authorities="com.watchrx.watchrxhealth.fileprovider"
286-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:267:13-73
287            android:exported="false"
287-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:268:13-37
288            android:grantUriPermissions="true" >
288-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:269:13-47
289            <meta-data
289-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:270:13-272:53
290                android:name="android.support.FILE_PROVIDER_PATHS"
290-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:271:17-67
291                android:resource="@xml/filepaths" />
291-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:272:17-50
292        </provider>
293
294        <receiver
294-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:275:9-278:39
295            android:name="com.watchrx.watchrxhealth.receivers.CheckQueueReceiver"
295-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:276:13-57
296            android:enabled="true"
296-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:277:13-35
297            android:exported="true" />
297-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:278:13-36
298        <receiver
298-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:279:9-282:39
299            android:name="com.watchrx.watchrxhealth.receivers.ReminderReceiver"
299-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:280:13-55
300            android:enabled="true"
300-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:281:13-35
301            android:exported="true" />
301-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:282:13-36
302        <receiver
302-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:283:9-286:39
303            android:name="com.watchrx.watchrxhealth.receivers.CustomAlertReceiver"
303-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:284:13-58
304            android:enabled="true"
304-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:285:13-35
305            android:exported="true" />
305-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:286:13-36
306        <receiver
306-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:287:9-290:39
307            android:name="com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver"
307-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:288:13-67
308            android:enabled="true"
308-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:289:13-35
309            android:exported="true" />
309-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:290:13-36
310        <receiver
310-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:291:9-294:39
311            android:name="com.watchrx.watchrxhealth.syncup.SyncupReceiver"
311-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:292:13-50
312            android:enabled="true"
312-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:293:13-35
313            android:exported="true" />
313-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:294:13-36
314        <receiver
314-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:295:9-307:20
315            android:name="com.watchrx.watchrxhealth.receivers.RebootComplete"
315-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:296:13-53
316            android:enabled="true"
316-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:297:13-35
317            android:exported="true"
317-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:298:13-36
318            android:permission="android.permission.RECEIVE_BOOT_COMPLETED" >
318-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:299:13-75
319            <intent-filter>
319-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:300:13-306:29
320                <action android:name="android.intent.action.BOOT_COMPLETED" />
320-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:301:17-79
320-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:301:25-76
321                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
321-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:302:17-82
321-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:302:25-79
322                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
322-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:303:17-80
322-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:303:25-77
323
324                <category android:name="android.intent.category.DEFAULT" />
324-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:17-76
324-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:27-73
325            </intent-filter>
326        </receiver>
327        <receiver
327-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:308:9-315:20
328            android:name="com.watchrx.watchrxhealth.receivers.TimeZoneChangedReceiver"
328-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:309:13-62
329            android:enabled="true"
329-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:310:13-35
330            android:exported="true" >
330-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:311:13-36
331            <intent-filter>
331-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:312:13-314:29
332                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
332-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:17-81
332-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:25-78
333            </intent-filter>
334        </receiver>
335        <receiver
335-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:316:9-330:20
336            android:name="com.watchrx.watchrxhealth.receivers.NewPackageInstalled"
336-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:317:13-58
337            android:enabled="true"
337-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:318:13-35
338            android:exported="true" >
338-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:319:13-36
339            <intent-filter>
339-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:320:13-329:29
340                <action android:name="android.intent.action.PACKAGE_ADDED" />
340-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:321:17-78
340-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:321:25-75
341                <action android:name="android.intent.action.PACKAGE_INSTALL" />
341-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:322:17-80
341-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:322:25-77
342                <action android:name="android.intent.action.PACKAGE_REMOVED" />
342-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:323:17-80
342-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:323:25-77
343                <action android:name="android.intent.action.PACKAGE_REPLACED" />
343-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:324:17-81
343-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:324:25-78
344
345                <category android:name="android.intent.category.DEFAULT" />
345-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:17-76
345-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:305:27-73
346
347                <data android:scheme="package" />
347-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:328:17-50
347-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:328:23-47
348            </intent-filter>
349        </receiver>
350        <receiver
350-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:331:9-338:20
351            android:name="com.watchrx.watchrxhealth.receivers.InternetStatusChangeReceiver"
351-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:332:13-67
352            android:exported="true" >
352-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:333:13-36
353            <intent-filter>
353-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:334:13-337:29
354                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
354-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:335:17-79
354-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:335:25-76
355                <!-- <action android:name="android.net.wifi.WIFI_STATE_CHANGED" /> -->
356            </intent-filter>
357        </receiver>
358        <receiver
358-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:339:9-342:39
359            android:name="com.watchrx.watchrxhealth.receivers.HrtBroadcastReceiver"
359-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:340:13-59
360            android:enabled="true"
360-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:341:13-35
361            android:exported="true" />
361-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:342:13-36
362        <receiver
362-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:343:9-346:39
363            android:name="com.watchrx.watchrxhealth.receivers.NetworkHeartBeat"
363-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:344:13-55
364            android:enabled="true"
364-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:345:13-35
365            android:exported="true" />
365-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:346:13-36
366        <receiver
366-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:347:9-350:39
367            android:name="com.watchrx.watchrxhealth.receivers.ScheduleMessageReceiver"
367-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:348:13-62
368            android:enabled="true"
368-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:349:13-35
369            android:exported="true" />
369-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:350:13-36
370        <receiver
370-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:351:9-354:39
371            android:name="com.watchrx.watchrxhealth.receivers.SendPedoMeterReceiver"
371-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:352:13-60
372            android:enabled="true"
372-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:353:13-35
373            android:exported="true" />
373-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:354:13-36
374        <receiver
374-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:355:9-358:39
375            android:name="com.watchrx.watchrxhealth.receivers.SendHealthConnectDataReceiver"
375-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:356:13-68
376            android:enabled="true"
376-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:357:13-35
377            android:exported="true" />
377-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:358:13-36
378        <receiver
378-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:359:9-362:39
379            android:name="com.watchrx.watchrxhealth.receivers.MidnightPedoMeterResetReceiver"
379-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:360:13-69
380            android:enabled="true"
380-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:361:13-35
381            android:exported="true" />
381-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:362:13-36
382        <receiver
382-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:363:9-366:39
383            android:name="com.watchrx.watchrxhealth.receivers.VitalReminderReceiver"
383-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:364:13-60
384            android:enabled="true"
384-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:365:13-35
385            android:exported="true" />
385-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:366:13-36
386        <receiver
386-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:367:9-370:39
387            android:name="com.watchrx.watchrxhealth.receivers.MidnightLogFileUploadReceiver"
387-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:368:13-68
388            android:enabled="true"
388-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:369:13-35
389            android:exported="true" />
389-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:370:13-36
390        <receiver
390-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:371:9-374:39
391            android:name="com.watchrx.watchrxhealth.receivers.BleBroadCastReceiver"
391-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:372:13-59
392            android:enabled="true"
392-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:373:13-35
393            android:exported="true" />
393-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:374:13-36
394
395        <service
395-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:376:9-386:19
396            android:name="com.watchrx.watchrxhealth.gcm.GCMPushReceiverService"
396-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:377:13-55
397            android:directBootAware="true"
397-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:378:13-43
398            android:exported="true"
398-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:379:13-36
399            android:permission="true" >
399-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:380:13-38
400            <intent-filter>
400-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:382:13-385:29
401                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
401-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:383:17-80
401-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:383:25-77
402                <action android:name="com.google.firebase.MESSAGING_EVENT" />
402-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:384:17-78
402-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:384:25-75
403            </intent-filter>
404        </service>
405        <service
405-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:387:9-389:40
406            android:name="com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService"
406-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:388:13-61
407            android:exported="false" />
407-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:389:13-37
408        <service
408-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:390:9-392:40
409            android:name="com.watchrx.watchrxhealth.gps.GeoFenceIntentServices"
409-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:391:13-55
410            android:exported="false" />
410-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:392:13-37
411        <service
411-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:393:9-395:40
412            android:name="com.watchrx.watchrxhealth.pedometer.SensorListener"
412-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:394:13-53
413            android:exported="false" />
413-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:395:13-37
414        <service
414-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:396:9-400:74
415            android:name="com.watchrx.watchrxhealth.WatchRxForegroundService"
415-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:397:13-53
416            android:exported="false"
416-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:398:13-37
417            android:foregroundServiceType="mediaPlayback"
417-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:399:13-58
418            android:permission="android.permission.FOREGROUND_SERVICE" />
418-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:400:13-71
419        <service
419-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:401:9-407:61
420            android:name="com.zipow.videobox.share.ScreenShareServiceForSDK"
420-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:402:13-77
421            android:exported="false"
421-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:403:13-37
422            android:foregroundServiceType="microphone|connectedDevice"
422-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:404:13-71
423            android:label="Zoom" />
423-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:405:13-33
424
425        <uses-library
425-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:409:9-411:40
426            android:name="org.apache.http.legacy"
426-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:410:13-50
427            android:required="false" />
427-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:411:13-37
428
429        <receiver
429-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
430            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
430-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
431            android:exported="true"
431-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
432            android:permission="com.google.android.c2dm.permission.SEND" >
432-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
433            <intent-filter>
433-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
434                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
434-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
434-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
435            </intent-filter>
436
437            <meta-data
437-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
438                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
438-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
439                android:value="true" />
439-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
440        </receiver>
441        <!--
442             FirebaseMessagingService performs security checks at runtime,
443             but set to not exported to explicitly avoid allowing another app to call it.
444        -->
445        <service
445-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
446            android:name="com.google.firebase.messaging.FirebaseMessagingService"
446-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
447            android:directBootAware="true"
447-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
448            android:exported="false" >
448-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
449            <intent-filter android:priority="-500" >
449-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
449-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
450                <action android:name="com.google.firebase.MESSAGING_EVENT" />
450-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:384:17-78
450-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:384:25-75
451            </intent-filter>
452        </service>
453        <service
453-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
454            android:name="com.google.firebase.components.ComponentDiscoveryService"
454-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
455            android:directBootAware="true"
455-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
456            android:exported="false" >
456-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
457            <meta-data
457-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
458                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
458-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
459                android:value="com.google.firebase.components.ComponentRegistrar" />
459-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
460            <meta-data
460-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
461                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
461-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
462                android:value="com.google.firebase.components.ComponentRegistrar" />
462-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\29c63916f894d15cc031166656a2aa2e\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
463            <meta-data
463-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
464                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
464-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
465                android:value="com.google.firebase.components.ComponentRegistrar" />
465-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d3c8fd7ad1ab83ab1661ae872cd259b5\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
466            <meta-data
466-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
467                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
467-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
468                android:value="com.google.firebase.components.ComponentRegistrar" />
468-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
469            <meta-data
469-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
470                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
470-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
471                android:value="com.google.firebase.components.ComponentRegistrar" />
471-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\493e5069a4d817811d310b5ea3aa08d9\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
472            <meta-data
472-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
473                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
473-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
474                android:value="com.google.firebase.components.ComponentRegistrar" />
474-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c091d3b9f4942984a90acbb27e753cf\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
475            <meta-data
475-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
476                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
476-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
477                android:value="com.google.firebase.components.ComponentRegistrar" />
477-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d4d21cea7b516fd93d9aca81cd0adbf\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
478            <meta-data
478-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
479                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
479-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
480                android:value="com.google.firebase.components.ComponentRegistrar" />
480-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
481        </service>
482
483        <receiver
483-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:32:9-40:20
484            android:name="com.google.firebase.messaging.directboot.FirebaseMessagingDirectBootReceiver"
484-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:33:13-104
485            android:directBootAware="true"
485-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:34:13-43
486            android:exported="true"
486-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:35:13-36
487            android:permission="com.google.android.c2dm.permission.SEND" >
487-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:36:13-73
488            <intent-filter>
488-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:37:13-39:29
489                <action android:name="com.google.firebase.messaging.RECEIVE_DIRECT_BOOT" />
489-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:38:17-92
489-->[com.google.firebase:firebase-messaging-directboot:23.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\b74f603974d896050a91f7f0cc1a352e\transformed\jetified-firebase-messaging-directboot-23.0.2\AndroidManifest.xml:38:25-89
490            </intent-filter>
491        </receiver>
492
493        <activity
493-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:23:9-27:75
494            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
494-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:24:13-93
495            android:excludeFromRecents="true"
495-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:25:13-46
496            android:exported="false"
496-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:26:13-37
497            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
497-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:27:13-72
498        <!--
499            Service handling Google Sign-In user revocation. For apps that do not integrate with
500            Google Sign-In, this service will never be started.
501        -->
502        <service
502-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:33:9-37:51
503            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
503-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:34:13-89
504            android:exported="true"
504-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:35:13-36
505            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
505-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:36:13-107
506            android:visibleToInstantApps="true" />
506-->[com.google.android.gms:play-services-auth:21.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\390358a07b4ce6b6bfb6250e68b3c82d\transformed\jetified-play-services-auth-21.3.0\AndroidManifest.xml:37:13-48
507        <service
507-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:24:9-32:19
508            android:name="androidx.health.platform.client.impl.sdkservice.HealthDataSdkService"
508-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:25:13-96
509            android:enabled="true"
509-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:26:13-35
510            android:exported="true" >
510-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:27:13-36
511            <intent-filter>
511-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:29:13-31:29
512                <action android:name="androidx.health.platform.client.ACTION_BIND_SDK_SERVICE" />
512-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:30:17-98
512-->[androidx.health.connect:connect-client:1.1.0-alpha11] C:\Users\<USER>\.gradle\caches\transforms-3\de5e0f29db83a3edeccb293e7a8745c6\transformed\jetified-connect-client-1.1.0-alpha11\AndroidManifest.xml:30:25-95
513            </intent-filter>
514        </service>
515
516        <activity
516-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
517            android:name="com.google.android.gms.common.api.GoogleApiActivity"
517-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
518            android:exported="false"
518-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
519            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
519-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a44e8efa791fec526e44d72b1cad3c4\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
520
521        <provider
521-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
522            android:name="com.google.firebase.provider.FirebaseInitProvider"
522-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
523            android:authorities="com.watchrx.watchrxhealth.firebaseinitprovider"
523-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
524            android:directBootAware="true"
524-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
525            android:exported="false"
525-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
526            android:initOrder="100" />
526-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d326e4fca2023f8ddf9c403ad638148\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
527        <provider
527-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
528            android:name="androidx.startup.InitializationProvider"
528-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
529            android:authorities="com.watchrx.watchrxhealth.androidx-startup"
529-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
530            android:exported="false" >
530-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
531            <meta-data
531-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
532                android:name="androidx.work.WorkManagerInitializer"
532-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
533                android:value="androidx.startup" />
533-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
534            <meta-data
534-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
535                android:name="androidx.emoji2.text.EmojiCompatInitializer"
535-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
536                android:value="androidx.startup" />
536-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa90089037e89115f8f008545b5ea63d\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
537            <meta-data
537-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
538                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
538-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
539                android:value="androidx.startup" />
539-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a828079e1a860fd718ba7bff15bc5cdb\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
540            <meta-data
540-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
541                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
541-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
542                android:value="androidx.startup" />
542-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
543        </provider>
544
545        <service
545-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
546            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
546-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
547            android:directBootAware="false"
547-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
548            android:enabled="@bool/enable_system_alarm_service_default"
548-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
549            android:exported="false" />
549-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
550        <service
550-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
551            android:name="androidx.work.impl.background.systemjob.SystemJobService"
551-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
552            android:directBootAware="false"
552-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
553            android:enabled="@bool/enable_system_job_service_default"
553-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
554            android:exported="true"
554-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
555            android:permission="android.permission.BIND_JOB_SERVICE" />
555-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
556        <service
556-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
557            android:name="androidx.work.impl.foreground.SystemForegroundService"
557-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
558            android:directBootAware="false"
558-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
559            android:enabled="@bool/enable_system_foreground_service_default"
559-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
560            android:exported="false" />
560-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
561
562        <receiver
562-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
563            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
563-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
564            android:directBootAware="false"
564-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
565            android:enabled="true"
565-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
566            android:exported="false" />
566-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
567        <receiver
567-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
568            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
568-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
569            android:directBootAware="false"
569-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
570            android:enabled="false"
570-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
571            android:exported="false" >
571-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
572            <intent-filter>
572-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
573                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
573-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
573-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
574                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
574-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
574-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
575            </intent-filter>
576        </receiver>
577        <receiver
577-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
578            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
578-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
579            android:directBootAware="false"
579-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
580            android:enabled="false"
580-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
581            android:exported="false" >
581-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
582            <intent-filter>
582-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
583                <action android:name="android.intent.action.BATTERY_OKAY" />
583-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
583-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
584                <action android:name="android.intent.action.BATTERY_LOW" />
584-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
584-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
585            </intent-filter>
586        </receiver>
587        <receiver
587-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
588            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
588-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
589            android:directBootAware="false"
589-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
590            android:enabled="false"
590-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
591            android:exported="false" >
591-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
592            <intent-filter>
592-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
593                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
593-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
593-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
594                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
594-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
594-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
595            </intent-filter>
596        </receiver>
597        <receiver
597-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
598            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
598-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
599            android:directBootAware="false"
599-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
600            android:enabled="false"
600-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
601            android:exported="false" >
601-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
602            <intent-filter>
602-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:334:13-337:29
603                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
603-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:335:17-79
603-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:335:25-76
604            </intent-filter>
605        </receiver>
606        <receiver
606-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
607            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
607-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
608            android:directBootAware="false"
608-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
609            android:enabled="false"
609-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
610            android:exported="false" >
610-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
611            <intent-filter>
611-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
612                <action android:name="android.intent.action.BOOT_COMPLETED" />
612-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:301:17-79
612-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:301:25-76
613                <action android:name="android.intent.action.TIME_SET" />
613-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
613-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
614                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
614-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:17-81
614-->C:\Users\<USER>\Documents\GitHub\watchrxwatch\app\src\main\AndroidManifest.xml:313:25-78
615            </intent-filter>
616        </receiver>
617        <receiver
617-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
618            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
618-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
619            android:directBootAware="false"
619-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
620            android:enabled="@bool/enable_system_alarm_service_default"
620-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
621            android:exported="false" >
621-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
622            <intent-filter>
622-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
623                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
623-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
623-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
624            </intent-filter>
625        </receiver>
626        <receiver
626-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
627            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
627-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
628            android:directBootAware="false"
628-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
629            android:enabled="true"
629-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
630            android:exported="true"
630-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
631            android:permission="android.permission.DUMP" >
631-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
632            <intent-filter>
632-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
633                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
633-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
633-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\b87b3f2d7275f8fdc70256821a83cc8d\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
634            </intent-filter>
635        </receiver>
636
637        <uses-library
637-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
638            android:name="androidx.window.extensions"
638-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
639            android:required="false" />
639-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
640        <uses-library
640-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
641            android:name="androidx.window.sidecar"
641-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
642            android:required="false" />
642-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\87f5b861fcf03439e943ebcc6db5de8c\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
643
644        <receiver
644-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
645            android:name="androidx.profileinstaller.ProfileInstallReceiver"
645-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
646            android:directBootAware="false"
646-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
647            android:enabled="true"
647-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
648            android:exported="true"
648-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
649            android:permission="android.permission.DUMP" >
649-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
650            <intent-filter>
650-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
651                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
651-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
651-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
652            </intent-filter>
653            <intent-filter>
653-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
654                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
654-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
654-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
655            </intent-filter>
656            <intent-filter>
656-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
657                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
657-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
657-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
658            </intent-filter>
659            <intent-filter>
659-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
660                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
660-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
660-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\45bc3260ac66271161914b22da96ddbc\transformed\jetified-profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
661            </intent-filter>
662        </receiver>
663
664        <service
664-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
665            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
665-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
666            android:exported="false" >
666-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
667            <meta-data
667-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
668                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
668-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
669                android:value="cct" />
669-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\653473498a694b9dea8259bfa4be3dc0\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
670        </service>
671        <service
671-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
672            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
672-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
673            android:exported="false"
673-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
674            android:permission="android.permission.BIND_JOB_SERVICE" >
674-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
675        </service>
676
677        <receiver
677-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
678            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
678-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
679            android:exported="false" />
679-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\145f7f899f1b91e65694afafb530a75d\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
680
681        <service
681-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
682            android:name="androidx.room.MultiInstanceInvalidationService"
682-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
683            android:directBootAware="true"
683-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
684            android:exported="false" />
684-->[androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\transforms-3\fb028a7e1bc191434a34bd3a6c1ad8ee\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
685    </application>
686
687</manifest>
