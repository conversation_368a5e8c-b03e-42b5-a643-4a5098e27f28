package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.LogUtils;

import static com.watchrx.watchrxhealth.constants.CommonConstants.MAX_TRIGGER_TIME_DIFFERENCE;
import static java.lang.Math.abs;


public class HrtBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {

        LogUtils.debug("Received  Alarm For Heart Collection");

        long triggerTimeMillis = intent.getLongExtra("triggerAt", -1);
        long currentTimeMillis = System.currentTimeMillis();

        if (abs(currentTimeMillis - triggerTimeMillis) > MAX_TRIGGER_TIME_DIFFERENCE) {
            LogUtils.debug("This alarm is  outside the time window");
            return; // too much difference; no longer valid alarm
        }
        Globals.intentMap.remove(intent.getStringExtra("day") + "-" + intent.getStringExtra("time"));

        /*Intent i = new Intent(context, HeartRateActivity.class);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(i);*/

        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("HeartRateActivity");
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        }else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
        }
    }
}
