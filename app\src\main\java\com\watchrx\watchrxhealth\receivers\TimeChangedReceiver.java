package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

public class TimeChangedReceiver extends BroadcastReceiver {
    private static final boolean dontExclude = true;

    @Override
    public void onReceive(Context context, Intent intent) {

        if (dontExclude) {
            LogUtils.debug("*****TimeChanged Event has been detected. Alarms need to be reset*****");
            ReminderUtils.setupDailyScheduleUpdater(context);
            ReminderUtils.setupScheduleForToday(context);
        } else {
            LogUtils.debug("TimeChanged Event has been detected. But we will not reset the Alarms as we don't currently cater to this corner case where time changes.");
        }
    }
}
