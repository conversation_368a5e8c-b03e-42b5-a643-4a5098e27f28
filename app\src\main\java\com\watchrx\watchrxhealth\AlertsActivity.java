package com.watchrx.watchrxhealth;

import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.adapter.AlertsAdapter;
import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.models.AlertsModel;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class AlertsActivity extends AppCompatActivity {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        setContentView(R.layout.activity_notifications);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        RecyclerView recyclerView = findViewById(R.id.rvAlerts);
        List<AlertsModel> alertsModelList = new ArrayList<>();
        List<Alerts> alertsList = Alerts.getFromDB();
        for (Alerts alert : alertsList) {
            AlertsModel data = new AlertsModel();
            data.setAlertDate(getTime(alert.getAlertDate()) == null ? "" : getTime(alert.getAlertDate()));
            data.setAlertMessage(alert.getAlertMessage());
            data.setAlertType(alert.getAlertType());
            data.setMedicineName(alert.getMedicineName());
            alertsModelList.add(data);
        }

        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(new AlertsAdapter(AlertsActivity.this, alertsModelList, new AlertsAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(AlertsModel item) {
                Toast.makeText(AlertsActivity.this, "Clicked to " + item.getAlertType(), Toast.LENGTH_LONG).show();
            }
        }));

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if ((keyCode == KeyEvent.KEYCODE_BACK)) {
            Log.d(this.getClass().getName(), "back button pressed");
            Alerts.deleteAllRows();
        }
        return super.onKeyDown(keyCode, event);
    }

    private static String getTime(String time) {
        try {
            final SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, hh:mm:ss a", Locale.US);
            final Date dateObj = sdf.parse(time);
            return new SimpleDateFormat("MMM dd, hh:mm a", Locale.US).format(dateObj);
        } catch (final ParseException e) {
            e.printStackTrace();
        }
        return null;
    }
}