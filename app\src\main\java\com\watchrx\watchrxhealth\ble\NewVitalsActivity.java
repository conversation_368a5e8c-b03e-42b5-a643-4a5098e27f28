package com.watchrx.watchrxhealth.ble;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.ProgressDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.PackageManager;
import android.media.MediaPlayer;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Vibrator;
import android.util.Log;
import android.util.SparseArray;
import android.view.View;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.taidoc.pclinklibrary.android.bluetooth.util.BluetoothUtil;
import com.taidoc.pclinklibrary.connection.AndroidBluetoothConnection;
import com.taidoc.pclinklibrary.connection.AndroidBluetoothConnection.LeConnectedListener;
import com.taidoc.pclinklibrary.connection.util.ConnectionManager;
import com.taidoc.pclinklibrary.constant.PCLinkLibraryConstant;
import com.taidoc.pclinklibrary.constant.PCLinkLibraryEnum.User;
import com.taidoc.pclinklibrary.exceptions.CommunicationTimeoutException;
import com.taidoc.pclinklibrary.exceptions.ExceedRetryTimesException;
import com.taidoc.pclinklibrary.exceptions.NotConnectSerialPortException;
import com.taidoc.pclinklibrary.exceptions.NotSupportMeterException;
import com.taidoc.pclinklibrary.meter.AbstractMeter;
import com.taidoc.pclinklibrary.meter.record.AbstractRecord;
import com.taidoc.pclinklibrary.meter.record.BloodGlucoseRecord;
import com.taidoc.pclinklibrary.meter.record.BloodPressureRecord;
import com.taidoc.pclinklibrary.meter.record.SpO2Record;
import com.taidoc.pclinklibrary.meter.record.TemperatureRecord;
import com.taidoc.pclinklibrary.meter.record.WeightScaleRecord;
import com.taidoc.pclinklibrary.meter.util.MeterManager;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.TimeZone;


@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
public class NewVitalsActivity extends AppCompatActivity implements BluetoothAdapter.LeScanCallback, Runnable {
    public static final int MESSAGE_STATE_CONNECTING = 1;
    public static final int MESSAGE_STATE_CONNECT_FAIL = 2;
    public static final int MESSAGE_STATE_CONNECT_DONE = 3;
    public static final int MESSAGE_STATE_CONNECT_NONE = 4;
    public static final int MESSAGE_STATE_CONNECT_METER_SUCCESS = 5;
    public static final int MESSAGE_STATE_CHECK_METER_BT_DISTENCE = 7;
    public static final int MESSAGE_STATE_CHECK_METER_BT_DISTENCE_FAIL = 8;
    public static final int MESSAGE_STATE_NOT_SUPPORT_METER = 9;
    public static final int MESSAGE_STATE_NOT_CONNECT_SERIAL_PORT = 10;
    public static final int MESSAGE_STATE_SCANED_DEVICE = 11;

    private static final String TAG = "NewVitalsActivity";

    private BluetoothAdapter mBluetoothAdapter;
    private SparseArray<BluetoothDevice> mDevices;
    private String mMacAddress;
    private AbstractMeter mTaiDocMeter = null;

    private AndroidBluetoothConnection mConnection;
    private boolean mBLEMode;

    private static int vitalScheduleId = 0;
    private static String scanDeviceName = null;
    private static String vitalTypeName = null;

    private static final Handler timeoutHandler = new Handler();
    private static final long REMINDER_TIME_OUT = 45 * 1000;
    private static final long[] vibratePattern = {0, 500, 200};
    private static final List<VitalTypeValueModel> dataNeedToSend = new ArrayList<>();

    LinearLayout bgLayout;
    LinearLayout bgExtraLayout;
    LinearLayout bpLayout;
    LinearLayout thermometerLayout;
    LinearLayout progressBarLayout;

    //    TextView viewMeasurementDate;
    TextView viewMeasureType;
    TextView viewBGValue;
    TextView viewSysValue;
    TextView viewDiaValue;
    TextView viewPulseValue;
    TextView viewThermometerValue;
    TextView thermometerValueTitle;
    TextView weightTitle;

    // spO2
    LinearLayout spO2Layout;
    TextView viewSpO2Value;
    TextView viewSpO2PulseValue;

    // Weight
    LinearLayout weightLayout;
    TextView viewWeightValue;
    TextView viewBMIValue;

    //Progress bar
    ProgressBar progress_bar_dialog;
    private final Handler scanHandler = new Handler();

    TextView dateTimeValue;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);

        setContentView(R.layout.activity_vitals_measurement);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("");

        dataNeedToSend.clear();
        initViews();
        BluetoothManager manager = (BluetoothManager) getSystemService(BLUETOOTH_SERVICE);
        assert manager != null;
        mBluetoothAdapter = manager.getAdapter();
        mDevices = new SparseArray<>();

        String vitalScheduleIdStr = getIntent().getStringExtra("vitalScheduleId");
        if (vitalScheduleIdStr != null && !vitalScheduleIdStr.isEmpty()) {
            vitalScheduleId = Integer.parseInt(vitalScheduleIdStr);
            LogUtils.debug("Schedule Vital ID :" + vitalScheduleId);
        }
        String deviceNameIntent = getIntent().getStringExtra("deviceName");
        if (deviceNameIntent != null) {
            scanDeviceName = deviceNameIntent;
            LogUtils.debug("Scanning Device Name :" + scanDeviceName);
        }

        String vitalTypeNameInt = getIntent().getStringExtra("vitalTypeName");
        if (vitalTypeNameInt != null) {
            vitalTypeName = vitalTypeNameInt;
            LogUtils.debug("Need to measure Data For :" + vitalTypeName);
        }

        if (timeoutHandler == null) {
            Handler timeoutHandler = new Handler();
            timeoutHandler.postDelayed(NewVitalsActivity.this, REMINDER_TIME_OUT);
            LogUtils.debug("timeoutHandler object died. so using new handler object Inside OnCreate. Going to set timeout Handler.");
        } else {
            LogUtils.debug("Inside OnCreate. Going to set timeout Handler.");
            timeoutHandler.postDelayed(NewVitalsActivity.this, REMINDER_TIME_OUT);
        }

        GeneralUtils.stopSpeaking();
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);
        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                GeneralUtils.speak("Please turn on medical device to take " + vitalTypeName + " measurement  $");
            }
        });
        viewMeasureType.setText(vitalTypeName);
        RecyclerView recyclerView = findViewById(R.id.vitalRv);
    }

    private void initViews() {

        bgLayout =
                findViewById(R.id.bgLayout);
        bgExtraLayout = findViewById(R.id.bgExtraLayout);
        bpLayout = findViewById(R.id.bpLayout);
        thermometerLayout = findViewById(R.id.thermometerLayout);
        progressBarLayout = findViewById(R.id.progress_bar_layout);
        progress_bar_dialog = findViewById(R.id.progress_bar_dialog);

//        viewMeasurementDate = findViewById(R.id.measurementDate);
        viewMeasureType = findViewById(R.id.measurementType);
        viewBGValue = findViewById(R.id.bgValue);

        viewSysValue = findViewById(R.id.sysValue);
        viewDiaValue = findViewById(R.id.diaValue);
        viewPulseValue = findViewById(R.id.pulseValue);
        viewThermometerValue = findViewById(R.id.thermometerValue);

        thermometerValueTitle = findViewById(R.id.thermometerValueTitle);
        weightTitle = findViewById(R.id.weightTitle);

        // spO2
        spO2Layout = findViewById(R.id.spO2Layout);
        viewSpO2Value = findViewById(R.id.spO2Value);
        viewSpO2PulseValue = findViewById(R.id.spO2PulseValue);

        // Weight
        weightLayout = findViewById(R.id.weightScaleLayout);
        viewWeightValue = findViewById(R.id.weightValue);
        viewBMIValue = findViewById(R.id.bmiValue);

        dateTimeValue = findViewById(R.id.dateTimeValue);
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.debug("onResume Detected....");
        if (!mBluetoothAdapter.isEnabled()) {
            LogUtils.debug("Is Bluetooth Enabled :" + mBluetoothAdapter.isEnabled());
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                return;
            }
            mBluetoothAdapter.enable();
            LogUtils.debug("Bluetooth Status After  mBluetoothAdapter.enable() :" + mBluetoothAdapter.isEnabled());
            if (mBluetoothAdapter.isEnabled()) {
                LogUtils.debug("Device Scan going to start....");
                startScan.run();
            }
        } else {
            startScan.run();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
//        disconnectMeter();
    }


    private final Runnable startScan = new Runnable() {
        @Override
        public void run() {
            Log.e("startScan : ", "Scanning Started...");
            scanHandler.postDelayed(stopScan, 5000);
            mBluetoothAdapter.startLeScan(NewVitalsActivity.this);
        }
    };
    private final Runnable stopScan = new Runnable() {
        @Override
        public void run() {
            Log.e("stopScan : ", "Scanning Stopping ..." + mDevices.size());
            if (mDevices.size() == 0) {
                startScan.run();
            } else {
                mBluetoothAdapter.stopLeScan(NewVitalsActivity.this);
            }
        }
    };


    @Override
    public void onLeScan(final BluetoothDevice device, int i, byte[] bytes) {
        Log.w("Inside onLeScan", "Inside onLeScan......");
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (device.getName() != null && !device.getName().isEmpty()) {
                    if (device.getName().equalsIgnoreCase(scanDeviceName)) {
                        mDevices.put(device.hashCode(), device);
                        LogUtils.debug("Device Scan : Device Name :" + Objects.requireNonNull(device.getName()));
                    }
                }
            }
        });
        if (mDevices.size() > 0) {
            if (mDevices.size() > 0) {
                BluetoothDevice device1 = mDevices.valueAt(0);
                mMacAddress = device1.getAddress();
                mBLEMode = true;
            }
            if (mMacAddress == null) {
                Toast.makeText(NewVitalsActivity.this, "No Device", Toast.LENGTH_SHORT).show();
                return;
            }
            if (mTaiDocMeter == null) {
                setupAndroidBluetoothConnection();
                connectMeter();
                mBluetoothAdapter.stopLeScan(NewVitalsActivity.this);
            }
        }
    }

    private void updatePairedList() {
        mConnection.updatePairedList(mMacAddress);
    }

    private void connectMeter() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Looper.prepare();
                try {
                    meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_CONNECTING);
                    updatePairedList();
                    if (mBLEMode) {
                        mConnection.setLeConnectedListener(mLeConnectedListener);

                        if (mConnection.getState() == AndroidBluetoothConnection.STATE_NONE) {
                            mConnection.LeListen();
                        }

                        new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if (mConnection.getState() == AndroidBluetoothConnection.STATE_LISTEN) {
                                    if (mLeConnectedListener != null) {
                                        mLeConnectedListener.onConnectionTimeout();
                                    }
                                }
                            }
                        }, 10000);
                    } else {

                        if (mConnection.getState() == AndroidBluetoothConnection.STATE_NONE) {
                            mConnection.listen();
                        }
                    }
                } catch (CommunicationTimeoutException e) {
                    Log.e(TAG, e.getMessage(), e);
                    meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_CONNECT_FAIL);
                } catch (NotSupportMeterException e) {
                    Log.e(TAG, "not support meter", e);
                    meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_NOT_SUPPORT_METER);
                } catch (NotConnectSerialPortException e) {
                    meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_NOT_CONNECT_SERIAL_PORT);
                } catch (ExceedRetryTimesException e) {
                    meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_NOT_SUPPORT_METER);
                }
                Looper.loop();
            }
        }).start();
    }


    private void disconnectMeter() {
        new Thread(new Runnable() {
            @Override
            public void run() {
                Looper.prepare();
                try {
                    if (mTaiDocMeter != null) {
                        mTaiDocMeter.turnOffMeterOrBluetooth(0);
                    }
                    if (mBLEMode) {
                        if (mConnection != null) {
                            mConnection.setLeConnectedListener(null);
                            mConnection.LeDisconnect();
                        }
                    } else {
                        if (mConnection != null) {
                            mConnection.disconnect();
                            mConnection.LeDisconnect();
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, e.getMessage(), e);
                }
                Looper.loop();
            }
        }).start();
    }

    private void setupAndroidBluetoothConnection() {
        if (mConnection == null) {
            Log.d("MS", "setupAndroidBluetoothConnection()");
            try {
                mConnection = ConnectionManager.createAndroidBluetoothConnection(mBTConnectionHandler);
            } catch (Exception ee) {
                ee.printStackTrace();
            }
        }
    }

    private final LeConnectedListener mLeConnectedListener = new LeConnectedListener() {

        @Override
        public void onConnectionTimeout() {
            LogUtils.debug("Connection Timeout Detected...");
        }

        @Override
        public void onConnectionStateChange_Disconnect(BluetoothGatt gatt,
                                                       int status, int newState) {
            LogUtils.debug("Bluetooth Device Disconnected From Here.");
            mTaiDocMeter = null;
        }

        @SuppressLint("NewApi")
        @Override
        public void onDescriptorWrite_Complete(BluetoothGatt gatt,
                                               BluetoothGattDescriptor descriptor, int status) {
            mConnection.LeConnected(gatt.getDevice());
        }

        @Override
        public void onCharacteristicChanged_Notify(BluetoothGatt gatt,
                                                   BluetoothGattCharacteristic characteristic) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    Looper.prepare();
                    try {
                        mTaiDocMeter = MeterManager.detectConnectedMeter(mConnection);
                    } catch (Exception e) {
                        meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_NOT_SUPPORT_METER);
                    }

                    NewVitalsActivity.this.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            readData();
                        }
                    });
                    Looper.loop();
                }
            }).start();
        }

        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt,
                                            BluetoothGattCharacteristic characteristic) {
        }

        @Override
        public void onBPMeasuring(BluetoothGatt bluetoothGatt, BluetoothGattCharacteristic bluetoothGattCharacteristic) {

        }
    };

    private void readData() {
        if (mTaiDocMeter == null) {
            meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_NOT_SUPPORT_METER);
            return;
        }
        LogUtils.debug("Started Data Reading From Here....");
        SimpleDateFormat formatterDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);

        String beforeMeterTime = formatterDate.format(mTaiDocMeter
                .getSystemClock().getSystemClockTime());
        LogUtils.debug("Before Sync Date and Time :" + beforeMeterTime);

        Date nowTime = new Date();
        mTaiDocMeter.setSystemClock(nowTime);
        String afterMeterTime = formatterDate.format(mTaiDocMeter
                .getSystemClock().getSystemClockTime());
        LogUtils.debug("After Sync Date and Time :" + afterMeterTime);

        AbstractRecord record = mTaiDocMeter.getStorageDataRecord(0,
                User.CurrentUser);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        getLatestRecord(formatterDate, record);
        if (progress_bar_dialog != null) {
            progress_bar_dialog.setVisibility(View.GONE);
        }
        LogUtils.debug("Data Reading Completed.");
        mBluetoothAdapter.stopLeScan(NewVitalsActivity.this);
    }

    private void finishJob() {
        LogUtils.debug("Stopping the Screen From User Interface");
        if (dataNeedToSend.size() == 0) {
            CommUtils.sendVitalDataToServer(this, null, vitalScheduleId, "Failed", null, vitalTypeName);
        }
        disconnectMeter();
        mDevices.clear();
        dataNeedToSend.clear();
        mBluetoothAdapter.stopLeScan(NewVitalsActivity.this);
        scanHandler.removeCallbacks(this);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        timeoutHandler.removeCallbacks(NewVitalsActivity.this);
        if (progressBarLayout != null) {
            progressBarLayout.setVisibility(View.GONE);
        }
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        NewVitalsActivity.this.finish();
    }

    private void getLatestRecord(SimpleDateFormat formatterDate,
                                 AbstractRecord record, Object... params) {

        String tzId = TimeZone.getDefault().getID();
        Log.e("Current Timezone", tzId);

        progressBarLayout.setVisibility(View.GONE);
        (findViewById(R.id.dateTimeTv)).setVisibility(View.VISIBLE);
        if (record == null || record instanceof BloodPressureRecord) {
            int sysValue;
            int diaValue;
            int pulseValue;
            Date date;
            if (record == null) {
                sysValue = Integer.parseInt(params[0].toString());
                diaValue = Integer.parseInt(params[1].toString());
                pulseValue = Integer.parseInt(params[2].toString());
                date = new Date(System.currentTimeMillis());
            } else {
                sysValue = ((BloodPressureRecord) record)
                        .getSystolicValue();
                diaValue = ((BloodPressureRecord) record)
                        .getDiastolicValue();
                pulseValue = ((BloodPressureRecord) record).getPulseValue();
                date = ((BloodPressureRecord) record).getMeasureTime();
            }

            String measurementDate = formatterDate.format(date);

            VitalTypeValueModel systolicBPValueModel = new VitalTypeValueModel();
            systolicBPValueModel.setVitalType("Systolic Blood Pressure");
            systolicBPValueModel.setVitalValue(sysValue + "");
            dataNeedToSend.add(systolicBPValueModel);
            LogUtils.debug("Systolic Blood Pressure:" + sysValue);

            VitalTypeValueModel diastolicBPValueModel = new VitalTypeValueModel();
            diastolicBPValueModel.setVitalType("Diastolic Blood Pressure");
            diastolicBPValueModel.setVitalValue(diaValue + "");
            dataNeedToSend.add(diastolicBPValueModel);
            LogUtils.debug("Diastolic Blood Pressure:" + diaValue);

            VitalTypeValueModel heartRateValueModel = new VitalTypeValueModel();
            heartRateValueModel.setVitalType("Heart Rate");
            heartRateValueModel.setVitalValue(pulseValue + "");
            dataNeedToSend.add(heartRateValueModel);
            LogUtils.debug("Heart Rate:" + pulseValue);

            VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
            measureDateValueModel.setVitalType("measuredDateTime");
            measureDateValueModel.setVitalValue(measurementDate);
            dataNeedToSend.add(measureDateValueModel);
            LogUtils.debug("Measured Date Time" + measurementDate);
            // Set views
            bgLayout.setVisibility(View.GONE);
            bgExtraLayout.setVisibility(View.GONE);
            bpLayout.setVisibility(View.VISIBLE);
            thermometerLayout.setVisibility(View.GONE);
            weightLayout.setVisibility(View.GONE);
            spO2Layout.setVisibility(View.GONE);
//            viewMeasurementDate.setText(measurementDate);
            viewMeasureType.setText(vitalTypeName);
            viewSysValue.setText(String.valueOf(sysValue));
            viewDiaValue.setText(String.valueOf(diaValue));
            viewPulseValue.setText(String.valueOf(pulseValue));

            dateTimeValue.setText(measurementDate);
        } else if (record instanceof BloodGlucoseRecord) {
            // convert value
            String measurementDate = formatterDate
                    .format(((BloodGlucoseRecord) record).getMeasureTime());
            int bgValue = ((BloodGlucoseRecord) record).getGlucoseValue();

            VitalTypeValueModel bloodSugarValueModel = new VitalTypeValueModel();
            bloodSugarValueModel.setVitalType("Random Blood Sugar");
            bloodSugarValueModel.setVitalValue(bgValue + "");
            dataNeedToSend.add(bloodSugarValueModel);
            LogUtils.debug("Random Blood Sugar:" + bgValue);

            VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
            measureDateValueModel.setVitalType("measuredDateTime");
            measureDateValueModel.setVitalValue(measurementDate);
            dataNeedToSend.add(measureDateValueModel);
            LogUtils.debug("Measured Date Time" + measurementDate);

            // set views
            bgLayout.setVisibility(View.VISIBLE);
            bgExtraLayout.setVisibility(View.GONE);
            bpLayout.setVisibility(View.GONE);
            thermometerLayout.setVisibility(View.GONE);
            weightLayout.setVisibility(View.GONE);
//            viewMeasurementDate.setText(measurementDate);
            viewMeasureType.setText(vitalTypeName);
            viewBGValue.setText(String.valueOf(bgValue));

            dateTimeValue.setText(measurementDate);
        } else if (record instanceof TemperatureRecord) {
            // Convert value
            double thermometerValue = ((TemperatureRecord) record)
                    .getObjectTemperatureValue();
            String measurementDate = formatterDate
                    .format(((TemperatureRecord) record).getMeasureTime());
            double F = thermometerValue * (9f / 5) + 32;

            VitalTypeValueModel bloodSugarValueModel = new VitalTypeValueModel();
            bloodSugarValueModel.setVitalType("Temperature");
            bloodSugarValueModel.setVitalValue(String.format(Locale.US, "%.2f", F));
            dataNeedToSend.add(bloodSugarValueModel);
            LogUtils.debug("Temperature:" + String.format(Locale.US, "%.2f", F));

            VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
            measureDateValueModel.setVitalType("measuredDateTime");
            measureDateValueModel.setVitalValue(measurementDate);
            dataNeedToSend.add(measureDateValueModel);
            LogUtils.debug("Measured Date Time" + measurementDate);

            // Set views
            bgLayout.setVisibility(View.GONE);
            bgExtraLayout.setVisibility(View.GONE);
            bpLayout.setVisibility(View.GONE);
            thermometerLayout.setVisibility(View.VISIBLE);
            spO2Layout.setVisibility(View.GONE);
            weightLayout.setVisibility(View.GONE);
//            viewMeasurementDate.setText(measurementDate);
            viewMeasureType.setText(vitalTypeName);
            DecimalFormat df = new DecimalFormat("#.##");
            if (tzId.contains("America") || tzId.contains("US")) {
                thermometerValueTitle.setText(R.string.temp_f);
                viewThermometerValue.setText(String.format(Locale.US, "%.2f", F));
            } else {
                thermometerValueTitle.setText(R.string.temp_c);
                viewThermometerValue.setText(df.format(thermometerValue));
            }

            dateTimeValue.setText(measurementDate);
        } else if (record instanceof SpO2Record) {
            int spO2Value = ((SpO2Record) record).getSpO2();
            int pulseValue = ((SpO2Record) record).getPulse();
            String measurementDate = formatterDate
                    .format(((SpO2Record) record).getMeasureTime());
            Log.e("DateTime", measurementDate);
            VitalTypeValueModel bloodSugarValueModel = new VitalTypeValueModel();
            bloodSugarValueModel.setVitalType("Oxygen Saturation");
            bloodSugarValueModel.setVitalValue("" + spO2Value);
            dataNeedToSend.add(bloodSugarValueModel);
            LogUtils.debug("Oxygen Saturation:" + spO2Value);

            VitalTypeValueModel heartRateValueModel = new VitalTypeValueModel();
            heartRateValueModel.setVitalType("Heart Rate");
            heartRateValueModel.setVitalValue("" + pulseValue);
            dataNeedToSend.add(heartRateValueModel);

            VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
            measureDateValueModel.setVitalType("measuredDateTime");
            measureDateValueModel.setVitalValue(measurementDate);
            dataNeedToSend.add(measureDateValueModel);
            LogUtils.debug("Measured Date Time" + measurementDate);

            spO2Layout.setVisibility(View.VISIBLE);
            bgLayout.setVisibility(View.GONE);
            bgExtraLayout.setVisibility(View.GONE);
            bpLayout.setVisibility(View.GONE);
            thermometerLayout.setVisibility(View.GONE);
            weightLayout.setVisibility(View.GONE);
//            viewMeasurementDate.setText(measurementDate);
            viewMeasureType.setText(vitalTypeName);
            viewSpO2Value.setText(String.valueOf(spO2Value));
            viewSpO2PulseValue.setText(String.valueOf(pulseValue));
            dateTimeValue.setText(measurementDate);
        } else if (record instanceof WeightScaleRecord) {
            // Convert value
            double weight = ((WeightScaleRecord) record).getWeight();
            double bmi = ((WeightScaleRecord) record).getBmi();

            String measurementDate = formatterDate
                    .format(((WeightScaleRecord) record).getMeasureTime());

            double convertValue = weight * 2.205;
            DecimalFormat df = new DecimalFormat("#.##");

            VitalTypeValueModel weightVital = new VitalTypeValueModel();
            weightVital.setVitalType("Weight");
            weightVital.setVitalValue("" + df.format(convertValue));
            dataNeedToSend.add(weightVital);
            LogUtils.debug("Weight : " + df.format(convertValue));

            VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
            measureDateValueModel.setVitalType("measuredDateTime");
            measureDateValueModel.setVitalValue(measurementDate);
            dataNeedToSend.add(measureDateValueModel);
            LogUtils.debug("Measured Date Time" + measurementDate);

            // Set views
            bgLayout.setVisibility(View.GONE);
            bgExtraLayout.setVisibility(View.GONE);
            bpLayout.setVisibility(View.GONE);
            thermometerLayout.setVisibility(View.GONE);
            spO2Layout.setVisibility(View.GONE);
            weightLayout.setVisibility(View.VISIBLE);
//            viewMeasurementDate.setText(measurementDate);
            viewMeasureType.setText(vitalTypeName);
            // DecimalFormat df = new DecimalFormat("#.##");
            if (tzId.contains("America") || tzId.contains("US")) {
                weightTitle.setText(R.string.weight_lbs);
                viewWeightValue.setText(df.format(convertValue));
                viewBMIValue.setText(df.format(bmi));
            } else {
                weightTitle.setText(R.string.weight_kg);
                viewWeightValue.setText(String.format("%s", weight));
                viewBMIValue.setText(String.format("%s", bmi));
            }
            dateTimeValue.setText(measurementDate);
        }
        LogUtils.debug("While Sending Data To Server  Data Count : " + dataNeedToSend.size());
        if (dataNeedToSend.size() > 0) {
            try {
                JSONArray jsonArray = new JSONArray();
                String measuredDate = null;
                for (VitalTypeValueModel v : dataNeedToSend) {
                    JSONObject jsonObj = new JSONObject();
                    String vt = v.getVitalType();
                    String vv = v.getVitalValue();
                    jsonObj.put("vitalTypeName", vt);
                    jsonObj.put("vitalValue", vv);
                    if (vt.equalsIgnoreCase("measuredDateTime")) {
                        measuredDate = vv;
                    } else {
                        jsonArray.put(jsonObj);
                    }
                }
                CommUtils.sendVitalDataToServer(this, jsonArray, vitalScheduleId, "success", measuredDate, null);
                GeneralUtils.stopSpeaking();
                GeneralUtils.speak("Data synced successfully!!");
            } catch (Exception e) {
                CommUtils.sendVitalDataToServer(this, null, vitalScheduleId, "Failed", null, vitalTypeName);
                GeneralUtils.stopSpeaking();
                GeneralUtils.speak("Sorry sync device data, Please try again after sometime.");
            }
        }
        timeoutHandler.removeCallbacks(this);
        timeoutHandler.postDelayed(NewVitalsActivity.this, 5000);
    }

    @SuppressLint("HandlerLeak")
    private final Handler mBTConnectionHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            try {
                switch (msg.what) {
                    case PCLinkLibraryConstant.MESSAGE_STATE_CHANGE:
                        switch (msg.arg1) {
                            case AndroidBluetoothConnection.STATE_CONNECTED_BY_LISTEN_MODE:
                                try {
                                    mTaiDocMeter = MeterManager.detectConnectedMeter(mConnection);
                                } catch (Exception e) {
                                    throw new NotSupportMeterException();
                                }
                                if (mTaiDocMeter == null) {
                                    throw new NotSupportMeterException();
                                }
                                break;
                            case AndroidBluetoothConnection.STATE_CONNECTING:
                            case AndroidBluetoothConnection.STATE_LISTEN:
                                break;
                            case AndroidBluetoothConnection.STATE_SCANED_DEVICE:
                                meterCommuHandler.sendEmptyMessage(MESSAGE_STATE_SCANED_DEVICE);
                                break;
                            case AndroidBluetoothConnection.STATE_NONE:
                                break;
                        }
                        break;
                    case PCLinkLibraryConstant.MESSAGE_TOAST:

                        break;
                    default:
                        break;
                }
            } catch (NotSupportMeterException e) {
                Log.e(TAG, "not support meter", e);
                LogUtils.debug("not support meter " + e.getMessage());
            }
        }
    };

    @SuppressLint("HandlerLeak")
    private final Handler meterCommuHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case MESSAGE_STATE_CONNECTING -> LogUtils.debug("Connecting...");
                case MESSAGE_STATE_SCANED_DEVICE -> {
                    final BluetoothDevice device = BluetoothUtil.getPairedDevice(mConnection.getConnectedDeviceAddress());
                    mConnection.LeConnect(getApplicationContext(), device);
                }
                case MESSAGE_STATE_CONNECT_DONE ->
                        LogUtils.debug("Connection Done...:MESSAGE_STATE_CONNECT_DONE");
                case MESSAGE_STATE_CONNECT_FAIL ->
                        LogUtils.debug("Connection Failed...:MESSAGE_STATE_CONNECT_FAIL");
                case MESSAGE_STATE_CONNECT_NONE ->
                        LogUtils.debug("Connection None...:MESSAGE_STATE_CONNECT_NONE");
                case MESSAGE_STATE_CONNECT_METER_SUCCESS ->
                        LogUtils.debug("Connection Meter Success...:MESSAGE_STATE_CONNECT_METER_SUCCESS");
                case MESSAGE_STATE_CHECK_METER_BT_DISTENCE -> {
                    ProgressDialog baCmdDialog = new ProgressDialog(
                            NewVitalsActivity.this);
                    baCmdDialog.setCancelable(false);
                    baCmdDialog.setMessage("send ba command");
                    baCmdDialog.setButton(DialogInterface.BUTTON_POSITIVE, "cancel",
                            new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                }
                            });
                    baCmdDialog.show();
                }
                case MESSAGE_STATE_CHECK_METER_BT_DISTENCE_FAIL ->
                        LogUtils.debug("MESSAGE_STATE_CHECK_METER_BT_DISTENCE_FAIL");
                case MESSAGE_STATE_NOT_SUPPORT_METER, MESSAGE_STATE_NOT_CONNECT_SERIAL_PORT ->
                        LogUtils.debug("MESSAGE_STATE_NOT_CONNECT_SERIAL_PORT OR MESSAGE_STATE_NOT_SUPPORT_METER");
            }
        }
    };

    @Override
    public void run() {
        LogUtils.debug("Timeout detected.");
        GeneralUtils.stopSpeaking();
        GeneralUtils.stopBeeping();
        timeoutHandler.removeCallbacks(this);
        finishJob();
    }
}
