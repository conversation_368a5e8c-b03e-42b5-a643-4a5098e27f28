package com.watchrx.watchrxhealth.voip;

import android.app.Activity;
import android.media.AudioManager;
import android.media.Ringtone;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.VibrationEffect;
import android.os.Vibrator;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.twilio.voice.Call;
import com.twilio.voice.CallException;
import com.twilio.voice.CallInvite;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;

import java.util.Set;

public class IncomingCallActivity extends Activity {

    private TextView txtCaller, txtTimer, txtQuality;
    private ImageView imgCaller;
    private FloatingActionButton btnAccept, btnReject, btnMute, btnSpeaker;
    private Ringtone ringtone;
    private Vibrator vibrator;
    private boolean isMuted = false;
    private boolean isSpeakerOn = false;

    private CallInvite callInvite;
    private Call activeCall;
    private AudioManager audioManager;
    private CountDownTimer callTimer;
    String from = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_incoming_call);

        imgCaller = findViewById(R.id.imgCaller);
        txtCaller = findViewById(R.id.txtCaller);
        txtTimer = findViewById(R.id.txtTimer);
        txtQuality = findViewById(R.id.txtQuality);
        btnAccept = findViewById(R.id.btnAccept);
        btnReject = findViewById(R.id.btnReject);
        btnMute = findViewById(R.id.btnMute);
        btnSpeaker = findViewById(R.id.btnSpeaker);

        audioManager = (AudioManager) getSystemService(AUDIO_SERVICE);
        vibrator = (Vibrator) getSystemService(VIBRATOR_SERVICE);

        from = getIntent().getStringExtra("from");
        txtCaller.setText("Call from: " + from);

        playRingtone();
        vibrate();

        callInvite = VoiceManager.getInstance(getApplicationContext()).getActiveCallInvite();

        btnAccept.setOnClickListener(v -> acceptCall());
        btnReject.setOnClickListener(v -> rejectCall());
        btnMute.setOnClickListener(v -> toggleMute());
        btnSpeaker.setOnClickListener(v -> toggleSpeaker());
    }

    private void playRingtone() {
        Uri ringtoneUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_RINGTONE);
        ringtone = RingtoneManager.getRingtone(getApplicationContext(), ringtoneUri);
        if (ringtone != null) {
            ringtone.play();
        }
    }

    private void vibrate() {
        if (vibrator != null && vibrator.hasVibrator()) {
            long[] pattern = {0, 1000, 500};
            vibrator.vibrate(VibrationEffect.createWaveform(pattern, 0));
        }
    }

    private void stopRingtoneAndVibration() {
        if (ringtone != null && ringtone.isPlaying()) ringtone.stop();
        if (vibrator != null) vibrator.cancel();
    }

    private void acceptCall() {
        stopRingtoneAndVibration();

        if (callInvite != null) {
            callInvite.accept(getApplicationContext(), new Call.Listener() {
                @Override
                public void onConnected(@NonNull Call call) {
                    activeCall = call;
                    startCallTimer();
                    btnAccept.setEnabled(false);
                    btnReject.setEnabled(true);
                    btnMute.setEnabled(true);
                    btnSpeaker.setEnabled(true);
                }

                @Override
                public void onReconnecting(@NonNull Call call, @NonNull CallException callException) {
                    txtCaller.setText("Reconnecting please wait...");
                }

                @Override
                public void onReconnected(@NonNull Call call) {
                    txtCaller.setText("Call from: " + from);
                }

                @Override
                public void onDisconnected(@NonNull Call call, @Nullable CallException callException) {
                    finish();
                }

                @Override
                public void onCallQualityWarningsChanged(@NonNull Call call, @NonNull Set<Call.CallQualityWarning> currentWarnings, @NonNull Set<Call.CallQualityWarning> previousWarnings) {
                    Call.Listener.super.onCallQualityWarningsChanged(call, currentWarnings, previousWarnings);
                    runOnUiThread(() -> {
                        if (currentWarnings.contains(Call.CallQualityWarning.WARN_HIGH_RTT)) {
                            txtQuality.setText("High latency");
                        } else if (currentWarnings.contains(Call.CallQualityWarning.WARN_HIGH_PACKET_LOSS)) {
                            txtQuality.setText("Packet loss");
                        } else {
                            txtQuality.setText("Good quality");
                        }
                    });
                }

                @Override
                public void onConnectFailure(@NonNull Call call, @NonNull CallException callException) {
                    finish();
                }

                @Override
                public void onRinging(@NonNull Call call) {
                    txtCaller.setText("Ringing...");
                }
            });
        }
        NotificationHelper.clearIncomingCallNotification(getApplicationContext());
        btnAccept.setEnabled(false);
        btnReject.setEnabled(true);
    }

    private void rejectCall() {
        stopRingtoneAndVibration();

        if (activeCall != null) {
            activeCall.disconnect();
            activeCall = null;
        }

        if (callInvite != null) {
            callInvite.reject(getApplicationContext());
            VoiceManager.getInstance(getApplicationContext()).clearActiveCallInvite();
        }

        NotificationHelper.clearIncomingCallNotification(getApplicationContext());
        finish();
    }

    private void toggleMute() {
        if (activeCall != null) {
            isMuted = !isMuted;
            activeCall.mute(isMuted);
            btnMute.setImageResource(isMuted ? R.drawable.mic_off : R.drawable.mic_on);
        }
    }

    private void toggleSpeaker() {
        isSpeakerOn = !isSpeakerOn;
        audioManager.setSpeakerphoneOn(isSpeakerOn);
        btnSpeaker.setImageResource(isSpeakerOn ? R.drawable.speaker_on : R.drawable.speaker_off);
    }

    private void startCallTimer() {
        long startTime = System.currentTimeMillis();
        callTimer = new CountDownTimer(Long.MAX_VALUE, 1000) {
            public void onTick(long millisUntilFinished) {
                long duration = (System.currentTimeMillis() - startTime) / 1000;
                long min = duration / 60;
                long sec = duration % 60;
                txtTimer.setText(String.format("%02d:%02d", min, sec));
            }

            public void onFinish() {
            }
        };
        callTimer.start();
    }

    @Override
    protected void onDestroy() {
        stopRingtoneAndVibration();
        if (callTimer != null) callTimer.cancel();
        super.onDestroy();
    }
}
