package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.healthconnect.HealthConnectManager;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class SendHealthConnectDataReceiver extends BroadcastReceiver {
    
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.e("SendHealthConnectDataReceiver", "SendHealthConnectDataReceiver Triggered " + new Date());
        LogUtils.debug("Health Connect data sender triggered");
        
        try {
            HealthConnectManager healthConnectManager = new HealthConnectManager(context);
            
            if (healthConnectManager.checkHealthConnectAvailability() != HealthConnectManager.HealthConnectAvailability.AVAILABLE) {
                LogUtils.debug("Health Connect is not available, skipping data send");
                return;
            }
            
            for (String vitalType : com.watchrx.watchrxhealth.globals.Globals.healthConnectVitalsList) {
                sendHealthConnectVitalToServer(context, healthConnectManager, vitalType);
            }

            ReminderUtils.setReminderToSendHealthConnectDataToServer(context);
            
        } catch (Exception e) {
            LogUtils.debug("Error in SendHealthConnectDataReceiver: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void sendHealthConnectVitalToServer(Context context, HealthConnectManager healthConnectManager, String vitalType) {
        try {
            LogUtils.debug("Processing Health Connect vital: " + vitalType);

            switch (vitalType) {
                case "heartRate":
                    if (healthConnectManager.hasHeartRatePermissionsSync()) {
                        HealthConnectManager.HeartRateData heartRateData = healthConnectManager.getLatestHeartRateSync();
                        if (heartRateData != null) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(heartRateData.getTime()));
                            LogUtils.debug("Sending Health Connect heart rate to server: " + heartRateData.getBeatsPerMinute() + " bpm");
                            CommUtils.sendHealthConnectHeartRateToServer(context, heartRateData.getBeatsPerMinute(), measurementTime);
                        } else {
                            LogUtils.debug("No heart rate data available to send");
                        }
                    } else {
                        LogUtils.debug("Heart rate permissions not available, skipping heart rate data send");
                    }
                    break;

                case "bloodSugar":
                    if (healthConnectManager.hasBloodGlucosePermissionsSync()) {
                        HealthConnectManager.BloodGlucoseData latestGlucose = healthConnectManager.getLatestBloodGlucoseSync();
                        if (latestGlucose != null) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(latestGlucose.getTime()));
                            LogUtils.debug("Sending Health Connect blood glucose to server: " + latestGlucose.getLevel() + " mmol/L");
                            CommUtils.sendHealthConnectBloodGlucoseToServer(context, latestGlucose.getLevel(), measurementTime);
                        } else {
                            LogUtils.debug("No blood glucose data available to send");
                        }
                    } else {
                        LogUtils.debug("Blood glucose permissions not available, skipping blood glucose data send");
                    }
                    break;

                case "bloodPressure":
                    if (healthConnectManager.hasBloodPressurePermissionsSync()) {
                        HealthConnectManager.BloodPressureData bpData = healthConnectManager.getLatestBloodPressureSync();
                        if (bpData != null) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(bpData.getTime()));
                            LogUtils.debug("Sending Health Connect blood pressure to server: " + bpData.getSystolic() + "/" + bpData.getDiastolic() + " mmHg");
                            CommUtils.sendHealthConnectBloodPressureToServer(context, bpData.getSystolic(), bpData.getDiastolic(), measurementTime);
                        } else {
                            LogUtils.debug("No blood pressure data available to send");
                        }
                    } else {
                        LogUtils.debug("Blood pressure permissions not available, skipping blood pressure data send");
                    }
                    break;

                case "spo2":
                    if (healthConnectManager.hasOxygenSaturationPermissionsSync()) {
                        HealthConnectManager.OxygenSaturationData spo2Data = healthConnectManager.getLatestOxygenSaturationSync();
                        if (spo2Data != null) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(spo2Data.getTime()));
                            LogUtils.debug("Sending Health Connect oxygen saturation to server: " + spo2Data.getPercentage() + "%");
                            CommUtils.sendHealthConnectOxygenSaturationToServer(context, spo2Data.getPercentage(), measurementTime);
                        } else {
                            LogUtils.debug("No oxygen saturation data available to send");
                        }
                    } else {
                        LogUtils.debug("Oxygen saturation permissions not available, skipping oxygen saturation data send");
                    }
                    break;

                case "steps":
                    if (healthConnectManager.hasStepsPermissionsSync()) {
                        long stepsData = healthConnectManager.getTodayStepsSync();
                        if (stepsData >= 0) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(new Date());
                            LogUtils.debug("Sending Health Connect steps to server: " + stepsData + " steps");
                            CommUtils.sendHealthConnectStepsToServer(context, stepsData, measurementTime);
                        } else {
                            LogUtils.debug("No steps data available to send");
                        }
                    } else {
                        LogUtils.debug("Steps permissions not available, skipping steps data send");
                    }
                    break;

                case "sleep":
                    if (healthConnectManager.hasSleepPermissionsSync()) {
                        HealthConnectManager.SleepData sleepData = healthConnectManager.getLatestSleepSync();
                        if (sleepData != null) {
                            String sleepStartTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(sleepData.getStartTime()));
                            String sleepEndTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(sleepData.getEndTime()));
                            LogUtils.debug("Sending Health Connect sleep to server: " + sleepData.getDuration() + " minutes");
                            CommUtils.sendHealthConnectSleepToServer(context, sleepData.getDuration(), sleepStartTime, sleepEndTime);
                        } else {
                            LogUtils.debug("No sleep data available to send");
                        }
                    } else {
                        LogUtils.debug("Sleep permissions not available, skipping sleep data send");
                    }
                    break;

                case "weight":
                    if (healthConnectManager.hasWeightPermissionsSync()) {
                        HealthConnectManager.WeightData weightData = healthConnectManager.getLatestWeightSync();
                        if (weightData != null) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(weightData.getTime()));
                            LogUtils.debug("Sending Health Connect weight to server: " + weightData.getWeight() + " kg");
                            CommUtils.sendHealthConnectWeightToServer(context, weightData.getWeight(), measurementTime);
                        } else {
                            LogUtils.debug("No weight data available to send");
                        }
                    } else {
                        LogUtils.debug("Weight permissions not available, skipping weight data send");
                    }
                    break;

                case "temperature":
                    if (healthConnectManager.hasBodyTemperaturePermissionsSync()) {
                        HealthConnectManager.BodyTemperatureData temperatureData = healthConnectManager.getLatestBodyTemperatureSync();
                        if (temperatureData != null) {
                            String measurementTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
                                    .format(Date.from(temperatureData.getTime()));
                            LogUtils.debug("Sending Health Connect body temperature to server: " + temperatureData.getTemperature() + " °C");
                            CommUtils.sendHealthConnectBodyTemperatureToServer(context, temperatureData.getTemperature(), measurementTime);
                        } else {
                            LogUtils.debug("No body temperature data available to send");
                        }
                    } else {
                        LogUtils.debug("Body temperature permissions not available, skipping body temperature data send");
                    }
                    break;

                default:
                    LogUtils.debug("Unknown Health Connect vital type: " + vitalType);
                    break;
            }

        } catch (Exception e) {
            LogUtils.debug("Error sending Health Connect vital " + vitalType + " to server: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
