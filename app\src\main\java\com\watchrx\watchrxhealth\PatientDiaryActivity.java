package com.watchrx.watchrxhealth;

import android.app.TimePickerDialog;
import android.content.DialogInterface;
import android.os.Bundle;
import android.text.InputType;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.PatientDiaryAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.PatientDiaryListModel;
import com.watchrx.watchrxhealth.models.PatientDiaryModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ProgressDialogUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class PatientDiaryActivity extends AppCompatActivity {

    private FloatingActionButton addNewDiary;
    private RecyclerView recyclerView;
    private final List<String> medications = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_patient_diary);

        recyclerView = findViewById(R.id.allDairys);

        addNewDiary = findViewById(R.id.addNewDiary);
        addNewDiary.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                addNewDiaryFun();
            }
        });
        getPatientDiaryFromServer();
        medications.add("Choose Medication");
        List<MedicationScheduleMaster> scheduleMasters = MedicationScheduleMaster.getFromDB();
        for (MedicationScheduleMaster scheduleMaster : scheduleMasters) {
            medications.add(scheduleMaster.getMedicineName());
        }
    }

    private void addNewDiaryFun() {
        final AlertDialog.Builder builder = new AlertDialog.Builder(this);
        LayoutInflater inflater = this.getLayoutInflater();
        View custom_dialog = inflater.inflate(R.layout.login_dialog, null);


        final Spinner medicationSpn = custom_dialog.findViewById(R.id.medicationList);
        ArrayAdapter<String> timeSlotsValues = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, medications);
        timeSlotsValues.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        medicationSpn.setAdapter(timeSlotsValues);

        final EditText medicationTime = custom_dialog.findViewById(R.id.medicationTime);
        medicationTime.setInputType(InputType.TYPE_NULL);
        medicationTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final Calendar c = Calendar.getInstance();
                int mHour = c.get(Calendar.HOUR_OF_DAY);
                int mMinute = c.get(Calendar.MINUTE);
                TimePickerDialog picker = new TimePickerDialog(PatientDiaryActivity.this, 3,
                        new TimePickerDialog.OnTimeSetListener() {
                            @Override
                            public void onTimeSet(TimePicker tp, int hourOfDay, int minute) {
                                String AM_PM = " AM";
                                String mm_precede = "";
                                String hh_precede = "";
                                if (hourOfDay >= 12) {
                                    AM_PM = " PM";
                                    if (hourOfDay >= 13 && hourOfDay < 24) {
                                        hourOfDay -= 12;
                                    } else {
                                        hourOfDay = 12;
                                    }
                                } else if (hourOfDay == 0) {
                                    hourOfDay = 12;
                                }
                                if (minute < 10) {
                                    mm_precede = "0";
                                }
                                if (hourOfDay < 10) {
                                    hh_precede = "0";
                                }
                                medicationTime.setText(String.format("Medication time- " + hh_precede + hourOfDay + ":" + mm_precede + minute + AM_PM, Locale.ENGLISH));
                            }
                        }, mHour, mMinute, false);
                picker.setTitle("Medication Time");
                picker.show();
            }
        });

        final EditText mealTime = custom_dialog.findViewById(R.id.mealTime);
        mealTime.setInputType(InputType.TYPE_NULL);
        mealTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final Calendar c = Calendar.getInstance();
                int mHour = c.get(Calendar.HOUR_OF_DAY);
                int mMinute = c.get(Calendar.MINUTE);
                TimePickerDialog picker = new TimePickerDialog(PatientDiaryActivity.this, 3,
                        new TimePickerDialog.OnTimeSetListener() {
                            @Override
                            public void onTimeSet(TimePicker tp, int hourOfDay, int minute) {
                                String AM_PM = " AM";
                                String mm_precede = "";
                                String hh_precede = "";
                                if (hourOfDay >= 12) {
                                    AM_PM = " PM";
                                    if (hourOfDay >= 13 && hourOfDay < 24) {
                                        hourOfDay -= 12;
                                    } else {
                                        hourOfDay = 12;
                                    }
                                } else if (hourOfDay == 0) {
                                    hourOfDay = 12;
                                }
                                if (minute < 10) {
                                    mm_precede = "0";
                                }
                                if (hourOfDay < 10) {
                                    hh_precede = "0";
                                }
                                mealTime.setText(String.format("Meal time- " + hh_precede + hourOfDay + ":" + mm_precede + minute + AM_PM, Locale.ENGLISH));
                            }
                        }, mHour, mMinute, false);
                picker.setTitle("Meal Time");
                picker.show();
            }
        });


        builder.setPositiveButton("Add", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });

        builder.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });

        builder.setView(custom_dialog);
        builder.setCancelable(false);
        final AlertDialog dialog = builder.create();
        dialog.show();
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (TextUtils.isEmpty(medicationTime.getText().toString())) {
                    medicationTime.setError("This is required");
                    return;
                }
                if (TextUtils.isEmpty(mealTime.getText().toString())) {
                    mealTime.setError("This is required");
                    return;
                }
                if (medicationSpn.getSelectedItem().toString().equals("Choose Medication")) {
                    ((TextView) medicationSpn.getSelectedView()).setError("Select Medication");
                    return;
                }
                dialog.dismiss();
                sendDiaryToServer(medicationSpn.getSelectedItem().toString(), mealTime.getText().toString(), medicationTime.getText().toString());
            }
        });
    }

    private void sendDiaryToServer(String medicationName, String mealTime, String medicationTime) {
        try {
            URL url = new URL(URLConstants.ADD_DIARY);
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("diaryText", "Medication- " + medicationName + "\n" + medicationTime + "\n" + mealTime);
            jsonObject.accumulate("medication", medicationName);
            jsonObject.accumulate("medicationTime", medicationTime);
            jsonObject.accumulate("mealTime", mealTime);

            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            new RestAsyncTask(url, jsonObject.toString(), null, new DiaryResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class DiaryResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult result) {
            ProgressDialogUtil.dismiss();
            if (result.getResult() == null) {
                LogUtils.debug("Failed to add diary..");
                Log.e("Error :", "");
                Toast.makeText(WatchApp.getContext(), "Failed to add diary..", Toast.LENGTH_LONG).show();
            } else {
                try {
                    JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    Log.e("Result", jsonObject.toString());
                    if (jsonObject.optBoolean("status")) {
                        Toast.makeText(WatchApp.getContext(), "Diary created successfully.", Toast.LENGTH_LONG).show();
                        getPatientDiaryFromServer();
                    } else {
                        Toast.makeText(WatchApp.getContext(), "Failed to add diary.", Toast.LENGTH_LONG).show();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void getPatientDiaryFromServer() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DATE, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            Date monthFirstDay = calendar.getTime();
            SimpleDateFormat startDateDF = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
            SimpleDateFormat currentDateDF = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
            String startDateStr = startDateDF.format(monthFirstDay);
            String currentDate = currentDateDF.format(new Date(System.currentTimeMillis()));
            try {
                LogUtils.debug("Going to Patient Diary");
                URL url = new URL(URLConstants.GET_DIARY);
                String patientId = PatientDetails.getFromDB().getPatientId();
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate("patientId", patientId);
                jsonObject.accumulate("startDate", startDateStr);
                jsonObject.accumulate("endDate", currentDate);
                jsonObject.accumulate("index", 0);
                jsonObject.accumulate("pageSize", 1000);
                LogUtils.debug("Getting Diary Info: " + jsonObject.toString());
                new RestAsyncTask(url, jsonObject.toString(), null, new DiaryDetailsResponseHandler(), null).execute();
            } catch (MalformedURLException | JSONException e) {
                LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class DiaryDetailsResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }
            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        LogUtils.debug("Diary Response : " + result);
                        PatientDiaryListModel responseMessage = new Gson().fromJson((String) result, PatientDiaryListModel.class);
                        if (responseMessage != null && responseMessage.isStatus()
                                && responseMessage.getPatientDiaryResponseVOs() != null
                                && responseMessage.getPatientDiaryResponseVOs().size() > 0) {
                            List<PatientDiaryModel> dataModelList = new ArrayList<>(responseMessage.patientDiaryResponseVOs);
                            recyclerView.setVisibility(View.VISIBLE);
                            recyclerView.setHasFixedSize(true);
                            recyclerView.setLayoutManager(new LinearLayoutManager(PatientDiaryActivity.this));
                            recyclerView.setAdapter(new PatientDiaryAdapter(PatientDiaryActivity.this, dataModelList));
                            recyclerView.invalidate();
                            Objects.requireNonNull(recyclerView.getAdapter()).notifyDataSetChanged();
                        } else {
                            recyclerView.setVisibility(View.GONE);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}