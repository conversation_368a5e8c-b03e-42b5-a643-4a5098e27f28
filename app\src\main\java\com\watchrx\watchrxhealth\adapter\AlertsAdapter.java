package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.AlertsModel;

import java.util.List;

public class AlertsAdapter extends RecyclerView.Adapter<AlertsAdapter.MyViewHolder> {
    Context mContext;
    List<AlertsModel> contactsData;

    public interface OnItemClickListener {
        void onItemClick(AlertsModel item);
    }

    private final AlertsAdapter.OnItemClickListener listener;

    public AlertsAdapter(Context context, List<AlertsModel> contactsData, AlertsAdapter.OnItemClickListener listener) {
        this.mContext = context;
        this.contactsData = contactsData;
        this.listener = listener;
    }

    @NonNull
    @Override
    public AlertsAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.alert_list_item, parent, false);
        return new AlertsAdapter.MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull AlertsAdapter.MyViewHolder holder, int position) {
        AlertsModel alertsModel = contactsData.get(position);
        String alertTypeStr;
        String alertDescStr;
        if (alertsModel.getAlertType().equalsIgnoreCase("Missed call")) {
            alertTypeStr = "Missed call";
            alertDescStr =
                    " You have missed call from " + alertsModel.getAlertMessage().replaceAll("[^0-9]", "") + " " + alertsModel.getAlertDate();
        } else if (alertsModel.getAlertType().equalsIgnoreCase("MissedRegularReminder")) {
            alertTypeStr = "Missed Regular Reminder";
            alertDescStr =
                    " Medication [" + alertsModel.getMedicineName() + "]" + " were missed at " + alertsModel.getAlertMessage() + " " + alertsModel.getAlertDate();
        } else if (alertsModel.getAlertType().equalsIgnoreCase("MissedNurseReminder")) {
            alertTypeStr = "Missed Nurse Reminder";
            alertDescStr =
                    " Medication [" + alertsModel.getMedicineName() + "]" + " were missed at " + alertsModel.getAlertMessage() + " " + alertsModel.getAlertDate();
        } else if (alertsModel.getAlertType().equalsIgnoreCase("Call Duration")) {
            alertTypeStr = "Call Duration ";
            alertDescStr = alertsModel.getAlertMessage() + " " + alertsModel.getAlertDate();
        } else {
            alertTypeStr = "Normal Alert";
            alertDescStr = alertsModel.getAlertMessage() + " " + alertsModel.getAlertDate();
        }
        holder.alertType.setText(alertTypeStr);
        holder.alertDesc.setText(alertDescStr);
        holder.bind(alertsModel, listener);
    }

    @Override
    public int getItemCount() {
        return contactsData.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView alertDesc;
        TextView alertType;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            alertDesc = itemView.findViewById(R.id.alert_desc);
            alertType = itemView.findViewById(R.id.alert_type);
        }

        public void bind(final AlertsModel item, final AlertsAdapter.OnItemClickListener listener) {
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(item);
                }
            });
        }
    }
}
