//package com.watchrx.watch.ble;
//
//import android.annotation.SuppressLint;
//import android.bluetooth.BluetoothAdapter;
//import android.bluetooth.BluetoothDevice;
//import android.bluetooth.BluetoothGatt;
//import android.bluetooth.BluetoothGattCallback;
//import android.bluetooth.BluetoothGattCharacteristic;
//import android.bluetooth.BluetoothGattDescriptor;
//import android.bluetooth.BluetoothManager;
//import android.bluetooth.BluetoothProfile;
//import android.content.Context;
//import android.content.Intent;
//import android.media.MediaPlayer;
//import android.os.Build;
//import android.os.Bundle;
//import android.os.Handler;
//import android.os.Message;
//import android.os.Vibrator;
//import android.util.Log;
//import android.util.SparseArray;
//import android.view.KeyEvent;
//import android.view.LayoutInflater;
//import android.view.View;
//import android.view.Window;
//import android.view.WindowManager;
//import android.widget.Button;
//import android.widget.ImageView;
//import android.widget.TextView;
//import android.widget.ToggleButton;
//
//import androidx.annotation.RequiresApi;
//import androidx.appcompat.app.AlertDialog;
//import androidx.appcompat.app.AppCompatActivity;
//
//import com.watchrx.watch.PhoneCallsActivity;
//import com.watchrx.watch.R;
//import com.watchrx.watch.db.PatientDetails;
//import com.watchrx.watch.globals.Globals;
//import com.watchrx.watch.queue.NotifyNewEntryInQueue;
//import com.watchrx.watch.utils.CommUtils;
//import com.watchrx.watch.utils.GeneralUtils;
//import com.watchrx.watch.utils.LogUtils;
//import com.watchrx.watch.utils.ProgressDialogUtil;
//import com.watchrx.watch.utils.SoundUtils;
//
//import org.json.JSONArray;
//import org.json.JSONObject;
//
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.Calendar;
//import java.util.Date;
//import java.util.List;
//import java.util.Locale;
//import java.util.Objects;
//import java.util.UUID;
//
//@RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN_MR2)
//public class VitalActivity extends AppCompatActivity implements BluetoothAdapter.LeScanCallback, Runnable {
//
//    private BluetoothAdapter mBluetoothAdapter;
//    private SparseArray<BluetoothDevice> mDevices;
//    private BluetoothGatt mConnectedGatt;
//
//    private static final String TAG = "VitalActivity";
//    private static final UUID CUSTOM_SERVICE = UUID.fromString("00001523-1212-efde-1523-785feabcd123");
//    private static final UUID CUSTOM_SERVICE_CHAR = UUID.fromString("00001524-1212-efde-1523-785feabcd123");
//    // private static final UUID CUSTOM_SERVICE = UUID.fromString("0000180f-0000-1000-8000-00805f9b34fb");
//    // private static final UUID CUSTOM_SERVICE_CHAR = UUID.fromString("00002a19-0000-1000-8000-00805f9b34fb");
//
//
//    private static final UUID CONFIG_DESCRIPTOR = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb");
//    private static final int MSG_VITAL_VALUES = 1;
//    private static final int MSG_PROGRESS = 2;
//    private static final int MSG_DISMISS = 3;
//    private static final int MSG_CLEAR = 4;
//    private static final int SEND_DATA_TO_SERVER = 5;
//    private static final byte[] measuredDateCommand = new byte[]{0x51, 0x25, 0x00, 0x00, 0x00, 0x01, (byte) 0xA3, 0x1A};
//    private static final byte[] deviceClock = new byte[]{0x51, 0x23, 0x0, 0x0, 0x0, 0x0, (byte) 0xA3, 0x17};
//    private static final byte[] serialNumber = new byte[]{0x51, 0x28, 0x0, 0x0, 0x0, 0x0, (byte) 0xA3, 0x1C};
//    private static final byte[] measurement = new byte[]{0x51, 0x26, 0x00, 0x00, 0x00, 0x01, (byte) 0xA3, 0x1B};
//    private static final byte[] weight = new byte[]{0x51, 0x71, 0x02, 0x00, 0x00, (byte) 0xA3, 0x67};
//    private int vitalScheduleId = 0;
//    private String scanDeviceName = "FORA P20";
//    private String vitalTypeName = "Blood Pressure";
//    private int mState = 0;
//    private static final List<VitalTypeValueModel> dataNeedToSend = new ArrayList<>();
//    private static final long[] vibratePattern = {0, 500, 200};
//    private static Handler timeoutHandler = new Handler();
//    private static final long REMINDER_TIME_OUT = 90 * 1000;
//    private static AlertDialog mAlertDialog = null;
//    private static TextView tvTip;
//    private Button scan, connect;
//
//    @Override
//    protected void onCreate(Bundle savedInstanceState) {
//        super.onCreate(savedInstanceState);
//
//        ArrayList<String> list = CommUtils.getRoundDialDevices();
//        if (list.contains(CommUtils.getDeviceName())) {
//            String color = PatientDetails.getFromDB().getAppColor();
//            if (color.equalsIgnoreCase("Pink")) {
//                setContentView(R.layout.activity_vital_round);
//            } else {
//                setContentView(R.layout.activity_vital_round_blue);
//            }
//        } else {
//            setContentView(R.layout.activity_vital);
//        }
//
//        Window window = this.getWindow();
//        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
//                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
//                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
//
//        mAlertDialog = new AlertDialog.Builder(VitalActivity.this, R.style.CustomProgressDialog).create();
//        View loadView = LayoutInflater.from(VitalActivity.this).inflate(R.layout.custom_progress_dialog_view, null);
//        mAlertDialog.setView(loadView, 0, 0, 0, 0);
//        mAlertDialog.setCanceledOnTouchOutside(false);
//        tvTip = loadView.findViewById(R.id.tvTip);
//
//        BluetoothManager manager = (BluetoothManager) getSystemService(BLUETOOTH_SERVICE);
//        mBluetoothAdapter = manager.getAdapter();
//        mDevices = new SparseArray<>();
//        dataNeedToSend.clear();
//
//        String vitalScheduleIdStr = getIntent().getStringExtra("vitalScheduleId");
//        if (vitalScheduleIdStr != null && !vitalScheduleIdStr.isEmpty()) {
//            vitalScheduleId = Integer.parseInt(vitalScheduleIdStr);
//            LogUtils.debug("Schedule Vital ID :" + vitalScheduleId);
//        }
//        String deviceNameIntent = getIntent().getStringExtra("deviceName");
//        if (deviceNameIntent != null) {
//            scanDeviceName = deviceNameIntent;
//            // scanDeviceName = "Battery";
//            LogUtils.debug("Scanning Device Name :" + scanDeviceName);
//        }
//
//        String vitalTypeNameInt = getIntent().getStringExtra("vitalTypeName");
//        if (vitalTypeNameInt != null) {
//            vitalTypeName = vitalTypeNameInt;
//            ((TextView) (findViewById(R.id.vital_type))).setText(vitalTypeName);
//            LogUtils.debug("Need to measure Data For :" + vitalTypeName);
//        }
//
//        if (!mBluetoothAdapter.isEnabled()) {
//            LogUtils.debug("Is Bluetooth Enabled :" + mBluetoothAdapter.isEnabled());
//            mBluetoothAdapter.enable();
//            mHandler.postDelayed(mStartRunnable, 2500);
//        }
//        mState = 0;
//        scan = (findViewById(R.id.scan));
//        scan.setVisibility(View.GONE);
//        scan.setOnClickListener(new View.OnClickListener() {
//            @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//            @Override
//            public void onClick(View v) {
//                startScan();
//            }
//        });
//
//        connect = (findViewById(R.id.connect_to_device));
//        connect.setVisibility(View.GONE);
//        connect.setOnClickListener(new View.OnClickListener() {
//            @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//            @Override
//            public void onClick(View v) {
//                connectToDevice();
//            }
//        });
//
//        (findViewById(R.id.disconnect_to_device)).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                CommUtils.sendVitalDataToServer(VitalActivity.this, null, vitalScheduleId, "Failed", null, vitalTypeName);
//                LogUtils.debug("Exit Button Clicked ");
//                finishJob();
//            }
//        });
//        if (timeoutHandler == null) {
//            Handler timeoutHandler = new Handler();
//            timeoutHandler.postDelayed(VitalActivity.this, REMINDER_TIME_OUT);
//            LogUtils.debug("timeoutHandler object died. so using new handler object Inside OnCreate. Going to set timeout Handler.");
//        } else {
//            LogUtils.debug("Inside OnCreate. Going to set timeout Handler.");
//            timeoutHandler.postDelayed(VitalActivity.this, REMINDER_TIME_OUT);
//        }
//
//        GeneralUtils.stopSpeaking();
//        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);
//        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
//            @Override
//            public void onCompletion(MediaPlayer mp) {
//                GeneralUtils.speak("Please turn on medical device to take " + vitalTypeName + " measurement  $");
//            }
//        });
//
//        ToggleButton muteUnmute = findViewById(R.id.toggleButton1);
//        SoundUtils.checkStateOfSound(muteUnmute, VitalActivity.this);
//
//        setupMuteUnmute();
//        setupPhoneCalls();
//    }
//
//    private void finishJob() {
//        LogUtils.debug("Closing the Screen......");
//        Log.e("Total Data Count", "" + dataNeedToSend.size());
//        GeneralUtils.stopSpeaking();
//        GeneralUtils.stopBeeping();
//        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
//        timeoutHandler.removeCallbacks(VitalActivity.this);
//        mHandler.removeCallbacks(mStopRunnable);
//        //  mHandler.removeCallbacks(mStartRunnable);
//        mBluetoothAdapter.stopLeScan(VitalActivity.this);
//        LogUtils.debug("Disconnecting the Bluetooth Gatt : " + mConnectedGatt);
//        if (mConnectedGatt != null) {
//            mConnectedGatt.disconnect();
//            mConnectedGatt.close();
//            LogUtils.debug("Disconnected the Bluetooth Gatt and Closing the  Bluetooth Gatt :" + mConnectedGatt);
//            mConnectedGatt = null;
//        }
////        if (mBluetoothAdapter.isEnabled()) {
////            mBluetoothAdapter.disable();
////            LogUtils.debug("Turning OFF Bluetooth : " + mBluetoothAdapter.isEnabled());
////        }
//        LogUtils.debug("Turned OFF Bluetooth : " + mBluetoothAdapter.isEnabled());
//        if (mAlertDialog != null && mAlertDialog.isShowing()) {
//            mAlertDialog.dismiss();
//        }
//        Globals.isScreenRunning = false;
//        NotifyNewEntryInQueue.notifyNewEntryInQueue();
//        VitalActivity.this.finish();
//        LogUtils.debug("Closing the Screen success");
//    }
//
//    private void setupPhoneCalls() {
//        ImageView settingsButton = findViewById(R.id.imageView_setting);
//
//        if (settingsButton != null) {
//            settingsButton.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View v) {
//                    LogUtils.debug("onClick event detected on Reminder screen for phone calls");
//                    Intent intent = new Intent(VitalActivity.this, PhoneCallsActivity.class);
//                    startActivity(intent);
//                }
//            });
//        }
//    }
//
//    private void setupMuteUnmute() {
//        ToggleButton muteUnmute = findViewById(R.id.toggleButton1);
//        muteUnmute.setOnClickListener(new View.OnClickListener() {
//            @SuppressWarnings("deprecation")
//            @Override
//            public void onClick(View v) {
//
//                boolean on = ((ToggleButton) v).isChecked();
//                if (!on) {
//                    SoundUtils.soundMute(VitalActivity.this);
//                } else {
//                    SoundUtils.soundUnMute(VitalActivity.this);
//                }
//            }
//        });
//    }
//
//    @Override
//    public boolean dispatchKeyEvent(KeyEvent event) {
//        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
//    }
//
//    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//    @Override
//    public void onLeScan(final BluetoothDevice device, int rssi, byte[] scanRecord) {
//
//        runOnUiThread(new Runnable() {
//            @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//            @Override
//            public void run() {
//                if (device.getName() != null && !device.getName().isEmpty()) {
//                    if (device.getName().equalsIgnoreCase(scanDeviceName)) {
//                        mDevices.put(device.hashCode(), device);
//                        LogUtils.debug("Device Scan : Device Name :" + Objects.requireNonNull(device.getName()));
//                    }
//                }
//            }
//        });
//        StringBuilder stringBuilder = new StringBuilder();
//        for (int i = 0; i < mDevices.size(); i++) {
//            BluetoothDevice dd = mDevices.valueAt(i);
//            stringBuilder.append(dd.getName()).append(" ");
//        }
//        if (stringBuilder.toString().isEmpty()) {
//            ((TextView) findViewById(R.id.device_name)).setText("No Device");
//        } else {
//            ((TextView) findViewById(R.id.device_name)).setText(stringBuilder.toString());
////            connect.setVisibility(View.VISIBLE);
////            scan.setVisibility(View.GONE);
////            connectToDevice();
//        }
//    }
//
//    @Override
//    protected void onResume() {
//        super.onResume();
//        LogUtils.debug("onResume Detected....");
//        if (!mBluetoothAdapter.isEnabled()) {
//            LogUtils.debug("Is Bluetooth Enabled :" + mBluetoothAdapter.isEnabled());
//            mBluetoothAdapter.enable();
//            LogUtils.debug("Bluetooth Status After  mBluetoothAdapter.enable() :" + mBluetoothAdapter.isEnabled());
//            if (mBluetoothAdapter.isEnabled()) {
//                LogUtils.debug("Device Scan going to start....");
//                //startScan();
//                mHandler.postDelayed(mStartRunnable, 2500);
//            }
//        } else {
//            mHandler.postDelayed(mStartRunnable, 2500);
//        }
//    }
//
//    private Runnable mStopRunnable = new Runnable() {
//        @Override
//        public void run() {
//            stopScan();
//        }
//    };
//    private Runnable mStartRunnable = new Runnable() {
//        @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//        @Override
//        public void run() {
//            startScan();
//        }
//    };
//
//    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//    private void startScan() {
//        Log.i("Device XXX :", "Device Scan Started......");
//        LogUtils.debug("Device Scan Started......");
//        mBluetoothAdapter.startLeScan(VitalActivity.this);
//        Log.i("Device Count :", mDevices.size() + "");
//        if (mDevices.size() <= 0) {
//            mHandler.postDelayed(mStartRunnable, 2500);
//        } else {
//            mHandler.postDelayed(mStopRunnable, 3500);
//        }
//        if (mDevices.size() > 0) {
//            connectToDevice();
//        }
//    }
//
//    private void stopScan() {
//        mBluetoothAdapter.stopLeScan(this);
//    }
//
//    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
//    private void connectToDevice() {
//        LogUtils.debug("Onclick Event On connectToDevice");
//        LogUtils.debug("Total Device Is Listed :" + mDevices.size());
//        if (mDevices.size() > 0) {
//            BluetoothDevice device = mDevices.valueAt(0);
//            if (device.getBondState() == 10) {
//                device.createBond();
//            }
//
//            LogUtils.debug("Going to connecting the device :" + device.getName() + " Mac IP : " + device.getAddress() + " Bond State :" + device.getBondState());
//            dataNeedToSend.clear();
//            if (mBluetoothAdapter != null) {
//                mBluetoothAdapter.cancelDiscovery();
//            }
//            LogUtils.debug("Bluetooth Gatt : " + mConnectedGatt);
//            if (mConnectedGatt == null) {
//                LogUtils.debug("Initializing Bluetooth Gatt : ");
//                if (Build.VERSION.SDK_INT >= 23) {
//                    mConnectedGatt = device.connectGatt(this, false, mGattCallback, BluetoothDevice.TRANSPORT_LE);
//                } else {
//                    mConnectedGatt = device.connectGatt(this, false, mGattCallback);
//                }
//                LogUtils.debug("Initializing Bluetooth Gatt Completed..." + mConnectedGatt);
//                LogUtils.debug("Waiting for Connection Callback ......");
//                mHandler.sendMessage(Message.obtain(null, MSG_PROGRESS, "Connecting to " + device.getName() + "..."));
//                mBluetoothAdapter.stopLeScan(this);
//            }
//        } else {
//            LogUtils.debug("No Device Found While Connecting");
//            ((TextView) findViewById(R.id.device_name)).setText("No Device Found");
//            // startScan();
//            mHandler.postDelayed(mStartRunnable, 2500);
//        }
//    }
//
//    private BluetoothGattCallback mGattCallback = new BluetoothGattCallback() {
//
//        private void reset() {
//            mState = 0;
//            dataNeedToSend.clear();
//            LogUtils.debug("Re-sets State Machine :" + mState);
//        }
//
//        private void advance() {
//            mState++;
//            LogUtils.debug(" State Machine State:" + mState);
//        }
//
//        /*
//         * Enable notification of changes on the data characteristic for each sensor
//         * by writing the ENABLE_NOTIFICATION_VALUE flag to that characteristic's
//         * configuration descriptor.
//         */
//        private void setNotificationCommand(BluetoothGatt gatt) {
//            BluetoothGattCharacteristic characteristic;
//            switch (mState) {
//                case 0:
////                case 1:
//                case 1:
//                case 2:
//                    characteristic = gatt.getService(CUSTOM_SERVICE)
//                            .getCharacteristic(CUSTOM_SERVICE_CHAR);
//                    break;
//                case 3:
//                    mHandler.sendMessage(Message.obtain(null, SEND_DATA_TO_SERVER, "Sending Data To Server"));
//                    mHandler.sendEmptyMessage(MSG_DISMISS);
//                    return;
//                default:
//                    mHandler.sendEmptyMessage(MSG_DISMISS);
//                    Log.i(TAG, "All Sensors Enabled");
//                    return;
//            }
//            //Enable local notifications
//            LogUtils.debug("Notification : Setting Local Notification client Side : " + true);
//            gatt.setCharacteristicNotification(characteristic, true);
//            //Enabled remote notifications
//
//            BluetoothGattDescriptor desc = characteristic.getDescriptor(CONFIG_DESCRIPTOR);
//            desc.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
//            gatt.writeDescriptor(desc);
//            LogUtils.debug("Write Descriptor : " + desc.getUuid() + " " + Arrays.toString(desc.getValue()));
//            mHandler.sendMessage(Message.obtain(null, MSG_PROGRESS, "Writing Descriptor.."));
//        }
//
//        /*
//         * Write the data characteristic's value for each device explicitly
//         */
//        private void setWriteCommand(BluetoothGatt gatt) {
//            BluetoothGattCharacteristic characteristic;
//            switch (mState) {
//                case 0:
//                    Log.d(TAG, "Writing command to Set Watch Time Device");
//                    characteristic = gatt.getService(CUSTOM_SERVICE)
//                            .getCharacteristic(CUSTOM_SERVICE_CHAR);
//                    characteristic.setValue(getWatchTimeToSet());
//                    break;
////                case 1:
////                    Log.d(TAG, "Writing command to get Data serialNumber");
////                    characteristic = gatt.getService(CUSTOM_SERVICE)
////                            .getCharacteristic(CUSTOM_SERVICE_CHAR);
////                    characteristic.setValue(serialNumber);
////                    break;
//                case 1:
//                    Log.d(TAG, "Writing command to get Data measurement");
//                    characteristic = gatt.getService(CUSTOM_SERVICE)
//                            .getCharacteristic(CUSTOM_SERVICE_CHAR);
//                    if (vitalTypeName.contains("Weight")) {
//                        characteristic.setValue(weight);
//                    } else {
//                        characteristic.setValue(measurement);
//                    }
//                    break;
//                case 2:
//                    Log.d(TAG, "Writing command to get Data deviceClock");
//                    characteristic = gatt.getService(CUSTOM_SERVICE)
//                            .getCharacteristic(CUSTOM_SERVICE_CHAR);
//                    if (vitalTypeName.equalsIgnoreCase("Weight")) {
//                        characteristic.setValue(deviceClock);
//                    } else {
//                        characteristic.setValue(measuredDateCommand);
//                    }
//                    break;
//                default:
//                    mHandler.sendEmptyMessage(MSG_DISMISS);
//                    Log.i(TAG, "All Sensors Enabled");
//                    return;
//            }
//            LogUtils.debug("Writing  Command: " + characteristic.getUuid() + " " + Arrays.toString(characteristic.getValue()));
//            mHandler.sendMessage(Message.obtain(null, MSG_PROGRESS, "Writing command..."));
//            gatt.writeCharacteristic(characteristic);
//        }
//
//        /*
//         * Read the data characteristic's value for each sensor explicitly
//         */
//        private void setReadCommand(BluetoothGatt gatt) {
//            BluetoothGattCharacteristic characteristic;
//            switch (mState) {
//                case 0:
////                case 1:
//                case 1:
//                case 2:
//                    characteristic = gatt.getService(CUSTOM_SERVICE)
//                            .getCharacteristic(CUSTOM_SERVICE_CHAR);
//                    break;
//                default:
//                    mHandler.sendEmptyMessage(MSG_DISMISS);
//                    Log.i(TAG, "All Sensors Enabled");
//                    return;
//            }
//            LogUtils.debug("Reading : " + characteristic.getUuid() + " " + Arrays.toString(characteristic.getValue()));
//            mHandler.sendMessage(Message.obtain(null, MSG_PROGRESS, "Reading command..."));
//            gatt.readCharacteristic(characteristic);
//        }
//
//
//        @Override
//        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
//            Log.d(TAG, "C-Status" + status + " -> " + connectionState(newState));
//            LogUtils.debug("Connection Callback triggered with Status Code : " + status + " " + connectionState(newState));
//            if (status == BluetoothGatt.GATT_SUCCESS && newState == BluetoothProfile.STATE_CONNECTED) {
//                LogUtils.debug("Successfully Connected to Bluetooth Gatt :" + gatt);
//                gatt.discoverServices();
//                mHandler.sendMessage(Message.obtain(null, MSG_PROGRESS, "Discovering Services..."));
//                LogUtils.debug("Connected Successfully");
//            } else if (status == BluetoothGatt.GATT_SUCCESS && newState == BluetoothProfile.STATE_DISCONNECTED) {
//                LogUtils.debug("Successfully Dis-Connected to Bluetooth Gatt :" + gatt);
//                mDevices.clear();
//                mHandler.sendEmptyMessage(MSG_CLEAR);
//                gatt.disconnect();
//                gatt.close();
//                LogUtils.debug("Disconnected Successfully");
//            } else if (status != BluetoothGatt.GATT_SUCCESS) {
//                LogUtils.debug("Successfully Dis-Connected to Bluetooth Gatt : " + gatt);
//                gatt.disconnect();
//                gatt.close();
//                mDevices.clear();
//                mHandler.sendEmptyMessage(MSG_CLEAR);
//                LogUtils.debug("Disconnected Successfully");
//            }
//        }
//
//        @Override
//        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
//            LogUtils.debug("Services Discovered :" + status);
//            mHandler.sendMessage(Message.obtain(null, MSG_PROGRESS, "Setting Up Notifications"));
//            reset();
//            setNotificationCommand(gatt);
//        }
//
//        @Override
//        public void onDescriptorWrite(BluetoothGatt gatt, BluetoothGattDescriptor descriptor, int status) {
//            LogUtils.debug("Write Descriptor completed with Status Code : " + status);
//            setWriteCommand(gatt);
//            // setReadCommand(gatt);
//        }
//
//        @Override
//        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
//            LogUtils.debug("Write Command : " + characteristic.getUuid() + " Value : | " + Arrays.toString(characteristic.getValue()) + " Status Code :" + status);
//            setReadCommand(gatt);
//        }
//
//        @Override
//        public void onCharacteristicRead(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
//            //For each read, pass the data up to the UI thread to update the display
//            Log.d(TAG, "Reading :" + characteristic.getUuid() + " Value : " + Arrays.toString(characteristic.getValue()));
//            LogUtils.debug(" Reading : Characteristic Read : " + characteristic.getUuid() + " Value | " + Arrays.toString(characteristic.getValue()) + " Status Code :" + status);
//            if (CUSTOM_SERVICE_CHAR.equals(characteristic.getUuid())) {
//                mHandler.sendMessage(Message.obtain(null, MSG_VITAL_VALUES, characteristic));
//            }
////            advance();
////            setNotificationCommand(gatt);
//        }
//
//        @Override
//        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
//            Log.d(TAG, "Characteristic changed :" + characteristic.getUuid() + " Value | " + Arrays.toString(characteristic.getValue()));
//            LogUtils.debug("Characteristic changed : " + characteristic.getUuid() + " | value " + Arrays.toString(characteristic.getValue()));
//            mHandler.sendMessage(Message.obtain(null, MSG_VITAL_VALUES, characteristic));
//            try {
//                Thread.sleep(2000);
//            } catch (Exception e) {
//                LogUtils.debug("Exception at Thread sleep for Characteristic changed");
//            }
//            advance();
//            setNotificationCommand(gatt);
//        }
//
//        @Override
//        public void onReadRemoteRssi(BluetoothGatt gatt, int rssi, int status) {
//            Log.d(TAG, "Remote RSSI: " + rssi);
//        }
//
//        private String connectionState(int status) {
//            switch (status) {
//                case BluetoothProfile.STATE_CONNECTED:
//                    return "Connected";
//                case BluetoothProfile.STATE_DISCONNECTED:
//                    return "Disconnected";
//                case BluetoothProfile.STATE_CONNECTING:
//                    return "Connecting";
//                case BluetoothProfile.STATE_DISCONNECTING:
//                    return "Disconnecting";
//                default:
//                    return String.valueOf(status);
//            }
//        }
//    };
//
//    private byte[] getWatchTimeToSet() {
//        Date date = new Date();
//        Log.d("MeterCmdService", "Date : " + date);
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        int year = calendar.get(Calendar.YEAR) - 2000;
//        int month = calendar.get(Calendar.MONTH) + 1;
//        int day = calendar.get(Calendar.DAY_OF_MONTH);
//        int hour = calendar.get(Calendar.HOUR_OF_DAY);
//        int minute = calendar.get(Calendar.MINUTE);
//        int[] tx33Cmd = new int[]{81, 51, ((month & 7) << 5) + day, (year << 1) + (month >> 3), minute, hour, 0, 0};
//        tx33Cmd[tx33Cmd.length - 2] = 163;
//        tx33Cmd[tx33Cmd.length - 1] = calculateOneByteCheckSum(tx33Cmd, 0, tx33Cmd.length - 2);
//
//        byte[] cmd = new byte[tx33Cmd.length];
//        for (int i = 0; i < tx33Cmd.length; i++) {
//            cmd[i] = toByte(tx33Cmd[i]);
//        }
//        Log.e("33Cmd", "" + Arrays.toString(cmd));
//        return cmd;
//    }
//
//    public byte toByte(int number) {
//        return (byte) (number);
//    }
//
//    public static int calculateOneByteCheckSum(int[] cmd, int startIndex, int endIndex) {
//        int checkSum = 0;
//        for (int i = startIndex; i <= endIndex; ++i) {
//            checkSum += cmd[i];
//        }
//        return checkSum & 255;
//    }
//
//    @SuppressLint("HandlerLeak")
//    private Handler mHandler = new Handler() {
//        @SuppressLint("NewApi")
//        @Override
//        public void handleMessage(final Message msg) {
//            BluetoothGattCharacteristic characteristic;
//            switch (msg.what) {
//                case MSG_VITAL_VALUES:
//                    characteristic = (BluetoothGattCharacteristic) msg.obj;
//                    if (characteristic.getValue() == null) {
//                        Log.w(TAG, "Error obtaining data");
//                        return;
//                    }
//                    updateVitalValues(characteristic);
//                    break;
//                case MSG_PROGRESS:
//                    if (mAlertDialog != null && !mAlertDialog.isShowing()) {
//                        tvTip.setText((String) msg.obj);
//                        mAlertDialog.show();
//                    }
//                    break;
//                case MSG_DISMISS:
//                    ProgressDialogUtil.dismiss();
//                    break;
//                case SEND_DATA_TO_SERVER:
//                    senDataToServer();
//                    break;
//                case MSG_CLEAR:
//                    break;
//            }
//        }
//    };
//
//    private void senDataToServer() {
//        LogUtils.debug("While Sending Data To Server : " + dataNeedToSend.size());
//        if (dataNeedToSend.size() > 0) {
//            try {
//                JSONArray jsonArray = new JSONArray();
//                String measuredDate = null;
//                for (VitalTypeValueModel v : dataNeedToSend) {
//                    JSONObject jsonObj = new JSONObject();
//                    String vt = v.getVitalType();
//                    String vv = v.getVitalValue();
//                    jsonObj.put("vitalTypeName", vt);
//                    jsonObj.put("vitalValue", vv);
//                    if (vt.equalsIgnoreCase("measuredDateTime")) {
//                        measuredDate = vv;
//                    } else {
//                        jsonArray.put(jsonObj);
//                    }
//                }
//                CommUtils.sendVitalDataToServer(this, jsonArray, vitalScheduleId, "success", measuredDate, null);
//                GeneralUtils.speak("Great job! We're all done here.");
//            } catch (Exception e) {
//                CommUtils.sendVitalDataToServer(this, null, vitalScheduleId, "Failed", null, vitalTypeName);
//                GeneralUtils.speak("Sorry unable to read measurement ");
//            }
//        } else {
//            CommUtils.sendVitalDataToServer(this, null, vitalScheduleId, "Failed", null, vitalTypeName);
//            GeneralUtils.speak("Sorry unable to read measurement ");
//        }
//        LogUtils.debug("Data Formed and pushed for Sending to Server");
//        finishJob();
//    }
//
//    private void updateVitalValues(BluetoothGattCharacteristic characteristic) {
//        if (characteristic != null) {
//            byte[] data = characteristic.getValue();
//            LogUtils.debug("Data Received  :" + Arrays.toString(data));
//            if (data == null || data.length <= 2) {
//                return;
//            }
//            String serialNumber;
//            String systolicBP;
//            String diastolicBP;
//            String heartRate;
//            String bloodSugar;
//            //String temperature;
//            // String spO2;
//
//            String executeCommand = toHex(data[1]);
//
//            LogUtils.debug("Executed Command is : In Decimal :" + data[1] + " to Hex :" + executeCommand);
//            if (executeCommand.equalsIgnoreCase("33")) {
//                LogUtils.debug("Sets Watch Time To Device and we have got response back : " + Arrays.toString(data));
//                return;
//            }
//            switch (executeCommand) {
//                case "27":
//                case "28": {
//                    serialNumber = "" + toHex(data[5]) + toHex(data[4]) + toHex(data[3]) + toHex(data[2]);
//                    VitalTypeValueModel serialValueModel = new VitalTypeValueModel();
//                    serialValueModel.setVitalType("serialNumber");
//                    serialValueModel.setVitalValue(serialNumber);
//                    // dataNeedToSend.add(serialValueModel);
//                    LogUtils.debug("Serial Number:" + serialNumber);
//                    break;
//                }
//                case "23":
//                    if (!(vitalTypeName.equalsIgnoreCase("Weight"))) {
//                        if (data.length == 8) {
//                            processTime(data);
//                        }
//                    }
//                    break;
//                case "25":
//                    processTime(data);
//                    break;
//                case "26":
//                    if (vitalTypeName.equalsIgnoreCase("Blood Pressure")) {
//                        systolicBP = "" + unsignedByteToInt(data[2]);
//
//                        VitalTypeValueModel systolicBPValueModel = new VitalTypeValueModel();
//                        systolicBPValueModel.setVitalType("Systolic Blood Pressure");
//                        systolicBPValueModel.setVitalValue(systolicBP);
//                        dataNeedToSend.add(systolicBPValueModel);
//                        LogUtils.debug("Systolic Blood Pressure:" + systolicBP);
//
//                        diastolicBP = "" + unsignedByteToInt(data[4]);
//                        VitalTypeValueModel diastolicBPValueModel = new VitalTypeValueModel();
//                        diastolicBPValueModel.setVitalType("Diastolic Blood Pressure");
//                        diastolicBPValueModel.setVitalValue(diastolicBP);
//                        dataNeedToSend.add(diastolicBPValueModel);
//                        LogUtils.debug("Diastolic Blood Pressure:" + diastolicBP);
//
//                        heartRate = "" + unsignedByteToInt(data[5]);
//                        VitalTypeValueModel heartRateValueModel = new VitalTypeValueModel();
//                        heartRateValueModel.setVitalType("Heart Rate");
//                        heartRateValueModel.setVitalValue(heartRate);
//                        dataNeedToSend.add(heartRateValueModel);
//                        LogUtils.debug("Heart Rate:" + heartRate);
//                    } else if (vitalTypeName.equalsIgnoreCase("Blood Sugar")) {
//                        bloodSugar = "" + unsignedByteToInt(data[2]);
//                        VitalTypeValueModel bloodSugarValueModel = new VitalTypeValueModel();
//                        bloodSugarValueModel.setVitalType("Random Blood Sugar");
//                        bloodSugarValueModel.setVitalValue(bloodSugar);
//                        dataNeedToSend.add(bloodSugarValueModel);
//                        LogUtils.debug("Random Blood Sugar:" + bloodSugar);
//                    } else if (vitalTypeName.equalsIgnoreCase("Temperature")) {
//                        double objectTemperatureValue = (double) ((unsignedByteToInt(data[3]) << 8) + unsignedByteToInt(data[2])) * 0.1D;
//                        double F = objectTemperatureValue * (9f / 5) + 32;
//
//                        VitalTypeValueModel bloodSugarValueModel = new VitalTypeValueModel();
//                        bloodSugarValueModel.setVitalType("Temperature");
//                        bloodSugarValueModel.setVitalValue(String.format(Locale.US, "%.2f", F));
//                        dataNeedToSend.add(bloodSugarValueModel);
//                        LogUtils.debug("Temperature:" + String.format(Locale.US, "%.2f", F));
//                    } else if (vitalTypeName.equalsIgnoreCase("Oxygen Saturation")) {
//
//                        int spO2 = (unsignedByteToInt(data[3]) << 8) + unsignedByteToInt(data[2]);
//                        int pulse = unsignedByteToInt(data[5]);
//
//                        VitalTypeValueModel bloodSugarValueModel = new VitalTypeValueModel();
//                        bloodSugarValueModel.setVitalType("Oxygen Saturation");
//                        bloodSugarValueModel.setVitalValue("" + spO2);
//                        dataNeedToSend.add(bloodSugarValueModel);
//                        LogUtils.debug("Oxygen Saturation:" + spO2);
//
//                        VitalTypeValueModel heartRateValueModel = new VitalTypeValueModel();
//                        heartRateValueModel.setVitalType("Heart Rate");
//                        heartRateValueModel.setVitalValue("" + pulse);
//                        dataNeedToSend.add(heartRateValueModel);
//                        LogUtils.debug("Heart Rate:" + pulse);
//                    }
//                    break;
//                case "71": {
//                    int day = data[6];
//                    int month = data[5];
//                    int year = data[4] + 2000;
//                    int minute = data[8];
//                    int hour = data[7];
//
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.set(1, year);
//                    calendar.set(2, month - 1);
//                    calendar.set(5, day);
//                    calendar.set(11, hour);
//                    calendar.set(12, minute);
//                    calendar.set(13, 0);
//                    calendar.set(14, 0);
//
//                    String measuredDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US).format(calendar.getTime());
//                    VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
//                    measureDateValueModel.setVitalType("measuredDateTime");
//                    measureDateValueModel.setVitalValue(measuredDateTime);
//                    dataNeedToSend.add(measureDateValueModel);
//                    LogUtils.debug("Measured Date :" + measuredDateTime);
//                }
//                break;
//                default: {
//                    LogUtils.debug("Default Switch Statement to get Weight Data");
//                    if (data.length > 8 && vitalTypeName.equalsIgnoreCase("Weight")) {
//                        double weightData = (double) ((unsignedByteToInt(data[0]) << 8) + unsignedByteToInt(data[1])) * 0.1D;
//                        double convertValue = weightData * 2.205; // Convert Kg to lbs
//                        VitalTypeValueModel weightVital = new VitalTypeValueModel();
//                        weightVital.setVitalType("Weight");
//                        weightVital.setVitalValue("" + convertValue);
//                        dataNeedToSend.add(weightVital);
//                    }
//                }
//                break;
//            }
//        }
//    }
//
//    public static int unsignedByteToInt(byte b) {
//        return (int) (b) & 0xFF;
//    }
//
//    private void processTime(byte[] data) {
//        String yearMonthDayBit = decimalToBinary(data[3]) + decimalToBinary(data[2]);
//        String minuteHourBit = decimalToBinary(data[5]) + decimalToBinary(data[4]);
//
//        String yearBit = yearMonthDayBit.substring(0, 7);
//        String monthBit = yearMonthDayBit.substring(7, 11);
//        String dayBit = yearMonthDayBit.substring(11, 16);
//
//        String hourBit = minuteHourBit.substring(3, 8);
//        String minBit = minuteHourBit.substring(10, 16);
//
//        String measureDate = 20 + "" + binaryToDecimal(yearBit) + "-" + addZeroToTime(binaryToDecimal(monthBit)) + "-" + addZeroToTime(binaryToDecimal(dayBit))
//                + " " + addZeroToTime(binaryToDecimal(hourBit)) + ":" + addZeroToTime(binaryToDecimal(minBit)) + ":00";
//
//        VitalTypeValueModel measureDateValueModel = new VitalTypeValueModel();
//        measureDateValueModel.setVitalType("measuredDateTime");
//        measureDateValueModel.setVitalValue(measureDate);
//        dataNeedToSend.add(measureDateValueModel);
//        LogUtils.debug("Measured Date :" + measureDate);
//    }
//
//    private String toHex(byte value) {
//        return Integer.toHexString(value);
//    }
//
//    private String decimalToBinary(byte value) {
//        int x = (int) value & 0xFF;
//        StringBuilder bin = new StringBuilder(Integer.toBinaryString(x));
//        while (bin.length() < 8) {
//            bin.insert(0, "0");
//        }
//        return bin.toString();
//    }
//
//    public int binaryToDecimal(String binaryNumber) {
//        return Integer.parseInt(binaryNumber, 2);
//    }
//
//    private String addZeroToTime(int value) {
//        String realValue = String.valueOf(value);
//        return realValue.length() == 1 ? "0" + realValue : realValue;
//    }
//
//    @Override
//    public void run() {
//        int reminderRetryCount = 0;
//        LogUtils.debug("Timeout detected. The Reminder was not acknowledged through a click. reminderRetryCount: " + reminderRetryCount);
//        GeneralUtils.stopSpeaking();
//        GeneralUtils.stopBeeping();
//        timeoutHandler.removeCallbacks(this);
////        CommUtils.sendVitalDataToServer(this, null, vitalScheduleId, "Failed", null, vitalTypeName);
////        finishJob();
//        senDataToServer();
//    }
//}