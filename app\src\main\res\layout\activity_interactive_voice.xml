<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="10dp">

    <include
        android:id="@+id/voiceInterface"
        layout="@layout/voice_component"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_above="@+id/space_interactive_voice_component"
        android:layout_centerHorizontal="true" />

    <Space
        android:id="@+id/space_interactive_voice_component"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentBottom="true" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/transcriptLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="50dp"
            android:text="Transcript:"
            android:textAlignment="textEnd"
            android:textColor="#8D9496"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/transcriptTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Transcript Placeholder"
            android:textAlignment="textEnd"
            android:textColor="#404142" />

        <TextView
            android:id="@+id/responseLabel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="50dp"
            android:text="Response as text:"
            android:textColor="#8D9496"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/responseTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Response Placeholder"
            android:textColor="#404142" />
    </LinearLayout>
</RelativeLayout>
