package com.watchrx.watchrxhealth.constants;

public interface URLConstants {
    String MAIN_URL = "https://watchrxapp.com";

//    String MAIN_URL = "https://15-dot-watchrx-1007.appspot.com";
    String AUTHENTICATE_URL = MAIN_URL + "/api/v2/authenticate";
    String OTP_VALIDATION_CHECK_URL = MAIN_URL + "/api/v2/patientOtpStatus";
    String VALIDATE_OTP = MAIN_URL + "/api/v2/validateOTPAndSetPassword";
    String RESEND_OTP = MAIN_URL + "/api/v2/resentOTP";
    String BASE_URL = MAIN_URL + "/_ah/api/";
    String REGISTER_GCM_URL = BASE_URL + "registration/v2/registerGCM";
    String PATIENT_SECURE_API = BASE_URL + "patientsecureapi/v2/";
    String REGISTER_WATCH_URL = PATIENT_SECURE_API + "registerwatch";
    String MISSED_MEDICATION_URL = PATIENT_SECURE_API + "medicationmissed";
    String SW_UPDATE_REPORT_URL = PATIENT_SECURE_API + "insertWatchUpgradeStatus";
    String GPS_TRACK_LOGS = PATIENT_SECURE_API + "updateGPSTrackingInfo";
    String ALIVE_STATUS_URL = PATIENT_SECURE_API + "watchstatus";
    String UPLOAD_LOG_FILES = MAIN_URL + "/v2/testfileupload";
    String TEXT_MESSAGE_RESPONSE_URL = PATIENT_SECURE_API + "insertTextMessageReply";
    String SCHEDULED_MESSAGE_RESPONSE_URL = PATIENT_SECURE_API + "insertScheduledTextMessageReply";
    String PEDOMETER_URL = PATIENT_SECURE_API + "insertPedometerReading";
    String VITAL = PATIENT_SECURE_API + "insertVitals";
    String TEXT_MESSAGE_COUNT = PATIENT_SECURE_API + "getTextMessageByPatientId";
    String LATEST_VITAL_DATA = PATIENT_SECURE_API + "getLatestVital";
    String VITAL_DETAILS_DATA = PATIENT_SECURE_API + "getVitalByDate";
    String SLEEP_MONITOR = PATIENT_SECURE_API + "sleepMonitor";
    String SLEEP_UPDATE = PATIENT_SECURE_API + "updateSleepData";
    String ADD_MEDS = PATIENT_SECURE_API + "savePatienPrescriptions";
    String ADD_DIARY = PATIENT_SECURE_API + "insertPatientDiary";
    String GET_DIARY = PATIENT_SECURE_API + "patientDiary";
    String MEDICATION_IMAGE_UPLOAD = MAIN_URL + "/v2/saveMedicationImage";
    String VIDEO_CALL_ACCESS_TOKEN = PATIENT_SECURE_API + "getVideocallAccessToken";
    String LATEST_ALERTS = PATIENT_SECURE_API + "getAlertsByPatient";
    String EVENTS = PATIENT_SECURE_API + "getTasksByPatient";
    String VITAL_GRAPH_DATA = PATIENT_SECURE_API + "vitalByPatient";
    String VITAL_PEDO_GRAPH_DATA = PATIENT_SECURE_API + "vitalPedometerByPatient";

    String CHAT_DATA = PATIENT_SECURE_API + "chatDataByPatient";
    String SEND_CHAT_DATA = PATIENT_SECURE_API + "saveChatDataByPatient";
    String ZOOM_VIDEO_CALL_ACCESS_TOKEN = PATIENT_SECURE_API + "zoomAccessToken";

    String VOIP_CALL_ACCESS_TOKEN = PATIENT_SECURE_API + "getVOIPAccessToken";
}
