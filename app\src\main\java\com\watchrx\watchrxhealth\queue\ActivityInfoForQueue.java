package com.watchrx.watchrxhealth.queue;

import android.content.Context;

public class ActivityInfoForQueue {
    public String beforeOrAfterFood;
    public String timeSlot;
    private int timesAlreadyCalled;
    private Long triggerAt;
    private Long nanoSecTime;
    public Context context;
    private String toActivityName;
    private String alertId;
    private String caregiverName;

    public String getBeforeOrAfterFood() {
        return beforeOrAfterFood;
    }

    public void setBeforeOrAfterFood(String beforeOrAfterFood) {
        this.beforeOrAfterFood = beforeOrAfterFood;
    }

    public String getTimeSlot() {
        return timeSlot;
    }

    public void setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
    }

    int getTimesAlreadyCalled() {
        return timesAlreadyCalled;
    }

    public void setTimesAlreadyCalled(int timesAlreadyCalled) {
        this.timesAlreadyCalled = timesAlreadyCalled;
    }

    Long getTriggerAt() {
        return triggerAt;
    }

    public void setTriggerAt(Long triggerAt) {
        this.triggerAt = triggerAt;
    }

    Long getNanoSecTime() {
        return nanoSecTime;
    }

    public void setNanoSecTime(Long nanoSecTime) {
        this.nanoSecTime = nanoSecTime;
    }

    public Context getContext() {
        return context;
    }

    public void setContext(Context context) {
        this.context = context;
    }

    public String getToActivityName() {
        return toActivityName;
    }

    public void setToActivityName(String toActivityName) {
        this.toActivityName = toActivityName;
    }

    public String getAlertId() {
        return alertId;
    }

    public void setAlertId(String alertId) {
        this.alertId = alertId;
    }

    public String getCaregiverName() {
        return caregiverName;
    }

    public void setCaregiverName(String caregiverName) {
        this.caregiverName = caregiverName;
    }
}
