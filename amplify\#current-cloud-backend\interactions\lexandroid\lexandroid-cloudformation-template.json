{"AWSTemplateFormatVersion": "2010-09-09", "Description": "Lex chatbot creation from Amplify CLI", "Parameters": {"authRoleName": {"Type": "String"}, "unauthRoleName": {"Type": "String"}, "authRoleArn": {"Type": "String"}, "env": {"Type": "String"}, "deploymentBucketName": {"Type": "String"}, "s3Key": {"Type": "String"}}, "Mappings": {"RegionMapping": {"us-east-1": {"lexRegion": "us-east-1"}, "us-east-2": {"lexRegion": "us-east-1"}, "sa-east-1": {"lexRegion": "us-east-1"}, "ca-central-1": {"lexRegion": "us-east-1"}, "us-west-1": {"lexRegion": "us-west-2"}, "us-west-2": {"lexRegion": "us-west-2"}, "cn-north-1": {"lexRegion": "us-west-2"}, "cn-northwest-1": {"lexRegion": "us-west-2"}, "ap-south-1": {"lexRegion": "us-west-2"}, "ap-northeast-3": {"lexRegion": "us-west-2"}, "ap-northeast-2": {"lexRegion": "us-west-2"}, "ap-southeast-1": {"lexRegion": "ap-southeast-1"}, "ap-southeast-2": {"lexRegion": "ap-southeast-2"}, "ap-northeast-1": {"lexRegion": "ap-northeast-1"}, "eu-central-1": {"lexRegion": "eu-central-1"}, "eu-west-1": {"lexRegion": "eu-west-1"}, "eu-west-2": {"lexRegion": "eu-west-2"}, "eu-west-3": {"lexRegion": "eu-west-1"}}}, "Metadata": {"AWS::CloudFormation::Interface": {"ParameterGroups": [{"Label": {"default": "Creating lex chatbot"}, "Parameters": ["inputs"]}]}}, "Conditions": {"ShouldNotCreateEnvResources": {"Fn::Equals": [{"Ref": "env"}, "NONE"]}}, "Resources": {"LambdaFunction": {"Type": "AWS::Lambda::Function", "Properties": {"Handler": "index.handler", "FunctionName": {"Fn::If": ["ShouldNotCreateEnvResources", "lexa_cfnlambda_9c02692c", {"Fn::Join": ["", ["lexa_cfnlambda_9c02692c", "-", {"Ref": "env"}]]}]}, "Role": {"Fn::GetAtt": ["LambdaExecutionRole", "<PERSON><PERSON>"]}, "Environment": {"Variables": {"ENV": {"Ref": "env"}}}, "Runtime": "nodejs14.x", "Timeout": 300, "Code": {"S3Bucket": {"Ref": "deploymentBucketName"}, "S3Key": {"Ref": "s3Key"}}}}, "LambdaExecutionRole": {"Type": "AWS::IAM::Role", "Properties": {"RoleName": {"Fn::If": ["ShouldNotCreateEnvResources", "lexLambdaRole9c02692c", {"Fn::Join": ["", ["lexLambdaRole9c02692c", "-", {"Ref": "env"}]]}]}, "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}, "Policies": [{"PolicyName": "cloudWatchPolicy9c02692c", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:*:*:*"}]}}, {"PolicyName": "lexPolicy9c02692c", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["lex:*"], "Resource": "*"}]}}, {"PolicyName": "IAMPolicy", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["iam:*"], "Resource": "*"}]}}, {"PolicyName": "lambdaPolicy9c02692c", "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["lambda:*"], "Resource": "*"}]}}]}}, "LambdaFunctionOutputs9c02692c": {"Type": "Custom::LambdaCallout", "Properties": {"ServiceToken": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}, "region": {"Ref": "AWS::Region"}, "lexRegion": {"Fn::FindInMap": ["RegionMapping", {"Ref": "AWS::Region"}, "lexRegion"]}}}, "CognitoUnauthPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lex_amplify_9c02692c", "Roles": [{"Ref": "unauthRoleName"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": ["lex:<PERSON><PERSON><PERSON><PERSON>", "lex:PostText"], "Effect": "Allow", "Resource": [{"Fn::Join": ["", ["arn:aws:lex:", {"Ref": "AWS::Region"}, ":", {"Fn::Select": ["4", {"Fn::Split": [":", {"Ref": "authRoleArn"}]}]}, ":bot:", {"Fn::If": ["ShouldNotCreateEnvResources", "OrderFlowers", {"Fn::Join": ["", ["OrderFlowers", "_", {"Ref": "env"}]]}]}, ":*"]]}]}]}}}, "CognitoAuthPolicy": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyName": "lex_amplify_9c02692c", "Roles": [{"Ref": "authRoleName"}], "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": ["lex:<PERSON><PERSON><PERSON><PERSON>", "lex:PostText"], "Effect": "Allow", "Resource": [{"Fn::Join": ["", ["arn:aws:lex:", {"Ref": "AWS::Region"}, ":", {"Fn::Select": ["4", {"Fn::Split": [":", {"Ref": "authRoleArn"}]}]}, ":bot:", {"Fn::If": ["ShouldNotCreateEnvResources", "OrderFlowers", {"Fn::Join": ["", ["OrderFlowers", "_", {"Ref": "env"}]]}]}, ":*"]]}]}]}}}}, "Outputs": {"Region": {"Value": {"Fn::FindInMap": ["RegionMapping", {"Ref": "AWS::Region"}, "lexRegion"]}}, "BotName": {"Value": {"Fn::If": ["ShouldNotCreateEnvResources", "OrderFlowers", {"Fn::Join": ["", ["OrderFlowers", "_", {"Ref": "env"}]]}]}}, "FunctionArn": {"Value": {"Fn::GetAtt": ["LambdaFunction", "<PERSON><PERSON>"]}}}}