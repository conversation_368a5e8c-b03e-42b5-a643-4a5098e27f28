<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_reminder"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/App_color"
    android:keepScreenOn="true"
    android:orientation="vertical"
    android:weightSum="3"
    tools:context="com.watchrx.watchrxhealth.NurseOnTheWayActivity">

    <RelativeLayout
        android:id="@+id/imagelayout2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.75"
        android:paddingLeft="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:weightSum="1">

        <TextView
            android:id="@+id/reminderText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:text="Message"
            android:textColor="#6fffff"
            android:textSize="40sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/imagelayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_gravity="center"
        android:layout_weight="1.5"
        android:paddingLeft="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp">

        <TextView
            android:id="@+id/nurse_reminder"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_alignParentTop="true"
            android:layout_alignParentBottom="true"
            android:gravity="center_vertical|center_horizontal"
            android:textColor="@color/white"
            android:textSize="33sp"
            android:textStyle="bold" />
    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.75"
        android:orientation="horizontal"
        android:paddingLeft="3dp"
        android:paddingRight="5dp"
        android:paddingBottom="2dp"
        android:transitionGroup="false"
        android:weightSum="3">

        <ToggleButton
            android:id="@+id/toggleButton1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.87"
            android:background="@drawable/check"
            android:checked="true"
            android:textOff=""
            android:textOn=""
            android:textSize="19sp" />

        <ImageView
            android:id="@+id/warning"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.26"
            android:background="@drawable/warning"
            android:visibility="invisible" />

        <ImageButton
            android:id="@+id/imageView_setting"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_weight="0.84"
            android:background="@drawable/phn" />
    </LinearLayout>

</LinearLayout>