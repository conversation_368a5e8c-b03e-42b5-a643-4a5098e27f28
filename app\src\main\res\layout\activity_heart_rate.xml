<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/hrtlayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:keepScreenOn="true"
    tools:context=".HeartRateActivity">

    <TextView
        android:id="@+id/hrttxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="Heart Rate"
        android:textColor="#46ffe9"
        android:textSize="40sp"
        android:textStyle="bold" />

    <ImageView
        android:id="@+id/heartImage"
        android:layout_width="72dp"
        android:layout_height="52dp"
        android:layout_above="@id/heartRateText"
        android:layout_below="@+id/hrttxt"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="-3dp"
        android:src="@drawable/ic_heart" />

    <TextView
        android:id="@+id/listTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:textColor="#46ffe9" />

    <TextView
        android:id="@+id/heartRateText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/listTxt"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:text="--"
        android:textColor="@color/white"
        android:textSize="40sp" />


    <TextView
        android:id="@+id/bpmText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/heartRateText"
        android:layout_centerHorizontal="true"
        android:layout_marginStart="3dp"
        android:text="bpm"
        android:textColor="@color/white"
        android:textSize="25sp" />

</RelativeLayout>