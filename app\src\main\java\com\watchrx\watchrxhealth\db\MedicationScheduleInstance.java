package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class MedicationScheduleInstance {
    public static final String TABLE_MEDICATION_SCHEDULE_INSTANCE = "MedicationScheduleInstance";
    public static final String COL_MEDSCHED_I_MEDICINE_ID = "MedicineID";
    public static final String COL_MEDSCHED_I_FOOD = "BeforeAfterFood";
    public static final String COL_MEDSCHED_I_TIMESLOT = "TimeSlot";
    private static final String COL_MEDSCHED_I_QUANTITIES = "Quantities";
    public static final String[] COLUMNS_MEDSCHED_INSTANCE =
            {
                    COL_MEDSCHED_I_MEDICINE_ID,
                    COL_MEDSCHED_I_FOOD,
                    COL_MEDSCHED_I_TIMESLOT,
                    COL_MEDSCHED_I_QUANTITIES
            };
    public static final String DELETE_MEDSCHED_INSTANCE_TABLE =
            "DROP TABLE IF EXISTS " + TABLE_MEDICATION_SCHEDULE_INSTANCE + ";";
    public static final String CREATE_MEDSCHED_INSTANCE_TABLE =
            "CREATE TABLE " + TABLE_MEDICATION_SCHEDULE_INSTANCE +
                    "(" +
                    COL_MEDSCHED_I_MEDICINE_ID + " TEXT not null, " +
                    COL_MEDSCHED_I_FOOD + " TEXT not null , " +
                    COL_MEDSCHED_I_QUANTITIES + " TEXT not null ," +
                    COL_MEDSCHED_I_TIMESLOT + " TEXT not null" +
                    ");";
    protected static final String TAG = MedicationScheduleInstance.class.getSimpleName();
    private String medicineID;
    private String beforeAfterFood;
    private String timeSlot;

    public String getQuantities() {
        return quantities;
    }

    public void setQuantities(String quantities) {
        this.quantities = quantities;
    }

    private String quantities;

    public static void deleteRow(String medicineID, String beforeOrAfterFood, String timeSlot) {
        String whereClause = COL_MEDSCHED_I_MEDICINE_ID + " = \"" + medicineID + "\" AND " +
                COL_MEDSCHED_I_FOOD + " = \"" + beforeOrAfterFood + "\" AND " +
                COL_MEDSCHED_I_TIMESLOT + " = \"" + timeSlot + "\"";

        DBAdaptor.getDbAdaptorInstance().delete(TABLE_MEDICATION_SCHEDULE_INSTANCE, whereClause, null);
    }

    public static void deleteAllRows() {
        // Delete any rows already existing
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_MEDICATION_SCHEDULE_INSTANCE);
    }

    public static long addToDB(MedicationScheduleInstance record) {
        ContentValues values = new ContentValues();
        values.put(COL_MEDSCHED_I_MEDICINE_ID, record.medicineID);
        values.put(COL_MEDSCHED_I_FOOD, record.beforeAfterFood);
        values.put(COL_MEDSCHED_I_TIMESLOT, record.timeSlot);
        values.put(COL_MEDSCHED_I_QUANTITIES, record.quantities);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_MEDICATION_SCHEDULE_INSTANCE, values);
    }

    public static int checkInDB(String food, String timeSlots) {
        String whereClause = COL_MEDSCHED_I_FOOD + " LIKE \"%" + food + "%\" AND " +
                COL_MEDSCHED_I_TIMESLOT + " LIKE \"%" + timeSlots + "%\"";

        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_MEDICATION_SCHEDULE_INSTANCE, COLUMNS_MEDSCHED_INSTANCE, whereClause, null, null);

        int count = cursor.getCount();
        cursor.close();

        return count;
    }

    public static List<MedicationScheduleInstance> getFromDB(String food, String timeSlot) {
        String whereClause = COL_MEDSCHED_I_FOOD + " LIKE \"%" + food + "%\" AND " +
                COL_MEDSCHED_I_TIMESLOT + " LIKE \"%" + timeSlot + "%\"";

        List<MedicationScheduleInstance> matchingRecords = new ArrayList<MedicationScheduleInstance>();

        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_MEDICATION_SCHEDULE_INSTANCE, COLUMNS_MEDSCHED_INSTANCE, whereClause, null, null);

        while (cursor.moveToNext()) {
            MedicationScheduleInstance record = new MedicationScheduleInstance();

            try {
                record.setMedicineID(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_MEDICINE_ID)));
                record.setBeforeAfterFood(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_FOOD)));
                record.setTimeSlot(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_TIMESLOT)));
                record.setQuantities(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_QUANTITIES)));
                matchingRecords.add(record);
            } catch (Exception e) {
                // TODO: handle exception
                Log.e(TAG, "" + e);
            }
        }

        cursor.close();

        return matchingRecords;
    }

    public String getMedicineID() {
        return this.medicineID;
    }

    public MedicationScheduleInstance setMedicineID(String medicineID) {
        this.medicineID = medicineID;
        return this;
    }

    public String getBeforeAfterFood() {
        return this.beforeAfterFood;
    }

    public MedicationScheduleInstance setBeforeAfterFood(String beforeAfterFood) {
        this.beforeAfterFood = beforeAfterFood;
        return this;
    }

    public String getTimeSlot() {
        return this.timeSlot;
    }

    public MedicationScheduleInstance setTimeSlot(String timeSlot) {
        this.timeSlot = timeSlot;
        return this;
    }

    public static List<MedicationScheduleInstance> getCurrentDayReminders() {
        List<MedicationScheduleInstance> matchingRecords = new ArrayList<MedicationScheduleInstance>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_MEDICATION_SCHEDULE_INSTANCE, COLUMNS_MEDSCHED_INSTANCE, null, null, null);
        while (cursor.moveToNext()) {
            MedicationScheduleInstance record = new MedicationScheduleInstance();
            try {
                record.setMedicineID(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_MEDICINE_ID)));
                record.setBeforeAfterFood(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_FOOD)));
                record.setTimeSlot(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_TIMESLOT)));
                record.setQuantities(cursor.getString(cursor.getColumnIndexOrThrow(COL_MEDSCHED_I_QUANTITIES)));
                matchingRecords.add(record);
            } catch (Exception e) {
                Log.e(TAG, "" + e);
            }
        }
        cursor.close();
        return matchingRecords;
    }
}
