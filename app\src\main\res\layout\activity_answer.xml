<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_phone_calls"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    tools:context="com.watchrx.watchrxhealth.AnswerActivity">

    <LinearLayout
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_alignParentTop="true"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:weightSum="3">


        <TextView
            android:id="@+id/reminderText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/select_option"
            android:textColor="#6fffff"
            android:textSize="35sp"
            android:textStyle="bold" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/header"
        android:background="@color/App_color"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:paddingStart="5dp"
        android:paddingTop="5dp"
        android:paddingEnd="3dp"
        android:weightSum="1">

        <ListView
            android:id="@+id/answers"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:background="@drawable/list_view_custom"
            android:listSelector="@color/orange" />

    </LinearLayout>


</RelativeLayout>
