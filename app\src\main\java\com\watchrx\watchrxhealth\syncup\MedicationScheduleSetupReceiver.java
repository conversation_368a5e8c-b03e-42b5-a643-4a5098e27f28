package com.watchrx.watchrxhealth.syncup;

import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.InternetCheckConnectivity;
import com.watchrx.watchrxhealth.utils.LogUpdateToServer;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.io.File;
import java.util.Calendar;

import static com.watchrx.watchrxhealth.constants.CommonConstants.Max_Time_Out_For_Ping;

public class MedicationScheduleSetupReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(final Context context, Intent intent) {
        if (intent.getStringExtra("TimeToSync").equalsIgnoreCase("NightTime")) {
            if (Alerts.getFromDB().size() != 0) {
                Alerts.deleteAllRows();
                Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
                LocalBroadcastManager.getInstance(context).sendBroadcast(alertCount);
            }

            String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                    .getAbsolutePath() + "/WatchRx_DebugFile/";
            LogUpdateToServer.deleteLast3DayAgoFile(new File(rootPath));


            if (!CommUtils.isNetworkAvailable(context)) {
                Calendar cal = Calendar.getInstance();
                cal.setTimeInMillis(System.currentTimeMillis());
                cal.get(Calendar.HOUR_OF_DAY);
                if (cal.get(Calendar.HOUR_OF_DAY) <= 4) {
                    setReminder30Min(cal, context, "Internet is not connected");
                } else {
                    LogUtils.debug("Internet is not connected, and sync up is not taking place where i ll set for last day reminders.");
                    ReminderUtils.setupDailyScheduleUpdater(context);
                    ReminderUtils.setupScheduleForToday(context);
                }
            } else {
                @SuppressLint("HandlerLeak")
                Handler h = new Handler() {
                    @Override
                    public void handleMessage(Message msg) {
                        if (msg.what != 1) {
                            LogUtils.debug("Internet is connected , but Ping is not happening properly");
                            Calendar cal = Calendar.getInstance();
                            cal.setTimeInMillis(System.currentTimeMillis());
                            cal.get(Calendar.HOUR_OF_DAY);
                            if (cal.get(Calendar.HOUR_OF_DAY) <= 4) {
                                setReminder30Min(cal, context, "Ping is not happening properly");
                            } else {
                                LogUtils.debug("Internet is connected,but Ping is not happening properly, sync up is not taking place where i ll set for last day reminders.");
                                ReminderUtils.setupDailyScheduleUpdater(context);
                                ReminderUtils.setupScheduleForToday(context);
                            }
                        } else {
                            LogUtils.debug("*Ping success with server ***Going to sync with server*****Night");
                            SyncUpWithServer.registerWatchAtNight(context);
                        }
                    }
                };
                InternetCheckConnectivity.isNetworkAvailable(h, Max_Time_Out_For_Ping);
            }
        } else if (intent.getStringExtra("TimeToSync").equalsIgnoreCase("DayTime")) {

            if (SyncupFromDatabase.registerWatchForSync(ReadDataFromMemory.getDataForSync()).equalsIgnoreCase("operationsuccessful")) {
                /*final ToneGenerator tg = new ToneGenerator(AudioManager.STREAM_NOTIFICATION, 200);
                tg.startTone(ToneGenerator.TONE_PROP_BEEP2);*/
                ReminderUtils.setupDailyScheduleUpdater(context);
                ReminderUtils.setupScheduleForToday(context);
                ReadDataFromMemory.DeleteDirectory();
            } else if (SyncupFromDatabase.registerWatchForSync(ReadDataFromMemory.getDataForSync()).equalsIgnoreCase("Imei is not found in the database")) {
                LogUtils.debug("***** Sync Failed *****Imei is not found in the database");
                MainActivity.registrationFailed();
            } else if (SyncupFromDatabase.registerWatchForSync(ReadDataFromMemory.getDataForSync()).equalsIgnoreCase("Imei is not associated with any patient")) {
                MainActivity.registrationFailed();
                LogUtils.debug("***** Sync Failed *****Imei is not associated with any patient");
            } else if (SyncupFromDatabase.registerWatchForSync(ReadDataFromMemory.getDataForSync()).equalsIgnoreCase("Subscription is not active")) {
                LogUtils.debug("***** Sync Failed *****Subscription is not active");
                CommUtils.sendBroadcastForSubscription();
            } else if (SyncupFromDatabase.registerWatchForSync(ReadDataFromMemory.getDataForSync()).equalsIgnoreCase("BothDataAreSame")) {
                LogUtils.debug("***Sync Successfully *** But No data changed in server side it's similar to Local database");
            } else {
                LogUtils.debug("Sync has been failed may be sever communication was not takes place.");
            }

        } else {
            LogUtils.debug("****Unknown Sync up Alarm");
        }
    }

    public static void setReminder30Min(Calendar cal, Context context, String msg) {
        Intent intentAlarm = new Intent(context, MedicationScheduleSetupReceiver.class);
        intentAlarm.putExtra("TimeToSync", "NightTime");
//        PendingIntent pendingIntent = PendingIntent.getBroadcast(context, 1234, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        PendingIntent pendingIntent = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getBroadcast(
                    context,
                    1234, intentAlarm,
                    PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getBroadcast(context, 1234, intentAlarm, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        // first cancel any previous scheduler
        if (Globals.dailyScheduleMidnightUpdater != null) {
            alarmManager.cancel(Globals.dailyScheduleMidnightUpdater);
        }
        Globals.dailyScheduleMidnightUpdater = pendingIntent; // next save this for future use in clearing it if necessary
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis());
        calendar.add(Calendar.MINUTE, 30);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        else
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, calendar.getTimeInMillis(), pendingIntent);
        LogUtils.debug(msg + cal.get(Calendar.HOUR_OF_DAY) + ":" + cal.get(Calendar.MINUTE) + ":" + cal.get(Calendar.SECOND)
                + "i will try to re-connect it in next 30 min");
    }
}
