package com.watchrx.watchrxhealth.models;

import java.util.List;

public class LatestAlertModel {
    public boolean success;
    public List<AlertNotification> patientAlert;

    public void setStatus(boolean status) {
        this.success = status;
    }

    public void setPatientAlert(List<AlertNotification> patientAlert) {
        this.patientAlert = patientAlert;
    }

    public boolean isStatus() {
        return success;
    }

    public List<AlertNotification> getPatientAlert() {
        return patientAlert;
    }
}
