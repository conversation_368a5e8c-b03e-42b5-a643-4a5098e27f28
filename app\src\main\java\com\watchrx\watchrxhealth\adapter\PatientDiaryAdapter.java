package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.PatientDiaryModel;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

public class PatientDiaryAdapter extends RecyclerView.Adapter<PatientDiaryAdapter.MyViewHolder> {
    Context mContext;
    List<PatientDiaryModel> detailsModelsList;

    public PatientDiaryAdapter(Context context, List<PatientDiaryModel> detailsModelsList) {
        this.mContext = context;
        this.detailsModelsList = detailsModelsList;
    }

    @NonNull
    @Override
    public PatientDiaryAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.vital_details_lit_item, parent, false);
        return new PatientDiaryAdapter.MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull PatientDiaryAdapter.MyViewHolder holder, int position) {
        PatientDiaryModel detailsModel = detailsModelsList.get(position);
        holder.vitalData.setText(detailsModel.getDiaryDescription());
        holder.vitalDate.setText(getDateReadable(detailsModel.getCreatedDate()));
    }

    @Override
    public int getItemCount() {
        return detailsModelsList.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView vitalData;
        TextView vitalDate;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            vitalData = itemView.findViewById(R.id.vitalData);
            vitalDate = itemView.findViewById(R.id.vitalDate);
        }
    }

    private String getDateReadable(String dateStr) {
        try {
            // DateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            //DateFormat targetFormat = new SimpleDateFormat("EEE MM-dd-yyyy hh:mm a", Locale.ENGLISH);
            return uTCToLocal("yyyy-MM-dd HH:mm:ss", "EEE MM-dd-yyyy hh:mm a", dateStr);
        } catch (Exception e) {
            e.printStackTrace();
            return dateStr;
        }
    }

    public static String uTCToLocal(String dateFormatInPut, String dateFomratOutPut, String datesToConvert) {
        String dateToReturn = datesToConvert;
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormatInPut, Locale.ENGLISH);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date gmt = null;
        SimpleDateFormat sdfOutPutToSend = new SimpleDateFormat(dateFomratOutPut, Locale.ENGLISH);
        sdfOutPutToSend.setTimeZone(TimeZone.getDefault());
        try {
            gmt = sdf.parse(datesToConvert);
            dateToReturn = sdfOutPutToSend.format(gmt);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return dateToReturn;
    }
}
