package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Environment;

import com.watchrx.watchrxhealth.utils.LogUtils;

import java.io.File;

import static com.watchrx.watchrxhealth.utils.LogUpdateToServer.checkLogFile;

public class MidnightLogFileUploadReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        LogUtils.debug("Uploading Log File To Server is Started.");
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DebugFile/";
        checkLogFile(new File(rootPath), 1);
        LogUtils.debug("Uploading Log File To Server Completed.");
    }
}
