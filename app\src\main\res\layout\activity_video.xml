<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:keepScreenOn="true"
    tools:context=".twilio.VideoActivity">

    <include layout="@layout/content_video" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:orientation="vertical" >

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/switch_camera_action_fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:fabSize="mini"
            android:layout_marginBottom="@dimen/fab_margin"
            android:src="@drawable/ic_switch_camera_white_24dp"
            android:contentDescription="@string/todo" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/local_video_action_fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="@dimen/fab_margin"
            android:layout_gravity="center"
            app:fabSize="mini"
            android:src="@drawable/ic_videocam_white_24dp"
            android:contentDescription="@string/todo" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/mute_action_fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:fabSize="mini"
            android:layout_gravity="center"
            android:src="@drawable/ic_mic_white_24dp"
            android:contentDescription="@string/todo" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/connect_action_fab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/fab_margin"
            app:fabSize="normal"
            android:src="@drawable/ic_video_call_white_24dp"
            android:contentDescription="@string/todo" />

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
