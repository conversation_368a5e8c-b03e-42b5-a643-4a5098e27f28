package com.watchrx.watchrxhealth.queue;

import static com.watchrx.watchrxhealth.constants.CommonConstants.MAX_TRIGGER_TIME_DIFFERENCE;
import static java.lang.StrictMath.abs;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.BatteryActivity;
import com.watchrx.watchrxhealth.ChatActivity;
import com.watchrx.watchrxhealth.CustomAlertActivity;
import com.watchrx.watchrxhealth.HeartRateActivity;
import com.watchrx.watchrxhealth.NurseOnTheWayActivity;
import com.watchrx.watchrxhealth.PedoMeterActivity;
import com.watchrx.watchrxhealth.ReminderActivity;
import com.watchrx.watchrxhealth.ScheduleTextMessageActivity;
import com.watchrx.watchrxhealth.TextMessageActivity;
import com.watchrx.watchrxhealth.VisitVerificationActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.ZoomVideoCallScreen;
import com.watchrx.watchrxhealth.ble.NewVitalsActivity;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.twilio.VideoActivity;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.List;

public class NotifyNewEntryInQueue {


    public static void notifyNewEntryInQueue() {
        Log.i("Queue:", "Queue started" + Globals.priorityQueue.size() + Globals.isScreenRunning);
        LogUtils.debug("-----Inside Notify Queue -----");
        LogUtils.debug("Queue size = " + Globals.priorityQueue.size());
        LogUtils.debug("Is Any Screen is running " + Globals.isScreenRunning);

        if (!Globals.isScreenRunning) {
            while (!Globals.priorityQueue.isEmpty()) {
                ActivityInfoForQueue topActivity = Globals.priorityQueue.poll();
                if (topActivity != null) {
                    Globals.isScreenRunning = true;
                    LogUtils.debug("Screen need to process " + topActivity.getToActivityName());
                    String toActivityName = topActivity.getToActivityName();
                    Context context = topActivity.getContext();
                    if (toActivityName.equalsIgnoreCase("ReminderActivity")) {
                        long triggerTimeMillis = topActivity.getTriggerAt();
                        long currentTimeMillis = System.currentTimeMillis();
                        LogUtils.debug("While checking the Medication Elapse time is under 2 hours : diff in minutes Minutes: " + ((currentTimeMillis - triggerTimeMillis) / 60000));
                        if (abs(currentTimeMillis - triggerTimeMillis) > MAX_TRIGGER_TIME_DIFFERENCE) {
                            LogUtils.debug("This alarm is  outside the time window Suppose to trigger at : " + triggerTimeMillis + " beforeOrAfterFood " +
                                    topActivity.getBeforeOrAfterFood() + " timeSlot :" + topActivity.getTimeSlot());
                            List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(topActivity.getBeforeOrAfterFood(), topActivity.getTimeSlot());
                            CommUtils.sendReminderDisplayedLogToServer(WatchApp.getContext(), "MissedRegularReminder", null, topActivity.getBeforeOrAfterFood(), topActivity.getTimeSlot(), scheduleInstanceList);
                            continue; // too much difference; no longer valid alarm
                        }
                        if (topActivity.getAlertId() == null) {
                            Intent reminderIntent = new Intent(context, ReminderActivity.class);
                            reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            reminderIntent.putExtra("beforeOrAfterFood", topActivity.getBeforeOrAfterFood());
                            reminderIntent.putExtra("timeSlot", topActivity.getTimeSlot());
                            reminderIntent.putExtra("timesAlreadyCalled", topActivity.getTimesAlreadyCalled());
                            reminderIntent.putExtra("triggerAt", topActivity.getTriggerAt());
                            context.startActivity(reminderIntent);
                        } else {
                            Intent reminderIntent = new Intent(context, ReminderActivity.class);
                            reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            reminderIntent.putExtra("alertId", topActivity.getAlertId());
                            reminderIntent.putExtra("beforeOrAfterFood", topActivity.getBeforeOrAfterFood());
                            reminderIntent.putExtra("timeSlot", topActivity.getTimeSlot());
                            reminderIntent.putExtra("nurseName", topActivity.getCaregiverName());
                            context.startActivity(reminderIntent);
                        }

                    } else if (toActivityName.equalsIgnoreCase("TextMessageActivity")) {
                        Intent nurseOnTheWayIntent = new Intent(context, TextMessageActivity.class);
                        nurseOnTheWayIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        nurseOnTheWayIntent.putExtra("data", topActivity.getTimeSlot());
                        context.startActivity(nurseOnTheWayIntent);
                    } else if (toActivityName.equalsIgnoreCase("BatteryActivity")) {
                        Intent intent = new Intent(context, BatteryActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                        intent.putExtra("status", topActivity.getTimeSlot());
                        context.startActivity(intent);
                    } else if (toActivityName.equalsIgnoreCase("CustomAlertActivity")) {
                        Intent reminderIntent = new Intent(context, CustomAlertActivity.class);
                        reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        reminderIntent.putExtra("alertType", topActivity.getTimeSlot());
                        reminderIntent.putExtra("alertDetail", topActivity.getBeforeOrAfterFood());
                        context.startActivity(reminderIntent);
                    } else if (toActivityName.equalsIgnoreCase("HeartRateActivity")) {
                        Intent reminderIntent = new Intent(context, HeartRateActivity.class);
                        reminderIntent.putExtra("vitalScheduleId", topActivity.getAlertId());
                        reminderIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.startActivity(reminderIntent);
                    } else if (toActivityName.equalsIgnoreCase("NurseOnTheWayActivity")) {
                        Intent nurseOnTheWayIntent = new Intent(context, NurseOnTheWayActivity.class);
                        nurseOnTheWayIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        nurseOnTheWayIntent.putExtra("nurseName", topActivity.getCaregiverName());
                        nurseOnTheWayIntent.putExtra("status", topActivity.getTimeSlot());
                        context.startActivity(nurseOnTheWayIntent);

                    } else if (toActivityName.equalsIgnoreCase("VisitVerificationActivity")) {
                        Intent visitVerificationIntent = new Intent(context, VisitVerificationActivity.class);
                        visitVerificationIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        visitVerificationIntent.putExtra("nurseName", topActivity.getCaregiverName());
                        visitVerificationIntent.putExtra("visitVerificationCode", topActivity.getTimeSlot());
                        context.startActivity(visitVerificationIntent);

                    } else if (toActivityName.equalsIgnoreCase("ScheduleTextMessageActivity")) {
                        Intent scheduleMessage = new Intent(context, ScheduleTextMessageActivity.class);
                        scheduleMessage.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        scheduleMessage.putExtra("time", topActivity.getTimeSlot());
                        scheduleMessage.putExtra("questionId", topActivity.getAlertId());
                        scheduleMessage.putExtra("day", topActivity.getBeforeOrAfterFood());
                        context.startActivity(scheduleMessage);

                    } else if (toActivityName.equalsIgnoreCase("NewVitalsActivity")) {
                        Intent reminderIntent = new Intent(context, NewVitalsActivity.class);
                        reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        reminderIntent.putExtra("vitalScheduleId", topActivity.getAlertId());
                        reminderIntent.putExtra("deviceName", topActivity.getBeforeOrAfterFood());
                        reminderIntent.putExtra("vitalTypeName", topActivity.getCaregiverName());
                        context.startActivity(reminderIntent);
                    } else if (toActivityName.equalsIgnoreCase("PedoMeterActivity")) {
                        Intent reminderIntent = new Intent(context, PedoMeterActivity.class);
                        reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        reminderIntent.putExtra("stepsCount", Integer.parseInt(topActivity.getAlertId()));
                        reminderIntent.putExtra("status", topActivity.getCaregiverName());
                        context.startActivity(reminderIntent);
                    } else if (toActivityName.equalsIgnoreCase("VideoActivity")) {
                        Intent videoCallIntent = new Intent(context, VideoActivity.class);
                        videoCallIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        videoCallIntent.putExtra("roomId", topActivity.getTriggerAt());
                        context.startActivity(videoCallIntent);
                    } else if (toActivityName.equalsIgnoreCase("ChatActivity")) {
                        Intent intent = new Intent(context, ChatActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                        context.startActivity(intent);
                    } else if (toActivityName.equalsIgnoreCase("ZoomVideoCallScreen")) {
                        Intent videoCallIntent = new Intent(context, ZoomVideoCallScreen.class);
                        videoCallIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        videoCallIntent.putExtra("roomId", topActivity.getTriggerAt());
                        videoCallIntent.putExtra("program", topActivity.getBeforeOrAfterFood());
                        context.startActivity(videoCallIntent);
                    }
                }
            }
        }
    }
}