package com.watchrx.watchrxhealth.utils;

import android.os.Environment;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.CommonConstants;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class LogUpdateToServer {
    private static final String tempFileName = "updateLogPostRestart.txt";
    private String serverURL;

    public static void logUpdateController() {
        LogUtils.debug("checking for log update");
        String logFileName = LogUpdateToServer.checkForLogUpdate();
        if (logFileName != null) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.accumulate("updatelog", logFileName);
                jsonObject.accumulate("logtype", "CR");
                CommUtils.pushToServer(WatchApp.getContext(), jsonObject.toString(), URLConstants.UPLOAD_LOG_FILES);
            } catch (JSONException e) {
                CommUtils.printTraceToLogFile(e);
            }
        }
    }

    public static String getLogFileNameFromJson(String json) {
        try {
            JSONObject getLogFileNameFromJson_json = new JSONObject(json);
            if (getLogFileNameFromJson_json.has("updatelog")) {
                return getLogFileNameFromJson_json.getString("updatelog");
            }
        } catch (JSONException e) {
            CommUtils.printTraceToLogFile(e);
        }

        return null;
    }

    public static String getLogTypeFromJson(String json) {
        try {
            JSONObject getLogFileNameFromJson_json = new JSONObject(json);
            if (getLogFileNameFromJson_json.has("logtype")) {
                return getLogFileNameFromJson_json.getString("logtype");
            }
        } catch (JSONException e) {
            CommUtils.printTraceToLogFile(e);
        }

        return null;
    }


    private static String generateLogFileName() {
        return Globals.imei + "_" + CommUtils.getTodayDate() + "_" + CommUtils.getCurrentTime() + ".log";
    }

    private static String checkForLogUpdate() {
        String dirName = getLogUpdateDitrectory();
        File checkForLogUpdate_xx = new File(dirName + tempFileName);

        try {
            if (dirName != null && checkForLogUpdate_xx.exists()) {
                BufferedReader fr = new BufferedReader(new FileReader(checkForLogUpdate_xx));
                String logFileDate = fr.readLine();
                logFileDate = logFileDate.replace("\n", "");
                fr.close();
                File dir = new File(getLogFilesDitrectory() + " " + logFileDate + ".txt");
                if (dir.exists()) {
                    return dir.toString();
                }
            }
        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
        }
        return null;
    }

    static void setLogUpdateRequired() {

        String dirName = getLogUpdateDitrectory();
        try {
            if (dirName != null) {
                File xx = new File(dirName + tempFileName);
                if (!xx.exists()) {
                    xx.createNewFile();
                    xx.canWrite();
                }

                FileWriter fw = new FileWriter(xx);
                fw.write(CommUtils.getTodayDate());
                fw.flush();
                fw.close();
            }
        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
        }
    }


    public void updateLog() {
    }

    private static String getLogUpdateDitrectory() {
        String tempDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).toString() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY;
        if (SoftwareUpdateUtil.createDir(tempDir)) {
            return tempDir;
        }

        return null;
    }

    private static String getLogFilesDitrectory() {
        return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).toString() + CommonConstants.LOG_DIRECTORY;
    }

    public static void deleteCrashFile() {
        String dirName = getLogUpdateDitrectory();
        try {
            if (dirName != null) {
                File xx = new File(dirName + tempFileName);
                if (xx.exists()) {
                    xx.delete();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void checkLogFile(File dir, int numberOfFiles) {
        if (dir.exists()) {
            File[] files = dir.listFiles();
            String todaDate = todayDate();

            for (File file : files) {
                String myfile = file.getName().trim();
                if (todaDate.equalsIgnoreCase(myfile)) {
                    JSONObject jsonObject = new JSONObject();
                    try {
                        jsonObject.accumulate("updatelog", file);
                        jsonObject.accumulate("logtype", "OD");
                        CommUtils.pushToServer(WatchApp.getContext(), jsonObject.toString(), URLConstants.UPLOAD_LOG_FILES);
                    } catch (JSONException e) {
                        CommUtils.printTraceToLogFile(e);
                    }
                }
            }
        }
    }

    public static String[] getTodayDate(int size) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        Calendar cal = Calendar.getInstance();
        Date date = cal.getTime();
        String[] days = new String[size];
        days[0] = " " + sdf.format(date) + ".txt";

        for (int i = 1; i < size; i++) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
            date = cal.getTime();
            days[i] = " " + sdf.format(date) + ".txt";
        }
        return days;
    }

    public static String todayDate() {
        Calendar c = Calendar.getInstance();
        System.out.println("Current time => " + c.getTime());

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
        return df.format(c.getTime()) + ".txt";
    }

    public static boolean deleteLast3DayAgoFile(File path) {

        if (path.exists()) {
            File[] files = path.listFiles();
            String[] dates = getTodayDate(3);
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteLast3DayAgoFile(file);
                } else {
                    if (Arrays.asList(dates).contains(file.getName())) {
                        LogUtils.debug("don't delete , now keep this file " + file.getName() + "for up to 3 days ");
                    } else {
                        LogUtils.debug("Deleted file " + file.getName() + "because file validity expired.");
                        file.delete();
                    }
                }
            }
        }
        return true;
    }
}
