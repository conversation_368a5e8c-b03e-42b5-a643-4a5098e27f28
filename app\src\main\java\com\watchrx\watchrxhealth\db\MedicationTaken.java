package com.watchrx.watchrxhealth.db;


import android.content.ContentValues;

public class MedicationTaken {


    private static final String TABLE_MEDICATION_TAKEN = "MedicationTaken";
    private static final String COL_JSONOBJECT= "JsonObject";
    private static final String COL_API = "Api";

    static final String DELETE_TABLE_MEDICATION_TAKEN =
            "DROP TABLE IF EXISTS " + TABLE_MEDICATION_TAKEN + ";";

    static final String CREATE_TABLE_MEDICATION_TAKEN =
            "CREATE TABLE " + TABLE_MEDICATION_TAKEN +
                    "(" +
                    COL_JSONOBJECT + " TEXT, " +
                    COL_API + " TEXT" +
                    ");";

    private static final String[] COLUMNS_PATIENT_DETAILS = {
            COL_JSONOBJECT,
            COL_API

    };
    private static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_MEDICATION_TAKEN);
    }

    public static long addToDB(String json,String url)
    {

        ContentValues values = new ContentValues();

        values.put(COL_JSONOBJECT, json);
        values.put(COL_API, url);


        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_MEDICATION_TAKEN, values);
    }
}
