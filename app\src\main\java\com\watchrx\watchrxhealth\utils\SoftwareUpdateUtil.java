package com.watchrx.watchrxhealth.utils;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;

import androidx.core.content.FileProvider;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.CommonConstants;

import org.apache.commons.io.FileUtils;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;

/**
 * <NAME_EMAIL> on 2/6/2017.
 */

public class SoftwareUpdateUtil {
    private static SWStatusKeeper statusKeeper;

    private static boolean initialize(Context context, JSONObject message) {
        try {
            if (statusKeeper == null) {
                statusKeeper = new SWStatusKeeper();
                if (message == null) {
                    //we are coming from restart here
                    File statusFile = new File(SWStatusKeeper.getStatusFileName());
                    BufferedReader statusFileReader = new BufferedReader(new FileReader(statusFile));
                    String line = statusFileReader.readLine();
                    message = new JSONObject(line);
                    statusFileReader.close();
                }
                statusKeeper.SWStatusKeeper(message.getString("url"));
                return true;
            } else {

                try {
                    if (message == null) {
                        //we are coming from restart here
                        File statusFile = new File(SWStatusKeeper.getStatusFileName());
                        BufferedReader statusFileReader = new BufferedReader(new FileReader(statusFile));
                        String line = statusFileReader.readLine();
                        message = new JSONObject(line);
                        statusFileReader.close();
                    }
                    statusKeeper.SWStatusKeeper(message.getString("url"));
                    return true;
                } catch (Exception e) {
                    cleanup("URL_currupted");
                    CommUtils.sendSWUpdateReportToServer(context, "failed", "URL_currupted");
                    CommUtils.printTraceToLogFile(e);
                }

                LogUtils.debug("statuskeeper object is present already, current download is in progress");
            }
        } catch (JSONException e) {
            CommUtils.sendSWUpdateReportToServer(context, "failed", "URL_currupted");
            cleanup("URL_currupted");
            CommUtils.printTraceToLogFile(e);
        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
        }

        return false;
    }

    public static String getSWUpgradeStatus() {
        String status = null;
        File statusFile = new File(SWStatusKeeper.getStatusFileName());
        try {
            if (statusFile.exists()) {
                BufferedReader statusFileReader = new BufferedReader(new FileReader(statusFile));
                String line = statusFileReader.readLine();
                JSONObject status2JSON = new JSONObject(line);
                status = status2JSON.getString("status");
            }
        } catch (JSONException | IOException e) {
            CommUtils.printTraceToLogFile(e);
        }

        return status;
    }

    public static String getSWUpgradeStatusAPKVersion() {
        String status = null;
        File statusFile = new File(SWStatusKeeper.getStatusFileName());
        try {
            if (statusFile.exists()) {
                BufferedReader statusFileReader = new BufferedReader(new FileReader(statusFile));
                String line = statusFileReader.readLine();
                JSONObject status2JSON = new JSONObject(line);
                status = status2JSON.getString("apk_version");
                return status;
            }
        } catch (JSONException | IOException e) {
            CommUtils.printTraceToLogFile(e);
        }
        return null;
    }

    public static void SWUpgradeStatus(Context context) {
        File statusFile = new File(SWStatusKeeper.getStatusFileName());
        if (statusFile.exists()) {
            String status = getSWUpgradeStatus();
            if (status.equalsIgnoreCase(downloadingStatus.RELEASE_INSTALLED.toString())) {
                String versionRelease = Build.VERSION.RELEASE;
                CommUtils.sendSWUpdateReportToServer(context, "success", "upgrade_successful");
                copyFileFromSrcToDst();
                statusFile.delete();

            } else if (status.equalsIgnoreCase(downloadingStatus.UPGRADEINTRANSIT.toString())) {
                String appCurrentVersion = SoftwareUpdateUtil.getAppVersion(context);
                String oldVersionAPk = SoftwareUpdateUtil.getSWUpgradeStatusAPKVersion();
                if (appCurrentVersion != null && oldVersionAPk != null && appCurrentVersion.equalsIgnoreCase(oldVersionAPk)) {
                    LogUtils.debug("Before sending status to server : Old version app :" + oldVersionAPk + " Current version App :" + appCurrentVersion);
                    CommUtils.sendSWUpdateReportToServer(context, "failed", "upgrade_failed and restored in to old apk");
                    statusFile.delete();
                } else {
                    CommUtils.sendSWUpdateReportToServer(context, "success", "upgrade_successful");
                    copyFileFromSrcToDst();
                    statusFile.delete();
                }
            } else if (status.equalsIgnoreCase(downloadingStatus.ROLLINGBACK.toString())) {
                CommUtils.sendSWUpdateReportToServer(context, "failed", "upgrade_failed and restored in to old apk");
                statusFile.delete();

            } else if (status.equalsIgnoreCase(downloadingStatus.DONWLOADING_STARTED.toString())) {
                SoftwareUpdateUtil.initialize(context, null);
            } else {
                statusFile.delete();
            }
        }
    }

    public static boolean isSoftwareUpgradeInProgress() {
        String status = SoftwareUpdateUtil.getSWUpgradeStatus();
        return status != null;
    }

    public static void copyFileFromSrcToDst() {

        File sdcard = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        String apkFile = sdcard.toString() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY;

        File[] files = new File(apkFile).listFiles();

        File reqFile = null;
        String oldApkName = "";
        for (File file1 : files) {
            oldApkName = file1.getName().trim();
            if (getFileExt(oldApkName).equalsIgnoreCase("apk")) {
                reqFile = file1;
            }
        }
        if (reqFile != null) {
            File destination = new File(sdcard.toString() + CommonConstants.BACK_UP_FILE_DIRECTORY);
            if (destination.exists()) {

                File[] currentFiles = destination.listFiles();
                for (File f : currentFiles) {
                    f.delete();
                }
                try {
                    FileUtils.copyFile(reqFile, new File(destination + "/" + oldApkName));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    destination.mkdirs();
                    FileUtils.copyFile(reqFile, new File(destination + "/" + oldApkName));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    public static void downLoadManager(Context context, JSONObject message) {

        if (!initialize(context, message)) {
            return;
        }

        if (CommUtils.isNetworkAvailable(context)) {
            statusKeeper.setStatusDownloadStarted();
            boolean status = SoftwareUpdateUtil.apkDownLoad();

            if (status) {
                statusKeeper.setStatusDownloadCompleted();
                CommUtils.sendSWUpdateReportToServer(context, "success", "download_successful");
                restartApkOnNewRel();

            }
        } else {
            LogUtils.debug("no internet connection.... to download new release, will try upon getting network connection");
        }

    }

    private static boolean performApkVerification() {
        LogUtils.info("starting apk validity performance check");
        return true;
    }

    private static void cleanup(String status) {
        LogUtils.debug("software update is completed with status code : " + status);
        statusKeeper.cleanup();
        statusKeeper = null;
    }

    private static void restartApkOnNewRel() {
        try {
            LogUtils.debug("installing the applications ");
            statusKeeper.setUpgradeInTransit();
            File toInstall = new File(statusKeeper.getApkFile());
            Intent intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
            intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            Uri uriFile;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                uriFile = FileProvider.getUriForFile(WatchApp.getContext(), WatchApp.getContext().getApplicationContext().getPackageName() + ".fileprovider", toInstall);
            } else {
                uriFile = Uri.fromFile(toInstall);
            }
            intent.setDataAndType(uriFile, "application/vnd.android.package-archive");
            WatchApp.getContext().startActivity(Intent.createChooser(intent, "Open_Apk"));
            LogUtils.debug("Assumed Installed Button Clicked , Installed new version, So keeping Installed state.");
            SWStatusKeeper statusKeeper = new SWStatusKeeper();
            statusKeeper.setStatusReleaseInstalled();
        } catch (Exception e) {
            e.printStackTrace();
            CommUtils.printTraceToLogFile(e);
        }
    }


    public static String getAppVersion(Context context) {
        PackageManager manager = context.getPackageManager();
        PackageInfo info = null;
        try {
            info = manager.getPackageInfo(
                    context.getPackageName(), 0);
            return info.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        return null;
    }

    public static boolean createDir(String fileName) {
        File theDir = new File(fileName);
        boolean result = false;

        if (!theDir.exists()) {

            try {
                theDir.mkdir();
                result = true;
            } catch (SecurityException se) {
                LogUtils.debug(se.getMessage());
            }
        } else {
            result = true;
        }
        return result;
    }

    public static String getFileExt(String fileName) {
        return fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
    }

    private static boolean apkDownLoad() {
        File sdcard = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);

        if (!createDir(sdcard.toString() + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY)) {
            return false;
        }

        File fileName = new File(sdcard + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY + statusKeeper.getFileNameFromUrl()); //The file that will be saved on your computer

        File[] xx = new File(sdcard + CommonConstants.RELEASE_DOWNLOAD_DIRECTORY).listFiles();
        for (File file : xx) {
            String myfile = file.getName().trim();
            if (getFileExt(myfile).equalsIgnoreCase("apk")) {
                file.delete();
            }
        }

        try {
            if (!fileName.exists()) {
                fileName.createNewFile();
            } else {
                fileName.delete();
                fileName.createNewFile();
            }

            LogUtils.debug("starting downloading of release from: " + statusKeeper.getSource());
            URL link = new URL(statusKeeper.getSource().trim()); //The file that you want to download
            URLConnection urlConnection = link.openConnection();

            InputStream is = urlConnection.getInputStream();
            BufferedInputStream bis = new BufferedInputStream(is);

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            byte[] buf = new byte[1024];
            int n = 0;
            while (-1 != (n = bis.read(buf))) {
                out.write(buf, 0, n);
            }
            out.close();
            bis.close();
            byte[] response = out.toByteArray();

            FileOutputStream fos = new FileOutputStream(fileName);
            fos.write(response);
            fos.close();
            LogUtils.debug("file download completed for file :" + fileName);
            //Toast.makeText(WatchApp.getContext(), "new apk" + statusKeeper.getFileNameFromUrl() + " is downloaded successfuly", Toast.LENGTH_LONG).show();
            if (!performApkVerification()) {
                //delete file as thei .apk is not a valid file
                if (fileName.exists()) {
                    fileName.delete();
                }
                return false;
            }
        } catch (MalformedURLException e) {
            CommUtils.printTraceToLogFile(e);
            cleanup("URL_was_not_correct");
            return false;
        } catch (IOException e) {
            CommUtils.printTraceToLogFile(e);
            cleanup("File_not_found_error");
            return false;
        }
        return true;
    }
}

