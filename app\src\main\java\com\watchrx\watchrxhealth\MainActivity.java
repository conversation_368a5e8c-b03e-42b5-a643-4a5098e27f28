package com.watchrx.watchrxhealth;

import static com.watchrx.watchrxhealth.constants.CommonConstants.Max_Time_Out_For_Ping;
import static com.watchrx.watchrxhealth.globals.Globals.locationRequest;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.location.Location;
import android.location.LocationManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.util.SparseArray;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.Geofence;
import com.google.android.gms.location.GeofencingClient;
import com.google.android.gms.location.GeofencingRequest;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.card.MaterialCardView;
import com.google.firebase.messaging.FirebaseMessaging;
import com.twilio.voice.RegistrationException;
import com.twilio.voice.RegistrationListener;
import com.twilio.voice.Voice;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.db.DBAdaptor;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.PhoneNumbers;
import com.watchrx.watchrxhealth.db.ServerQueue;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.gps.GeoFenceIntentServices;
import com.watchrx.watchrxhealth.pedometer.Database;
import com.watchrx.watchrxhealth.pedometer.SensorListener;
import com.watchrx.watchrxhealth.pedometer.Util;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.BluetoothUtil;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.ExactAlarmHelper;
import com.watchrx.watchrxhealth.utils.InternetCheckConnectivity;
import com.watchrx.watchrxhealth.utils.LogUpdateToServer;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.LogoutDialog;
import com.watchrx.watchrxhealth.utils.ReminderUtils;
import com.watchrx.watchrxhealth.utils.RestartAppIfCrash;
import com.watchrx.watchrxhealth.utils.SoftwareUpdateUtil;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;


public class MainActivity extends AppCompatActivity implements BluetoothAdapter.LeScanCallback, Runnable {

    public static final String SERVER_COMM_INITIATED_INDICATOR_INTENT_FILTER = "com.watchrx.watch.serverCommInitiatedIndicator";
    public static final String ALERT_COUNT_INCREASED_INDICATOR = "com.watchrx.watch.alertCountIncreased";
    public static final String TIMEZONE_CHANGED = "com.watchrx.watch.TimeZonechanged";
    public static final String GPS_CROSSED_EVENT = "com.watchrx.watch.GPSCrossedevent";
    public static final String SUBSCRIPTION_FAILED = "com.watchrx.watch.SubscriptionFailed";

    public static final String GPS_ENABLED_DISABLED = "com.watchrx.watch.GpsEnabledDisabled";
    public static final String GPS_TRACK_STATUS_ENABLED_DISABLED = "com.watchrx.watch.GpsTrackEnabledDisabled";
    public static final String GPS_ADDRESS_CHANGED = "com.watchrx.watch.GpsAddressChanged";

    public static final String VITAL_SCAN_START = "com.watchrx.watch.VitalScanStart";
    public static final String SEND_MESSAGE_TO_WATCH = "com.watchrx.watch.SendMessageToWatch";


    MainActivity.ServerCommRequestedReceiver serverCommRequestedReceiver = new ServerCommRequestedReceiver();
    MainActivity.AlertCountReceiver alertCountReceiver = new MainActivity.AlertCountReceiver();
    @SuppressLint("StaticFieldLeak")
    private static ImageView dummy;

    protected static final String TAG = "MainActivity";
    protected List<Geofence> mGeoFenceList = new ArrayList<>();
    private boolean isFirstTime = false;

    private FusedLocationProviderClient fusedLocationClient;
    private GeofencingClient mGeofencingClient;
    private LocationCallback locationCallback;

    private RecyclerView recyclerView;

    private BluetoothAdapter mBluetoothAdapter;
    private SparseArray<BluetoothDevice> mDevices;

    private boolean mScanning;
    private final Handler handler = new Handler();
    private static final long SCAN_PERIOD = 15 * 1000;

    private static final int BLUETOOTH_PERMISSION_REQUEST_CODE = 1;
    private ActivityResultLauncher<Intent> enableBluetoothLauncher;

    @Override
    public void onLeScan(BluetoothDevice device, int i, byte[] bytes) {
        Log.w("MainActivity onLeScan", "MainActivity Inside onLeScan......" + new Date());
        if (BluetoothUtil.hasBluetoothPermissions(this)) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                            LogUtils.debug("BLUETOOTH_CONNECT Permission not granted so , onLeScan wont work.");
                            ActivityCompat.requestPermissions(MainActivity.this, new String[]{Manifest.permission.BLUETOOTH_CONNECT,
                                    Manifest.permission.BLUETOOTH_SCAN}, 1);
                            return;
                        }
                    }
                    if (device.getName() != null && !device.getName().isEmpty()) {
                        if (CommUtils.getMedicalDeviceToList().containsKey(device.getName())) {
                            mDevices.put(device.hashCode(), device);
                            LogUtils.debug("Device Scan : Device Name :" + Objects.requireNonNull(device.getName()));
                        }
                    }
                }
            });
            if (mDevices.size() > 0) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.BLUETOOTH_CONNECT,
                                Manifest.permission.BLUETOOTH_SCAN}, 1);
                        return;
                    }
                }
                mBluetoothAdapter.stopLeScan(MainActivity.this);
                handler.removeCallbacks(MainActivity.this);
                mScanning = false;
                Log.i("Scan", "MainActivity Scan completed..." + mDevices.size() + " Scanning Status" + mScanning + " " + new Date());
                LogUtils.debug("Main Activity BLE Scanning Completed Before full scan complete....Total Device Found : " + mDevices.size() + " Scanning Status" + mScanning + new Date());
                BluetoothDevice device1 = mDevices.valueAt(0);
                String vitalTypeName = CommUtils.getMedicalDeviceToList().get(device1.getName());
                VitalStatusDetails vitalStatusDetails = VitalStatusDetails.getVitalStatusByVitalName(vitalTypeName);
                if (vitalStatusDetails != null && vitalStatusDetails.getVitalStatus().equalsIgnoreCase("enable")) {
                    Log.e("MainActivity", "Checking status for " + vitalTypeName + " and " + vitalStatusDetails.getVitalStatus());
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setAlertId("0");
                    info.setBeforeOrAfterFood(device1.getName());
                    info.setCaregiverName(vitalTypeName);
                    info.setContext(MainActivity.this);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("NewVitalsActivity");
                    Globals.priorityQueue.add(info);
                    if (WatchApp.isInForeground()) {
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    } else {
                        final NotificationHelper notificationHelper = new NotificationHelper(this);
                        notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                    }
                } else {
                    if (vitalStatusDetails != null && vitalStatusDetails.getVitalStatus() != null)
                        LogUtils.debug("Reminder didn't proceed  due to : " + vitalTypeName + " is status " + vitalStatusDetails.getVitalStatus());
                }
                mDevices.clear();
            }
        }
    }

    private void scanLeDevice() {
        Log.e("scanLeDevice", "Before scanLeDevice method start : mScanning :" + mScanning + " " + new Date());
        LogUtils.debug("Before scanLeDevice method start : Scanning status:" + mScanning + " " + new Date());
        if (!mScanning) {
            if (BluetoothUtil.hasBluetoothPermissions(this)) {
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED &&
                        Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    return; // Extra safety check
                }

                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mScanning = false;
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                            if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                                ActivityCompat.requestPermissions(MainActivity.this, new String[]{Manifest.permission.BLUETOOTH_CONNECT,
                                        Manifest.permission.BLUETOOTH_SCAN}, 1);
                                return;
                            }
                        }
                        mBluetoothAdapter.stopLeScan(MainActivity.this);
                        Log.e("Scan", "Main Activity scanLeDevice Scan completed..." + mDevices.size() + " Scanning status:" + mScanning + " " + new Date());
                        LogUtils.debug("Main Activity BLE Scanning Completed....After " + SCAN_PERIOD + " Mili seconds Total Device Found : " + mDevices.size() + " " + new Date());
                        if (mDevices.size() > 0) {
                            BluetoothDevice device1 = mDevices.valueAt(0);
                            String vitalTypeName = CommUtils.getMedicalDeviceToList().get(device1.getName());
                            VitalStatusDetails vitalStatusDetails = VitalStatusDetails.getVitalStatusByVitalName(vitalTypeName);
                            if (vitalStatusDetails != null && vitalStatusDetails.getVitalStatus().equalsIgnoreCase("enable")) {
                                Log.e("MainActivity", "Checking status for " + vitalTypeName + " and " + vitalStatusDetails.getVitalStatus());
                                ActivityInfoForQueue info = new ActivityInfoForQueue();
                                info.setAlertId("0");
                                info.setBeforeOrAfterFood(device1.getName());
                                info.setCaregiverName(vitalTypeName);
                                info.setContext(MainActivity.this);
                                info.setNanoSecTime(System.nanoTime());
                                info.setToActivityName("NewVitalsActivity");
                                Globals.priorityQueue.add(info);
                                if (WatchApp.isInForeground()) {
                                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                                } else {
                                    final NotificationHelper notificationHelper = new NotificationHelper(MainActivity.this);
                                    notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                                }
                            } else {
                                if (vitalStatusDetails != null && vitalStatusDetails.getVitalStatus() != null)
                                    LogUtils.debug("Reminder didn't proceed  due to : " + vitalTypeName + " is status " + vitalStatusDetails.getVitalStatus());
                            }
                            mDevices.clear();
                        }
                    }
                }, SCAN_PERIOD);
                mScanning = true;
                mBluetoothAdapter.startLeScan(MainActivity.this);
                LogUtils.debug("Main Activity BLE Scanning started...." + new Date());
            }
        } else {
            if (mScanning) {
                Log.e("IsScanning", "Already its scanning.....");
                LogUtils.debug("Already scan in progress..." + new Date());
            } else {
                mScanning = false;
                mBluetoothAdapter.stopLeScan(MainActivity.this);
            }
        }
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        LogUtils.debug("onSaveInstanceState Method called ...");
        outState.putBoolean("isWatchRegistered", Globals.isWatchRegistered);
        outState.putString("imei", Globals.imei);
        super.onSaveInstanceState(outState);
    }

    @RequiresApi(api = Build.VERSION_CODES.S)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LogUtils.debug("Is any instance saved : " + savedInstanceState);
        if (savedInstanceState != null) {
            Globals.imei = savedInstanceState.getString("imei");
            Globals.isWatchRegistered = savedInstanceState.getBoolean("isWatchRegistered");
        }
        setContentView(R.layout.activity_main_dashboard);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayShowCustomEnabled(true);
            actionBar.setDisplayShowTitleEnabled(false);
            actionBar.setCustomView(R.layout.custom_action_bar);

            // Set title dynamically
            TextView title = actionBar.getCustomView().findViewById(R.id.title);
            PatientDetails details = PatientDetails.getFromDB();
            title.setText(details.getPatientName());
        }


        addPhoneNumbersInToDB();
        setProfileDetails();
        setDashboardIconsClick();
        LogUtils.debug("************MainActivity************");

        if (ExactAlarmHelper.hasExactAlarmPermission(this)) {
            LogUtils.debug("Setting up midnight alarm for updating");
            ReminderUtils.setupDailyScheduleUpdater(this);

            LogUtils.debug("Setting up reminders for today...");
            ReminderUtils.setupScheduleForToday(this);
            ReminderUtils.setReminderForHeartBeatPingToNetwork(MainActivity.this);

            ReminderUtils.setupReminderForLogFileUpload(this);
            ReminderUtils.setupReminderForCollectVitals(this);
            ReminderUtils.setupReminderForQueueCheck(this);
        } else {
            ExactAlarmHelper.requestExactAlarmPermission(this);
        }

        LogUtils.debug("Adding phone numbers from parents table to phone numbers table...");

        updatePendingLogsToSendInSQLITE();
        LogUtils.debug("Setting up 'communication inititated' receiver services");
        setupServerCommInitiatedIndicator();
        setupAlertCountIndicator();

        LogUpdateToServer.logUpdateController();
        SoftwareUpdateUtil.SWUpgradeStatus(this.getApplicationContext());
        PatientDetails p = PatientDetails.getFromDB();

        if (!checkPlayServices()) {
            showGoogleUpdate();
        } else {
            if (p.getGpsStatus() != null && p.getGpsStatus().equalsIgnoreCase("E")) {
                LogUtils.debug("******GPS Things Started Here ******* ");
                LocationManager locationManager = (LocationManager) getSystemService(LOCATION_SERVICE);
                if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER))) {
                    CommUtils.showToastMessage("GPS is Enabled in your device", MainActivity.this);
                    getFusedLocationClient();
                } else {
                    showGPSDisabledAlertToUser();
                }
            } else {
                LogUtils.debug("GPS Feature is not enabled from server");
            }
        }
        FirebaseMessaging.getInstance().setAutoInitEnabled(true);

        Intent serviceIntent = new Intent(this, WatchRxForegroundService.class);
        serviceIntent.putExtra("inputExtra", "WatchRx App Running...");
        ContextCompat.startForegroundService(this, serviceIntent);

        requestBluetoothPermissions();

        bluetoothSetUp();
        setUpVOIP();
        Thread.setDefaultUncaughtExceptionHandler(new RestartAppIfCrash(this));
    }

    private void setUpVOIP() {
        try {
            LogUtils.debug("Setting Up VOIP");
            URL url = new URL(URLConstants.VOIP_CALL_ACCESS_TOKEN);
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
            new RestAsyncTask(url, jsonObject.toString(), null, new VOIPAccessTokenResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public class VOIPAccessTokenResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            LogUtils.debug("Received the response from AccessTokenResponseHandler");
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error :", "Failed to get voip token");
            } else {
                try {
                    JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.has("roomId") && jsonObject.has("accessToken")) {
//                        connectToRoom(jsonObject.optString("roomId"), jsonObject.optString("accessToken"));
                        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(task -> {
                            if (!task.isSuccessful()) {
                                Log.e(TAG, "Failed to get FCM token", task.getException());
                                return;
                            }
                            Log.d(TAG, "FCM Token: " + task.getResult());
                            Voice.register(
                                    jsonObject.optString("accessToken"),
                                    Voice.RegistrationChannel.FCM,
                                    task.getResult(),
                                    registrationListener
                            );
                        });
                    } else {
                        LogUtils.debug("Failed to get Voip Access token from server");
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private final RegistrationListener registrationListener = new RegistrationListener() {
        @Override
        public void onRegistered(@NonNull String accessToken, @NonNull String fcmToken) {
            Log.d(TAG, "Successfully registered with Twilio Voice.");
        }

        @Override
        public void onError(RegistrationException error, @NonNull String accessToken, @NonNull String fcmToken) {
            Log.e(TAG, "Twilio Voice registration failed: " + error.getMessage());
        }
    };

    private static final int REQUEST_ENABLE_BT = 1;
    private static final int REQUEST_PERMISSIONS = 2;

    private void requestBluetoothPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            ActivityCompat.requestPermissions(this, new String[]{
                    Manifest.permission.BLUETOOTH_CONNECT,
                    Manifest.permission.BLUETOOTH_SCAN
            }, REQUEST_PERMISSIONS);
        } else {
            ActivityCompat.requestPermissions(this, new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION
            }, REQUEST_PERMISSIONS);
        }
    }


    private void enableBluetooth() {
        if (!BluetoothUtil.isBluetoothEnabled()) {
            BluetoothUtil.requestBluetoothEnable(enableBluetoothLauncher, this);
        }
    }

    private void bluetoothSetUp() {

        enableBluetoothLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
//                        Toast.makeText(this, "Bluetooth Enabled", Toast.LENGTH_SHORT).show();
                    } else {
//                        Toast.makeText(this, "Bluetooth Enable Canceled", Toast.LENGTH_SHORT).show();
                    }
                }
        );

        if (!BluetoothUtil.hasBluetoothPermissions(this)) {
            BluetoothUtil.requestBluetoothPermissions(this, BLUETOOTH_PERMISSION_REQUEST_CODE);
        } else {
            enableBluetooth();
        }

        BluetoothManager manager = (BluetoothManager) getSystemService(BLUETOOTH_SERVICE);
        assert manager != null;
        mBluetoothAdapter = manager.getAdapter();
        mDevices = new SparseArray<>();

        if (mBluetoothAdapter != null) {
            if (!mBluetoothAdapter.isEnabled()) {
                LogUtils.debug("Is Bluetooth Enabled :" + mBluetoothAdapter.isEnabled());
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                        ActivityCompat.requestPermissions(MainActivity.this, new String[]{Manifest.permission.BLUETOOTH_CONNECT,
                                Manifest.permission.BLUETOOTH_SCAN}, 1);
                        return;
                    }
                }
                enableBluetooth();
                LogUtils.debug("Bluetooth Status After  mBluetoothAdapter.enable() :" + mBluetoothAdapter.isEnabled());
                if (mBluetoothAdapter.isEnabled()) {
                    LogUtils.debug("Device Scan going to start....");
                    scanLeDevice();
                }
            } else {
                scanLeDevice();
            }
        }
    }

    private void setProfileDetails() {
        PatientDetails details = PatientDetails.getFromDB();
        TextView clinicName = findViewById(R.id.clinic_name);
        clinicName.setText(details.getClinicName());
        TextView providerName = findViewById(R.id.physician_name);
        FrameLayout chat_button = findViewById(R.id.chat_button);
        chat_button.setOnClickListener(view -> {
            Intent intent = new Intent(MainActivity.this, ChatActivity.class);
            startActivity(intent);
        });
        TextView cmName = findViewById(R.id.cm_name);
        providerName.setText((details.getProviderName()));

        FrameLayout clinic_phone_button = findViewById(R.id.clinic_phone);
        clinic_phone_button.setOnClickListener(view -> {
            runOnUiThread(() -> {
                if (details.getProviderPhone() != null && !details.getProviderPhone().isEmpty()) {
                    new android.app.AlertDialog.Builder(MainActivity.this)
                            .setTitle("WatchRx")
                            .setMessage("Are you sure, You want to call to " + details.getProviderPhone() + "?")
                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                    String number = "tel:" + details.getProviderPhone();
                                    Intent callIntent = new Intent(Intent.ACTION_CALL, Uri.parse(number));
                                    startActivity(callIntent);
                                }
                            }).setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                                @Override
                                public void onClick(DialogInterface dialogInterface, int i) {
                                    dialogInterface.dismiss();
                                }
                            }).show();
                } else {
                    new android.app.AlertDialog.Builder(MainActivity.this)
                            .setTitle("WatchRx")
                            .setMessage("You don't have any number to call !!")
                            .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                public void onClick(DialogInterface dialog, int which) {
                                    dialog.dismiss();
                                }
                            }).show();
                }
            });
        });
        cmName.setText((details.getCmName()));
        TextView fmNameTv = findViewById(R.id.family_name);
        List<PhoneNumbers> phoneNumbersList = PhoneNumbers.getFromDB();
        String fmName = " --";
        String fmPhone = " --";
        if (!phoneNumbersList.isEmpty()) {
            fmName = phoneNumbersList.get(0).getContactName();
            fmPhone = phoneNumbersList.get(0).getPhonenumber();
        }
        fmNameTv.setText(MessageFormat.format("Family: {0}", fmName));
        String finalFmPhone = fmPhone;
        FrameLayout family_phone = findViewById(R.id.family_phone);
        family_phone.setOnClickListener(view -> {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (finalFmPhone != null && !finalFmPhone.isEmpty() && !finalFmPhone.equalsIgnoreCase("--")) {
                        new android.app.AlertDialog.Builder(MainActivity.this)
                                .setTitle("WatchRx")
                                .setMessage("Are you sure, You want to call to " + finalFmPhone + "?")
                                .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                    public void onClick(DialogInterface dialog, int which) {
                                        dialog.dismiss();
                                        String number = "tel:" + finalFmPhone;
                                        Intent callIntent = new Intent(Intent.ACTION_CALL, Uri.parse(number));
                                        startActivity(callIntent);
                                    }
                                }).setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                                    @Override
                                    public void onClick(DialogInterface dialogInterface, int i) {
                                        dialogInterface.dismiss();
                                    }
                                }).show();
                    } else {
                        new android.app.AlertDialog.Builder(MainActivity.this)
                                .setTitle("WatchRx")
                                .setMessage("You don't have any number to call !!")
                                .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                    public void onClick(DialogInterface dialog, int which) {
                                        dialog.dismiss();
                                    }
                                }).show();
                    }
                }
            });
        });
    }

    @Override
    public void run() {

    }


    private void setDashboardIconsClick() {

        MaterialCardView myVitalsCard = findViewById(R.id.card_my_vitals);
        myVitalsCard.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, VitalDashboard.class);
            startActivity(intent);
        });

        MaterialCardView textMessageCardView = findViewById(R.id.card_messages);
        textMessageCardView.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, ChatActivity.class);
            startActivity(intent);
        });

        MaterialCardView medicationCardView = findViewById(R.id.card_medications);
        medicationCardView.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, MedicationDetailsActivity.class);
            startActivity(intent);
        });

        MaterialCardView reminderCard = findViewById(R.id.reminder_card);
        reminderCard.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, ReminderDetailsActivity.class);
            startActivity(intent);
        });

        MaterialCardView appointment = findViewById(R.id.appointment_card);
        appointment.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, MyTaskCalendar.class);
            startActivity(intent);
        });

        MaterialCardView summary = findViewById(R.id.summary_card);
        summary.setOnClickListener(v -> {
            Intent intent = new Intent(MainActivity.this, LatestAlertsActivity.class);
            startActivity(intent);
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_logout) {
            LogoutDialog.showLogoutConfirmation(this, new Runnable() {
                @Override
                public void run() {
                    // Step 1: Clean up (your code)
                    ReminderUtils.clearExistingReminderAlarms(getApplicationContext());
                    ReminderUtils.clearExistingDailyUpdaterAlarm(getApplicationContext());
                    ReminderUtils.clearExistingPedoMeterAlarm(getApplicationContext());

                    stopService(new Intent(getApplicationContext(), SensorListener.class));
                    stopService(new Intent(getApplicationContext(), WatchRxForegroundService.class));
                    Globals.isWatchRegistered = false;
                    CommUtils.disableBroadcastReceiver(getApplicationContext());
                    Database.getInstance(getApplicationContext()).reCreateDB(getApplicationContext());

                    // Step 2: Finish all activities
                    finishAndRemoveTask(); // Finish this task and remove it from recents

                    // Step 3: Kill the process
                    android.os.Process.killProcess(android.os.Process.myPid());
                }
            });

        }
        return super.onOptionsItemSelected(item);
    }

    private void locationCallMethod() {
        locationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(@NonNull LocationResult locationResult) {
                for (Location location : locationResult.getLocations()) {
                    Log.d("Where", "@@@" + location.getLatitude());
                }

                Location location = locationResult.getLocations().get(0);

                if (Globals.isCrossed) {
                    Log.i("MA***************", "Location changed" + new Date());
                    LogUtils.debug("Location changed" + new Date());

                    try {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());
                        jsonObject.accumulate("trackLatitude", String.valueOf(location.getLatitude()));
                        jsonObject.accumulate("trackLongitude", String.valueOf(location.getLongitude()));

                        JSONObject subJsonObject = new JSONObject();
                        subJsonObject.accumulate("alertType", "GPS Alert");
                        subJsonObject.accumulate("alertDescription", "Tracking Info. Lat:" + location.getLatitude() + " ,Long:" + location.getLongitude());
                        subJsonObject.accumulate("missedTimeSlot", "");
                        subJsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));
                        subJsonObject.accumulate("missedBeforeOrAfterFood", "");
                        subJsonObject.accumulate("missedMedicationIds", "");

                        jsonObject.accumulate("alertInfo", subJsonObject);

                        CommUtils.pushToServer(MainActivity.this, jsonObject.toString(), URLConstants.GPS_TRACK_LOGS);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        };
    }

    public void populateGeoFenceList() {
        PatientDetails details = PatientDetails.getFromDB();

        if (details.getRadius() != null && details.getLatLong() != null) {
            float radius = Float.parseFloat(details.getRadius());
            String[] latlngData = details.getLatLong().split(",");
            double latitude = Double.parseDouble(latlngData[0].split(":")[1]);
            double longitude = Double.parseDouble(latlngData[1].split(":")[1]);
            if (latitude == 0.0 && longitude == 0.0) {
                LogUtils.debug("unbale to add geo fence due to 0 value for both lat lang from server");
            } else {
                mGeoFenceList.add(new Geofence.Builder()
                        .setRequestId(details.getCaregiverId())
                        .setCircularRegion(latitude, longitude, radius)
                        .setExpirationDuration(Geofence.NEVER_EXPIRE)
                        .setTransitionTypes(Geofence.GEOFENCE_TRANSITION_ENTER | Geofence.GEOFENCE_TRANSITION_EXIT)
                        .build());
                sendInitialTrackLatLong(latitude, longitude);
            }

        } else {
            LogUtils.debug("unbale to add geo fence due to 0 value for both lat lang from server");
        }
    }

    private void sendInitialTrackLatLong(double lat, double lng) {
        try {
            PatientDetails details = PatientDetails.getFromDB();

            String gpsStatus = details.getGpsStatus().equalsIgnoreCase("E") ? "Enabled" : "Disabled";
            String trackStatus = details.getTrackingStatus().equalsIgnoreCase("TE") ? "Enabled" : "Disabled";
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", details.getPatientId());
            jsonObject.accumulate("trackLatitude", "" + lat);
            jsonObject.accumulate("trackLongitude", "" + lng);

            JSONObject subJsonObject = new JSONObject();
            subJsonObject.accumulate("alertType", "GPS Alert Info");
            subJsonObject.accumulate("alertDescription", "GPS Tracking is initialized with Latitude = " + lat + ", Longitude = "
                    + lng + ", Radius = " + details.getRadius() + ", GPS Status = " + gpsStatus + ", Tracking Status = " + trackStatus);
            subJsonObject.accumulate("missedTimeSlot", "");
            subJsonObject.accumulate("createdDate", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).format(new Date(System.currentTimeMillis())));
            subJsonObject.accumulate("missedBeforeOrAfterFood", "");
            subJsonObject.accumulate("missedMedicationIds", "");

            jsonObject.accumulate("alertInfo", subJsonObject);

            CommUtils.pushToServer(this, jsonObject.toString(), URLConstants.GPS_TRACK_LOGS);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public void RemoveGeofence() {
        if (mGeofencingClient != null) {
            mGeofencingClient.removeGeofences(getPendingIntent()).addOnCompleteListener(new OnCompleteListener<Void>() {
                @Override
                public void onComplete(@NonNull Task<Void> task) {
                    if (task.isSuccessful()) {
                        Log.e("MainActivity", "Geo-Fence Removed");
                    }
                }
            }).addOnFailureListener(new OnFailureListener() {
                @Override
                public void onFailure(@NonNull Exception e) {
                    CommUtils.showToastMessage("Failed to remove Geo fence", WatchApp.getContext());
                }
            });
        }
    }

    private PendingIntent getPendingIntent() {
        Intent intent = new Intent(this, GeoFenceIntentServices.class);
        if (Globals.gpsFencePendingIntent != null) {
            Globals.gpsFencePendingIntent.cancel();
        }
        Globals.reminderAlarmCount++;
        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getService(this, Globals.reminderAlarmCount, intent, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getService(this, Globals.reminderAlarmCount, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }
        return pendingIntent;
    }

    protected synchronized void getFusedLocationClient() {
        LogUtils.debug("Inside getFusedLocationClient method");
        PatientDetails patientDetails = PatientDetails.getFromDB();
        if (patientDetails.getGpsStatus().equalsIgnoreCase("E")) {
            LogUtils.debug("GPS Feature is enabled");
            if (fusedLocationClient == null) {
                fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
                LogUtils.debug("Fused Location Client Object" + fusedLocationClient);
            }
            if (mGeofencingClient == null) {
                mGeofencingClient = LocationServices.getGeofencingClient(this);
                LogUtils.debug("Fused Geo fence Client Object" + mGeofencingClient);
            }
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                    && ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                return;
            }

            locationRequest = LocationRequest.create();
            locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
            locationRequest.setInterval(20 * 60 * 1000);
            locationRequest.setFastestInterval(20 * 60 * 1000);
            if (locationCallback == null) {
                locationCallMethod();
            }
            if (fusedLocationClient != null && locationCallback != null) {
                fusedLocationClient.removeLocationUpdates(locationCallback);
                fusedLocationClient.requestLocationUpdates(locationRequest,
                        locationCallback,
                        Looper.getMainLooper());
            }

            if (!isFirstTime) {
                RemoveGeofence();
                LogUtils.debug("First Time Geo Fence Adding");
                populateGeoFenceList();
                mGeofencingClient.addGeofences(getGeofencingRequest(), getPendingIntent())
                        .addOnCompleteListener(new OnCompleteListener<Void>() {
                            @Override
                            public void onComplete(@NonNull Task<Void> task) {
                                if (task.isSuccessful()) {
                                    Log.i("Add Geo fence ", "" + task.getResult());
                                    CommUtils.showToastMessage("Geo Fence Added Successfully", MainActivity.this);
                                    LogUtils.debug("Geo Fence Added Successfully");
                                }
                            }
                        }).addOnFailureListener(new OnFailureListener() {
                            @Override
                            public void onFailure(@NonNull Exception e) {
                                LogUtils.debug("Geo Fence Added Failed " + e.getMessage());
                                CommUtils.printTraceToLogFile(e);
                                CommUtils.showToastMessage("Geo Fence Adding Failed", WatchApp.getContext());
                            }
                        });
                isFirstTime = true;
            }
        } else {
            CommUtils.showToastMessage("GPS Feature not enabled from Server", WatchApp.getContext());
        }
    }

    private GeofencingRequest getGeofencingRequest() {
        GeofencingRequest.Builder builder = new GeofencingRequest.Builder();
        builder.setInitialTrigger(GeofencingRequest.INITIAL_TRIGGER_EXIT);
        builder.addGeofences(mGeoFenceList);
        return builder.build();
    }

    private void showGPSDisabledAlertToUser() {
        AlertDialog.Builder alertDialogBuilder = new AlertDialog.Builder(this);
        alertDialogBuilder.setMessage("GPS is disabled in your device. Would you like to enable it?")
                .setCancelable(false)
                .setPositiveButton("YES",
                        new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {
                                Intent callGPSSettingIntent = new Intent(
                                        android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                                startActivityForResult(callGPSSettingIntent, 1);
                            }
                        });
        alertDialogBuilder.setNegativeButton("NO",
                new DialogInterface.OnClickListener() {
                    public void onClick(DialogInterface dialog, int id) {
                        dialog.cancel();
                    }
                });
        AlertDialog alert = alertDialogBuilder.create();
        alert.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1) {
            getFusedLocationClient();
        }
    }

    private void updatePendingLogsToSendInSQLITE() {
        File sdcard = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        File file = new File(sdcard + "/WatchRx_DataBase/WatchRx_Logs/", "Logs.txt");
        StringBuilder data = new StringBuilder();

        if (file.exists()) {
            try {
                InputStream fis = new FileInputStream(file);
                DataInputStream in = new DataInputStream(fis);
                BufferedReader br = new BufferedReader(new InputStreamReader(in));
                String strLine;
                while ((strLine = br.readLine()) != null) {
                    data.append(strLine);
                }
                in.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (data.length() > 0) {
                LogUtils.debug("These data need to store in Sqlite =" + data);
                ServerQueue.deleteAllRows();
                String[] listofdata = data.toString().split("#");
                if (listofdata.length != 0) {
                    for (String logs : listofdata) {
                        String[] log = logs.split("%");
                        ServerQueue.addToDB(log[1], log[0]);
                    }
                    if (file.exists()) {
                        file.delete();
                    }
                }
            }
        }
    }

    private void addPhoneNumbersInToDB() {
        PhoneNumbers.deleteAllRows();
        PatientDetails patientDetails = PatientDetails.getFromDB();
        String sosMobileNo = patientDetails.getSosMobileNo();
        String pId = patientDetails.getPatientId();
        if (sosMobileNo != null && !sosMobileNo.isEmpty()) {
            LogUtils.debug("These numbers need to be add to Phone numbers table =" + sosMobileNo);
            String[] sosNos = sosMobileNo.split(",");
            for (String sosNo : sosNos) {
                String[] numbers = sosNo.split(":");
                PhoneNumbers phoneNumbers = new PhoneNumbers();
                phoneNumbers.setPatientId(pId);
                phoneNumbers.setContactName(numbers[0]);
                phoneNumbers.setPhonenumber(numbers[1].replace("-", ""));
                PhoneNumbers.addToDB(phoneNumbers);
            }
        }
    }

    private boolean isMyLauncherDefault() {
        PackageManager localPackageManager = getPackageManager();
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.addCategory("android.intent.category.HOME");
        String str = localPackageManager.resolveActivity(intent,
                PackageManager.MATCH_DEFAULT_ONLY).activityInfo.packageName;
        return str.equals(getPackageName());
    }

    private void killApp() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
        alertDialog.setTitle("WatchRx");
        alertDialog.setCancelable(false);
        alertDialog.setIcon(R.drawable.watch);
        alertDialog.setMessage("CONFIRM TO EXIT !");
        alertDialog.setPositiveButton("YES",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        if (isMyLauncherDefault()) {
                            getPackageManager().clearPackagePreferredActivities(getPackageName());
                            clearReceiversAndAlarms();
                        } else {
                            clearReceiversAndAlarms();
                        }
                    }
                });
        alertDialog.setNegativeButton("NO",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        arg0.dismiss();
                    }
                });
        AlertDialog alertDialogBuilder = alertDialog.create();
        alertDialogBuilder.show();
    }

    private void clearReceiversAndAlarms() {
        LogUtils.debug("Forcefully App is closing by user.....");
        if (PatientDetails.getFromDB().getPedoMeterStatus().equalsIgnoreCase("enabled")) {
            Database db = Database.getInstance(this);
            int todayOffset = db.getSteps(Util.getToday());
            int since_boot = db.getCurrentSteps();
            int steps_today = Math.max(todayOffset + since_boot, 0);
            Log.i("Steps To Send", " Date : " + steps_today);
            LogUtils.debug("Steps To Send  Date : " + new Date() + " Steps Count :" + steps_today);
            CommUtils.sendPedoMeterLogToServer(WatchApp.getContext(), steps_today, true, false);
            db.reCreateDB(this);
            this.stopService(new Intent(this, SensorListener.class));
        }
        ReminderUtils.clearExistingReminderAlarms(MainActivity.this);
        ReminderUtils.clearExistingDailyUpdaterAlarm(MainActivity.this);
        LocalBroadcastManager.getInstance(MainActivity.this).unregisterReceiver(serverCommRequestedReceiver);
        Alerts.deleteAllRows();
        DBAdaptor.getDbAdaptorInstance().close();
        Globals.isWatchRegistered = false;
        CommUtils.disableBroadcastReceiver(WatchApp.getContext());
        RemoveGeofence();
        finish();
        android.os.Process.killProcess(android.os.Process.myPid());
        System.exit(2);
    }

    public void setupServerCommInitiatedIndicator() {
        IntentFilter i = new IntentFilter();
        i.addAction(SERVER_COMM_INITIATED_INDICATOR_INTENT_FILTER);
        LocalBroadcastManager.getInstance(this).registerReceiver(serverCommRequestedReceiver, i);
    }

    public void setupAlertCountIndicator() {
        Alerts.deleteAllRows();
        updateUI("0");
        IntentFilter i = new IntentFilter();
        i.addAction(ALERT_COUNT_INCREASED_INDICATOR);
        i.addAction(TIMEZONE_CHANGED);
        i.addAction(GPS_CROSSED_EVENT);
        i.addAction(SUBSCRIPTION_FAILED);
        i.addAction(GPS_ENABLED_DISABLED);
        i.addAction(GPS_TRACK_STATUS_ENABLED_DISABLED);
        i.addAction(GPS_ADDRESS_CHANGED);
        i.addAction(VITAL_SCAN_START);
        i.addAction(SEND_MESSAGE_TO_WATCH);
        LocalBroadcastManager.getInstance(this).registerReceiver(alertCountReceiver, i);
    }

    @Override
    protected void onRestart() {
        List<Alerts> alertCount = Alerts.getFromDB();
        updateUI("" + alertCount.size());
        super.onRestart();
    }

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.debug("onResume state,Clock screen visible to user");
        if (mBluetoothAdapter != null) {
            if (!mBluetoothAdapter.isEnabled()) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        LogUtils.debug("BLUETOOTH_CONNECT permission not granted.");
                        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.BLUETOOTH_CONNECT,
                                Manifest.permission.BLUETOOTH_SCAN}, 1);
                        return;
                    }
                    enableBluetooth();
                } else {
                    enableBluetooth();
                }
            } else {
                scanLeDevice();
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                           @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Permission Granted for Request code :" + requestCode);
        }
        if (requestCode == BLUETOOTH_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                enableBluetooth();
            } else {
//                Toast.makeText(this, "Bluetooth Permission Denied", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void showGoogleUpdate() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
        alertDialog.setTitle("WatchRx");
        alertDialog.setCancelable(false);
        alertDialog.setIcon(R.drawable.watch);
        alertDialog.setMessage("Update Google Play Services");
        alertDialog.setPositiveButton("YES",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        String LINK_TO_GOOGLE_PLAY_SERVICES = "play.google.com/store/apps/details?id=com.google.android.gms&hl=en";
                        try {
                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("market://" + LINK_TO_GOOGLE_PLAY_SERVICES)));
                        } catch (android.content.ActivityNotFoundException anfe) {
                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse("https://" + LINK_TO_GOOGLE_PLAY_SERVICES)));
                        }
                        clearReceiversAndAlarms();
                    }
                });
        alertDialog.setNegativeButton("NO",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        arg0.dismiss();
                    }
                });
        AlertDialog alertDialogBuilder = alertDialog.create();
        alertDialogBuilder.show();
    }

    private boolean checkPlayServices() {
        GoogleApiAvailability apiAvailability = GoogleApiAvailability.getInstance();
        int resultCode = apiAvailability.isGooglePlayServicesAvailable(this);
        if (resultCode != ConnectionResult.SUCCESS) {
            if (apiAvailability.isUserResolvableError(resultCode)) {
                return false;
            }
            return false;
        }
        return true;
    }

    @Override
    protected void onPause() {
        super.onPause();
        LogUtils.debug("onPause state,Clock screen not visible to user");
    }

    @Override
    protected void onDestroy() {
        Log.e("onDestroy", "onDestroy Called");
        LogUtils.debug("On Destroy detected");
        super.onDestroy();
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        Log.e("MainActivity", "Hard Button Event Detected.." + event.getCharacters());
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }


    public void updateUI(final String count) {
        MainActivity.this.runOnUiThread(new Runnable() {
            @Override
            public void run() {

            }
        });
    }

    public static void registrationFailed() {
        dummy.setVisibility(View.VISIBLE);
    }

    public class AlertCountReceiver extends BroadcastReceiver {
        @SuppressLint("MissingPermission")
        @Override
        public void onReceive(final Context context, Intent intent) {
            if (intent.getAction().equalsIgnoreCase(TIMEZONE_CHANGED)) {
                LocalBroadcastManager.getInstance(MainActivity.this).unregisterReceiver(alertCountReceiver);
                MainActivity.this.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        ReminderUtils.clearExistingReminderAlarms(context);
                        ReminderUtils.clearExistingDailyUpdaterAlarm(context);
                        Globals.isWatchRegistered = false;
                        CommUtils.disableBroadcastReceiver(WatchApp.getContext());
                        ReminderUtils.clearExistingPedoMeterAlarm(context);
                        context.stopService(new Intent(context, SensorListener.class));
                        Database db = Database.getInstance(WatchApp.getContext());
                        db.reCreateDB(context);

                        Intent intent = new Intent(context, SplashActivity.class);

                        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                                | Intent.FLAG_ACTIVITY_CLEAR_TASK
                                | Intent.FLAG_ACTIVITY_NEW_TASK);

                        PendingIntent pendingIntent;
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                            pendingIntent = PendingIntent.getActivity(context.getApplicationContext(), Globals.reminderAlarmCount, intent, PendingIntent.FLAG_IMMUTABLE);
                        } else {
                            pendingIntent = PendingIntent.getActivity(context.getApplicationContext(), Globals.reminderAlarmCount, intent, PendingIntent.FLAG_UPDATE_CURRENT);
                        }

                        AlarmManager mgr = (AlarmManager) context
                                .getSystemService(Context.ALARM_SERVICE);
                        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 1000,
                                pendingIntent);
                        finish();
                        System.exit(2);
                    }
                });
            } else if (intent.getAction().equalsIgnoreCase(SUBSCRIPTION_FAILED)) {
                finishTheApp(context);
            } else if (intent.getAction().equalsIgnoreCase(GPS_CROSSED_EVENT)) {
                if (Globals.isCrossed) {
                    if (PatientDetails.getFromDB().getTrackingStatus().equalsIgnoreCase("TE")) {
                        fusedLocationClient.removeLocationUpdates(locationCallback);
                        locationRequest = LocationRequest.create();
                        locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
                        locationRequest.setInterval(60 * 1000);
                        locationRequest.setFastestInterval(60 * 1000);
                        fusedLocationClient.requestLocationUpdates(locationRequest,
                                locationCallback,
                                Looper.getMainLooper());

                    } else {
                        fusedLocationClient.removeLocationUpdates(locationCallback);
                        locationRequest = LocationRequest.create();
                        locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
                        locationRequest.setInterval(20 * 60 * 1000);
                        locationRequest.setFastestInterval(20 * 60 * 1000);
                        fusedLocationClient.requestLocationUpdates(locationRequest,
                                locationCallback,
                                Looper.getMainLooper());
                    }

                } else {
                    fusedLocationClient.removeLocationUpdates(locationCallback);
                    locationRequest = LocationRequest.create();
                    locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
                    locationRequest.setInterval(20 * 60 * 1000);
                    locationRequest.setFastestInterval(20 * 60 * 1000);
                    fusedLocationClient.requestLocationUpdates(locationRequest,
                            locationCallback,
                            Looper.getMainLooper());
                }
            } else if (intent.getAction().equalsIgnoreCase(GPS_ENABLED_DISABLED)) {
                PatientDetails p = PatientDetails.getFromDB();
                if (p.getGpsStatus().equalsIgnoreCase("E")) {
                    LocationManager locationManager = (LocationManager) getSystemService(LOCATION_SERVICE);
                    if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER))) {
                        CommUtils.showToastMessage("GPS is Enabled in your device", MainActivity.this);
                        getFusedLocationClient();
                    } else {
                        showGPSDisabledAlertToUser();
                    }
                } else {
                    fusedLocationClient.removeLocationUpdates(locationCallback);
                    RemoveGeofence();
                    isFirstTime = false;
                    Globals.firstTimeCrossed = false;
                    Globals.isCrossed = false;
                }
            } else if (intent.getAction().equalsIgnoreCase(GPS_TRACK_STATUS_ENABLED_DISABLED)) {
                if (Globals.isCrossed) {
                    if (PatientDetails.getFromDB().getGpsStatus().equalsIgnoreCase("E")) {
                        if (PatientDetails.getFromDB().getTrackingStatus().equalsIgnoreCase("TE")) {
                            fusedLocationClient.removeLocationUpdates(locationCallback);
                            locationRequest = LocationRequest.create();
                            locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
                            locationRequest.setInterval(60 * 1000);
                            locationRequest.setFastestInterval(60 * 1000);
                            fusedLocationClient.requestLocationUpdates(locationRequest,
                                    locationCallback,
                                    Looper.getMainLooper());
                        } else {
                            fusedLocationClient.removeLocationUpdates(locationCallback);
                            locationRequest = LocationRequest.create();
                            locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
                            locationRequest.setInterval(20 * 60 * 1000);
                            locationRequest.setFastestInterval(20 * 60 * 1000);
                            fusedLocationClient.requestLocationUpdates(locationRequest,
                                    locationCallback,
                                    Looper.getMainLooper());
                        }
                    }
                } else {
                    fusedLocationClient.removeLocationUpdates(locationCallback);
                    locationRequest = LocationRequest.create();
                    locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
                    locationRequest.setInterval(20 * 60 * 1000);
                    locationRequest.setFastestInterval(20 * 60 * 1000);
                    fusedLocationClient.requestLocationUpdates(locationRequest,
                            locationCallback,
                            Looper.getMainLooper());
                }
            } else if (intent.getAction().equalsIgnoreCase(GPS_ADDRESS_CHANGED)) {
                PatientDetails p = PatientDetails.getFromDB();
                if (p.getGpsStatus().equalsIgnoreCase("E")) {
                    isFirstTime = false;
                    Globals.firstTimeCrossed = false;
                    Globals.isCrossed = false;
                    LocationManager locationManager = (LocationManager) getSystemService(LOCATION_SERVICE);
                    if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER) || (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER))) {
                        CommUtils.showToastMessage("GPS is Enabled in your device", MainActivity.this);
                        getFusedLocationClient();
                    } else {
                        showGPSDisabledAlertToUser();
                    }

                } else {
                    if ((fusedLocationClient != null && isFirstTime) || Globals.isCrossed) {
                        RemoveGeofence();
                        fusedLocationClient.removeLocationUpdates(locationCallback);
                    }
                    LogUtils.debug("GPS Feature is not enabled from server");
                }
            } else {
                List<Alerts> alertCount = Alerts.getFromDB();
                updateUI("" + alertCount.size());
            }
        }
    }

    private void finishTheApp(Context context) {
        ReminderUtils.clearExistingReminderAlarms(context);
        ReminderUtils.clearExistingDailyUpdaterAlarm(context);
        ReminderUtils.clearExistingPedoMeterAlarm(context);
        context.stopService(new Intent(context, SensorListener.class));
        Globals.isWatchRegistered = false;
        CommUtils.disableBroadcastReceiver(WatchApp.getContext());
        context.stopService(new Intent(context, SensorListener.class));
        Database db = Database.getInstance(WatchApp.getContext());
        db.reCreateDB(context);

        Intent intent = new Intent(context, SplashActivity.class);

        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP
                | Intent.FLAG_ACTIVITY_CLEAR_TASK
                | Intent.FLAG_ACTIVITY_NEW_TASK);

        Globals.reminderAlarmCount++;
        PendingIntent pendingIntent;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            pendingIntent = PendingIntent.getActivity(context.getApplicationContext(), Globals.reminderAlarmCount, intent, PendingIntent.FLAG_IMMUTABLE);
        } else {
            pendingIntent = PendingIntent.getActivity(context.getApplicationContext(), Globals.reminderAlarmCount, intent, PendingIntent.FLAG_UPDATE_CURRENT);
        }

        AlarmManager mgr = (AlarmManager) context
                .getSystemService(Context.ALARM_SERVICE);
        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 1000,
                pendingIntent);

        //This will finish your activity manually
        finishAndRemoveTask();

        //This will stop your application and take out from it.
        System.exit(2);

    }

    public static class ServerCommRequestedReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {

            if (CommUtils.isNetworkAvailable(context)) {
                @SuppressLint("HandlerLeak") Handler h = new Handler() {
                    @Override
                    public void handleMessage(Message msg) {
                        if (msg.what != 1) { // code if not connected
                            LogUtils.debug("Internet is connected ,but data transmission is not happening properly(Ping to server Failed)*** ");
                        } else { // code if connected

                            synchronized (Globals.synchronized_were) {
                                List<ServerQueue> recordList = ServerQueue.getFromDB();
                                LogUtils.debug("Internet is connected ,ping to server is success");
                                LogUtils.debug("Number of records to push: " + recordList.size());
                                int i = 0;
                                for (ServerQueue record : recordList) {
                                    try {
                                        if (record.getState().equalsIgnoreCase("NOT_SENT")) {
                                            if (getLogFileNameFromJson(record.getMsg()).equalsIgnoreCase("Yes")) {
                                                URL url = new URL(record.getUrl());
                                                LogUtils.debug("Loop: " + (i++) + " :: Attempting delivery to server: \n\t- URL: " + url + "\n\t- Msg: " + record.getMsg() + "\n\t- RowId: " + record.getRowId());
                                                ServerQueue.updateState(record.getRowId(), "IN_PROGRESS");
                                                new RestAsyncTask(url, record.getMsg(), null, new UploadLogToServerResultHandler(), record.getRowId()).execute();
                                            } else {
                                                URL url = new URL(record.getUrl());
                                                LogUtils.debug("Loop: " + (i++) + " :: Attempting delivery to server: \n\t- URL: " + url + "\n\t- Msg: " + record.getMsg() + "\n\t- RowId: " + record.getRowId());
                                                ServerQueue.updateState(record.getRowId(), "IN_PROGRESS");
                                                new RestAsyncTask(url, record.getMsg(), null, new ServerCommResultHandler(), record.getRowId()).execute();
                                            }
                                        }
                                    } catch (MalformedURLException e) {
                                        e.printStackTrace();
                                    }
                                }
                            }
                        }
                    }
                };
                InternetCheckConnectivity.isNetworkAvailable(h, Max_Time_Out_For_Ping);
            } else {
                LogUtils.debug("Internet is not connected");
            }
        }
    }

    public static String getLogFileNameFromJson(String json) {
        try {
            JSONObject getLogFileNameFromJson_json = new JSONObject(json);
            if (getLogFileNameFromJson_json.has("updatelog")) {
                return "Yes";
            } else {
                return "No";
            }
        } catch (JSONException e) {
            CommUtils.printTraceToLogFile(e);
        }
        return "No";
    }

    private static class ServerCommResultHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                LogUtils.debug("handlerResult is null.");
                return;
            }
            Object param = handlerResult.getParam();
            if (param instanceof Long) {
                if (handlerResult.getResult() == null) {
                    LogUtils.debug("Message sending failed For RowId: " + param + "; will retry later once Internet is available again.");
                    ServerQueue.updateState((long) param, "NOT_SENT");
                } else {
                    LogUtils.debug("Message successfully sent to server for RowId: " + (long) param);
                    ServerQueue.deleteRow((long) param);
                }
            }
        }
    }

    private static class UploadLogToServerResultHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                LogUtils.debug("handlerResult is null.");
                return;
            }

            Object param = handlerResult.getParam();
            if (param instanceof Long) {
                if (handlerResult.getResult() == null) {
                    LogUtils.debug("File Upload failed For RowId: " + param + "; will retry later once Internet is available again.");
                    ServerQueue.updateState((long) param, "NOT_SENT");
                } else {
                    try {
                        JSONObject getLogFileNameFromJson_json = new JSONObject(handlerResult.getResult().toString());
                        boolean success = getLogFileNameFromJson_json.getBoolean("success");
                        if (success) {
                            LogUtils.debug("File uploaded successfully  to server for RowId: " + (long) param);
                            ServerQueue.deleteRow((long) param);
                            LogUpdateToServer.deleteCrashFile();
                        } else {
                            LogUtils.debug("File uploading failed For RowId: " + param + "; will retry later once Internet is available again.");
                            ServerQueue.updateState((long) param, "NOT_SENT");
                        }

                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                }
            }
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        setIntent(intent);
        LogUtils.debug("Main Activity onNewIntent Method called..." + intent);
        NotificationHelper notificationHelper = new NotificationHelper(MainActivity.this);
        notificationHelper.cancelAllNotifications();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
    }
}
