package com.watchrx.watchrxhealth;

import android.os.Build;
import android.os.Bundle;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.adapter.ReminderAdapter;
import com.watchrx.watchrxhealth.db.CustomerAlertsDB;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.models.Reminder;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import org.jetbrains.annotations.Contract;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class ReminderDetailsActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_reminder_details);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        RecyclerView recyclerView = findViewById(R.id.notification_list);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        Calendar calendar = Calendar.getInstance();

        String dayOfWeek = getFormatter("EEE").format(calendar.getTime());
        String shortDayOfWeek = dayOfWeek.substring(0, 2);

        List<Reminder> remindersList = new ArrayList<>();
        List<MedicationScheduleInstance> dbRemindersList = MedicationScheduleInstance.getCurrentDayReminders();
        for (MedicationScheduleInstance reminder : dbRemindersList) {
            Calendar cal = ReminderUtils.getMedicationTime(shortDayOfWeek, reminder.getTimeSlot(), reminder.getBeforeAfterFood());
            remindersList.add(new Reminder(medicineName(reminder.getMedicineID()), "Medicine", "Daily",
                    getFormatter("dd MMM yyyy").format(calendar.getTime()),
                    getFormatter("h:mm a").format(cal.getTime()), cal.getTimeInMillis(), R.drawable.ic_medicine));
        }

        List<CustomerAlertsDB> customerAlertsDBList = CustomerAlertsDB.getFromDB();
        for (CustomerAlertsDB custom : customerAlertsDBList) {
            Calendar cal = ReminderUtils.getCustomAlertCalendar(custom.getAlterTime());
            if (custom.getType() != null && custom.getType().equalsIgnoreCase("schedule")) {
                if (ReminderUtils.isDateValidForCustomAlert(custom.getStartDate(), custom.getEndDate())) {
                    if (cal.getTimeInMillis() > System.currentTimeMillis()) {
                        remindersList.add(new Reminder(custom.getAlterType(), "Custom Reminder", "Schedule",
                                getFormatter("dd MMM yyyy").format(calendar.getTime()),
                                getFormatter("h:mm a").format(cal.getTime()), cal.getTimeInMillis(), R.drawable.ic_alert));
                    }
                }
            } else {
                if (cal.getTimeInMillis() > System.currentTimeMillis()) {
                    remindersList.add(new Reminder(custom.getAlterType(), "Custom Reminder", "Daily",
                            getFormatter("dd MMM yyyy").format(calendar.getTime()),
                            getFormatter("h:mm a").format(cal.getTime()), cal.getTimeInMillis(), R.drawable.ic_alert));
                }
            }
        }
        List<ScheduleMessagesDB> scheduleMessage = ScheduleMessagesDB.getFromDB();
        for (ScheduleMessagesDB messagesDB : scheduleMessage) {
            String[] days = messagesDB.getDayOfWeek().split("\\|");
            String[] times = messagesDB.getTimeSlots().split("\\|");
            for (String day : days) {
                for (String time : times) {
                    Calendar cal = ReminderUtils.getFixedCalendar(time, day);
                    if (cal != null && (cal.getTimeInMillis() > (System.currentTimeMillis()))) {
                        remindersList.add(new Reminder(messagesDB.getQuestionName(), "Message Reminder", "Daily",
                                getFormatter("dd MMM yyyy").format(calendar.getTime()),
                                getFormatter("h:mm a").format(cal.getTime()), cal.getTimeInMillis(), R.drawable.text));
                    }
                }
            }
        }
        Collections.sort(remindersList, Comparator.comparingLong(Reminder::getDuration));
        ReminderAdapter adapter = new ReminderAdapter(remindersList);
        recyclerView.setAdapter(adapter);
    }

    @NonNull
    @Contract("_ -> new")
    private SimpleDateFormat getFormatter(String pattern) {
        return new SimpleDateFormat(pattern, Locale.getDefault());
    }

    private String medicineName(String medId) {
        MedicationScheduleMaster medicine = MedicationScheduleMaster.getForMedicineId(medId);
        return medicine.getMedicineName();
    }
}