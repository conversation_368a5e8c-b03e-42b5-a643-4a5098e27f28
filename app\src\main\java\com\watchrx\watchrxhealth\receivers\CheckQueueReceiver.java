package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

public class CheckQueueReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        LogUtils.debug("Is App running in foreground while Checking Queue Clear and Due check :" + WatchApp.isInForeground());
        if (!WatchApp.isInForeground()) {
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        }
        ReminderUtils.setupReminderForQueueCheck(context);
    }
}