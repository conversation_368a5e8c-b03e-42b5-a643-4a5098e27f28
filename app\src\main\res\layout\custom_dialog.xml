<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginTop="30dp"
        android:layout_marginRight="20dp"
        android:background="@drawable/dialog_bg"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/status_icon_success"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:visibility="gone"
            android:background="@drawable/ic_success" />

        <ImageView
            android:id="@+id/status_icon_failed"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="10dp"
            android:visibility="visible"
            android:background="@drawable/ic_cancel" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="20dp"
            android:orientation="vertical">


            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_horizontal|center_vertical"
                android:textColor="#000"
                android:textSize="@dimen/d_20_s"
                android:textStyle="bold|italic" />

            <Button
                android:id="@+id/cancel_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:text="@string/ok" />

        </LinearLayout>
    </LinearLayout>
</RelativeLayout>