package com.watchrx.watchrxhealth.utils;

import android.os.Build;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;

public class DateUtils {


    public static String formatDateString(String dateString, String inputPattern, String outputPattern) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(inputPattern, Locale.ENGLISH);
                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputPattern, Locale.ENGLISH);
                LocalDateTime dateTime = LocalDateTime.parse(dateString, inputFormatter);
                return dateTime.format(outputFormatter);
            } else {
                SimpleDateFormat inputFormat = new SimpleDateFormat(inputPattern, Locale.ENGLISH);
                SimpleDateFormat outputFormat = new SimpleDateFormat(outputPattern, Locale.ENGLISH);
                Date date = inputFormat.parse(dateString);
                assert date != null;
                return outputFormat.format(date);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return dateString;
        }
    }
}
