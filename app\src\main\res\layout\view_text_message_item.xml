<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/d_5_d"
    android:orientation="vertical"
    android:padding="1dp">

    <LinearLayout
        android:id="@+id/messageDateTimeLayout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|center_vertical"
        android:layout_margin="@dimen/d_5_d"
        android:orientation="vertical">

        <TextView
            android:id="@+id/messageDateTime"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_margin="@dimen/d_10_d"
            android:fontFamily="@font/lato_light"
            android:textSize="@dimen/dashboard_vital_text" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/chat_left_msg_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:background="@drawable/shape_bg_incoming_bubble"
        android:orientation="vertical">

        <TextView
            android:id="@+id/incomingMessage"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_margin="@dimen/d_5_d"
            android:fontFamily="@font/lato_regular"
            android:padding="@dimen/d_5_d"
            android:text="Hello"
            android:textSize="@dimen/message_text" />

        <TextView
            android:id="@+id/incomingMessageTime"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:fontFamily="@font/lato_light"
            android:textSize="@dimen/message_text" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/chat_right_msg_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:background="@drawable/shape_bg_outgoing_bubble"
        android:orientation="vertical">

        <TextView
            android:id="@+id/outgoingMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/d_5_d"
            android:fontFamily="@font/lato_regular"
            android:padding="@dimen/d_5_d"
            android:textSize="@dimen/message_text" />

        <TextView
            android:id="@+id/outgoingMessageTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_margin="@dimen/d_5_d"
            android:fontFamily="@font/lato_light"
            android:textSize="@dimen/message_text" />
    </LinearLayout>
</LinearLayout>