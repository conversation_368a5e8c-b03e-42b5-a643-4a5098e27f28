<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:background="@drawable/gradient_background"
        android:padding="16dp"
        tools:context=".auth.LoginScreen">

        <ImageView
            android:id="@+id/appLogo"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginTop="32dp"
            android:src="@drawable/app_logo_1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/userName"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_marginTop="16dp"
            android:autofillHints=""
            android:background="@drawable/rounded_edittext"
            android:clickable="false"
            android:editable="false"
            android:hint="@string/email_id"
            android:imeOptions="actionNext"
            android:importantForAutofill="no"
            android:inputType="textEmailAddress"
            android:paddingStart="16dp"
            android:textColor="@color/colorText"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/appLogo" />

        <EditText
            android:id="@+id/password"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_marginTop="8dp"
            android:autofillHints=""
            android:background="@drawable/rounded_edittext"
            android:drawableEnd="@drawable/ic_eye_off"
            android:hint="@string/password"
            android:importantForAutofill="no"
            android:inputType="textPassword"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:textColor="@color/colorText"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/userName" />

        <!--    <TextView-->
        <!--        android:id="@+id/passwordHint"-->
        <!--        android:layout_width="0dp"-->
        <!--        android:layout_height="wrap_content"-->
        <!--        android:layout_marginTop="4dp"-->
        <!--        android:fontFamily="@font/lato_regular"-->
        <!--        android:padding="4dp"-->
        <!--        android:text="@string/password_hint"-->
        <!--        android:textColor="@color/red"-->
        <!--        android:textSize="12sp"-->
        <!--        app:layout_constraintEnd_toEndOf="parent"-->
        <!--        app:layout_constraintStart_toStartOf="parent"-->
        <!--        app:layout_constraintTop_toBottomOf="@id/password" />-->

        <LinearLayout
            android:id="@+id/passwordHint"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password">

            <TextView
                android:id="@+id/criteriaUppercase"
                style="@style/PasswordCriteria"
                android:text="✖ At least 1 uppercase letter" />

            <TextView
                android:id="@+id/criteriaLowercase"
                style="@style/PasswordCriteria"
                android:text="✖ At least 1 lowercase letter" />

            <TextView
                android:id="@+id/criteriaDigit"
                style="@style/PasswordCriteria"
                android:text="✖ At least 1 digit" />

            <TextView
                android:id="@+id/criteriaSpecial"
                style="@style/PasswordCriteria"
                android:text="✖ At least 1 special character" />

            <TextView
                android:id="@+id/criteriaLength"
                style="@style/PasswordCriteria"
                android:text="✖ 8–20 characters" />

            <TextView
                android:id="@+id/criteriaWhitespace"
                style="@style/PasswordCriteria"
                android:text="✖ No spaces allowed" />

        </LinearLayout>


        <Button
            android:id="@+id/loginOrNexButton"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_marginTop="16dp"
            android:background="@drawable/button_gradient"
            android:fontFamily="@font/lato_regular"
            android:text="Continue"
            android:textColor="@color/white"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/passwordHint" />

        <TextView
            android:id="@+id/forgotPassword"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:fontFamily="@font/lato_black"
            android:text="@string/forgot_your_password"
            android:textColor="@color/red"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/loginOrNexButton" />

        <TextView
            android:id="@+id/privacyPolicy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/lato_black"
            android:text="@string/privacy_policy"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
