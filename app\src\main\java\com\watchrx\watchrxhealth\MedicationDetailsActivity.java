package com.watchrx.watchrxhealth;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.adapter.MedicationDetailsAdapter;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.models.MedicationDetailsModel;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public class MedicationDetailsActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_medication_details);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        RecyclerView recyclerView = findViewById(R.id.rvMedications);
        List<MedicationDetailsModel> medicationDetailsModelList = new ArrayList<>();
        List<MedicationScheduleMaster> scheduleMasters = MedicationScheduleMaster.getFromDB();
        for (MedicationScheduleMaster scheduleMaster : scheduleMasters) {
            MedicationDetailsModel model = new MedicationDetailsModel();
            model.setMedicineName(scheduleMaster.getMedicineName());
            model.setDaysOfWeek(scheduleMaster.getDaysOfWeek());
            model.setMedicineImage(scheduleMaster.getMedicineImage());
            model.setTimeSlots(scheduleMaster.getTimeSlots());
            model.setDosage(scheduleMaster.getMedicineStrength());
            model.setBeforeOrAfter(scheduleMaster.getBeforeOrAfterFood());
            model.setQuantity(scheduleMaster.getQuantities());
            model.setFixed(scheduleMaster.getBeforeOrAfterFood());
            model.setColor(scheduleMaster.getMedicineReminderColor());
            medicationDetailsModelList.add(model);
        }
        Collections.sort(medicationDetailsModelList, new Comparator<MedicationDetailsModel>() {
            @Override
            public int compare(MedicationDetailsModel lhs, MedicationDetailsModel rhs) {
                return lhs.getMedicineName().compareTo(rhs.getMedicineName());
            }
        });
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setHasFixedSize(true);
        recyclerView.setLayoutManager(layoutManager);
        recyclerView.setAdapter(new MedicationDetailsAdapter(MedicationDetailsActivity.this, medicationDetailsModelList, new MedicationDetailsAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(MedicationDetailsModel item) {

            }
        }));
        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
        Button addNewMed = findViewById(R.id.addMedication);
        addNewMed.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MedicationDetailsActivity.this, AddMedication.class);
                startActivity(intent);
                finish();
            }
        });

        Button dairy = findViewById(R.id.dairy);
        dairy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MedicationDetailsActivity.this, PatientDiaryActivity.class);
                startActivity(intent);
                finish();
            }
        });
    }
}