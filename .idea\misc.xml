<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CMakeSettings">
    <configurations>
      <configuration PROFILE_NAME="Debug" CONFIG_NAME="Debug" />
    </configurations>
  </component>
  <component name="DesignSurface">
    <option name="filePathToZoomLevelMap">
      <map>
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/background_with_border.xml" value="0.1685" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/bg_edittext.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/bg_received_message.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/bg_sent_message.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/button_gradient.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/circle_background.xml" value="0.2" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/custom_day_background.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/gradient_background.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/green_background.xml" value="0.1685" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/ic_baseline_directions_walk_24.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/ic_baseline_help_24.xml" value="0.2115" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/ic_baseline_navigate_next.xml" value="0.2115" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/ic_message.xml" value="0.2115" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/ic_sound.xml" value="0.1735" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/rounded_background.xml" value="0.1" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/rounded_corner_img.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/rounded_corners.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/rounded_edittext.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/sample_circle.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/sample_three_icons.xml" value="0.1465" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_selected.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_selected_left.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_selected_right.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_selector.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_selector_left.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_selector_right.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_text_selector.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_unselected.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_unselected_left.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/drawable/toggle_unselected_right.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/font/font.xml" value="0.15625" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activit_main_new.xml" value="0.20416666666666666" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_add_medication.xml" value="0.11388888888888889" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_battery.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_bluetooth_devices.xml" value="0.1" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_chat.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_contacts_list.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_custom_alerts.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_dashboard.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_daughter_reminder.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_g_p_s.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_heart_rate.xml" value="0.20416666666666666" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_interactive_voice.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_login.xml" value="0.17" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_main.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_medication.xml" value="0.11388888888888889" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_medication_details.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_my_task_calendar.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_notifications.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_patient_diary.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_pedometer_new.xml" value="0.1" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_reminder.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_reminder_details.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_sleep_monitor.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_splash.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_text_message.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_view_all_text_message.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_vital_dashboard.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_vital_details.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_vitals_graph.xml" value="0.16" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_vitals_measurement.xml" value="0.15" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/activity_web_view.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/custom_calendar_day.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/item_date.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/item_event.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/item_message_received.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/item_message_sent.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/layout_confirmation_dialog.xml" value="0.1" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/marker_view.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/med_details_item.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/medication_list_item.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/medicine_details_item.xml" value="0.14" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/notification_item.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/reminder_item.xml" value="0.21" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/view_text_message_item.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/vital_details_lit_item.xml" value="0.1361111111111111" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/layout/vital_list_item.xml" value="0.21" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/menu/bottom_nav_menu.xml" value="0.20416666666666666" />
        <entry key="..\:/MobileAppV2/watchrxwatch/app/src/main/res/menu/menu.xml" value="0.1361111111111111" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/drawable/button_shape.xml" value="0.2045" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/drawable/ic_bluetooth_white_24dp.xml" value="0.107" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/drawable/ic_call_end_white_24px.xml" value="0.191" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/drawable/resend_otp_button.xml" value="0.2045" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/activity_interaction.xml" value="0.12642140468227425" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/activity_interactive_voice.xml" value="0.1" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/activity_login.xml" value="0.16" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/activity_main.xml" value="0.12642140468227425" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/activity_medication_details.xml" value="0.1277027027027027" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/activity_video.xml" value="0.11475409836065574" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/content_video.xml" value="0.1277027027027027" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/layout/preference_dialog_number_edittext.xml" value="0.1277027027027027" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/menu/menu_video_activity.xml" value="0.12642140468227425" />
        <entry key="..\:/WatchRx Mobile App/app/src/main/res/xml/custom_prog.xml" value="0.12608695652173912" />
      </map>
    </option>
  </component>
  <component name="EntryPointsManager">
    <list size="1">
      <item index="0" class="java.lang.String" itemvalue="org.greenrobot.eventbus.Subscribe" />
    </list>
  </component>
  <component name="NullableNotNullManager">
    <option name="myDefaultNullable" value="org.jetbrains.annotations.Nullable" />
    <option name="myDefaultNotNull" value="androidx.annotation.NonNull" />
    <option name="myNullables">
      <value>
        <list size="16">
          <item index="0" class="java.lang.String" itemvalue="org.jetbrains.annotations.Nullable" />
          <item index="1" class="java.lang.String" itemvalue="edu.umd.cs.findbugs.annotations.Nullable" />
          <item index="2" class="java.lang.String" itemvalue="android.support.annotation.Nullable" />
          <item index="3" class="java.lang.String" itemvalue="androidx.annotation.Nullable" />
          <item index="4" class="java.lang.String" itemvalue="androidx.annotation.RecentlyNullable" />
          <item index="5" class="java.lang.String" itemvalue="com.android.annotations.Nullable" />
          <item index="6" class="java.lang.String" itemvalue="javax.annotation.Nullable" />
          <item index="7" class="java.lang.String" itemvalue="javax.annotation.CheckForNull" />
          <item index="8" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.qual.Nullable" />
          <item index="9" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NullableDecl" />
          <item index="10" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NullableType" />
          <item index="11" class="java.lang.String" itemvalue="org.eclipse.jdt.annotation.Nullable" />
          <item index="12" class="java.lang.String" itemvalue="io.reactivex.annotations.Nullable" />
          <item index="13" class="java.lang.String" itemvalue="io.reactivex.rxjava3.annotations.Nullable" />
          <item index="14" class="java.lang.String" itemvalue="org.jspecify.annotations.Nullable" />
          <item index="15" class="java.lang.String" itemvalue="jakarta.annotation.Nullable" />
        </list>
      </value>
    </option>
    <option name="myNotNulls">
      <value>
        <list size="15">
          <item index="0" class="java.lang.String" itemvalue="org.jetbrains.annotations.NotNull" />
          <item index="1" class="java.lang.String" itemvalue="edu.umd.cs.findbugs.annotations.NonNull" />
          <item index="2" class="java.lang.String" itemvalue="android.support.annotation.NonNull" />
          <item index="3" class="java.lang.String" itemvalue="androidx.annotation.NonNull" />
          <item index="4" class="java.lang.String" itemvalue="androidx.annotation.RecentlyNonNull" />
          <item index="5" class="java.lang.String" itemvalue="com.android.annotations.NonNull" />
          <item index="6" class="java.lang.String" itemvalue="javax.annotation.Nonnull" />
          <item index="7" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.qual.NonNull" />
          <item index="8" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NonNullDecl" />
          <item index="9" class="java.lang.String" itemvalue="org.checkerframework.checker.nullness.compatqual.NonNullType" />
          <item index="10" class="java.lang.String" itemvalue="org.eclipse.jdt.annotation.NonNull" />
          <item index="11" class="java.lang.String" itemvalue="io.reactivex.annotations.NonNull" />
          <item index="12" class="java.lang.String" itemvalue="io.reactivex.rxjava3.annotations.NonNull" />
          <item index="13" class="java.lang.String" itemvalue="org.jspecify.annotations.NonNull" />
          <item index="14" class="java.lang.String" itemvalue="jakarta.annotation.Nonnull" />
        </list>
      </value>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_17" default="true" project-jdk-name="17" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/build/classes" />
  </component>
  <component name="ProjectType">
    <option name="id" value="Android" />
  </component>
</project>