package com.watchrx.watchrxhealth;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.adapter.AnswersAdapter;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.AnswersModel;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ScheduleTextMessageActivity extends AppCompatActivity implements Runnable {
    private static final long[] vibratePattern = {0, 500, 200};
    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private Handler timeoutHandler;
    private TextView message;
    private int reminderRetryCount = 0;
    private ScheduleMessagesDB scheduleMessagesDB = null;
    private final List<AnswersModel> answerList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Window window = this.getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        setContentView(R.layout.activity_text_message);
        message = findViewById(R.id.message);
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(ScheduleTextMessageActivity.this, REMINDER_TIME_OUT);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);

        LinearLayout layout = findViewById(R.id.layout_reminder);

        Bundle bundle = getIntent().getExtras();
        String questionId = bundle != null ? bundle.getString("questionId") : null;
        if (questionId != null) {
            scheduleMessagesDB = ScheduleMessagesDB.getQuestionNameFromDB(questionId);
            if (scheduleMessagesDB == null) {
                return;
            }
            String answerData = scheduleMessagesDB.getAnswer();
            answerData = answerData.replace("[", "");
            answerData = answerData.replace("]", "");
            String[] tokens = answerData.split(",");
            for (String str : tokens) {
                if (!str.isEmpty()) {
                    AnswersModel answersModel = new AnswersModel();
                    answersModel.setAnswer(str);
                    answerList.add(answersModel);
                }
            }
            message.setText(scheduleMessagesDB.getQuestionName());
        } else {
            return;
        }
        ConstraintLayout materialButton = findViewById(R.id.texMessageView);
        RecyclerView recyclerView = findViewById(R.id.answers);
        if (answerList.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            recyclerView.setHasFixedSize(true);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(new AnswersAdapter(ScheduleTextMessageActivity.this, answerList, new AnswersAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(AnswersModel item) {
                    Toast.makeText(ScheduleTextMessageActivity.this, "Clicked to " + item.getAnswer(), Toast.LENGTH_LONG).show();
                    CommUtils.sendScheduledMessageResponseLogToServer(WatchApp.getContext(), scheduleMessagesDB.getQuestionId(), item.getAnswer());
                    GeneralUtils.stopBeeping();
                    GeneralUtils.stopSpeaking();
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    GeneralUtils.speak("Thanks for your answer");
                    timeoutHandler.removeCallbacks(ScheduleTextMessageActivity.this);
                    Globals.isScreenRunning = false;
                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    finish();
                }
            }));
        } else {
            materialButton.setVisibility(View.VISIBLE);
        }

        materialButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if ((scheduleMessagesDB.getAnswer().isEmpty()) || (scheduleMessagesDB.getAnswer().length() == 2)) {
                    CommUtils.sendScheduledMessageResponseLogToServer(WatchApp.getContext(), scheduleMessagesDB.getQuestionId(), "User responded");
                    GeneralUtils.stopBeeping();
                    GeneralUtils.stopSpeaking();
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    GeneralUtils.speak("Thanks for your answer");
                    timeoutHandler.removeCallbacks(ScheduleTextMessageActivity.this);
                    Globals.isScreenRunning = false;
                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    finish();
                }
            }
        });

        ((Button) findViewById(R.id.send)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EditText editText = findViewById(R.id.custom_message);
                if (editText != null && !TextUtils.isEmpty(editText.getText().toString())) {
                    try {
                        CommUtils.sendTextMessageResponseLogToServer(WatchApp.getContext(), scheduleMessagesDB.getQuestionId(), editText.getText().toString());
                        GeneralUtils.stopBeeping();
                        GeneralUtils.stopSpeaking();
                        GeneralUtils.speak("Thanks for your confirmation");
                        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                        Globals.isScreenRunning = false;
                        timeoutHandler.removeCallbacks(ScheduleTextMessageActivity.this);
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                        ScheduleTextMessageActivity.this.finish();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    editText.setError("Please enter your message.");
                }
            }
        });

        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                if (answerList.size() > 0) {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Hi $ you received a message  $Please Select Tap Any option , to respond it ");
                    } else {
                        GeneralUtils.speak("Hola $ recibió un mensaje  $Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                    }
                } else {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Hi $ you received a message  $Please Tap ANYWHERE, to respond it ");
                    } else {
                        GeneralUtils.speak("Hola $ recibió un mensaje  $Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                    }
                }
            }
        });

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    public void run() {
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(this);
        reminderRetryCount++;
        if (reminderRetryCount < 2) {
            GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Hi $ you received a message  $Please Tap ANYWHERE, to respond it ");

                    } else {
                        GeneralUtils.speak("Hola $ recibió un mensaje  $Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                    }

                }
            });
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        } else {
            ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
            try {
                CommUtils.sendScheduledMessageResponseLogToServer(WatchApp.getContext(), scheduleMessagesDB.getQuestionId(), "No response from user");
            } catch (Exception e) {
                e.printStackTrace();
            }
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
    }
}