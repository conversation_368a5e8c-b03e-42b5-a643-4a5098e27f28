package com.watchrx.watchrxhealth.syncup;


import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.CustomAlertsDetails;
import com.watchrx.watchrxhealth.db.CustomerAlertsDB;
import com.watchrx.watchrxhealth.db.MedicationDetail;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ScheduleMessageVO;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.db.VitalConfiguration;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.messages.RegisterResponseMessage;
import com.watchrx.watchrxhealth.messages.VitalConfigVO;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Calendar;

public class MidnightRegistrationResponseHandler implements TaskResultHandler {


    private byte[] getMedicineImage(String url) {
        try {
            URL imageUrl = new URL(url);
            URLConnection urlConnection = imageUrl.openConnection();

            InputStream is = urlConnection.getInputStream();
            BufferedInputStream bis = new BufferedInputStream(is);

            ByteArrayOutputStream baos = new ByteArrayOutputStream(5000);
            int nextByte;
            while ((nextByte = bis.read()) != -1) {
                baos.write(nextByte);
            }
            return baos.toByteArray();
        } catch (Exception e) {
            LogUtils.debug("Error at Image downloading ,so i am using default image");

            Bitmap bitmap = BitmapFactory.decodeResource(WatchApp.getContext().getResources(), R.drawable.deafult_medicine);
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            return stream.toByteArray();
        }
    }

    @SuppressLint("StaticFieldLeak")
    @Override
    public void handleResult(HandlerResult handlerResult) {
        if (handlerResult == null) {
            return;
        }

        final Object result = handlerResult.getResult();
        if (result == null) {
            LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
        }

        new AsyncTask<Void, Void, String>() {

            @Override
            protected String doInBackground(Void... params) {

                String isRegistrationSuccessful = "";

                if ((result instanceof String)) {

                    RegisterResponseMessage responseMessage = new Gson().fromJson((String) result, RegisterResponseMessage.class);
                    LogUtils.debug("Response message ->" + result);
                    LogUtils.saveDataToFile((String) result);
                    if (responseMessage != null && responseMessage.isSuccessResponse()) {
                        PatientDetails patientDetails = new PatientDetails();
                        patientDetails.setPatientId(responseMessage.patientId);
                        patientDetails.setPatientName(responseMessage.patientName);
                        patientDetails.setCaregiverId(responseMessage.patientId);
                        patientDetails.setCaregiverName(responseMessage.patientId);
                        patientDetails.setChosenTimeForTimeSlots(responseMessage.chosenTimeForTimeSlots);
                        patientDetails.setSosMobileNo(responseMessage.sosMobileNo);
                        patientDetails.setAddress(responseMessage.address);
                        patientDetails.setGpsStatus(responseMessage.gpsStatus);
                        patientDetails.setRadius(responseMessage.radius);
                        patientDetails.setTrackingStatus(responseMessage.trackingStatus);
                        patientDetails.setLatLong(responseMessage.latLong);
                        patientDetails.setAppColor(responseMessage.appColor);
                        patientDetails.setAppLanguage(responseMessage.appLanguage);
                        patientDetails.setHeartRateStatus(responseMessage.heartRateStatus);
                        patientDetails.setHeartScheduleDays(responseMessage.heartScheduleDaysOfWeek);
                        patientDetails.setHeartRateScheduleTimeSlots(responseMessage.heartRateScheduleTimeSlots);
                        if (responseMessage.pedometerConfigurationInfo != null) {
                            if (responseMessage.pedometerConfigurationInfo.state != null) {
                                patientDetails.setPedoMeterStatus(responseMessage.pedometerConfigurationInfo.state);
                            } else {
                                patientDetails.setPedoMeterStatus("disable");
                            }
                            if (responseMessage.pedometerConfigurationInfo.timeInterval != null) {
                                patientDetails.setPedoMeterInterval(responseMessage.pedometerConfigurationInfo.timeInterval);
                            } else {
                                patientDetails.setPedoMeterStatus("60");
                            }
                        } else {
                            patientDetails.setPedoMeterStatus("disable");
                            patientDetails.setPedoMeterStatus("60");
                        }
                        if (responseMessage.sleepData != null) {
                            patientDetails.setSleepStartTime(responseMessage.sleepData.sleepStartTime);
                            patientDetails.setSleepEndTime(responseMessage.sleepData.sleepEndTime);
                        } else {
                            patientDetails.setSleepStartTime("21:30");
                            patientDetails.setSleepEndTime("7:30");
                        }

                        patientDetails.setProviderName(responseMessage.provider);
                        patientDetails.setProviderPhone(responseMessage.providerPhone != null ? responseMessage.providerPhone : "--");
                        patientDetails.setCmName(responseMessage.caseManager);
                        patientDetails.setCmPhone(responseMessage.caseManagerPhone != null ? responseMessage.caseManagerPhone : "--");
                        patientDetails.setClinicName(responseMessage.clinicName != null ? responseMessage.clinicName : "--");

                        PatientDetails.addToDB(patientDetails);
                        MedicationScheduleMaster.deleteAllRows();

                        if (responseMessage.medicationDetail != null) {
                            LogUtils.debug("Going to add " + responseMessage.medicationDetail.size() + " medications in schedule master.");
                            for (MedicationDetail medicationDetail : responseMessage.medicationDetail) {

                                MedicationScheduleMaster scheduleMaster = new MedicationScheduleMaster();
                                scheduleMaster.setMedicineId(medicationDetail.medicineId);
                                scheduleMaster.setMedicineName(medicationDetail.medicineName);
                                scheduleMaster.setMedicineDosage(medicationDetail.dosage);
                                scheduleMaster.setMedicineStrength(medicationDetail.strength);
                                scheduleMaster.setMedicineReminderColor(medicationDetail.color != null ? medicationDetail.color : "");
                                //scheduleMaster.setMedicineImage(getMedicineImage(medicationDetail.image));
                                scheduleMaster.setMedicineForm(medicationDetail.medicineForm);

                                byte[] arr = getMedicineImage(medicationDetail.image);
                                scheduleMaster.setMedicineImage(arr);
                                Bitmap bitmap = BitmapFactory.decodeByteArray(arr, 0, arr.length);
                                LogUtils.SaveImage(bitmap, medicationDetail.medicineId);

                                scheduleMaster.setBeforeOrAfterFood(medicationDetail.medtime_rel_food);
                                scheduleMaster.setTimeSlots(medicationDetail.timeSlots);
                                scheduleMaster.setDaysOfWeek(medicationDetail.daysOfWeek);
                                scheduleMaster.setQuantities(medicationDetail.quantities);
                                MedicationScheduleMaster.addToDB(scheduleMaster);
                                LogUtils.debug("Added a medication with details: [" + scheduleMaster.getMedicineName() + "; " + scheduleMaster.getTimeSlots() + "; " + scheduleMaster.getBeforeOrAfterFood() + "; " + scheduleMaster.getDaysOfWeek() + "]");
                            }
                        }
                        CustomerAlertsDB.deleteAllRows();
                        if (responseMessage.customAlertsDetails != null) {
                            for (CustomAlertsDetails medicationDetail : responseMessage.customAlertsDetails) {
                                CustomerAlertsDB alertsDetails = new CustomerAlertsDB();
                                alertsDetails.setPatientId(medicationDetail.patientId);
                                alertsDetails.setAlterType(medicationDetail.alertType);
                                alertsDetails.setAlterTime(medicationDetail.alertTime);
                                alertsDetails.setAlterDetail(medicationDetail.alertDetail);
                                alertsDetails.setType(medicationDetail.type);
                                alertsDetails.setStartDate(medicationDetail.startDate);
                                alertsDetails.setEndDate(medicationDetail.endDate);
                                long res = CustomerAlertsDB.addToDB(alertsDetails);
                                Log.e("SplashActivity ", " CustomAlertsDetails res : " + res);
                            }
                        }

                        ScheduleMessagesDB.deleteAllRows();
                        if (responseMessage.scheduledTextMessageInfoList != null && responseMessage.scheduledTextMessageInfoList.size() > 0) {
                            for (ScheduleMessageVO scheduleMessage : responseMessage.scheduledTextMessageInfoList) {
                                ScheduleMessagesDB messagesDB = new ScheduleMessagesDB();
                                messagesDB.setQuestionId(scheduleMessage.scheduledTextMessagesId);
                                messagesDB.setQuestionName(scheduleMessage.question);
                                messagesDB.setAnswer(scheduleMessage.answer);
                                messagesDB.setSenderName(scheduleMessage.senderName);
                                messagesDB.setDayOfWeek(scheduleMessage.dayOfWeek);
                                messagesDB.setTimeSlots(scheduleMessage.timeSlots);
                                long messageInserted = ScheduleMessagesDB.addToDB(messagesDB);
                                Log.e("SplashActivity ", " Scheduled Message Inserted To DB : " + messageInserted);
                            }
                        }

                        VitalConfiguration.deleteAllRows();
                        if (responseMessage.vitalScheduleInfoList != null && !responseMessage.vitalScheduleInfoList.isEmpty()) {
                            for (VitalConfigVO configuration : responseMessage.vitalScheduleInfoList) {
                                VitalConfiguration vitalConfiguration = new VitalConfiguration();
                                vitalConfiguration.setVitalScheduleId(configuration.vitalScheduleId);
                                vitalConfiguration.setVitalTypeName(configuration.vitalTypeName);
                                vitalConfiguration.setCollectMode(configuration.collectMode);
                                vitalConfiguration.setScheduleDayOfWeek(configuration.scheduleDayOfWeek);
                                vitalConfiguration.setFrequency(configuration.frequency);
                                vitalConfiguration.setTimeSlots(configuration.timeSlots);
                                vitalConfiguration.setDeviceSerialId(configuration.deviceSerialId);
                                vitalConfiguration.setDeviceMeasures(configuration.deviceMeasures);
                                vitalConfiguration.setDeviceName(configuration.deviceName);
                                vitalConfiguration.setStartDate(configuration.startDate);
                                vitalConfiguration.setEndDate(configuration.endDate);
                                long messageInserted = VitalConfiguration.addToDB(vitalConfiguration);
                                Log.e("SplashActivity ", "Vital Added: " + messageInserted);
                            }
                        }
                        VitalStatusDetails.deleteAllRows();
                        if (responseMessage.vitalTypeStatusList != null && !responseMessage.vitalTypeStatusList.isEmpty()) {
                            for (VitalConfigVO configuration : responseMessage.vitalTypeStatusList) {
                                VitalStatusDetails vitalStatusDetails = new VitalStatusDetails();
                                vitalStatusDetails.setVitalTypeName(configuration.vitalTypeName);
                                vitalStatusDetails.setVitalStatus(configuration.vitalStatus);
                                long messageInserted = VitalStatusDetails.addToDB(vitalStatusDetails);
                                Log.e("SplashActivity ", "Vital Status Added: " + messageInserted);
                            }
                        }
                        isRegistrationSuccessful = responseMessage.responseMessage;
                    } else if (responseMessage != null && responseMessage.isImeNotFoundResponse()) {
                        isRegistrationSuccessful = responseMessage.responseMessage;
                    } else if (responseMessage != null && responseMessage.isImeNotAssignedResponse()) {
                        isRegistrationSuccessful = responseMessage.responseMessage;
                    } else if (responseMessage != null && responseMessage.isWatchInActive()) {
                        isRegistrationSuccessful = responseMessage.responseMessage;
                    } else if (responseMessage != null && responseMessage.isPatientInfoNotFound()) {
                        isRegistrationSuccessful = responseMessage.responseMessage;
                    }
                }
                return isRegistrationSuccessful;
            }

            @Override
            protected void onPostExecute(String isRegistrationSuccessful) {
                if (isRegistrationSuccessful.equalsIgnoreCase("operationsuccessful")) {
                    LogUtils.debug("==========^-AFTER SYNC SETTING ALL ALARMS^========");
                    ReminderUtils.setupDailyScheduleUpdater(WatchApp.getContext());
                    ReminderUtils.setupScheduleForToday(WatchApp.getContext());
                    Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
                    LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
                    /*final ToneGenerator tg = new ToneGenerator(AudioManager.STREAM_NOTIFICATION, 200);
                    tg.startTone(ToneGenerator.TONE_PROP_BEEP2);*/
                } else if (isRegistrationSuccessful.equalsIgnoreCase("Imei is not found in the database")) {
                    LogUtils.debug("***** Sync Failed *****Imei is not found in the database");
                    MainActivity.registrationFailed();
                } else if (isRegistrationSuccessful.equalsIgnoreCase("Imei is not associated with any patient")) {
                    MainActivity.registrationFailed();
                    LogUtils.debug("***** Sync Failed *****Imei is not associated with any patient");
                } else if (SyncupFromDatabase.registerWatchForSync(ReadDataFromMemory.getDataForSync()).equalsIgnoreCase("Subscription is not active")) {
                    LogUtils.debug("***** Sync Failed *****Subscription is not active");
                    CommUtils.sendBroadcastForSubscription();
                } else {
                    LogUtils.debug("Sync failed may be server communication problem.");
                    Calendar cal = Calendar.getInstance();
                    cal.setTimeInMillis(System.currentTimeMillis());
                    if ((cal.get(Calendar.HOUR_OF_DAY) >= 0 && cal.get(Calendar.HOUR_OF_DAY) <= 4)) {
                        MedicationScheduleSetupReceiver.setReminder30Min(cal, WatchApp.getContext(), "Internet is not connected");
                    } else {
                        LogUtils.debug("Internet is not connected, and sync up is not taking place where i ll set for last day reminders.");
                        ReminderUtils.setupDailyScheduleUpdater(WatchApp.getContext());
                        ReminderUtils.setupScheduleForToday(WatchApp.getContext());
                    }
                }
            }
        }.execute();
    }

}
