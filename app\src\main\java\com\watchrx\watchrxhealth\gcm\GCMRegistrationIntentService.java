package com.watchrx.watchrxhealth.gcm;

import android.app.IntentService;
import android.content.Intent;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.firebase.messaging.FirebaseMessaging;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.voip.VoiceManager;

public class GCMRegistrationIntentService extends IntentService {

    public static final String REGISTRATION_SUCCESS = "RegistrationSuccess";
    public static final String REGISTRATION_ERROR = "RegistrationError";
    private static final String TAG = GCMRegistrationIntentService.class.getSimpleName();

    public GCMRegistrationIntentService() {
        super(TAG);
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        registerGcm();
    }

    private void registerGcm() {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(task -> {
                    Intent registrationComplete;
                    if (!task.isSuccessful()) {
                        LogUtils.debug("Fetching FCM registration token failed");
                        Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                        registrationComplete = new Intent(REGISTRATION_ERROR);
                    } else {
                        String token = task.getResult();
                        LogUtils.debug("Fetching FCM registration token success: Token " + token);
                        registrationComplete = new Intent(REGISTRATION_SUCCESS);
                        registrationComplete.putExtra("token", token);
//                        VoiceManager.getInstance(getApplicationContext());
                    }
                    LocalBroadcastManager.getInstance(GCMRegistrationIntentService.this)
                            .sendBroadcast(registrationComplete);
                });
    }
}
