<?xml version="1.0" encoding="utf-8"?>
<rotate xmlns:android="http://schemas.android.com/apk/res/android"
    android:fromDegrees="0"
    android:pivotX="50%"
    android:pivotY="50%"
    android:toDegrees="1080">

    <shape xmlns:android="http://schemas.android.com/apk/res/android"
        android:innerRadiusRatio="3"
        android:shape="ring"
        android:thicknessRatio="8"
        android:useLevel="false">

        <gradient
            android:angle="0"
            android:endColor="#A5865F"
            android:startColor="@android:color/transparent"
            android:type="sweep"
            android:useLevel="false" />

        <size
            android:width="48dip"
            android:height="48dip" />
    </shape>
</rotate>