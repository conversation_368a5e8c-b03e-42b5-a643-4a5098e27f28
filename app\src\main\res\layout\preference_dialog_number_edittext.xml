<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginTop="48dp"
    android:layout_marginBottom="48dp"
    android:overScrollMode="ifContentScrolls">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="5dip"
        android:orientation="vertical">

        <TextView android:id="@android:id/message"
            style="?android:attr/textAppearanceSmall"
            android:layout_marginBottom="48dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="?android:attr/textColorSecondary" />

        <EditText
            android:id="@android:id/edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="number"
            tools:ignore="Autofill,LabelFor" />

    </LinearLayout>

</ScrollView>