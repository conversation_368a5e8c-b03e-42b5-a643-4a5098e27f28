package com.watchrx.watchrxhealth.receivers;


import static com.watchrx.watchrxhealth.utils.LogUpdateToServer.checkLogFile;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.Environment;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.db.VitalConfiguration;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.messages.WatchAliveStatus;
import com.watchrx.watchrxhealth.pedometer.Database;
import com.watchrx.watchrxhealth.pedometer.SensorListener;
import com.watchrx.watchrxhealth.pedometer.Util;
import com.watchrx.watchrxhealth.queue.ActivityInfoForQueue;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;
import com.watchrx.watchrxhealth.utils.SoftwareUpdateUtil;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.util.Calendar;

public class MessageHandler {

    private static final String TAG = MessageHandler.class.getSimpleName();


    public static void processIncomingMessage(Context context, String message) {
        try {
            JSONObject jsonMessage = new JSONObject(message);
            LogUtils.debug("i got message from GCM->" + message);
            String messageType = jsonMessage.getString("messageType");
            if (messageType.equalsIgnoreCase("UpdatedMedicationInfo")) {
                processIncomingMedicationUpdate(context);
            } else if (messageType.equalsIgnoreCase("nurseReminder")) {
                processIncomingNurseReminder(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("nurseOnTheWay")) {
                processIncomingNurseOnTheWay(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("visitVerification")) {
                processIncomingVisitVerification(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("softwareUpgrade")) {
                processIncomingSoftwareUpdate(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("heartBeat")) {
                processIncomingWatchAliveStatus(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("logupdate")) {
                processIncomingLogUpdate(context);
            } else if (messageType.equalsIgnoreCase("subscriptionCancelled") || messageType.equalsIgnoreCase("WatchUnAssign")) {
                processSubscriptionFailed(context);
            } else if (messageType.equalsIgnoreCase("GPS Status")) {
                processIncomingGPSEnableDisabledStatus(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("Tracking Status")) {
                processIncomingGPSTrackingEnableDisabledStatus(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("AddressChanged")) {
                processIncomingChangedAddressValue(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("sendHearRate")) {
                processIncomingHearRateReminder(context);
            } else if (messageType.equalsIgnoreCase("textMessage")) {
//                processIncomingTextMessage(context, message);
                processChattingTextMessage(context);
            } else if (messageType.equalsIgnoreCase("pedometerAlert")) {
//                processIncomingPedoMeterStatus(context, jsonMessage);
                Log.d("WatchApp", "Ignore");
            } else if (messageType.equalsIgnoreCase("ScheduledMessageUpdated")) {
                processScheduledTextMessage(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("vital")) {
                processVitalConfiguration(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("videoRoom")) {
                processIncomingVideoCall(context, jsonMessage);
            } else if (messageType.equalsIgnoreCase("zoomVideoCall")) {
                processIncomingZoomVideoCall(context, jsonMessage);
            } else {
                LogUtils.debug("message type is not recognised" + message);
            }
        } catch (JSONException e) {
            Log.d(TAG, "Incoming Message Malformed: " + message);
        }
    }

    private static void processIncomingZoomVideoCall(Context context, JSONObject jsonMessage) {
        long roomId = jsonMessage.optLong("roomId");
        String program = jsonMessage.optString("program", "rpm");
        LogUtils.debug("RoomId :" + roomId);
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setTriggerAt(roomId);
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setBeforeOrAfterFood(program);
        info.setToActivityName("ZoomVideoCallScreen");
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.showIncomingCallNotification(context);
        }
    }

    private static void processIncomingVideoCall(Context context, JSONObject jsonMessage) {
        long roomId = jsonMessage.optLong("roomId");
        LogUtils.debug("RoomId :" + roomId);
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setTriggerAt(roomId);
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("VideoActivity");
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.videoCallNotification("You have an video call invite, Please click here to join.",
                    "WatchRx Video Call Notification");
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void processVitalConfiguration(Context context, JSONObject jsonMessage) {
        try {
            String updateType = jsonMessage.optString("updatetype");
            String status = jsonMessage.optString("status");
            if (status.equalsIgnoreCase("collectNow")) {
                if (jsonMessage.optString("deviceName").equalsIgnoreCase("Watch")
                        && jsonMessage.optString("vitalTypeName").equalsIgnoreCase("Heart Rate")) {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("HeartRateActivity");
                    info.setAlertId("0");
                    Globals.priorityQueue.add(info);
                    if (WatchApp.isInForeground()) {
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    } else {
                        final NotificationHelper notificationHelper = new NotificationHelper(context);
                        notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                    }
                } else {
                    ActivityInfoForQueue info = new ActivityInfoForQueue();
                    info.setAlertId("0");
                    info.setBeforeOrAfterFood(jsonMessage.optString("deviceName"));
                    info.setCaregiverName(jsonMessage.optString("vitalTypeName"));
                    info.setContext(context);
                    info.setNanoSecTime(System.nanoTime());
                    info.setToActivityName("VitalActivity");
                    Globals.priorityQueue.add(info);
                    if (WatchApp.isInForeground()) {
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    } else {
                        final NotificationHelper notificationHelper = new NotificationHelper(context);
                        notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
                    }
                }
            } else if (status.equalsIgnoreCase("configurationUpdated")) {
                if (updateType.equalsIgnoreCase("update")) {
                    Log.w("Updated", "" + jsonMessage.optJSONObject("vitalconfig"));
                    updateVitalConfig(context, jsonMessage.optJSONObject("vitalconfig"));
                } else if (updateType.equalsIgnoreCase("add")) {
                    Log.w("Added", "" + jsonMessage.optJSONObject("vitalconfig"));
                    addVitalsToDb(context, jsonMessage.optJSONObject("vitalconfig"));
                } else if (updateType.equalsIgnoreCase("delete")) {
                    Log.w("Deleted", "" + jsonMessage.optJSONObject("vitalconfig"));
                    deleteVitalDataFromDB(context, jsonMessage.optJSONObject("vitalconfig"));
                }
            } else if (status.equalsIgnoreCase("statusChanged")) {
                VitalStatusDetails statusDetails = new VitalStatusDetails();
                statusDetails.setVitalStatus(jsonMessage.optString("vitalStatus"));
                statusDetails.setVitalTypeName(jsonMessage.optString("vitalTypeName"));
                long updatedRecordCount = VitalStatusDetails.updateVitalStatusToDB(statusDetails);
                Log.e("Vital Update:", updatedRecordCount + " Vital status Updated for :" + jsonMessage.optString("vitalTypeName") + "-" + jsonMessage.optString("vitalStatus"));
                if (jsonMessage.optString("vitalStatus").equalsIgnoreCase("enable")) {
                    ReminderUtils.setUpVitalConfigurationReminder(context);
                }
                updateVitalStatusLocalDB(jsonMessage);
            }
        } catch (Exception e) {
            Log.d(TAG, "Incoming Message Malformed: " + jsonMessage);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static void deleteVitalDataFromDB(Context context, JSONObject jsonMessage) {
        try {
            String vitalId = jsonMessage.optString("vitalScheduleId");
            long isDeleted = VitalConfiguration.deleteVitalFromDB(vitalId);
            LogUtils.debug("Vital Deleted  : " + isDeleted);
            modifyVitalLocalDB("delete", jsonMessage);
        } catch (Exception e) {
            Log.d(TAG, "Incoming Message Malformed: " + jsonMessage);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static void addVitalsToDb(Context context, JSONObject jsonMessage) {
        try {
            VitalConfiguration vitalConfiguration = new VitalConfiguration();
            vitalConfiguration.setVitalScheduleId(jsonMessage.optString("vitalScheduleId"));
            vitalConfiguration.setVitalTypeName(jsonMessage.optString("vitalTypeName"));
            vitalConfiguration.setCollectMode(jsonMessage.optString("collectMode"));
            vitalConfiguration.setScheduleDayOfWeek(jsonMessage.optString("scheduleDayOfWeek"));
            vitalConfiguration.setFrequency(jsonMessage.optString("frequency"));
            vitalConfiguration.setTimeSlots(jsonMessage.optString("timeSlots"));
            vitalConfiguration.setDeviceSerialId(jsonMessage.optString("deviceSerialId"));
            vitalConfiguration.setDeviceMeasures(jsonMessage.optString("deviceMeasures"));
            vitalConfiguration.setDeviceName(jsonMessage.optString("deviceName"));
            vitalConfiguration.setStartDate(jsonMessage.optString("startDate"));
            vitalConfiguration.setEndDate(jsonMessage.optString("endDate"));

            long isAdded = VitalConfiguration.addToDB(vitalConfiguration);
            LogUtils.debug("New Vital Added : " + isAdded);
            ReminderUtils.setUpVitalConfigurationReminder(context);
            modifyVitalLocalDB("add", jsonMessage);

        } catch (Exception e) {
            Log.d(TAG, "Incoming Message Malformed: " + jsonMessage);
        }
    }


    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static void updateVitalConfig(Context context, JSONObject jsonMessage) {
        try {
            VitalConfiguration vitalConfiguration = new VitalConfiguration();
            vitalConfiguration.setVitalScheduleId(jsonMessage.optString("vitalScheduleId"));
            vitalConfiguration.setVitalTypeName(jsonMessage.optString("vitalTypeName"));
            vitalConfiguration.setCollectMode(jsonMessage.optString("collectMode"));
            vitalConfiguration.setScheduleDayOfWeek(jsonMessage.optString("scheduleDayOfWeek"));
            vitalConfiguration.setFrequency(jsonMessage.optString("frequency"));
            vitalConfiguration.setTimeSlots(jsonMessage.optString("timeSlots"));
            vitalConfiguration.setDeviceSerialId(jsonMessage.optString("deviceSerialId"));
            vitalConfiguration.setDeviceMeasures(jsonMessage.optString("deviceMeasures"));
            vitalConfiguration.setDeviceName(jsonMessage.optString("deviceName"));
            vitalConfiguration.setStartDate(jsonMessage.optString("startDate"));
            vitalConfiguration.setEndDate(jsonMessage.optString("endDate"));
            long isUpdated = VitalConfiguration.updateVitalToDB(vitalConfiguration);
            LogUtils.debug("Vitals Updated : " + isUpdated);
            ReminderUtils.setUpVitalConfigurationReminder(context);
            modifyVitalLocalDB("update", jsonMessage);

        } catch (Exception e) {
            Log.d(TAG, "Incoming Message Malformed: " + jsonMessage);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static void updateVitalStatusLocalDB(JSONObject jsonMessage) {

        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/";
        File file = new File(rootPath, "WatchRx.txt");

        try {
            FileReader fr = new FileReader(file);
            String s;
            StringBuilder totalStr = new StringBuilder();
            try (BufferedReader br = new BufferedReader(fr)) {
                while ((s = br.readLine()) != null) {
                    totalStr.append(s);
                }
                JSONObject object = new JSONObject(totalStr.toString());
                String vitalTypeName = jsonMessage.optString("vitalTypeName");
                String vitalStatus = jsonMessage.optString("vitalStatus");
                JSONArray vitalTypeStatusList = object.optJSONArray("vitalTypeStatusList");
                for (int i = 0; i < vitalTypeStatusList.length(); i++) {
                    JSONObject vitalObj = vitalTypeStatusList.getJSONObject(i);
                    if (vitalObj.getString("vitalTypeName").equalsIgnoreCase(vitalTypeName)) {
                        vitalTypeStatusList.remove(i);
                        JSONObject newStatusObj = new JSONObject();
                        newStatusObj.put("vitalTypeName", vitalTypeName);
                        newStatusObj.put("vitalStatus", vitalStatus);
                        vitalTypeStatusList.put(newStatusObj);
                        object.put("vitalTypeStatusList", vitalTypeStatusList);
                    }
                }
                Log.i("Updated Json", object.toString());
                LogUtils.saveDataToFile(object.toString());
            }
        } catch (Exception e) {
            Log.d(TAG, "Incoming Message Malformed: " + jsonMessage);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private static void modifyVitalLocalDB(String type, JSONObject jsonMessage) {

        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/";
        File file = new File(rootPath, "WatchRx.txt");

        try {
            FileReader fr = new FileReader(file);
            String s;
            StringBuilder totalStr = new StringBuilder();
            try (BufferedReader br = new BufferedReader(fr)) {
                while ((s = br.readLine()) != null) {
                    totalStr.append(s);
                }
                JSONObject object = new JSONObject(totalStr.toString());

                String vitalId = jsonMessage.optString("vitalScheduleId");
                JSONArray vitalScheduleInfoList = object.optJSONArray("vitalScheduleInfoList");

                if (type.equalsIgnoreCase("add")) {
                    if (vitalScheduleInfoList != null && vitalScheduleInfoList.length() > 0) {
                        vitalScheduleInfoList.put(jsonMessage);
                        object.put("vitalScheduleInfoList", vitalScheduleInfoList);
                    } else {
                        JSONArray newVitalInfoObj = new JSONArray();
                        newVitalInfoObj.put(jsonMessage);
                        object.put("vitalScheduleInfoList", newVitalInfoObj);
                    }

                } else if (type.equalsIgnoreCase("update")) {
                    for (int i = 0; i < vitalScheduleInfoList.length(); i++) {
                        JSONObject vitalObj = vitalScheduleInfoList.getJSONObject(i);
                        if (vitalObj.getString("vitalScheduleId").equalsIgnoreCase(vitalId)) {
                            vitalScheduleInfoList.remove(i);
                            vitalScheduleInfoList.put(jsonMessage);
                        }
                    }
                    object.put("vitalScheduleInfoList", vitalScheduleInfoList);
                } else if (type.equalsIgnoreCase("delete")) {
                    for (int i = 0; i < vitalScheduleInfoList.length(); i++) {
                        JSONObject vitalObj = vitalScheduleInfoList.getJSONObject(i);
                        if (vitalObj.getString("vitalScheduleId").equalsIgnoreCase(vitalId)) {
                            vitalScheduleInfoList.remove(i);
                        }
                        object.put("vitalScheduleInfoList", vitalScheduleInfoList);
                    }
                }
                Log.i("Updated Json", object.toString());
                LogUtils.saveDataToFile(object.toString());
            }
        } catch (Exception e) {
            Log.d(TAG, "Incoming Message Malformed: " + jsonMessage);
        }
    }

    private static void processIncomingTextMessage(Context context, String message) {
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("TextMessageActivity");
        info.setTimeSlot(message);
        Globals.priorityQueue.add(info);

        try {
            JSONObject jsonMessage = new JSONObject(message);
            Intent alertCount = new Intent(MainActivity.SEND_MESSAGE_TO_WATCH);
            alertCount.putExtra("message", "WatchRx Notification \n" + jsonMessage.optString("question"));
            LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
        } catch (Exception ignored) {
        }
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
        }
    }

    public static void processIncomingChangedAddressValue(Context context, JSONObject jsonMessage) {
        PatientDetails details = PatientDetails.getFromDB();
        try {
            String latitude = jsonMessage.optString("latitude");
            String longitude = jsonMessage.optString("longitude");
            String gpsStatus = jsonMessage.optString("gpsStatus");
            String trackStatus = jsonMessage.optString("trackStatus");
            String radius = jsonMessage.optString("radius");
            String latLong = "Lat:" + latitude + "," + "Long:" + longitude;
            //Update Local File
            updateDatabaseFileForGPS(latLong, radius, gpsStatus, trackStatus);
            PatientDetails.updateGPSInfo(details.getPatientId(), latLong, radius, gpsStatus, trackStatus);
            Intent alertCount = new Intent(MainActivity.GPS_ADDRESS_CHANGED);
            LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void processIncomingGPSTrackingEnableDisabledStatus(Context context, JSONObject jsonMessage) {
        try {
            String status = jsonMessage.getString("Status");
            if (status.equalsIgnoreCase("Enabled")) {

                String search = "\"trackingStatus\": \"TD\"";
                String realValue = "\"trackingStatus\": \"TE\"";
                modifiedDataBaseFile(realValue, search);

                PatientDetails details = PatientDetails.getFromDB();
                PatientDetails.updateGPSTrackStatus(details.getPatientId(), "TE");
                Intent alertCount = new Intent(MainActivity.GPS_TRACK_STATUS_ENABLED_DISABLED);
                LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
            } else if (status.equalsIgnoreCase("Disabled")) {

                String search = "\"trackingStatus\": \"TE\"";
                String realValue = "\"trackingStatus\": \"TD\"";
                modifiedDataBaseFile(realValue, search);

                PatientDetails details = PatientDetails.getFromDB();
                PatientDetails.updateGPSTrackStatus(details.getPatientId(), "TD");
                Intent alertCount = new Intent(MainActivity.GPS_TRACK_STATUS_ENABLED_DISABLED);
                LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);

            } else {
                LogUtils.debug("Not valid message Type for GPS Tracking Status");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public static void processIncomingGPSEnableDisabledStatus(Context context, JSONObject jsonMessage) {
        try {
            String status = jsonMessage.getString("Status");
            if (status.equalsIgnoreCase("Enabled")) {
                String search = "\"gpsStatus\": \"D\"";
                String realValue = "\"gpsStatus\": \"E\"";
                modifiedDataBaseFile(realValue, search);
                PatientDetails details = PatientDetails.getFromDB();
                PatientDetails.updateGPSStatus(details.getPatientId(), "E");
                Intent alertCount = new Intent(MainActivity.GPS_ENABLED_DISABLED);
                LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
            } else if (status.equalsIgnoreCase("Disabled")) {
                String search = "\"gpsStatus\": \"E\"";
                String realValue = "\"gpsStatus\": \"D\"";
                modifiedDataBaseFile(realValue, search);
                PatientDetails details = PatientDetails.getFromDB();
                PatientDetails.updateGPSStatus(details.getPatientId(), "D");
                Intent alertCount = new Intent(MainActivity.GPS_ENABLED_DISABLED);
                LocalBroadcastManager.getInstance(WatchApp.getContext()).sendBroadcast(alertCount);
            } else {
                LogUtils.debug("Not valid message Type for GPS Status");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    private static void modifiedDataBaseFile(String value, String search) {
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/";
        File file = new File(rootPath, "WatchRx.txt");

        try {
            FileReader fr = new FileReader(file);
            String s;
            StringBuilder totalStr = new StringBuilder();
            try (BufferedReader br = new BufferedReader(fr)) {

                while ((s = br.readLine()) != null) {
                    totalStr.append(s);
                }
                totalStr = new StringBuilder(totalStr.toString().replaceAll(search, value));
                FileWriter fw = new FileWriter(file);
                fw.write(totalStr.toString());
                fw.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void processSubscriptionFailed(Context context) {
        CommUtils.sendBroadcastForSubscription();
    }

    public static void processIncomingLogUpdate(Context context) {
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DebugFile/";
        checkLogFile(new File(rootPath), 1);
    }

    public static void processIncomingMedicationUpdate(Context context) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(System.currentTimeMillis());
        cal.get(Calendar.HOUR_OF_DAY);
        if (cal.get(Calendar.HOUR_OF_DAY) <= 15) {
            ReminderUtils.setUpSyncUpdaterAlarm(context);
        } else {
            LogUtils.debug("Syncup will happen at 12:01:05, no need to syncup now");
        }
    }


    public static void processIncomingSoftwareUpdate(Context context, JSONObject message) {
        try {
            LogUtils.debug("softwareUpdate request is received version url : " + message.getString("url"));
            SoftwareUpdateUtil.downLoadManager(context, message);
        } catch (JSONException e) {
            LogUtils.debug("Incoming software update Message Malformed: " + message);
            e.printStackTrace();
        }
    }


    private static void processIncomingNurseReminder(Context context, JSONObject message) {
       /* Intent reminderIntent = new Intent(context, ReminderActivity.class);
        reminderIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            reminderIntent.putExtra("alertId", message.getString("alertId"));
            reminderIntent.putExtra("beforeOrAfterFood", message.getString("missedBeforeOrAfterFood"));
            reminderIntent.putExtra("timeSlot", message.getString("missedTimeSlot"));
            reminderIntent.putExtra("nurseName", message.getString("caregiverName"));
            context.startActivity(reminderIntent);
        } catch (JSONException e) {
            e.printStackTrace();
        }*/

        try {
            ActivityInfoForQueue info = new ActivityInfoForQueue();
            info.setBeforeOrAfterFood(message.getString("missedBeforeOrAfterFood"));
            info.setTimeSlot(message.getString("missedTimeSlot"));
            info.setAlertId(message.getString("alertId"));
            info.setCaregiverName(message.getString("caregiverName"));
            info.setContext(context);
            info.setNanoSecTime(System.nanoTime());
            info.setToActivityName("ReminderActivity");
            Globals.priorityQueue.add(info);
            if (WatchApp.isInForeground()) {
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
            } else {
                final NotificationHelper notificationHelper = new NotificationHelper(context);
                notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private static void processIncomingNurseOnTheWay(Context context, JSONObject message) {

      /*  Intent nurseOnTheWayIntent = new Intent(context, NurseOnTheWayActivity.class);
        nurseOnTheWayIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            nurseOnTheWayIntent.putExtra("nurseName", message.getString("caregiverName"));
            nurseOnTheWayIntent.putExtra("status", message.getString("status"));
            context.startActivity(nurseOnTheWayIntent);
        } catch (JSONException e) {
            e.printStackTrace();
        }*/

        try {
            ActivityInfoForQueue info = new ActivityInfoForQueue();

            info.setTimeSlot(message.getString("status"));
            info.setCaregiverName(message.getString("caregiverName"));
            info.setContext(context);
            info.setNanoSecTime(System.nanoTime());
            info.setToActivityName("NurseOnTheWayActivity");
            Globals.priorityQueue.add(info);
            if (WatchApp.isInForeground()) {
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
            } else {
                final NotificationHelper notificationHelper = new NotificationHelper(context);
                notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private static void processIncomingVisitVerification(Context context, JSONObject message) {
       /* Intent visitVerificationIntent = new Intent(context, VisitVerificationActivity.class);
        visitVerificationIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        try {
            visitVerificationIntent.putExtra("nurseName", message.getString("caregiverName"));
            visitVerificationIntent.putExtra("visitVerificationCode", message.getString("visitVerificationCode"));
            context.startActivity(visitVerificationIntent);
        } catch (JSONException e) {
            e.printStackTrace();
        }*/
        try {
            ActivityInfoForQueue info = new ActivityInfoForQueue();
            info.setTimeSlot(message.getString("visitVerificationCode"));
            info.setCaregiverName(message.getString("caregiverName"));
            info.setContext(context);
            info.setNanoSecTime(System.nanoTime());
            info.setToActivityName("VisitVerificationActivity");
            Globals.priorityQueue.add(info);
            if (WatchApp.isInForeground()) {
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
            } else {
                final NotificationHelper notificationHelper = new NotificationHelper(context);
                notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public static void processIncomingWatchAliveStatus(Context context, JSONObject message) {
        WatchAliveStatus aliveStatus = new WatchAliveStatus();
        aliveStatus.imei = Globals.imei;
        CommUtils.pushToServer(context, new Gson().toJson(aliveStatus), URLConstants.ALIVE_STATUS_URL);
    }

    private static void processIncomingHearRateReminder(Context context) {
       /* Intent i = new Intent(context, HeartRateActivity.class);
        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(i);*/
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("HeartRateActivity");
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        }
    }

    public static void processIncomingPedoMeterStatus(Context context, JSONObject message) {
        try {
            String status = message.getString("status");
            if (status.equalsIgnoreCase("sendCountNow")) {
                PatientDetails details = PatientDetails.getFromDB();
                if (details.getPedoMeterStatus().equalsIgnoreCase("enable")) {
                    Database db = Database.getInstance(WatchApp.getContext());
                    int todayOffset = db.getSteps(Util.getToday());
                    int since_boot = db.getCurrentSteps();
                    int steps_today = Math.max(todayOffset + since_boot, 0);
                    Log.i("Steps To Send", " Date : " + steps_today);
                    LogUtils.debug("While sendCountNow ::: Sending Steps to server : todayOffset value = " + todayOffset + " Since Boot Complete Sensor Counts : " + since_boot + " Current Counts Now : " + steps_today);
                    CommUtils.sendPedoMeterLogToServer(WatchApp.getContext(), steps_today, false, false);
                    showPedoMeterActivity(context, steps_today, "Steps");
                } else {
                    LogUtils.debug("Pedo Meter is not enabled");
                }
            } else if (status.equalsIgnoreCase("resetPedometer")) {
                PatientDetails details = PatientDetails.getFromDB();
                if (details.getPedoMeterStatus().equalsIgnoreCase("enable")) {
                    Database db = Database.getInstance(WatchApp.getContext());
                    int todayOffset = db.getSteps(Util.getToday());
                    int since_boot = db.getCurrentSteps();
                    int steps_today = Math.max(todayOffset + since_boot, 0);
                    Log.i("Steps To Send", " Date : " + steps_today);
                    LogUtils.debug("While Reset : Sending Steps to server : todayOffset value = " + todayOffset + " Since Boot Complete Sensor Counts : " + since_boot + " Current Counts Now : " + steps_today);
                    CommUtils.sendPedoMeterLogToServer(WatchApp.getContext(), steps_today, true, false);
                    db.reCreateDB(context);
                    context.stopService(new Intent(context, SensorListener.class));
                    context.startService(new Intent(context, SensorListener.class));
                    LogUtils.debug("Pedo Meter Reset Successfully");
                    showPedoMeterActivity(context, steps_today, "Steps");
                } else {
                    LogUtils.debug("To Resets Pedometer should turn on First");
                }

            } else if (status.equalsIgnoreCase("pedometerConfigurationUpdated")) {
                String state = message.getString("state");
                String interval = message.getString("timeInterval");
                Log.w("State :", state);
                Log.w("Interval :", interval);
                PatientDetails details = PatientDetails.getFromDB();
                if (state.equalsIgnoreCase("enable")) {
                    if (details.getPedoMeterStatus().equalsIgnoreCase("disable")) {
                        Database db = Database.getInstance(WatchApp.getContext());
                        db.reCreateDB(context);
                        PatientDetails.updatePedoMeter(details.getPatientId(), "enable", interval);
                        context.startService(new Intent(context, SensorListener.class));
                        ReminderUtils.setupMidNightPedoMeterReset(context);
                        ReminderUtils.setReminderToSendStepsToServer(context);
                        LogUtils.debug("Pedometer Enabled Successfully");
                        showPedoMeterActivity(context, 0, "Enabled");
                    } else {
                        PatientDetails.updatePedoMeterInterVal(details.getPatientId(), interval);
                        LogUtils.debug("Pedometer Already Enabled");
                        Database db = Database.getInstance(WatchApp.getContext());
                        int todayOffset = db.getSteps(Util.getToday());
                        int since_boot = db.getCurrentSteps();
                        int steps_today = Math.max(todayOffset + since_boot, 0);
                        showPedoMeterActivity(context, steps_today, "Steps");
                    }
                } else if (state.equalsIgnoreCase("disable")) {
                    Database db = Database.getInstance(WatchApp.getContext());
                    db.reCreateDB(context);
                    PatientDetails.updatePedoMeter(details.getPatientId(), "disable", interval);
                    context.stopService(new Intent(context, SensorListener.class));
                    ReminderUtils.clearExistingPedoMeterAlarm(context);
                    LogUtils.debug("Pedometer disabled");
                    showPedoMeterActivity(context, 0, "Disabled");
                }
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private static void showPedoMeterActivity(Context context, int count, String status) {
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("PedoMeterActivity");
        info.setAlertId("" + count);
        info.setCaregiverName(status);
        Globals.priorityQueue.add(info);
        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.notify("You have WatchRx notification please, open the app.", "WatchRx Notification");
        }
    }

    public static void processScheduledTextMessage(Context context, JSONObject jsonObject) {
        if (jsonObject.has("scheduledTextMessageInfoList")) {
            JSONArray messageList = jsonObject.optJSONArray("scheduledTextMessageInfoList");
            if (messageList.length() > 0) {
                for (int i = 0; i < messageList.length(); i++) {
                    try {
                        int deleted = ScheduleMessagesDB.deleteRowById(messageList.getJSONObject(i).optString("scheduledTextMessagesId"));
                        Log.e("Deleted Count", "" + deleted);
                        String status = messageList.getJSONObject(i).optString("status");
                        if (status.equalsIgnoreCase("update") || status.equalsIgnoreCase("save")) {
                            ScheduleMessagesDB messagesDB = new ScheduleMessagesDB();
                            messagesDB.setQuestionId(messageList.getJSONObject(i).optString("scheduledTextMessagesId"));
                            messagesDB.setQuestionName(messageList.getJSONObject(i).optString("question"));
                            messagesDB.setAnswer(messageList.getJSONObject(i).optString("answer"));
                            messagesDB.setSenderName(messageList.getJSONObject(i).optString("senderName"));
                            messagesDB.setDayOfWeek(messageList.getJSONObject(i).optString("dayOfWeek"));
                            messagesDB.setTimeSlots(messageList.getJSONObject(i).optString("timeSlots"));
                            long messageInserted = ScheduleMessagesDB.addToDB(messagesDB);
                            Log.e("Message Handler ", " Scheduled Message Inserted To DB : " + messageInserted);
                        }
                        updateDatabaseFile(messageList.getJSONObject(i), messageList);
                    } catch (Exception e) {
                        LogUtils.debug("Exception While Resetting the Schedule messages");
                    }
                }
                ReminderUtils.setScheduleMessageAlarm(context);
            }
        }
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    public static void updateDatabaseFile(JSONObject jsonObject, JSONArray jsonArray) {
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/";
        File file = new File(rootPath, "WatchRx.txt");
        try {
            FileReader fr = new FileReader(file);
            String s;
            StringBuilder totalStr = new StringBuilder();
            try (BufferedReader br = new BufferedReader(fr)) {
                while ((s = br.readLine()) != null) {
                    totalStr.append(s);
                }
                JSONObject object = new JSONObject(totalStr.toString());
                Log.w("Full Data", object.toString());

                String newQuestionId = jsonObject.optString("scheduledTextMessagesId");

                if (object.has("scheduledTextMessageInfoList")) {
                    JSONArray oldObj = object.optJSONArray("scheduledTextMessageInfoList");
                    if (oldObj.length() > 0) {
                        for (int j = 0; j < oldObj.length(); j++) {
                            String oldQId = oldObj.optJSONObject(j).optString("scheduledTextMessagesId");
                            if (newQuestionId.equalsIgnoreCase(oldQId)) {
                                Log.e("Is Matched", "Yes ");
                                oldObj.remove(j);
                                if (jsonObject.optString("status").equalsIgnoreCase("save")
                                        || jsonObject.optString("status").equalsIgnoreCase("update")) {
                                    oldObj.put(j, jsonObject);
                                    object.remove("scheduledTextMessageInfoList");
                                    object.put("scheduledTextMessageInfoList", oldObj);

                                }
                                Log.i("Updated Json", object.toString());
                                LogUtils.saveDataToFile(object.toString());
                            } else {
                                Log.e("Is Matched", "No ");
                                oldObj.put(jsonObject);
                                LogUtils.saveDataToFile(object.toString());
                            }
                            Log.i("New Obj", oldObj.toString());
                        }
                    } else {
                        oldObj.put(jsonObject);
                        LogUtils.saveDataToFile(object.toString());
                    }
                } else {
                    object.put("scheduledTextMessageInfoList", jsonArray);
                    LogUtils.saveDataToFile(object.toString());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @TargetApi(Build.VERSION_CODES.KITKAT)
    public static void updateDatabaseFileForGPS(String latLng, String radius, String gpsStatus, String trackingStatus) {
        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/";
        File file = new File(rootPath, "WatchRx.txt");

        try {
            FileReader fr = new FileReader(file);
            String s;
            StringBuilder totalStr = new StringBuilder();
            try (BufferedReader br = new BufferedReader(fr)) {
                while ((s = br.readLine()) != null) {
                    totalStr.append(s);
                }
                JSONObject object = new JSONObject(totalStr.toString());
                Log.w("Full Data", object.toString());

                //update LatLong
                object.remove("latLong");
                object.put("latLong", latLng);

                //Update radius
                object.remove("radius");
                object.put("radius", radius);

                //Update Gps Status
                object.remove("gpsStatus");
                object.put("gpsStatus", gpsStatus);

                //Update Track Status
                object.remove("trackingStatus");
                object.put("trackingStatus", trackingStatus);
                LogUtils.saveDataToFile(object.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void processChattingTextMessage(Context context) {
        ActivityInfoForQueue info = new ActivityInfoForQueue();
        info.setContext(context);
        info.setNanoSecTime(System.nanoTime());
        info.setToActivityName("ChatActivity");
        Globals.priorityQueue.add(info);

        if (WatchApp.isInForeground()) {
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
        } else {
            final NotificationHelper notificationHelper = new NotificationHelper(context);
            notificationHelper.notify("You have received message from care manager, open the app.", "WatchRx Notification");
        }
    }
}