#Thu Jul 31 11:35:06 EDT 2025
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_directions_walk_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_directions_walk_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/geo_fecne.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\geo_fecne.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_emergency.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_emergency.png
com.watchrx.watchrxhealth.app-main-6\:/mipmap-hdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\watchrx_app_icon_background.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/speaker_on.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\speaker_on.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_heart.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_heart.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_zoom_video_call_screen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_zoom_video_call_screen.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_unselected_right.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_unselected_right.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_video_call_actiity.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_video_call_actiity.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_pedometer.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_pedometer.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/low_bat_green.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\low_bat_green.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/icons_meds_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\icons_meds_40.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/dial_pink.PNG=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dial_pink.PNG
com.watchrx.watchrxhealth.app-main-6\:/drawable/text.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\text.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_text_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_text_message.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/item_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\item_selector.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vital_details.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vital_details.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_regular.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_regular.ttf
com.watchrx.watchrxhealth.app-main-6\:/layout/layout_confirmation_dialog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\layout_confirmation_dialog.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxxhdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\watchrx_app_icon_foreground.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bluetooth_orange.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bluetooth_orange.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/phone.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\phone.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/turn_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\turn_off.png
com.watchrx.watchrxhealth.app-main-6\:/xml/settings.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\xml\\settings.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-hdpi/ic_loading.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_loading.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/nurse_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\nurse_1.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_organge_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_organge_gradient.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_help_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_help_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_alarm_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_alarm_icon.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_video.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_video.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_green_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_green_gradient.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_call.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/custome_loader.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\custome_loader.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/mic_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\mic_off.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_call_black_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_call_black_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/button_shape.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_shape.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_txt_password.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_txt_password.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/no.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\no.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/rounded_edittext.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_edittext.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_sent_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_sent_message.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_visit_verification.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_visit_verification.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bluetooth_searching.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bluetooth_searching.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/home_logo.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\home_logo.png
com.watchrx.watchrxhealth.app-main-6\:/font/lato_thin.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_thin.ttf
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_calorie.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_calorie.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\ic_launcher.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_bluetooth.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_bluetooth.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/sample_three_icons.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\sample_three_icons.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/sample_circle.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\sample_circle.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_medical.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_medical.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/icons8_thermometer_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\icons8_thermometer_40.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_dashboard.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_dashboard.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/right_arrow.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\right_arrow.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_latest_alerts.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_latest_alerts.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_weight_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_weight_40.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_switch_camera_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_switch_camera_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_light.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_light.ttf
com.watchrx.watchrxhealth.app-main-6\:/xml/custom_prog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\xml\\custom_prog.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_login.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_login.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_spo2.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_spo2.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_person_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person_icon.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/unmute_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\unmute_1.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/mail.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\mail.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_headset_mic_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_headset_mic_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/item_count.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\item_count.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/rounded_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_background.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/dialog_loading.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dialog_loading.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_blood_sugar.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_blood_sugar.png
com.watchrx.watchrxhealth.app-main-6\:/raw/amplifyconfiguration.json=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\raw\\amplifyconfiguration.json
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_weigt.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_weigt.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_video_call_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_video_call_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/rounded_corner_img.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_corner_img.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/rounded_corners.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_corners.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_temp_f.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_temp_f.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xhdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\watchrx_app_icon_background.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/watch.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\watch.png
com.watchrx.watchrxhealth.app-main-6\:/layout/item_date.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_date.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/conta_name_shape.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\conta_name_shape.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_btn_welcome.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_btn_welcome.png
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxhdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\watchrx_app_icon_foreground.png
com.watchrx.watchrxhealth.app-main-6\:/font/lato_black.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_black.ttf
com.watchrx.watchrxhealth.app-main-6\:/drawable/vital_card_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\vital_card_background.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vitals_graph.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vitals_graph.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bordered_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bordered_background.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_sleep_monitor.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_sleep_monitor.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_incoming_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_incoming_call.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/nurse_2.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\nurse_2.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/cutsom_progress_bar.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\cutsom_progress_bar.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_sms_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_sms_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_launcher_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_background.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_heart_rate.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_heart_rate.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/button_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_gradient.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/pedometer_item_bg.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\pedometer_item_bg.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/reply_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\reply_view.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_refresh_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_refresh_24.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\ic_launcher.png
com.watchrx.watchrxhealth.app-main-6\:/layout/dummy_lay.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\dummy_lay.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_call_end_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_call_end_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxhdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\watchrx_app_icon.png
com.watchrx.watchrxhealth.app-main-6\:/layout/answer_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\answer_list_item.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_view_all_text_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_view_all_text_message.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/layout_corner.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\layout_corner.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_appointment_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_appointment_icon.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_contact.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_contact.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_red_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_red_gradient.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/blue_outline.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\blue_outline.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_pedometer_new.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_pedometer_new.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/doctor.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\doctor.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/reminder.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\reminder.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/sleep.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\sleep.png
com.watchrx.watchrxhealth.app-main-6\:/layout/vital_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\vital_list_item.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_zoom_video_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_zoom_video_call.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_selector_right.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_selector_right.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_foot_steps.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_foot_steps.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/imeiwarning.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\imeiwarning.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/later.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\later.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bp.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bp.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/circle.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/resend_otp_button.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\resend_otp_button.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/button_login.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_login.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_txt_username.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_txt_username.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_messages_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_messages_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_mic_white_off_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_mic_white_off_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_chat_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_chat_1.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_new_vitals.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_new_vitals.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\ic_launcher.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/mute_sound.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\mute_sound.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\ic_launcher.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_my_task_calendar.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_my_task_calendar.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/check.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\check.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/custom_day_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\custom_day_background.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_fill_heart.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_fill_heart.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bluetooth_connected.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bluetooth_connected.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/custom_thumb.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\custom_thumb.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_selected_left.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_selected_left.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_videocam_off_black_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_videocam_off_black_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vital.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vital.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/custom_progress_dialog_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\custom_progress_dialog_view.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_thermo.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_thermo.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_splash.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_splash.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/sleep_monitor.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\sleep_monitor.png
com.watchrx.watchrxhealth.app-main-6\:/font/lato_bold_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_bold_italic.ttf
com.watchrx.watchrxhealth.app-main-6\:/menu/menu_patient_diary.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_patient_diary.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_sound.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_sound.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_medical_services_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_medical_services_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_selector_left.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_selector_left.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-anydpi-v26/watchrx_app_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\watchrx_app_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vitals_health_connect.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vitals_health_connect.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_spo2_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_spo2_40.png
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxxhdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\watchrx_app_icon.png
com.watchrx.watchrxhealth.app-main-6\:/layout/notification_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\notification_item.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_btn_login.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_btn_login.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_battery.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_battery.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/view_text_message_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\view_text_message_item.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/blue_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\blue_icon.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/logo.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\logo.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_purple_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_purple_gradient.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_launcher.png
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxxhdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\watchrx_app_icon_background.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_main_dashboard.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main_dashboard.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_heart_rate_pink.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_heart_rate_pink.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_selected_right.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_selected_right.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_thermo_64.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_thermo_64.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_medication.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_medication.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/answer_item_list.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\answer_item_list.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_stop_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_stop_call.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_selector.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/appointment.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\appointment.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_medication.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_medication.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/content_video.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\content_video.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_edittext.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_edittext.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xhdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\watchrx_app_icon_round.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_appointment_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_appointment_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_custom_alerts.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_custom_alerts.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxxhdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxxhdpi-v4\\watchrx_app_icon_round.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_health_vitals.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_health_vitals.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_question.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_question.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/dash_doc.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dash_doc.png
com.watchrx.watchrxhealth.app-main-6\:/layout/marker_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\marker_view.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vitals_measurement.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vitals_measurement.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_icon_white.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_icon_white.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_message.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/text_answer_sq.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\text_answer_sq.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/medication_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\medication_list_item.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_patient_diary.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_patient_diary.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_plus.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_plus.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/reminder_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\reminder_item.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxhdpi/ic_blue_connected.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\ic_blue_connected.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/capsules.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\capsules.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/utensils.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\utensils.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/medical_assistance.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\medical_assistance.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_chat_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_chat_40.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_success.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_success.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xhdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\watchrx_app_icon.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_text_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_text_selector.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/center_circle.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\center_circle.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-hdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\watchrx_app_icon_foreground.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_call_end_white_24px.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_call_end_white_24px.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_mic_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_mic_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/anim/rotate.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\anim\\rotate.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_video_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_video_call.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_enter_otpatictivity.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_enter_otpatictivity.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_permissions_rationale.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_permissions_rationale.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/contact_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\contact_list_item.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xhdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xhdpi-v4\\watchrx_app_icon_foreground.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/clock.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\clock.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/mute_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\mute_1.png
com.watchrx.watchrxhealth.app-main-6\:/layout/preference_dialog_number_edittext.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\preference_dialog_number_edittext.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/app_logo_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\app_logo_1.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/dial.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dial.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/send_button_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\send_button_background.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_launcher_foreground.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_interactive_voice.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_interactive_voice.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_reminders_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_reminders_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/login_dialog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\login_dialog.xml
com.watchrx.watchrxhealth.app-main-6\:/font/font.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\font.xml
com.watchrx.watchrxhealth.app-main-6\:/xml/filepaths.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\xml\\filepaths.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_chat_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_chat_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/green_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\green_background.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_bold.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_bold.ttf
com.watchrx.watchrxhealth.app-main-6\:/layout/item_event.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_event.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_favorite_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_favorite_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/down_arrow.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\down_arrow.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_bloodtype_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_bloodtype_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/background_with_border.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\background_with_border.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_heart_rate.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_heart_rate.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/med_details_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\med_details_item.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/dash_chat.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dash_chat.png
com.watchrx.watchrxhealth.app-main-6\:/xml/accessibility_service_config.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\xml\\accessibility_service_config.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-hdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\watchrx_app_icon.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_chat.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_chat.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/custom_calendar_day.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\custom_calendar_day.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/item_message_sent.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_message_sent.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_cancel.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_cancel.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/call_end.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\call_end.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_bg_topheader.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_bg_topheader.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/call_open.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\call_open.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_no_alerts.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_no_alerts.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_daughter_reminder.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_daughter_reminder.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/custom_dialog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\custom_dialog.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_person.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_person.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_black_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_black_italic.ttf
com.watchrx.watchrxhealth.app-main-6\:/menu/bottom_nav_menu.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\menu\\bottom_nav_menu.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_main.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_main.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/item_message_received.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\item_message_received.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_light_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_light_italic.ttf
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_circle_white.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_circle_white.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_interaction.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_interaction.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_mic_off_black_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_mic_off_black_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-mdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\watchrx_app_icon.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_eye_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_eye_off.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/voice_assistance.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\voice_assistance.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/medicine.jpg=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\medicine.jpg
com.watchrx.watchrxhealth.app-main-6\:/drawable/shape_bg_incoming_bubble.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\shape_bg_incoming_bubble.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/nurse.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\nurse.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_notifications.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_notifications.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_round_bloodpressure_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_round_bloodpressure_24.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_answer.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_answer.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bottom_navigation_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bottom_navigation_selector.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/comments.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\comments.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vital_detail.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vital_detail.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_contacts_list.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_contacts_list.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/speaker_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\speaker_off.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/full_bat_green.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\full_bat_green.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/support.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\support.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_chat_40_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_chat_40_1.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/warning.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\warning.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/yes.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\yes.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_summary_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_summary_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/custom_action_bar.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\custom_action_bar.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_error_outline_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_error_outline_24.xml
com.watchrx.watchrxhealth.app-main-6\:/menu/refresh.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\menu\\refresh.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_login_screen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_login_screen.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/calendar.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\calendar.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_g_p_s.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_g_p_s.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-mdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\watchrx_app_icon_foreground.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/phn.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\phn.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_heart_pulse.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_heart_pulse.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/button_shape_red.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_shape_red.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_vitals_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_vitals_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_icon_semitrans_white.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_icon_semitrans_white.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/button_shape_green.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\button_shape_green.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxhdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\watchrx_app_icon_background.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_web_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_web_view.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_enter_password.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_enter_password.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_videocam_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_videocam_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_meds_filled.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_meds_filled.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/dialog_bg.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dialog_bg.xml
com.watchrx.watchrxhealth.app-main-6\:/menu/menu_video_activity.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu_video_activity.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_reminder_details.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_reminder_details.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/fm_person.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\fm_person.png
com.watchrx.watchrxhealth.app-main-6\:/mipmap-mdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\watchrx_app_icon_background.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_device_thermostat_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_device_thermostat_24.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/send.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\send.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/circular_round.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circular_round.xml
com.watchrx.watchrxhealth.app-main-6\:/color/toggle_text_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\color\\toggle_text_selector.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-anydpi-v26/watchrx_app_icon_round.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-anydpi-v26\\watchrx_app_icon_round.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/vital_signs.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\vital_signs.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_volume_up_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_volume_up_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/deafult_medicine.PNG=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\deafult_medicine.PNG
com.watchrx.watchrxhealth.app-main-6\:/layout/text_phone.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\text_phone.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/support_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\support_icon.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_baseline_navigate_next.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_baseline_navigate_next.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_thin_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_thin_italic.ttf
com.watchrx.watchrxhealth.app-main-6\:/raw/video_call_tone.mpeg=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\raw\\video_call_tone.mpeg
com.watchrx.watchrxhealth.app-main-6\:/drawable/call_24.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\call_24.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_wifi_config.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_wifi_config.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/alert_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\alert_list_item.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/progress_bg.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\progress_bg.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_nurse_on_the_way.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_nurse_on_the_way.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/user.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\user.png
com.watchrx.watchrxhealth.app-main-6\:/layout/bluetooth_device_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\bluetooth_device_item.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/paracetmaol.jpg=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\paracetmaol.jpg
com.watchrx.watchrxhealth.app-main-6\:/layout/vital_details_lit_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\vital_details_lit_item.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_add_medication.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_add_medication.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/text_center.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\text_center.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_bluetooth_devices.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_bluetooth_devices.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/gray_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\gray_icon.png
com.watchrx.watchrxhealth.app-main-6\:/layout/get_latest_measurement_record.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\get_latest_measurement_record.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-mdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-mdpi-v4\\watchrx_app_icon_round.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_meds.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_meds.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_thermo_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_thermo_40.png
com.watchrx.watchrxhealth.app-main-6\:/mipmap-xxhdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-xxhdpi-v4\\watchrx_app_icon_round.png
com.watchrx.watchrxhealth.app-main-6\:/layout/custom_reply_layout.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\custom_reply_layout.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/rounded_logo_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\rounded_logo_background.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/pgoress_screen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\pgoress_screen.xml
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_reminder.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_reminder.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_medications_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_medications_icon.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/mic_on.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\mic_on.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/toggle_unselected_left.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\toggle_unselected_left.xml
com.watchrx.watchrxhealth.app-main-6\:/font/lato_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\font\\lato_italic.ttf
com.watchrx.watchrxhealth.app-main-6\:/drawable/drugs.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\drugs.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/gradient_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\gradient_background.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_alert.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_alert.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\background.xml
com.watchrx.watchrxhealth.app-main-6\:/raw/awsconfiguration.json=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\raw\\awsconfiguration.json
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_medication_details.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_medication_details.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_medicine.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_medicine.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_heart_filled.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_heart_filled.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_phonelink_ring_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_phonelink_ring_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/message.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\message.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_vital_dashboard.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_vital_dashboard.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_bluetooth_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_bluetooth_white_24dp.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_eye.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_eye.png
com.watchrx.watchrxhealth.app-main-6\:/layout/activity_resend_otpscreen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\layout\\activity_resend_otpscreen.xml
com.watchrx.watchrxhealth.app-main-6\:/menu/menu.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\menu\\menu.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/user_icon_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\user_icon_1.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/list_view_custom.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\list_view_custom.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_dot.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_dot.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/ic_photo_user.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\ic_photo_user.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/shape_bg_outgoing_bubble.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\shape_bg_outgoing_bubble.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/bg_received_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\bg_received_message.xml
com.watchrx.watchrxhealth.app-main-6\:/drawable/circle_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\circle_background.xml
com.watchrx.watchrxhealth.app-main-6\:/mipmap-hdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\mipmap-hdpi-v4\\watchrx_app_icon_round.png
com.watchrx.watchrxhealth.app-main-6\:/drawable/dial_pink1_blue.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\packaged_res\\debug\\drawable\\dial_pink1_blue.png
