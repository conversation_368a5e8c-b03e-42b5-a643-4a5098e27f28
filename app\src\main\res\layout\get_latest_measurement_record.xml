<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/getLastMeasurementRecordLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/App_color"
    android:orientation="vertical"
    android:padding="15dp"
    android:weightSum="3">

    <LinearLayout
        android:id="@+id/vitalTitle"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.25"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible"
        android:weightSum="1">

        <TextView
            android:id="@+id/measurementType"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/blue"
            android:textSize="25sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/progress_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="visible">

        <ProgressBar
            android:id="@+id/progress_bar_dialog"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1.5" />

        <TextView
            android:id="@+id/statusTitle"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.5"
            android:gravity="center"
            android:text="@string/statusTitle"
            android:textColor="@color/blue"
            android:textSize="25sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/bgLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/bgValueTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bg_value"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/bgValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/bgExtraLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/value"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/bgExtraValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/state" />

        <TextView
            android:id="@+id/bgExtraState"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/bpLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/sysValueTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/sys_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/sysValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="23sp" />

        <TextView
            android:id="@+id/diaValueTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/dia_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/diaValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="23sp" />

        <TextView
            android:id="@+id/pulseValueTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pul_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/pulseValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="23sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/thermometerLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/thermometerValueTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/thermometer_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/thermometerValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="38sp" />
    </LinearLayout>

    <!-- BG -->

    <LinearLayout
        android:id="@+id/weightScaleLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/weightTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/weight_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/weightValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="38sp" />

        <TextView
            android:id="@+id/bmiTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/bmi_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/bmiValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="38sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/spO2Layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:id="@+id/spO2Title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/spO2_value"
            android:textAllCaps="true"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/spO2Value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="38sp" />

        <TextView
            android:id="@+id/spO2PulseTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/pul_value"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:id="@+id/spO2PulseValue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceLarge"
            android:textColor="@color/blue"
            android:textSize="38sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="0.75"
        android:orientation="horizontal"
        android:paddingStart="3dp"
        android:paddingEnd="5dp"
        android:paddingBottom="2dp"
        android:weightSum="3">

        <ImageButton
            android:id="@+id/imageView_setting"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="bottom"
            android:layout_weight="0.84"
            android:background="@drawable/phn"
            android:contentDescription="@string/todo" />

        <ImageView
            android:id="@+id/warning"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:layout_weight="1.26"
            android:background="@drawable/warning"
            android:visibility="invisible" />

        <ToggleButton
            android:id="@+id/toggleButton1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.87"
            android:background="@drawable/check"
            android:checked="true"
            android:textOff=""
            android:textOn=""
            android:textSize="19sp" />


    </LinearLayout>
</LinearLayout>