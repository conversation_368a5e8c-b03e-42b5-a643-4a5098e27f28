#Thu Jul 31 11:35:06 EDT 2025
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_txt_username.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_txt_username.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/low_bat_green.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_low_bat_green.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/icons_meds_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_icons_meds_40.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_eye.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_eye.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_icon_semitrans_white.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_icon_semitrans_white.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/button_shape_green.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_shape_green.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/item_date.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_item_date.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/background_with_border.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_background_with_border.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_btn_login.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_btn_login.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/sample_circle.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_sample_circle.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_notifications.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_notifications.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/nurse_2.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_nurse_2.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_reminder.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_reminder.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/user_icon_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_user_icon_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/view_text_message_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_view_text_message_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/circular_round.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_circular_round.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/dash_chat.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_dash_chat.png.flat
com.watchrx.watchrxhealth.app-main-62\:/menu/menu_patient_diary.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\menu_menu_patient_diary.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_person.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vital_detail.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vital_detail.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_contact.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_contact.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_thermo.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_thermo.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/call_open.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_call_open.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/layout_confirmation_dialog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_layout_confirmation_dialog.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_messages_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_messages_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_video_call_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_video_call_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/calendar.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_calendar.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_wifi_config.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_wifi_config.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_bg_topheader.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bg_topheader.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_my_task_calendar.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_my_task_calendar.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/marker_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_marker_view.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_nurse_on_the_way.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_nurse_on_the_way.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_mic_white_off_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_mic_white_off_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/item_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_item_selector.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/raw/amplifyconfiguration.json=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\raw_amplifyconfiguration.json.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_pedometer_new.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_pedometer_new.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/blue_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_blue_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_sound.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_sound.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/mail.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_mail.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_custom_alerts.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_custom_alerts.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_alarm_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_alarm_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/button_login.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_login.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_contacts_list.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_contacts_list.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxhdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_watchrx_app_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/send_button_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_send_button_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_videocam_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_videocam_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_heart_rate_pink.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_heart_rate_pink.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_heart_filled.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_heart_filled.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_purple_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_purple_gradient.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_heart_rate.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_heart_rate.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/check.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_check.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_call_end_white_24px.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_call_end_white_24px.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_chat_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_chat_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/yes.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_yes.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_italic.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_round_bloodpressure_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_round_bloodpressure_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_heart.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_heart.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_volume_up_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_volume_up_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_pedometer.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_pedometer.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bp.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bp.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxhdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_watchrx_app_icon_background.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/dash_doc.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_dash_doc.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/answer_item_list.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_answer_item_list.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/dialog_bg.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_dialog_bg.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-anydpi-v26/watchrx_app_icon_round.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_watchrx_app_icon_round.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_red_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_red_gradient.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_no_alerts.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_no_alerts.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-hdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_watchrx_app_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_question.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_question.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/item_message_received.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_item_message_received.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/doctor.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_doctor.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/imeiwarning.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_imeiwarning.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-mdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_watchrx_app_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/user.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_user.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_medicine.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_medicine.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/green_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_green_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_spo2_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_spo2_40.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/dial_pink1_blue.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_dial_pink1_blue.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/nurse_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_nurse_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_enter_otpatictivity.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_enter_otpatictivity.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/pgoress_screen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_pgoress_screen.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_favorite_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_favorite_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/mic_on.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_mic_on.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/phn.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_phn.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_medication_details.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_medication_details.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_add_medication.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_add_medication.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_login_screen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login_screen.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/down_arrow.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_down_arrow.png.flat
com.watchrx.watchrxhealth.app-main-62\:/menu/refresh.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\menu_refresh.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-hdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_watchrx_app_icon_background.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_appointment_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_appointment_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-hdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_watchrx_app_icon_foreground.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/mic_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_mic_off.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bluetooth_orange.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bluetooth_orange.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/text_center.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_text_center.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_unselected_left.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_unselected_left.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/speaker_on.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_speaker_on.png.flat
com.watchrx.watchrxhealth.app-main-62\:/raw/awsconfiguration.json=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\raw_awsconfiguration.json.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_fill_heart.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_fill_heart.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_received_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_received_message.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/medical_assistance.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_medical_assistance.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/contact_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_contact_list_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_sms_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_sms_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_navigate_next.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_navigate_next.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_g_p_s.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_g_p_s.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/get_latest_measurement_record.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_get_latest_measurement_record.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_switch_camera_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_switch_camera_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/cutsom_progress_bar.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_cutsom_progress_bar.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_mic_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_mic_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/layout_corner.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_layout_corner.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_txt_password.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_txt_password.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_permissions_rationale.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_permissions_rationale.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_foot_steps.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_foot_steps.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/phone.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_phone.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_mic_off_black_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_mic_off_black_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/support_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_support_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_light_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_light_italic.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_thin_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_thin_italic.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/login_dialog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_login_dialog.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/menu/menu_video_activity.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\menu_menu_video_activity.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/paracetmaol.jpg=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_paracetmaol.jpg.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vitals_health_connect.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vitals_health_connect.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_dot.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_dot.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/bluetooth_device_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_bluetooth_device_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/full_bat_green.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_full_bat_green.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_heart_rate.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_heart_rate.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_calorie.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_calorie.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_call.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/right_arrow.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_right_arrow.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_incoming_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_incoming_call.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/sleep_monitor.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_sleep_monitor.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_eye_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_eye_off.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/custom_calendar_day.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_custom_calendar_day.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_green_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_green_gradient.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/turn_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_turn_off.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/font.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_font.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxxhdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_watchrx_app_icon_background.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/speaker_off.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_speaker_off.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxhdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_watchrx_app_icon_round.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-hdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_watchrx_app_icon_round.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/reminder.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_reminder.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/logo.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_logo.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_regular.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_regular.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxxhdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_watchrx_app_icon_foreground.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_bold.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_bold.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/vital_details_lit_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_vital_details_lit_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_circle_white.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_circle_white.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_patient_diary.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_patient_diary.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_heart_pulse.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_heart_pulse.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/call_end.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_call_end.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_icon_white.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_icon_white.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/reply_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_reply_view.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/home_logo.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_home_logo.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_medication.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_medication.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/menu/bottom_nav_menu.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\menu_bottom_nav_menu.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/text.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_text.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_text_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_text_selector.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/color/toggle_text_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\color_toggle_text_selector.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_medications_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_medications_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-hdpi/ic_loading.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_loading.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/dummy_lay.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_dummy_lay.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_temp_f.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_temp_f.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_zoom_video_call_screen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_zoom_video_call_screen.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vital_dashboard.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vital_dashboard.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/fm_person.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_fm_person.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/gradient_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_gradient_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/support.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_support.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/custom_action_bar.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_custom_action_bar.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/clock.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_clock.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_enter_password.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_enter_password.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/shape_bg_incoming_bubble.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_shape_bg_incoming_bubble.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_bloodtype_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_bloodtype_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_blood_sugar.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_blood_sugar.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-hdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-hdpi_ic_launcher.png.flat
com.watchrx.watchrxhealth.app-main-62\:/xml/settings.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\xml_settings.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/voice_assistance.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_voice_assistance.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_sleep_monitor.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_sleep_monitor.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_selector.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_message.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/center_circle.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_center_circle.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxhdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_launcher.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_view_all_text_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_view_all_text_message.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_text_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_text_message.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_btn_welcome.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_btn_welcome.png.flat
com.watchrx.watchrxhealth.app-main-62\:/raw/video_call_tone.mpeg=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\raw_video_call_tone.mpeg.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-mdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_watchrx_app_icon_round.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_thermo_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_thermo_40.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/resend_otp_button.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_resend_otp_button.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/rounded_corners.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_corners.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxxhdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_watchrx_app_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/xml/custom_prog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\xml_custom_prog.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/button_shape_red.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_shape_red.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_bluetooth.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bluetooth.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/rounded_corner_img.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_corner_img.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_alert.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_alert.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_vitals_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_vitals_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_edittext.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_edittext.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_selector_left.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_selector_left.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/item_event.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_item_event.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxhdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_watchrx_app_icon_foreground.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_organge_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_organge_gradient.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/custom_dialog.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_custom_dialog.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/vital_signs.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_vital_signs.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xhdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_ic_launcher.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/call_24.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_call_24.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_health_vitals.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_health_vitals.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/list_view_custom.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_list_view_custom.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/custom_reply_layout.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_custom_reply_layout.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/custome_loader.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_custome_loader.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-mdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_watchrx_app_icon_background.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_main_dashboard.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main_dashboard.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_login.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_login.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_video.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_video.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_refresh_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_refresh_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/dial.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_dial.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/mute_sound.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_mute_sound.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/message.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_message.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_black_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_black_italic.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_video_call_actiity.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_video_call_actiity.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/button_shape.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_shape.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_splash.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_splash.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_reminder_details.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_reminder_details.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/sample_three_icons.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_sample_three_icons.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_meds_filled.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_meds_filled.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/answer_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_answer_list_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/circle_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/notification_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_notification_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/dial_pink.PNG=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_dial_pink.PNG.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_videocam_off_black_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_videocam_off_black_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_daughter_reminder.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_daughter_reminder.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_device_thermostat_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_device_thermostat_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/geo_fecne.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_geo_fecne.png.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxxhdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_watchrx_app_icon_round.png.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_black.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_black.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_emergency.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_emergency.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/custom_day_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_custom_day_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/progress_bg.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_progress_bg.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_visit_verification.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_visit_verification.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/dialog_loading.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_dialog_loading.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/icons8_thermometer_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_icons8_thermometer_40.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_plus.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_plus.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_help_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_help_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_battery.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_battery.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/blue_outline.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_blue_outline.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vitals_graph.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vitals_graph.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xhdpi/watchrx_app_icon_round.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_watchrx_app_icon_round.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_reminders_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_reminders_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/vital_card_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_vital_card_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/capsules.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_capsules.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/medication_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_medication_list_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bluetooth_searching.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bluetooth_searching.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_light.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_light.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/content_video.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_content_video.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_answer.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_answer.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_phonelink_ring_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_phonelink_ring_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/vital_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_vital_list_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/xml/filepaths.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\xml_filepaths.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/utensils.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_utensils.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_launcher_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_medical_services_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_medical_services_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/later.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_later.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_chat_40_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_chat_40_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_weight_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_weight_40.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/preference_dialog_number_edittext.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_preference_dialog_number_edittext.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/drugs.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_drugs.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_directions_walk_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_directions_walk_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_resend_otpscreen.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_resend_otpscreen.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_selected_left.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_selected_left.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/gray_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_gray_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_headset_mic_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_headset_mic_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_bluetooth_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_bluetooth_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_dashboard.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_dashboard.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_cancel.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_cancel.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/reminder_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_reminder_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/send.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_send.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_thermo_64.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_thermo_64.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_weigt.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_weigt.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_medical.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_medical.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_appointment_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_appointment_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bluetooth_connected.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bluetooth_connected.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_stop_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_stop_call.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/no.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_no.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_unselected_right.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_unselected_right.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/rounded_logo_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_logo_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_chat_40.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_chat_40.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/item_count.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_item_count.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_selected_right.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_selected_right.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_latest_alerts.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_latest_alerts.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_medication.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_medication.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bordered_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bordered_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xhdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_watchrx_app_icon_foreground.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_zoom_video_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_zoom_video_call.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/appointment.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_appointment.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/circle.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_circle.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/custom_thumb.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_custom_thumb.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/alert_list_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_alert_list_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_meds.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_meds.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/med_details_item.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_med_details_item.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/app_logo_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_app_logo_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_sent_message.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_sent_message.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/unmute_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_unmute_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/mute_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_mute_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vital.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vital.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_main.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_main.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/sleep.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_sleep.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/watch.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_watch.png.flat
com.watchrx.watchrxhealth.app-main-62\:/anim/rotate.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\anim_rotate.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/conta_name_shape.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_conta_name_shape.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/menu/menu.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\menu_menu.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/medicine.jpg=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_medicine.jpg.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_interactive_voice.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_interactive_voice.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_interaction.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_interaction.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vitals_measurement.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vitals_measurement.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_thin.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_thin.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_launcher_foreground.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xhdpi/watchrx_app_icon_background.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_watchrx_app_icon_background.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_baseline_error_outline_24.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_baseline_error_outline_24.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bottom_navigation_selector.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bottom_navigation_selector.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_photo_user.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_photo_user.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_chat.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_chat.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-mdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_ic_launcher.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/text_phone.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_text_phone.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/custom_progress_dialog_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_custom_progress_dialog_view.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_vital_details.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_vital_details.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxxhdpi/ic_launcher.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxxhdpi_ic_launcher.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_new_vitals.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_new_vitals.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_web_view.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_web_view.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/activity_bluetooth_devices.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_activity_bluetooth_devices.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_call_black_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_call_black_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_chat_1.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_chat_1.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/nurse.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_nurse.png.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/item_message_sent.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_item_message_sent.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/layout/text_answer_sq.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\layout_text_answer_sq.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xxhdpi/ic_blue_connected.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xxhdpi_ic_blue_connected.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_spo2.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_spo2.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_call_end_white_24dp.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_call_end_white_24dp.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-xhdpi/watchrx_app_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-xhdpi_watchrx_app_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_video_call.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_video_call.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/rounded_background.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_background.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/pedometer_item_bg.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_pedometer_item_bg.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/button_gradient.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_button_gradient.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/font/lato_bold_italic.ttf=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\font_lato_bold_italic.ttf.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-anydpi-v26/watchrx_app_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-anydpi-v26_watchrx_app_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/rounded_edittext.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_rounded_edittext.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/toggle_selector_right.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_toggle_selector_right.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/mipmap-mdpi/watchrx_app_icon_foreground.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\mipmap-mdpi_watchrx_app_icon_foreground.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_person_icon.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_person_icon.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/deafult_medicine.PNG=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_deafult_medicine.PNG.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/comments.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_comments.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/warning.png=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_warning.png.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/bg_summary_icon.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_bg_summary_icon.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/shape_bg_outgoing_bubble.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_shape_bg_outgoing_bubble.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/xml/accessibility_service_config.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\xml_accessibility_service_config.xml.flat
com.watchrx.watchrxhealth.app-main-62\:/drawable/ic_success.xml=C\:\\Users\\sriva\\Documents\\GitHub\\watchrxwatch\\app\\build\\intermediates\\merged_res\\debug\\drawable_ic_success.xml.flat
