<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <shape android:shape="rectangle">
            <!--make a gradient background-->
            <corners android:radius="@dimen/d_12_d" />
            <!-- make the button corners rounded-->
            <gradient android:endColor="@color/white" android:startColor="@color/grey" android:type="linear" />

            <stroke android:width="1dp" android:color="@color/red" />
        </shape>
    </item>
</selector>