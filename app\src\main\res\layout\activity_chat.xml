<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Wrapper for RecyclerView and ProgressBar -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- RecyclerView for Chat Messages -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp" />

        <!-- ProgressBar for Loading Messages at Center -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true"
            android:visibility="gone" />
    </FrameLayout>

    <!-- Input and Send Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp">

        <!-- Message Input Field -->
        <EditText
            android:id="@+id/edit_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.8"
            android:background="@drawable/bg_edittext"
            android:fontFamily="@font/lato_regular"
            android:hint="Type a message"
            android:inputType="text"
            android:padding="8dp" />

        <!-- Spacer -->
        <View
            android:layout_width="8dp"
            android:layout_height="match_parent" />

        <!-- Send Button -->
        <Button
            android:id="@+id/button_send"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_weight="0.3"
            android:background="@drawable/circle_background"
            android:fontFamily="@font/lato_regular"
            android:gravity="center"
            android:padding="4dp"
            android:text="SEND"
            android:textColor="#FFFFFF"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>
