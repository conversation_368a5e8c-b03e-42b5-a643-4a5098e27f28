package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.util.Date;

public class NetworkHeartBeat extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        Log.e("NetworkHeartBeat", "NetworkHeartBeat Triggered" + new Date());
        context.sendBroadcast(new Intent("com.google.android.intent.action.GTALK_HEARTBEAT"));
        context.sendBroadcast(new Intent("com.google.android.intent.action.MCS_HEARTBEAT"));
        ReminderUtils.setReminderForHeartBeatPingToNetwork(context);
    }
}
