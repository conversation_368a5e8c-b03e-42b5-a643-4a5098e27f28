<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cardView"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:layout_marginTop="@dimen/d_10_d"
    app:cardCornerRadius="@dimen/d_12_d">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/item_selector"
        android:orientation="horizontal"
        android:weightSum="2">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1.7"
            android:orientation="vertical"
            android:weightSum="2">

            <TextView
                android:id="@+id/bleName"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/d_5_d"
                android:layout_weight="1"
                android:fontFamily="@font/lato_bold"
                android:textColor="@color/light_blue"
                android:textSize="@dimen/d_25_s"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/bleMacId"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/d_5_d"
                android:layout_weight="1"
                android:fontFamily="@font/lato_light"
                android:textColor="@color/black"
                android:textSize="@dimen/d_20_s"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/bleName" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:orientation="horizontal"
            android:weightSum="1">

            <ImageView
                android:id="@+id/bluetooth_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/bluetooth_orange" />

        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>