<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">

    <View
        android:id="@+id/bg_top_header"
        android:layout_width="match_parent"
        android:layout_height="230dp"
        android:background="@drawable/ic_bg_topheader"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="80dp"
        android:layout_marginBottom="60dp"
        android:alignmentMode="alignMargins"
        android:columnCount="2"
        android:columnOrderPreserved="false"
        android:padding="14dp"
        android:rowCount="5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="@id/bg_top_header"
        app:layout_constraintVertical_bias="0.0"
        app:layout_editor_absoluteX="0dp">

        <androidx.cardview.widget.CardView
            android:id="@+id/heartRateCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="0"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/purple_500"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/d_25_d"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_heart_rate" />

                <TextView
                    android:id="@+id/heartRateTxt"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/pedometerCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="0"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/red"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_baseline_directions_walk_24" />

                <TextView
                    android:id="@+id/stepsCount"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/weightCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="1"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/purple_200"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/d_25_d"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_weight_40" />

                <TextView
                    android:id="@+id/weightTxt"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cvMedication"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="1"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/orange"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="25dp"
                    android:layout_weight="1.2"
                    android:src="@drawable/icons_meds_40" />

                <TextView
                    android:id="@+id/tvMedication"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/temperatureCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="2"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/green"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="25dp"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_thermo_40" />

                <TextView
                    android:id="@+id/temperatureTxt"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/textMessageCardView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="2"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/light_grey"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/d_25_d"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_chat_1" />

                <TextView
                    android:id="@+id/textMessageCount"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/bloodSugarCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="3"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/teal_700"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/d_25_d"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_blood_sugar" />

                <TextView
                    android:id="@+id/bloodSugarText"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/spo2Cv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="3"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/light_b"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1.2"
                    android:src="@drawable/ic_baseline_bloodtype_24" />

                <TextView
                    android:id="@+id/spo2Data"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/bloodPressureCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="4"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="0"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/light_blue"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <TextView
                    android:id="@+id/bloodPressureTextView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/blood_pressure"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:orientation="horizontal"
                    android:layout_weight="1">
                    <TextView
                        android:id="@+id/sysBloodPressureText"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="@string/_0"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dashboard_vital_text"
                        android:fontFamily="@font/lato_regular" />

                    <TextView
                        android:id="@+id/diaBloodPressureText"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.8"
                        android:gravity="center_horizontal|center_vertical"
                        android:text="@string/_0"
                        android:textAlignment="center"
                        android:textColor="@color/white"
                        android:textSize="@dimen/dashboard_vital_text"
                        android:visibility="gone"
                        android:fontFamily="@font/lato_regular" />
                </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>
        <androidx.cardview.widget.CardView
            android:id="@+id/sleepCv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_row="4"
            android:layout_rowSpan="1"
            android:layout_rowWeight="1"
            android:layout_column="1"
            android:layout_columnSpan="1"
            android:layout_columnWeight="1"
            android:layout_margin="5dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_horizontal|center_vertical"
                android:background="@color/teal_200"
                android:gravity="center"
                android:orientation="vertical"
                android:weightSum="2">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1.2"
                    android:src="@drawable/sleep" />

                <TextView
                    android:id="@+id/sleepTime"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="0.8"
                    android:gravity="center_horizontal|center_vertical"
                    android:text="@string/_0"
                    android:textAlignment="center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/dashboard_vital_text"
                    android:fontFamily="@font/lato_regular" />
            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </GridLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal|center_vertical"
        android:gravity="center"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:id="@+id/cvDate"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/d_12_d">

            <TextClock
                android:id="@+id/date"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:fontFamily="@font/lato_bold"
                android:format12Hour="hh:mm:ss"
                android:format24Hour="hh:mm:ss"
                android:gravity="center"
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="@dimen/d_25_s" />
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/cvTime"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/d_12_d">

            <TextClock
                android:id="@+id/time"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:fontFamily="@font/lato_bold"
                android:format12Hour="EEE, MMM d"
                android:format24Hour="EEE, MMM d"
                android:gravity="center"
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textSize="@dimen/d_25_s" />
        </androidx.cardview.widget.CardView>
    </LinearLayout>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />
</androidx.constraintlayout.widget.ConstraintLayout>