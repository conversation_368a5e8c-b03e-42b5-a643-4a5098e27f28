package com.watchrx.watchrxhealth.syncup;


import android.os.Environment;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;

public class ReadDataFromMemory {

    public static String getDataFromMemory() {
        String data = "";
        File rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        File file = new File(rootPath + "/WatchRx_DataBase/", "WatchRx.txt");

        if (file.exists()) {
            try {
                InputStream fis = new FileInputStream(file);
                DataInputStream in = new DataInputStream(fis);
                BufferedReader br = new BufferedReader(new InputStreamReader(in));
                String strLine;
                while ((strLine = br.readLine()) != null) {
                    data = data + strLine;
                }
                in.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return data;
    }

    static String getDataForSync() {
        String data = "";
        File rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        File file = new File(rootPath + "/WatchRx_DataBase/SyncDatabase", "WatchRx.txt");

        if (file.exists()) {
            try {
                InputStream fis = new FileInputStream(file);
                DataInputStream in = new DataInputStream(fis);
                BufferedReader br = new BufferedReader(new InputStreamReader(in));
                String strLine;
                while ((strLine = br.readLine()) != null) {
                    data = data + strLine;
                }
                in.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return data;
    }

    static void DeleteDirectory() {
        File rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        File datafile = new File(rootPath + "/WatchRx_DataBase/SyncDatabase", "WatchRx.txt");

        if (datafile.exists()) {
            datafile.delete();
        }

        File myDir = new File(rootPath + "/WatchRx_DataBase/SyncDatabase/WatchRx_Images");
        deleteDir(myDir);

    }

    private static boolean deleteDir(File dir) {
        //File rootPath = Environment.getExternalStorageDirectory();
        //File myDir = new File(rootPath + "/WatchRx_DataBase/SyncDatabase/WatchRx_Images");
        if (dir.isDirectory()) {
            String[] children = dir.list();
            for (String aChildren : children) {
                new File(dir, aChildren).delete();
            }
        }
        return dir.delete();
    }
}

