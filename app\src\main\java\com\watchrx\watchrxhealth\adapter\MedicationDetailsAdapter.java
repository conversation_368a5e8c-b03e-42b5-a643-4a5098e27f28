package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.res.ResourcesCompat;
import androidx.gridlayout.widget.GridLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.MedicationDetailsModel;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class MedicationDetailsAdapter extends RecyclerView.Adapter<MedicationDetailsAdapter.MyViewHolder> {
    Context mContext;
    List<MedicationDetailsModel> detailsModelsList;

    public interface OnItemClickListener {
        void onItemClick(MedicationDetailsModel item);
    }

    private final MedicationDetailsAdapter.OnItemClickListener listener;

    public MedicationDetailsAdapter(Context context, List<MedicationDetailsModel> detailsModelsList, MedicationDetailsAdapter.OnItemClickListener listener) {
        this.mContext = context;
        this.detailsModelsList = detailsModelsList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public MedicationDetailsAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.medication_list_item, parent, false);
        return new MedicationDetailsAdapter.MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull MedicationDetailsAdapter.MyViewHolder holder, int position) {
        MedicationDetailsModel detailsModel = detailsModelsList.get(position);
        holder.medicineName.setText(detailsModel.getMedicineName());
        holder.medicineColor.setText(MessageFormat.format("Color: {0}", detailsModel.getColor()));
        holder.schedule.setText(MessageFormat.format("Schedule:{0} ({1} times a day)", convertDaysString(detailsModel.getDaysOfWeek()), noOfMedsPerDay(detailsModel)));
        byte[] medsImage = detailsModel.getMedicineImage();
        Bitmap bitmap = BitmapFactory.decodeByteArray(medsImage, 0, medsImage.length);
        if (bitmap != null) {
            holder.imageView.setImageBitmap(bitmap);
        } else {
//            holder.imageView.setImageResource(R.drawable.deafult_medicine);
            holder.imageView.setVisibility(View.GONE);
        }
        holder.bind(detailsModel, listener);
        extractTimeSlots(holder, detailsModel);
    }

    public static String convertDaysString(String daysString) {
        String[] dayCodes = {"Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"};
        String[] dayNames = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
        String[] selectedDays = daysString.split("\\|");
        if (selectedDays.length == 7) {
            return "Daily";
        }
        StringBuilder weeklyString = new StringBuilder("Weekly (");
        for (String day : selectedDays) {
            int index = Arrays.asList(dayCodes).indexOf(day);
            if (index >= 0) {
                weeklyString.append(dayNames[index]).append(", ");
            }
        }
        if (weeklyString.length() > 7) {
            weeklyString.setLength(weeklyString.length() - 2);
        }
        weeklyString.append(")");
        return weeklyString.toString();
    }

    public static String convertTo12HourFormat(String time24Hour) {
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("HH:mm", Locale.US);
            SimpleDateFormat outputFormat = new SimpleDateFormat("hh:mm a", Locale.US);

            Date date = inputFormat.parse(time24Hour);
            assert date != null;
            return outputFormat.format(date);
        } catch (ParseException e) {
            e.printStackTrace();
            return "";
        }
    }

    private int noOfMedsPerDay(MedicationDetailsModel detailsModel) {
        return detailsModel.getTimeSlots().split("\\|").length;
    }

    private void extractTimeSlots(MyViewHolder holder, MedicationDetailsModel detailsModel) {
        String[] times = detailsModel.getTimeSlots().split("\\|");
        String[] quantities = detailsModel.getQuantity().split("\\|");
        String[] beforeOrAfterList = detailsModel.getBeforeOrAfter().split("\\|");

        String[][] schedule = generateSchedule(times, beforeOrAfterList, quantities, detailsModel.getBeforeOrAfter());
        holder.timingGrid.removeAllViews();
        for (String[] timing : schedule) {
            LinearLayout timingLayout = new LinearLayout(holder.itemView.getContext());
            timingLayout.setOrientation(LinearLayout.HORIZONTAL);
            timingLayout.setGravity(Gravity.CENTER_VERTICAL);
            timingLayout.setPadding(8, 8, 8, 8);

            ImageView timeSlotIcon = new ImageView(holder.itemView.getContext());
            timeSlotIcon.setImageResource(R.drawable.clock);
            timeSlotIcon.setLayoutParams(new LinearLayout.LayoutParams(40, 40));
            timingLayout.addView(timeSlotIcon);

            Typeface customTypeface = ResourcesCompat.getFont(holder.itemView.getContext(), R.font.lato_regular);

            TextView timeSlotText = new TextView(holder.itemView.getContext());
            timeSlotText.setText(timing[0]);
            timeSlotText.setTextSize(16f);
            timeSlotText.setTypeface(customTypeface, Typeface.NORMAL);
            timeSlotText.setPadding(8, 0, 0, 0);
            timingLayout.addView(timeSlotText);
            if (!timing[1].isEmpty()) {
                ImageView foodIcon = new ImageView(holder.itemView.getContext());
                foodIcon.setImageResource(R.drawable.utensils);
                foodIcon.setLayoutParams(new LinearLayout.LayoutParams(60, 60));
                foodIcon.setPadding(16, 0, 0, 0);
                timingLayout.addView(foodIcon);
            }
            TextView foodInstructionText = new TextView(holder.itemView.getContext());
            foodInstructionText.setText(timing[1]);
            foodInstructionText.setTextSize(16f);
            foodInstructionText.setTypeface(customTypeface, Typeface.NORMAL);
            foodInstructionText.setPadding(8, 0, 0, 0);
            timingLayout.addView(foodInstructionText);

            GridLayout.LayoutParams params = new GridLayout.LayoutParams();
            params.columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f);
            timingLayout.setLayoutParams(params);
            holder.timingGrid.addView(timingLayout);
        }
    }

    public static String[][] generateSchedule(String[] times, String[] beforeOrAfterList, String[] quantities, String isFixed) {
        String[][] schedule = new String[times.length][2];
        for (int i = 0; i < times.length; i++) {
            if (isFixed.isEmpty() || isFixed.equals("Fixed")) {
                schedule[i][0] = convertTo12HourFormat(times[i]) + " - (" + quantities[i] + ")";
                schedule[i][1] = "";
            } else {
                schedule[i][0] = convertCamelCaseToWords(times[i]);
                schedule[i][1] = convertCamelCaseToWords(beforeOrAfterList[i]) + " - (" + quantities[i] + ")";
            }
        }
        return schedule;
    }

    public static String convertCamelCaseToWords(String input) {
        return input.replaceAll("([a-z])([A-Z])", "$1 $2");
    }

    @Override
    public int getItemCount() {
        return detailsModelsList.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView medicineName, medicineColor, schedule;
        GridLayout timingGrid;
        ImageView imageView;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            medicineName = itemView.findViewById(R.id.medicineName);
            medicineColor = itemView.findViewById(R.id.medicineColor);
            schedule = itemView.findViewById(R.id.schedule);
            timingGrid = itemView.findViewById(R.id.timingGrid);
            imageView = itemView.findViewById(R.id.medicineImage);
        }

        public void bind(final MedicationDetailsModel item, final MedicationDetailsAdapter.OnItemClickListener listener) {
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(item);
                }
            });
        }
    }
}
