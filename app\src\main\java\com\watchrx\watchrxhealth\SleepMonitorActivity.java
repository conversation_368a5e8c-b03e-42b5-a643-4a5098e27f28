package com.watchrx.watchrxhealth;

import android.app.TimePickerDialog;
import android.os.Bundle;
import android.text.InputType;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.VitalDetailsAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.LatestVitalDataModel;
import com.watchrx.watchrxhealth.models.VitalDataModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class SleepMonitorActivity extends AppCompatActivity implements View.OnClickListener {

    private EditText sleepStartTimeET, sleepEndTimeET;
    private String sleepStart, sleepEnd;
    private RecyclerView recyclerView;
    private final String vitalType = "Sleep Monitor";
    private TextView mTxtViewDate;
    private ImageView mImageViewPrevious, mImageViewNext;
    private TextView noData;
    private ProgressBar progressbar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sleep_monitor);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        sleepStartTimeET = (EditText) findViewById(R.id.sleepStartTime);
        sleepEndTimeET = (EditText) findViewById(R.id.sleepEndTime);
        sleepStartTimeET.setInputType(InputType.TYPE_NULL);
        sleepEndTimeET.setInputType(InputType.TYPE_NULL);

        sleepStartTimeET.setOnClickListener(this);
        sleepEndTimeET.setOnClickListener(this);

        noData = findViewById(R.id.nodata);
        progressbar = findViewById(R.id.progressbar);

        sleepStart = PatientDetails.getFromDB().getSleepStartTime();
        sleepEnd = PatientDetails.getFromDB().getSleepEndTime();

        if (sleepStart == null || sleepStart.isEmpty()) {
            sleepStart = "21:00";
        }

        if (sleepEnd == null || sleepEnd.isEmpty()) {
            sleepEnd = "7:00";
        }

        sleepStartTimeET.setText(sleepStart);
        sleepEndTimeET.setText(sleepEnd);

        Button updateSettings = findViewById(R.id.updateSettings);
        updateSettings.setOnClickListener(this);

        recyclerView = findViewById(R.id.vitalRv);

        mTxtViewDate = findViewById(R.id.date);
        mImageViewPrevious = findViewById(R.id.before_date);
        mImageViewNext = findViewById(R.id.after_date);
        String currentDate = new SimpleDateFormat("MMM-yyyy", Locale.getDefault()).format(new Date());
        mTxtViewDate.setText(currentDate);
        setListener();
        getVitalDetails();
    }

    public void getDateRange(Calendar calendar) {
        Date startDay, ednDay;
        {
            calendar.set(Calendar.DAY_OF_MONTH,
                    calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            setTimeToBeginningOfDay(calendar);
            startDay = calendar.getTime();
        }
        {
            calendar.set(Calendar.DAY_OF_MONTH,
                    calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            setTimeToEndOfDay(calendar);
            ednDay = calendar.getTime();
        }
        Date currentDate = new Date(System.currentTimeMillis());
        if (ednDay.before(currentDate) || ednDay.equals(currentDate)) {
            getVitalDetailsByDate(startDay, ednDay);
        } else {
            ednDay = new Date(System.currentTimeMillis());
            getVitalDetailsByDate(startDay, ednDay);
        }
    }

    private static void setTimeToBeginningOfDay(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    private static void setTimeToEndOfDay(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }

    private void setListener() {
        mImageViewPrevious.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String sDate = mTxtViewDate.getText().toString();
                SimpleDateFormat dateFormat = new SimpleDateFormat("MMM-yyyy", Locale.getDefault());
                Date date = null;
                try {
                    date = dateFormat.parse(sDate);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.MONTH, -1);
                String yesterdayAsString = dateFormat.format(calendar.getTime());
                mTxtViewDate.setText(yesterdayAsString);
                getDateRange(calendar);
            }
        });

        mImageViewNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String sDate = mTxtViewDate.getText().toString();
                SimpleDateFormat dateFormat = new SimpleDateFormat("MMM-yyyy", Locale.getDefault());
                Date date = null;
                try {
                    date = dateFormat.parse(sDate);
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.MONTH, 1);
                if (calendar.getTime().before(new Date())) {
                    String nextAsString = dateFormat.format(calendar.getTime());
                    mTxtViewDate.setText(nextAsString);
                    getDateRange(calendar);
                }
            }
        });
    }

    @Override
    public void onClick(View v) {
        TimePickerDialog picker;
        if (v.getId() == R.id.sleepStartTime) {
            int hourOfDay = 21;
            int minute = 0;
            if (sleepStart != null && !sleepStart.isEmpty()) {
                String[] sleepStartArr = sleepStart.split(":");
                hourOfDay = Integer.parseInt(sleepStartArr[0]);
                minute = Integer.parseInt(sleepStartArr[1]);

            }
            picker = new TimePickerDialog(SleepMonitorActivity.this, 3,
                    new TimePickerDialog.OnTimeSetListener() {
                        @Override
                        public void onTimeSet(TimePicker tp, int sHour, int sMinute) {
                            sleepStartTimeET.setText(String.format(Locale.ENGLISH, "%d:%d", sHour, sMinute));
                        }
                    }, hourOfDay, minute, false);
            picker.setTitle("Sleep Start");
            picker.show();
        } else if (v.getId() == R.id.sleepEndTime) {
            int hourOfDay = 21;
            int minute = 0;
            if (sleepEnd != null && !sleepEnd.isEmpty()) {
                String[] sleepStartArr = sleepEnd.split(":");
                hourOfDay = Integer.parseInt(sleepStartArr[0]);
                minute = Integer.parseInt(sleepStartArr[1]);

            }
            picker = new TimePickerDialog(SleepMonitorActivity.this, 3,
                    new TimePickerDialog.OnTimeSetListener() {
                        @Override
                        public void onTimeSet(TimePicker tp, int sHour, int sMinute) {
                            sleepEndTimeET.setText(String.format(Locale.ENGLISH, "%d:%d", sHour, sMinute));
                        }
                    }, hourOfDay, minute, false);
            picker.setTitle("Sleep End");
            picker.show();
        } else if (v.getId() == R.id.updateSettings) {
            updateSleepSettings(sleepStartTimeET.getText().toString(), sleepEndTimeET.getText().toString());
        }
    }

    private void updateSleepSettings(String startTime, String endTime) {
        try {
            URL url = new URL(URLConstants.SLEEP_UPDATE);
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("sleepStartTime", startTime);
            jsonObject.accumulate("sleepEndTime", endTime);
            jsonObject.accumulate("patientId", PatientDetails.getFromDB().getPatientId());

            PatientDetails.updateSleepTime(PatientDetails.getFromDB().getPatientId(), startTime, endTime);
            new RestAsyncTask(url, jsonObject.toString(), null, new SleepUpdateResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class SleepUpdateResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult result) {
            if (result.getResult() == null) {
                LogUtils.debug("Failed to Update Sleep Data");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseMessage").equalsIgnoreCase("Success")) {
                        LogUtils.debug("Sleep Data Configuration Updated Successfully.");
                        Toast.makeText(SleepMonitorActivity.this, "Sleep data updated successfully.", Toast.LENGTH_SHORT).show();
                    } else {
                        PatientDetails.updateSleepTime(PatientDetails.getFromDB().getPatientId(), "", "");
                        LogUtils.debug("Sleep Data Configuration Updated Failed.");
                        Toast.makeText(SleepMonitorActivity.this, "Sleep data update failed.", Toast.LENGTH_SHORT).show();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }

            }
        }
    }

    private void getVitalDetails() {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, 0);
            calendar.set(Calendar.DATE, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
            Date monthFirstDay = calendar.getTime();
            SimpleDateFormat startDateDF = new SimpleDateFormat("yyyy-MM-dd 00:00:00", Locale.ENGLISH);
            SimpleDateFormat currentDateDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            String startDateStr = startDateDF.format(monthFirstDay);
            String currentDate = currentDateDF.format(new Date(System.currentTimeMillis()));
            try {
                LogUtils.debug("Going to register the GCM");
                URL url = new URL(URLConstants.VITAL_DETAILS_DATA);
                String patientId = PatientDetails.getFromDB().getPatientId();
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate("patientId", patientId);
                jsonObject.accumulate("vitalType", vitalType);
                jsonObject.accumulate("startDate", startDateStr);
                jsonObject.accumulate("endDate", currentDate);
                Log.i("Request", jsonObject.toString());
                recyclerView.setVisibility(View.GONE);
                new RestAsyncTask(url, jsonObject.toString(), progressbar, new VitalsDetailsResponseHandler(), null).execute();
            } catch (MalformedURLException | JSONException e) {
                LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getVitalDetailsByDate(Date start, Date end) {
        try {
            SimpleDateFormat startDateDF = new SimpleDateFormat("yyyy-MM-dd 00:00:00", Locale.ENGLISH);
            SimpleDateFormat currentDateDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            String startDateStr = startDateDF.format(start);
            String currentDate = currentDateDF.format(end);
            try {
                LogUtils.debug("Going to register the GCM");
                URL url = new URL(URLConstants.VITAL_DETAILS_DATA);
                String patientId = PatientDetails.getFromDB().getPatientId();
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate("patientId", patientId);
                jsonObject.accumulate("vitalType", vitalType);
                jsonObject.accumulate("startDate", startDateStr);
                jsonObject.accumulate("endDate", currentDate);
                recyclerView.setVisibility(View.GONE);
                new RestAsyncTask(url, jsonObject.toString(), progressbar, new VitalsDetailsResponseHandler(), null).execute();
            } catch (MalformedURLException | JSONException e) {
                LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
                e.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class VitalsDetailsResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }

            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        LatestVitalDataModel responseMessage = new Gson().fromJson((String) result, LatestVitalDataModel.class);
                        if (responseMessage != null && responseMessage.isStatus() && responseMessage.getData() != null && responseMessage.getData().size() > 0) {
                            List<VitalDataModel> dataModelList = new ArrayList<>(responseMessage.data);

                            Collections.sort(dataModelList, new Comparator<VitalDataModel>() {
                                public int compare(VitalDataModel m1, VitalDataModel m2) {
                                    return m2.getDate().compareTo(m1.getDate());
                                }
                            });
                            //constraintLayout.setVisibility(View.GONE);
                            recyclerView.setVisibility(View.VISIBLE);
                            recyclerView.setHasFixedSize(true);
                            recyclerView.setLayoutManager(new LinearLayoutManager(SleepMonitorActivity.this));
                            recyclerView.setAdapter(new VitalDetailsAdapter(SleepMonitorActivity.this, dataModelList, new VitalDetailsAdapter.OnItemClickListener() {
                                @Override
                                public void onItemClick(VitalDataModel item) {
                                }
                            }));
                            recyclerView.invalidate();
                            Objects.requireNonNull(recyclerView.getAdapter()).notifyDataSetChanged();
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            noData.setVisibility(View.VISIBLE);
                            noData.setText(R.string.no_data_found);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}