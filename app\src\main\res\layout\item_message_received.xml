<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="start"
    android:orientation="vertical"
    android:padding="8dp">

    <TextView
        android:id="@+id/textMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_received_message"
        android:fontFamily="@font/lato_regular"
        android:padding="8dp"
        android:textColor="#000000"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/textTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/lato_regular"
        android:textColor="#999999"
        android:textSize="12sp" />
</LinearLayout>
