package com.watchrx.watchrxhealth;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.applandeo.materialcalendarview.CalendarDay;
import com.applandeo.materialcalendarview.CalendarView;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.EventsAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.Event;
import com.watchrx.watchrxhealth.models.EventResponse;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.DrawableUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class MyTaskCalendar extends AppCompatActivity {

    private RecyclerView eventsRecyclerView;
    private TextView noAppointmentsTextView;
    private final List<Event> events = new ArrayList<>();
    private List<CalendarDay> calendarDays;
    private CalendarView calendarView;
    private ProgressBar progressBar;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_my_task_calendar);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("Appointments");

        calendarView = findViewById(R.id.calendarView);
        eventsRecyclerView = findViewById(R.id.eventsRecyclerView);
        noAppointmentsTextView = findViewById(R.id.noAppointmentsTextView);
        progressBar = findViewById(R.id.loader);

        calendarView.setVisibility(View.GONE);
        getAllEvents();
        calendarDays = new ArrayList<>();

        calendarView.setOnDayClickListener(eventDay -> {
            Calendar clickedDate = eventDay.getCalendar();
            List<Event> filteredEvents = filterEventsByDate(clickedDate);

            if (filteredEvents.isEmpty()) {
                noAppointmentsTextView.setVisibility(View.VISIBLE);
                eventsRecyclerView.setVisibility(View.GONE);
            } else {
                noAppointmentsTextView.setVisibility(View.GONE);
                eventsRecyclerView.setVisibility(View.VISIBLE);
                setupRecyclerView(filteredEvents);
            }
        });
    }

    private void markEventDates() {
        for (Event event : events) {
            try {
                Calendar eventDate = Calendar.getInstance();
                eventDate.setTime(Objects.requireNonNull(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
                        .parse(event.getStart())));
                CalendarDay calendarDay = new CalendarDay(eventDate);
                calendarDay.setImageDrawable(DrawableUtils.getThreeDots(this));
                calendarDays.add(calendarDay);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        calendarView.setCalendarDays(calendarDays);
    }

    private void setupRecyclerView(List<Event> eventList) {
        EventsAdapter adapter = new EventsAdapter(this, eventList);
        eventsRecyclerView.setLayoutManager(new LinearLayoutManager(this));
        eventsRecyclerView.setAdapter(adapter);
    }

    private List<Event> filterEventsByDate(Calendar date) {
        List<Event> filteredEvents = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String selectedDate = dateFormat.format(date.getTime());
        for (Event event : events) {
            if (event.getStart().startsWith(selectedDate)) {
                filteredEvents.add(event);
            }
        }
        return filteredEvents;
    }


    private void getAllEvents() {
        try {
            if (events != null && events.size() > 0) {
                events.clear();
            }
            URL url = new URL(URLConstants.EVENTS);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            new RestAsyncTask(url, jsonObject.toString(), progressBar, new MyTaskCalendar.EventsResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }

    private class EventsResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            calendarView.setVisibility(View.VISIBLE);
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }
            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        EventResponse responseMessage = new Gson().fromJson((String) result, EventResponse.class);
                        if (responseMessage != null && responseMessage.isStatus() && responseMessage.getData() != null && responseMessage.getData().size() > 0) {
                            for (Event event : responseMessage.getData()) {
                                events.add(new Event(
                                        event.getId(),
                                        event.getTitle(),
                                        event.getDescription(),
                                        event.getStart(),
                                        event.getEnd(),
                                        event.getPriority()));
                            }
                            markEventDates();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}