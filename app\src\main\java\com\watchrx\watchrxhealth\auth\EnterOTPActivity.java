package com.watchrx.watchrxhealth.auth;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.goodiebag.pinview.Pinview;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.SplashActivity;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONObject;

import java.net.URL;
import java.util.Objects;

public class EnterOTPActivity extends AppCompatActivity {

    private Pinview pinview;
    private Dialog loadingDialog;
    private Button loginButton;
    private String userName, password;
    public SharedPreferences sharedpreferences;
    public static final String mypreference = "WatchRx";
    public static final String userNameKey = "username";
    public static final String passwordKey = "password";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_enter_otpatictivity);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.layout), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        setupLoadingDialog();
        sharedpreferences = WatchApp.getContext().getSharedPreferences(mypreference, Context.MODE_PRIVATE);
        Objects.requireNonNull(getSupportActionBar()).hide();
        userName = getIntent().getStringExtra("userName");
        password = getIntent().getStringExtra("password");
        pinview = (Pinview) findViewById(R.id.otpET);
        pinview.setFocusable(true);
        pinview.setPinViewEventListener(new Pinview.PinViewEventListener() {
            @Override
            public void onDataEntered(Pinview pinview, boolean fromUser) {
                loginMethod(userName, password, pinview.getValue());
            }
        });
    }

    private void setupLoadingDialog() {
        loadingDialog = new Dialog(this);
        loadingDialog.setContentView(R.layout.dialog_loading);
        loadingDialog.setCancelable(false);

        loadingDialog.setOnShowListener(dialog -> {
            ImageView loaderImage = loadingDialog.findViewById(R.id.loaderImage);
            Animation rotateAnimation = AnimationUtils.loadAnimation(this, R.anim.rotate);
            loaderImage.startAnimation(rotateAnimation);
        });
    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    private void loginMethod(String email, String password, String otp) {
        try {
            showLoadingDialog();
            closeKeyboard();
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.VALIDATE_OTP);
            loginModel.emailId = email;
            loginModel.password = password;
            loginModel.otp = otp;
            String json = new Gson().toJson(loginModel);
            SharedPreferences.Editor editor = sharedpreferences.edit();
            editor.putString(userNameKey, email);
            editor.putString(passwordKey, password);
            editor.apply();
            new RestAsyncTask(url, json, null, new ValidateOTPResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showLoadingDialog() {
        if (loadingDialog == null) {
            setupLoadingDialog();
        }

        if (!loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }

    private class ValidateOTPResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            loadingDialog.dismiss();
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Toast.makeText(EnterOTPActivity.this, "Unable to login, Please try again after sometime", Toast.LENGTH_SHORT).show();
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("OTP is already validated.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterOTPActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage(jsonObject.optString("responseMessage"))
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("OTP or Email is invalid.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterOTPActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Invalid OTP, Please enter valid OTP")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("Password should be combination of number, special char, one lower and upper, of 8 digits.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(EnterOTPActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage(jsonObject.optString("responseMessage"))
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        Toast.makeText(EnterOTPActivity.this, "Login successful", Toast.LENGTH_SHORT).show();
                        Intent intent = new Intent(EnterOTPActivity.this, SplashActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                        finishAffinity();
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }
}