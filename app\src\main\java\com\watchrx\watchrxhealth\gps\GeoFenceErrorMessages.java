package com.watchrx.watchrxhealth.gps;

import android.content.Context;
import android.content.res.Resources;

import com.google.android.gms.location.GeofenceStatusCodes;

public class GeoFenceErrorMessages {
    private GeoFenceErrorMessages() {

    }

    public static String getErrorString(Context context, int errorCode) {
        Resources mResources = context.getResources();
        switch (errorCode) {
            case GeofenceStatusCodes.GEOFENCE_NOT_AVAILABLE:
                return "Geofence not available ";
            case GeofenceStatusCodes.GEOFENCE_TOO_MANY_GEOFENCES:
                return "To many Geofence";
            case GeofenceStatusCodes.GEOFENCE_TOO_MANY_PENDING_INTENTS:
                return "Too many pending intents";
            default:
                return "Unknown Geofence";
        }
    }
}
