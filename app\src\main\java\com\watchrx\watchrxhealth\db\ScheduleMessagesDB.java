package com.watchrx.watchrxhealth.db;

import android.content.ContentValues;
import android.database.Cursor;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class ScheduleMessagesDB {

    public static final String TABLE_SCHEDULE_MESSAGE = "ScheduledMessages";
    private static final String COL_QUESTION_ID = "QuestionId";
    private static final String COL_QUESTION_NAME = "QuestionName";
    private static final String COL_ANSWER = "Answer";
    private static final String COL_SENDER_NAME = "SenderName";
    private static final String COL_DAY_OF_WEEK = "DayOfWeek";
    private static final String COL_TIME_SLOTS = "TimeSlots";

    static final String CREATE_SCHEDULE_MESSAGE_TABLE =
            "CREATE TABLE " + TABLE_SCHEDULE_MESSAGE +
                    "(" +
                    COL_QUESTION_ID + " TEXT, " +
                    COL_QUESTION_NAME + " TEXT, " +
                    COL_ANSWER + " TEXT, " +
                    COL_SENDER_NAME + " TEXT, " +
                    COL_DAY_OF_WEEK + " TEXT, " +
                    COL_TIME_SLOTS + " TEXT " +
                    ");";

    private static final String[] COLUMNS_SCHEDULE_MESSAGE_TABLE = {
            COL_QUESTION_ID,
            COL_QUESTION_NAME,
            COL_ANSWER,
            COL_SENDER_NAME,
            COL_DAY_OF_WEEK,
            COL_TIME_SLOTS
    };

    private String questionId;
    private String questionName;
    private String answer;
    private String senderName;
    private String dayOfWeek;
    private String timeSlots;

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getDayOfWeek() {
        return dayOfWeek;
    }

    public void setDayOfWeek(String dayOfWeek) {
        this.dayOfWeek = dayOfWeek;
    }

    public String getTimeSlots() {
        return timeSlots;
    }

    public void setTimeSlots(String timeSlots) {
        this.timeSlots = timeSlots;
    }

    public static void deleteAllRows() {
        DBAdaptor.getDbAdaptorInstance().delete(TABLE_SCHEDULE_MESSAGE);
    }

    public static long addToDB(ScheduleMessagesDB messagesDB) {
        ContentValues values = new ContentValues();
        values.put(COL_QUESTION_ID, messagesDB.questionId);
        values.put(COL_QUESTION_NAME, messagesDB.questionName);
        values.put(COL_ANSWER, messagesDB.answer);
        values.put(COL_SENDER_NAME, messagesDB.senderName);
        values.put(COL_DAY_OF_WEEK, messagesDB.dayOfWeek);
        values.put(COL_TIME_SLOTS, messagesDB.timeSlots);
        return DBAdaptor.getDbAdaptorInstance().insert(TABLE_SCHEDULE_MESSAGE, values);
    }

    public static List<ScheduleMessagesDB> getFromDB() {
        List<ScheduleMessagesDB> matchingRecords = new ArrayList<>();
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_SCHEDULE_MESSAGE, COLUMNS_SCHEDULE_MESSAGE_TABLE, null, null, null);
        while (cursor.moveToNext()) {
            ScheduleMessagesDB scheduleMessagesDB = new ScheduleMessagesDB();

            try {
                scheduleMessagesDB.setQuestionId(cursor.getString(cursor.getColumnIndexOrThrow(COL_QUESTION_ID)));
                scheduleMessagesDB.setQuestionName(cursor.getString(cursor.getColumnIndexOrThrow(COL_QUESTION_NAME)));
                scheduleMessagesDB.setAnswer(cursor.getString(cursor.getColumnIndexOrThrow(COL_ANSWER)));
                scheduleMessagesDB.setSenderName(cursor.getString(cursor.getColumnIndexOrThrow(COL_SENDER_NAME)));
                scheduleMessagesDB.setDayOfWeek(cursor.getString(cursor.getColumnIndexOrThrow(COL_DAY_OF_WEEK)));
                scheduleMessagesDB.setTimeSlots(cursor.getString(cursor.getColumnIndexOrThrow(COL_TIME_SLOTS)));
                matchingRecords.add(scheduleMessagesDB);
            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();
        return matchingRecords;
    }

    public static ScheduleMessagesDB getQuestionNameFromDB(String questionId) {
        String whereClause = COL_QUESTION_ID + " LIKE \"%" + questionId + "%\"";
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_SCHEDULE_MESSAGE, COLUMNS_SCHEDULE_MESSAGE_TABLE, whereClause, null, null);
        ScheduleMessagesDB record = new ScheduleMessagesDB();
        while (cursor.moveToNext()) {
            try {
                record.setQuestionId(cursor.getString(cursor.getColumnIndexOrThrow(COL_QUESTION_ID)));
                record.setQuestionName(cursor.getString(cursor.getColumnIndexOrThrow(COL_QUESTION_NAME)));
                record.setAnswer(cursor.getString(cursor.getColumnIndexOrThrow(COL_ANSWER)));
                record.setSenderName(cursor.getString(cursor.getColumnIndexOrThrow(COL_SENDER_NAME)));
                record.setDayOfWeek(cursor.getString(cursor.getColumnIndexOrThrow(COL_DAY_OF_WEEK)));
                record.setTimeSlots(cursor.getString(cursor.getColumnIndexOrThrow(COL_TIME_SLOTS)));

            } catch (Exception e) {
                // TODO: handle exception
                Log.e("PhoneNumbers ", "" + e);
            }
        }
        cursor.close();
        return record;
    }

    public static int getEntryFromDB(String questionId, String time) {
        String whereClause = COL_QUESTION_ID + " LIKE \"%" + questionId + "%\"" + " AND " + COL_TIME_SLOTS + " LIKE \"%" + time + "%\"";
        Cursor cursor = DBAdaptor.getDbAdaptorInstance().getResultSet(TABLE_SCHEDULE_MESSAGE, COLUMNS_SCHEDULE_MESSAGE_TABLE, whereClause, null, null);
        cursor.moveToFirst();
        int re = cursor.getCount();
        cursor.close();
        return re < 0 ? 0 : re;
    }


    public static int deleteRowById(String id) {
        String whereClause = COL_QUESTION_ID + " = " + id + "";
        return DBAdaptor.getDbAdaptorInstance().delete(TABLE_SCHEDULE_MESSAGE, whereClause, null);

    }
}
