<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardBackgroundColor="@color/grey85"
    tools:context=".MedicationDetailsActivity">

    <LinearLayout
        android:id="@+id/layout"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:orientation="horizontal"
        android:weightSum="2"
        app:backgroundColor="@color/white_smoke"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">


        <Button
            android:id="@+id/addMedication"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/d_5_d"
            android:layout_weight="1"
            android:background="@drawable/button_gradient"
            android:fontFamily="@font/lato_bold"
            android:text="@string/medication"
            android:textSize="16sp" />

        <Button
            android:id="@+id/dairy"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/d_5_d"
            android:layout_weight="1"
            android:background="@drawable/button_gradient"
            android:fontFamily="@font/lato_bold"
            android:text="@string/dairy"
            android:textSize="16sp" />
    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMedications"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/layout"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />

</androidx.constraintlayout.widget.ConstraintLayout>