//package com.watchrx.watchrxhealth.receivers;
//
//
//import android.accessibilityservice.AccessibilityService;
//import android.util.Log;
//import android.view.accessibility.AccessibilityEvent;
//import android.view.accessibility.AccessibilityNodeInfo;
//
//import com.watchrx.watchrxhealth.utils.LogUtils;
//import com.watchrx.watchrxhealth.utils.SWStatusKeeper;
//import com.watchrx.watchrxhealth.utils.SoftwareUpdateUtil;
//
//import java.util.HashMap;
//import java.util.Map;
//
//public class MyAccessibilityService extends AccessibilityService {
//
//    Map<Integer, Boolean> handledMap = new HashMap<>();
//    boolean MODE_TALK_BACK_SCREEN = false;
//
//    public MyAccessibilityService() {
//    }
//
//    @Override
//    public void onAccessibilityEvent(AccessibilityEvent event) {
//        AccessibilityNodeInfo nodeInfo = event.getSource();
//        if (nodeInfo != null) {
//            int eventType = event.getEventType();
//            if (eventType == AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED ||
//                    eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
//                explore(nodeInfo);
//                if (handledMap.get(event.getWindowId()) == null) {
//                    boolean handled = iterateNodesAndHandle(nodeInfo);
//                    if (handled) {
//                        handledMap.put(event.getWindowId(), true);
//                    }
//                }
//            }
//        }
//    }
//
//    private void explore(AccessibilityNodeInfo view) {
//        int count = view.getChildCount();
//        for (int i = 0; i < count; i++) {
//            AccessibilityNodeInfo child = view.getChild(i);
//            if (!MODE_TALK_BACK_SCREEN) {
//                if (child.getText() != null && child.getText().toString().toLowerCase().contains("my smart install")) {
//                    child.getParent().performAction(AccessibilityNodeInfo.ACTION_CLICK);
//                    MODE_TALK_BACK_SCREEN = true;
//                    return;
//                }
//            } else {
//                if ("ToggleButton".equals(child.getClassName().toString())) { //there ony one toggle button on the screen
//                    child.getParent().performAction(AccessibilityNodeInfo.ACTION_CLICK);
//                    performGlobalAction(GLOBAL_ACTION_BACK);
//                    performGlobalAction(GLOBAL_ACTION_BACK);//need to go back two time - i don't know if that will work :)
//                    return;
//                }
//            }
//            explore(child);
//            child.recycle();
//        }
//    }
//
//    private boolean iterateNodesAndHandle(AccessibilityNodeInfo nodeInfo) {
//        if (nodeInfo != null) {
//            int childCount = nodeInfo.getChildCount();
//            if ("android.widget.Button".equals(nodeInfo.getClassName())) {
//                String nodeContent = nodeInfo.getText().toString();
//                Log.d("TAG", "content is " + nodeContent);
//                if ("Install".equals(nodeContent) || "complete".equals(nodeContent) || "Next".equals(nodeContent)) {
//                    nodeInfo.performAction(AccessibilityNodeInfo.ACTION_CLICK);
//                    if ("Install".equals(nodeContent)) {
//                        String status = SoftwareUpdateUtil.getSWUpgradeStatus();
//                        if (status != null && status.equalsIgnoreCase("ROLLINGBACK")) {
//                            LogUtils.debug("Install button was clicked by Accessibility services and  status is ROLLINGBACK");
//                        } else {
//                            LogUtils.debug("Install button was clicked by Accessibility services and SWS status is set as INSTALLED");
//                            SWStatusKeeper statusKeeper = new SWStatusKeeper();
//                            statusKeeper.setStatusReleaseInstalled();
//                        }
//                    }
//                    if ("Next".equals(nodeContent)) {
//                        for (int i = 0; i < childCount; i++) {
//                            if (iterateNodesAndHandle(nodeInfo.getChild(i))) {
//                                return true;
//                            }
//                        }
//                    }
//
//                }
//            } else if ("android.widget.ScrollView".equals(nodeInfo.getClassName())) {
//                nodeInfo.performAction(AccessibilityNodeInfo.ACTION_SCROLL_FORWARD);
//            }
//            for (int i = 0; i < childCount; i++) {
//                AccessibilityNodeInfo childNodeInfo = nodeInfo.getChild(i);
//                if (iterateNodesAndHandle(childNodeInfo)) {
//                    return true;
//                }
//            }
//        }
//        return false;
//    }
//
//    @Override
//    public void onInterrupt() {
//    }
//
//}
//
//
