package com.watchrx.watchrxhealth.syncup;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.MedicationDetail;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.messages.RegisterResponseMessage;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Calendar;

class DayTimeRegistrationResponseHandler implements TaskResultHandler {


    private byte[] getMedicineImage(String url) {
        try {
            URL imageUrl = new URL(url);
            URLConnection urlConnection = imageUrl.openConnection();

            InputStream is = urlConnection.getInputStream();
            BufferedInputStream bis = new BufferedInputStream(is);

            ByteArrayOutputStream baos = new ByteArrayOutputStream(5000);
            int nextByte;
            while ((nextByte = bis.read()) != -1) {
                baos.write(nextByte);
            }
            return baos.toByteArray();
        } catch (Exception e) {
            LogUtils.debug("Error at Image downloading ,so i am using default image");

            Bitmap bitmap = BitmapFactory.decodeResource(WatchApp.getContext().getResources(), R.drawable.deafult_medicine);
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            return stream.toByteArray();
        }
    }

    @SuppressLint("StaticFieldLeak")
    @Override
    public void handleResult(final HandlerResult handlerResult) {
        if (handlerResult == null) {
            return;
        }

        final Object result = handlerResult.getResult();
        if (result == null) {
            LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
        }

        new AsyncTask<Void, Void, String>() {

            @Override
            protected String doInBackground(Void... params) {

                String isRegistrationSuccessful = "";

                if ((result instanceof String)) {
                    if (!compareTwoJsonObject(result.toString())) {
                        RegisterResponseMessage responseMessage = new Gson().fromJson((String) result, RegisterResponseMessage.class);
                        LogUtils.debug("Response message ->" + result);
                        LogUtils.saveDataToFile(result.toString());
                        LogUtils.debug("Data saved in syncup directory");
                        LogUtils.saveDataToFileForSync(result.toString());

                        if (responseMessage != null && responseMessage.isSuccessResponse()) {

                            if (responseMessage.medicationDetail != null) {
                                for (MedicationDetail medicationDetail : responseMessage.medicationDetail) {
                                    byte[] arr = getMedicineImage(medicationDetail.image);
                                    Bitmap bitmap = BitmapFactory.decodeByteArray(arr, 0, arr.length);
                                    LogUtils.SaveImage(bitmap, medicationDetail.medicineId);
                                    LogUtils.SaveImageForSync(bitmap, medicationDetail.medicineId);
                                }
                            }
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        } else {
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        }
                    } else {
                        Globals.isServerDataSame = true;
                        isRegistrationSuccessful = "BothDataAreSame";
                        LogUtils.debug("Data from server and local Data are the same.so NO need to re-set all alarms");
                    }
                }
                return isRegistrationSuccessful;
            }

            @Override
            protected void onPostExecute(String isRegistrationSuccessful) {
                if (isRegistrationSuccessful.equalsIgnoreCase("operationsuccessful")) {
                    LogUtils.debug("** Data Download successful for Syncup**");
                    Calendar cal = Calendar.getInstance();
                    cal.setTimeInMillis(System.currentTimeMillis());
                    if ((cal.get(Calendar.HOUR_OF_DAY) >= 0 && cal.get(Calendar.HOUR_OF_DAY) <= 10)) {
                        LogUtils.debug("Going to set sync up reminder @ 11:00:00");
                        ReminderUtils.setUpSyncUpdaterAlarm11(WatchApp.getContext(), 11, 0);
                    } else if ((cal.get(Calendar.HOUR_OF_DAY) >= 11 && cal.get(Calendar.HOUR_OF_DAY) <= 15)) {
                        LogUtils.debug("Going to set sync up reminder @ 16:00:00");
                        ReminderUtils.setUpSyncUpdaterAlarm16(WatchApp.getContext(), 16, 0);
                    } else {
                        LogUtils.debug("Got GCM notification for sync up but alarm is already set @ 12:01:05");
                    }
                    /*final ToneGenerator tg = new ToneGenerator(AudioManager.STREAM_NOTIFICATION, 200);
                    tg.startTone(ToneGenerator.TONE_PROP_BEEP2);*/

                } else if (isRegistrationSuccessful.equalsIgnoreCase("Imei is not found in the database")) {
                    LogUtils.debug("***** Sync Failed *****Imei is not found in the database");
                    MainActivity.registrationFailed();
                } else if (isRegistrationSuccessful.equalsIgnoreCase("Imei is not associated with any patient")) {
                    MainActivity.registrationFailed();
                    LogUtils.debug("***** Sync Failed *****Imei is not associated with any patient");
                } else if (isRegistrationSuccessful.equalsIgnoreCase("Subscription is not active")) {
                    LogUtils.debug("***** Sync Failed *****Subscription is not active");
                    CommUtils.sendBroadcastForSubscription();
                } else if (isRegistrationSuccessful.equalsIgnoreCase("BothDataAreSame")) {
                    LogUtils.debug("***Sync Successfully *** But No data changed in server side it's similar to Local database");
                } else {
                    LogUtils.debug("Sync has been failed may be sever communication was not takes place.");
                }
            }
        }.execute();
    }

    private boolean compareTwoJsonObject(String result) {
        JSONObject jsonObj1 = null;
        JSONObject jsonObj2 = null;
        try {
            jsonObj1 = new JSONObject(result);
            jsonObj2 = new JSONObject(ReadDataFromMemory.getDataFromMemory().trim());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return jsonObj1.toString().equalsIgnoreCase(jsonObj2.toString());
    }
}
