package com.watchrx.watchrxhealth.syncup;

import android.content.Context;
import android.os.Build;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.messages.RegisterWatch;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.SoftwareUpdateUtil;

import java.net.MalformedURLException;
import java.net.URL;

public class SyncUpWithServer {
    private Context mContext;

    public static void registerWatchAtNight(Context context) {

        try {
            URL url = new URL(URLConstants.REGISTER_WATCH_URL);
            LogUtils.debug("Going to register the watch @Night Time, Sending IMEI is ====>" + Globals.imei);
            if (Globals.imei != null) {

                String versionRelease = Build.VERSION.RELEASE;
                String apkVersion = SoftwareUpdateUtil.getAppVersion(context).toLowerCase();

                RegisterWatch registerWatch = new RegisterWatch();
                registerWatch.imeiNo = Globals.imei;
                registerWatch.androidversionno = versionRelease.toLowerCase();
                registerWatch.apkversionno = apkVersion;
                LogUtils.debug("Sending data to server is ====>" + new Gson().toJson(registerWatch));

                new RestAsyncTask(url, new Gson().toJson(registerWatch), null, new MidnightRegistrationResponseHandler(), null).execute();
            } else {
            }
        } catch (MalformedURLException e) {
            LogUtils.debug("I got exception while registering the watch" + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void registerWatchAtDay(Context context) {

        try {
            URL url = new URL(URLConstants.REGISTER_WATCH_URL);
            LogUtils.debug("Going to register the watch @Day Time, Sending IMEI is ====>" + Globals.imei);
            if (Globals.imei != null) {
                String versionRelease = Build.VERSION.RELEASE;
                String apkVersion = SoftwareUpdateUtil.getAppVersion(context).toLowerCase();

                RegisterWatch registerWatch = new RegisterWatch();
                registerWatch.imeiNo = Globals.imei;
                registerWatch.androidversionno = versionRelease.toLowerCase();
                registerWatch.apkversionno = apkVersion;

                LogUtils.debug("Sending data to server is ====>" + new Gson().toJson(registerWatch));
                new RestAsyncTask(url, new Gson().toJson(registerWatch), null, new DayTimeRegistrationResponseHandler(), null).execute();
            }
        } catch (MalformedURLException e) {
            LogUtils.debug("I got exception while registering the watch" + e.getMessage());
            e.printStackTrace();
        }
    }
}
