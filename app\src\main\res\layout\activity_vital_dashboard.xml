<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white_smoke"
    tools:context=".VitalDashboard">

    <!-- ProgressBar for loading -->
    <ProgressBar
        android:id="@+id/loader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Health Connect button -->
    <Button
        android:id="@+id/btn_health_connect"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:background="@drawable/button_gradient"
        android:fontFamily="@font/lato_bold"
        android:text="Connect Health Connect"
        android:textColor="@android:color/white"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- RecyclerView for the list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/vital_list"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:padding="10dp"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_health_connect" />

    <!-- TextView for "No items found" message -->
    <TextView
        android:id="@+id/no_items_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/lato_bold"
        android:gravity="center"
        android:text="No vitals measurements found !!"
        android:textColor="@android:color/darker_gray"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- BottomNavigationView -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu" />
</androidx.constraintlayout.widget.ConstraintLayout>
