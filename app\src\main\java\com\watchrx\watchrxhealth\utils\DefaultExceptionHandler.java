package com.watchrx.watchrxhealth.utils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.Thread.UncaughtExceptionHandler;

/**
 */
public class DefaultExceptionHandler implements UncaughtExceptionHandler {
    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        StringWriter sw = new StringWriter();
        ex.printStackTrace(new PrintWriter(sw));
        String stackTrace = sw.toString(); // stack trace as a string

        LogUtils.debug("Got an Exception: " + ex.getMessage() + "Complete StackTrace is:\n" + stackTrace);

        //This will stop your application and take out from it.
        System.exit(2);
    }
}
