package com.watchrx.watchrxhealth;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.MediaScannerConnection;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiManager;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.appcompat.app.AppCompatActivity;
import android.util.Log;
import android.view.View;
import android.widget.ProgressBar;
import android.widget.Toast;

import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;


public class WifiConfig extends AppCompatActivity implements Runnable {


    //String networkSSID = "Attocom-11";
    // String networkPass = "atTo1c$m1";
    String networkSSID = "";
    String networkPass = "";
    private static Handler timeoutHandler = new Handler();

    private WifiManager wifi;
    private ProgressBar progressBar;
    InternetConnectedReceiver internetConnectedReceiver = new InternetConnectedReceiver();
    public static final String INTERNET_CONNECTED_INDICATOR_INTENT_FILTER = "com.watchrx.watch.internetConnectedIndicator";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_wifi_config);

        LogUtils.setLogLevel(LogUtils.DEBUG_LEVEL);
        CommUtils.enableBroadcastReceiver(this);
        LogUtils.debug("********************** Configuring to Wifi  ********************");

        LogUtils.debug("Reading wifi credential from File");
        readDetailFromFile();

        wifi = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        progressBar = (ProgressBar) findViewById(R.id.progressBar);

        IntentFilter i = new IntentFilter();
        i.addAction(INTERNET_CONNECTED_INDICATOR_INTENT_FILTER);
        LocalBroadcastManager.getInstance(this).registerReceiver(internetConnectedReceiver, i);

        if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
            finish();
            return;
        }

        if (CommUtils.isNetworkAvailable(this)) {
            LogUtils.debug("Network Connected and Going to Splash Screen");
            Intent intent = new Intent(WifiConfig.this, SplashActivity.class);
            startActivity(intent);
            WifiConfig.this.finish();

        } else {
            LogUtils.debug("Network Is not connected and trying to Connect ....");
            if (wifi.isWifiEnabled()) {
                LogUtils.debug("Wifi is already  enabled and going to connect ");
                removeNetwork();
            } else {
                LogUtils.debug("Wifi is not enabled before connecting ");
                if (wifi != null && !wifi.isWifiEnabled()) {
                    wifi.setWifiEnabled(true);
                    LogUtils.debug("Wifi enabled and going to connect");
                    removeNetwork();
                }
            }
        }

        (findViewById(R.id.configWifi)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                progressBar.setVisibility(View.VISIBLE);
                if (wifi.isWifiEnabled()) {
                    removeNetwork();
                } else {
                    Toast.makeText(WifiConfig.this, "Enable Wifi", Toast.LENGTH_SHORT).show();
                    if (wifi != null && !wifi.isWifiEnabled()) {
                        wifi.setWifiEnabled(true);
                        removeNetwork();
                    }
                }

            }
        });


        timeoutHandler.postDelayed(WifiConfig.this, 45 * 1000);
    }

    public void readDetailFromFile() {

        LogUtils.debug("Inside Read readDetailFromFile ");
        Properties props = new Properties();
        InputStream is = null;
        File sdcard = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        File wifiFileInBluetooth = new File(sdcard + "/bluetooth/", "WatchRxWifi.properties");

        String inputPath = sdcard + "/bluetooth/";
        String outputPath = sdcard + "/WatchRx_DataBase/";
        String inputFile = "WatchRxWifi.properties";
        try {
            if (wifiFileInBluetooth.exists()) {
                LogUtils.debug("File found in Bluetooth folder and copying in to WatchRx_DataBase folder");
                copyFile(inputPath, inputFile, outputPath);
                is = new FileInputStream(wifiFileInBluetooth);
            } else {
                File wifiFileInWatchRx = new File(sdcard + "/WatchRx_DataBase/", "WatchRxWifi.properties");
                if (wifiFileInWatchRx.exists()) {
                    LogUtils.debug("File found in WatchRx_DataBase");
                    is = new FileInputStream(wifiFileInWatchRx);
                } else {
                    LogUtils.debug("No File found in Bluetooth either WatchRx_DataBase Folder");
                }
            }

        } catch (Exception e) {
            is = null;
        }

        try {
            if (is != null) {
                props.load(is);
                this.networkSSID = props.getProperty("SSID").trim();
                this.networkPass = props.getProperty("Password").trim();

                LogUtils.debug("Reading Data is completed with SSID = " + this.networkSSID + " and Password = " + this.networkPass);

            }
        } catch (Exception e) {
            e.getMessage();
        }

    }

    private void copyFile(String inputPath, String inputFile, String outputPath) {

        LogUtils.debug("Copying the file from Bluetooth folder to WatchRx_DataBase");
        InputStream in = null;
        FileOutputStream out = null;
        try {

            //create output directory if it doesn't exist
            File dir = new File(outputPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            in = new FileInputStream(inputPath + inputFile);
            out = new FileOutputStream(outputPath + inputFile);

            byte[] buffer = new byte[1024];
            int read;
            while ((read = in.read(buffer)) != -1) {
                out.write(buffer, 0, read);
            }
            new File(inputPath + inputFile).delete();

            MediaScannerConnection.scanFile(WatchApp.getContext(), new String[]{dir.toString(),new File(inputPath).toString()}, null, null);
            in.close();
            in = null;

            // write the output file (You have now copied the file)
            out.flush();
            out.close();
            out = null;

        } catch (FileNotFoundException fnfe1) {
            Log.e("tag", fnfe1.getMessage());
        } catch (Exception e) {
            Log.e("tag", e.getMessage());
        }

    }

    @Override
    public void run() {

        Intent intent = new Intent(WifiConfig.this, SplashActivity.class);
        startActivity(intent);
        finish();

    }

    public class InternetConnectedReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            LogUtils.debug("Received Internet change notification.");
            Log.e("WIFI----->", "Received Internet change notification. Will try to register again.");
            synchronized (Globals.synchronized_were) {

                timeoutHandler.removeCallbacks(WifiConfig.this);
                progressBar.setVisibility(View.GONE);
                LogUtils.debug("Internet connected going to next screen");
                Toast.makeText(WatchApp.getContext(), "Connecting to WatchRx Server", Toast.LENGTH_LONG).show();
                Intent nextScreen = new Intent(WifiConfig.this, SplashActivity.class);
                startActivity(nextScreen);
                WifiConfig.this.finish();

            }
        }
    }

    public void removeNetwork() {
        List<WifiConfiguration> list = wifi.getConfiguredNetworks();
        if (list != null && list.size() > 0) {
            for (WifiConfiguration i : list) {
                wifi.removeNetwork(i.networkId);
                wifi.saveConfiguration();
            }
            connectToWifi();
        } else {
            connectToWifi();
        }
    }


    public void connectToWifi() {

        LogUtils.debug("Inside connectToWifi moethod");
        try {
            final WifiManager wifiManager = (WifiManager) super.getApplicationContext().getSystemService(android.content.Context.WIFI_SERVICE);
            WifiConfiguration wc = new WifiConfiguration();

            LogUtils.debug("Inside ReconnectToWifi networkSSID=" + networkSSID + "\nnetworkPass=" + networkPass);

            wc.SSID = "\"" + networkSSID + "\"";
            wc.preSharedKey = "\"" + networkPass + "\"";
            wc.status = WifiConfiguration.Status.ENABLED;
            wc.allowedProtocols.set(WifiConfiguration.Protocol.WPA);
            wc.allowedKeyManagement.set(WifiConfiguration.KeyMgmt.WPA_PSK);

            int netId = wifiManager.addNetwork(wc);
            if (netId == -1) {
                netId = getExistingNetworkId(wc.SSID);
            }
            wifiManager.disconnect();
            wifiManager.enableNetwork(netId, true);
            boolean status = wifiManager.reconnect();

            LogUtils.debug("Wifi connection has been done ");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private int getExistingNetworkId(String SSID) {
        WifiManager wifiManager = (WifiManager) super.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        List<WifiConfiguration> configuredNetworks = wifiManager.getConfiguredNetworks();
        Log.e("MainActivity", "List Data " + configuredNetworks.toString());
        if (configuredNetworks.size() > 0) {
            for (WifiConfiguration existingConfig : configuredNetworks) {
                if (existingConfig.SSID.equals(SSID)) {
                    return existingConfig.networkId;
                } else {
                    Toast.makeText(this, "Wifi  disconnected", Toast.LENGTH_SHORT).show();
                    Log.e("MainActivity", " wifi connection : Wifi  disconnected");
                }
            }
        }
        return -1;
    }
}