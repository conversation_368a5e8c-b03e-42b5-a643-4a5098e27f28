package com.watchrx.watchrxhealth;

import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.ProgressBar;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.NotificationAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.AlertNotification;
import com.watchrx.watchrxhealth.models.LatestAlertModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class LatestAlertsActivity extends AppCompatActivity {
    private RecyclerView recyclerView;
    private ProgressBar progressSpinner;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_latest_alerts);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        recyclerView = findViewById(R.id.notification_list);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        recyclerView.setLayoutManager(layoutManager);
        progressSpinner = findViewById(R.id.progress_spinner);
        setAlerts();
    }

    private void setAlerts() {
        try {
            LogUtils.debug("Going to last two days alerts");
            URL url = new URL(URLConstants.LATEST_ALERTS);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("patientUniqueId", patientId);
            jsonObject.put("index", 0);
            jsonObject.put("itemsPerPage", 1000);

            JSONArray alertsType = new JSONArray();
            alertsType.put("critical");
            alertsType.put("alarm");
            jsonObject.put("alertSeverityList", alertsType);

            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
            String currentDate = dateFormat.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_YEAR, -2);
            String endDate = dateFormat.format(calendar.getTime());
            jsonObject.put("startDate", endDate);
            jsonObject.put("endDate", currentDate);

            new RestAsyncTask(url, jsonObject.toString(), progressSpinner, new AlertsResponseHandler(), null).execute();

        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }

    private class AlertsResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in getting alerts.");
            }
            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        LatestAlertModel responseMessage = new Gson().fromJson((String) result, LatestAlertModel.class);
                        if (responseMessage != null && responseMessage.isStatus()
                                && responseMessage.getPatientAlert() != null && !responseMessage.getPatientAlert().isEmpty()) {

                            List<AlertNotification> dataModelList = new ArrayList<>(responseMessage.patientAlert);
                            NotificationAdapter adapter = new NotificationAdapter(dataModelList);
                            recyclerView.setAdapter(adapter);

                            recyclerView.setVisibility(View.VISIBLE);
                            findViewById(R.id.tv_no_alerts).setVisibility(View.GONE);

                        } else {
                            recyclerView.setVisibility(View.GONE);
                            findViewById(R.id.tv_no_alerts).setVisibility(View.VISIBLE);
                        }
                    }
                } catch (Exception e) {
                    recyclerView.setVisibility(View.GONE);
                    findViewById(R.id.tv_no_alerts).setVisibility(View.VISIBLE);
                }
            } else {
                recyclerView.setVisibility(View.GONE);
                findViewById(R.id.tv_no_alerts).setVisibility(View.VISIBLE);
            }
        }
    }
}