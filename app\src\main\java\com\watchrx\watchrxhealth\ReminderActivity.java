package com.watchrx.watchrxhealth;

import static com.watchrx.watchrxhealth.constants.CommonConstants.MAX_REMINDER_REPETITION;
import static com.watchrx.watchrxhealth.constants.CommonConstants.REMINDER_TIMEOUT_FOR_30MIN;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.db.MedicationScheduleInstance;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.util.Calendar;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class ReminderActivity extends AppCompatActivity implements Runnable, View.OnClickListener {

    private static final long[] vibratePattern = {0, 500, 200};
    private static final String STATE_TIMESLOT = "timeSlot";
    private static final String STATE_FOOD = "BeforeAfterFood";
    private static final String STATE_ALERT_ID = "AlertId";
    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private static final Handler timeoutHandler = new Handler();
    String timeSlot = null;
    String beforeOrAfterFood = null;
    String alertId = null;
    private int reminderRetryCount = 0;
    private TextView speechText;
    private Button snooze;
    int timesAlreadyCalled = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        alertId = getIntent().getStringExtra("alertId");
        beforeOrAfterFood = Objects.requireNonNull(getIntent().getExtras()).getString("beforeOrAfterFood");
        timeSlot = getIntent().getExtras().getString("timeSlot");

        LogUtils.debug("Reminder Alarm received for: " + timeSlot + "-" + beforeOrAfterFood);
        List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(beforeOrAfterFood, timeSlot);
        if (scheduleInstanceList.size() == 0) {
            LogUtils.debug("There are no medications to display for this alarm. The schedule instances are either null or empty.");
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
            return;
        }
        LogUtils.debug("User Interface Initialized.");
        setContentView(R.layout.activity_reminder);
        speechText = findViewById(R.id.speech);
        snooze = (Button) findViewById(R.id.snoozebutton);
        timesAlreadyCalled = getIntent().getIntExtra("timesAlreadyCalled", -1);
        GeneralUtils.stopSpeaking();
        startLEDAnimation();
        if (!beforeOrAfterFood.equalsIgnoreCase("Fixed") && timesAlreadyCalled == 1) {
            snooze.setClickable(true);
            snooze.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    long triggerAt = getIntent().getLongExtra("triggerAt", -1);
                    if (triggerAt == -1) {
                        triggerAt = System.currentTimeMillis();
                    }
                    GeneralUtils.stopBeeping();
                    next30MinReminder(timesAlreadyCalled, triggerAt);
                    LogUtils.debug("onClick event detected on Reminder screen for snooze button");
                    ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                    timeoutHandler.removeCallbacks(ReminderActivity.this);
                    try {
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    Globals.isScreenRunning = false;
                    NotifyNewEntryInQueue.notifyNewEntryInQueue();
                    finish();
                }
            });
        } else {
            LogUtils.debug("Snooze button not detected");
            snooze.setClickable(false);
            snooze.setText(R.string.remindertext);
            snooze.setBackgroundColor(Color.parseColor("#808080"));
        }
        if (timeoutHandler == null) {
            Handler timeoutHandler = new Handler();
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
            LogUtils.debug("timeoutHandler object died. so using new handler object Inside OnCreate. Going to set timeout Handler.");
        } else {
            LogUtils.debug("Inside OnCreate. Going to set timeout Handler.");
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        }
        afterVoiceCompleted();

        CommUtils.sendReminderDisplayedLogToServer(this, "Displayed Reminder", alertId, beforeOrAfterFood, timeSlot, scheduleInstanceList);
        LogUtils.debug("ReminderActivity -> scheduleInstanceList size for" + beforeOrAfterFood + "-" + timeSlot + "--" + scheduleInstanceList.size());

        ConstraintLayout reminderLayout = (ConstraintLayout) findViewById(R.id.activity_reminder);
        if (reminderLayout != null) {
            reminderLayout.setOnClickListener(this);
        }

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }

    private void afterVoiceCompleted() {
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);
        GeneralUtils.startBeeping(this, mp -> {
            int medCount = MedicationScheduleInstance.getFromDB(beforeOrAfterFood, timeSlot).size();
            if (medCount > 0) {
                if (alertId == null) {
                    String speech = GeneralUtils.getVoicePromptForRegularReminder(timeSlot, beforeOrAfterFood, medCount);
                    GeneralUtils.speak(speech);
                    final String speechDisplay = speech.replaceAll("\\$", "");
                    if (speechText != null) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                speechText.setText(speechDisplay.toLowerCase(Locale.ROOT));
                            }
                        });
                    }
                } else {
                    checkMissedAlertList();
                    String nurseName = getIntent().getExtras().getString("nurseName");
                    String nurseSpeech = GeneralUtils.getVoicePromptForNurseReminder(nurseName, timeSlot, beforeOrAfterFood, medCount);
                    GeneralUtils.speak(nurseSpeech);
                    final String nurseSpeechDisplay = nurseSpeech.replaceAll("\\$", "");
                    if (speechText != null) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                speechText.setText(nurseSpeechDisplay);
                            }
                        });
                    }
                }
            }
        });
    }

    private void checkMissedAlertList() {
        List<Alerts> alert_count = Alerts.getFromDB();
        LogUtils.debug("****While nurse reminding alert count = " + alert_count.size());
        if (alert_count.size() != 0) {
            if (containsAlerts(alert_count, "MissedRegularReminder")) {
                LogUtils.debug("Deleting alert from list MissedRegularReminder");
                Alerts.deleteRowAlert("MissedRegularReminder");
            } else if (containsAlerts(alert_count, "MissedNurseReminder")) {
                LogUtils.debug("Deleting alert from list MissedNurseReminder");
                Alerts.deleteRowAlert("MissedNurseReminder");
            } else {
                LogUtils.debug("No alerts in Alerts table ");
            }
        }
    }

    private static boolean containsAlerts(List<Alerts> c, String artype) {
        for (Alerts o : c) {
            if (o != null && o.getAlertType().equals(artype)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    protected void onSaveInstanceState(Bundle savedInstanceState) {
        // Save the user's current game state
        savedInstanceState.putString(STATE_TIMESLOT, timeSlot);
        savedInstanceState.putString(STATE_FOOD, beforeOrAfterFood);
        savedInstanceState.putString(STATE_ALERT_ID, alertId);

        // Always call the superclass so it can save the view hierarchy state
        super.onSaveInstanceState(savedInstanceState);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);

        // Restore state members from saved instance
        timeSlot = savedInstanceState.getString(STATE_TIMESLOT);
        beforeOrAfterFood = savedInstanceState.getString(STATE_FOOD);
        alertId = savedInstanceState.getString(STATE_ALERT_ID);
    }

    private void startLEDAnimation() {
        LogUtils.debug("Going to start LED Animation...");
        final Animation animation = new AlphaAnimation(1, 0); // Change alpha from fully visible to invisible
        animation.setDuration(300); // duration
        animation.setInterpolator(new LinearInterpolator()); // do not alter animation rate
        animation.setRepeatCount(Animation.INFINITE); // Repeat animation infinitely
        animation.setRepeatMode(Animation.REVERSE); // Reverse animation at the end so the button will fade back in

        LinearLayout LEDs = (LinearLayout) findViewById(R.id.LEDs);
        if (alertId == null) {
            LogUtils.debug("This is Normal reminder (i.e. NOT NURSE); Hence setting up LEDs.");
            if (LEDs != null) {
                LEDs.startAnimation(animation);
                LEDs.setVisibility(View.VISIBLE);
            }
            if (!beforeOrAfterFood.equalsIgnoreCase("Fixed") && timesAlreadyCalled == 1) {
                if (snooze != null) {
                    snooze.setEnabled(true);
                    snooze.setText(R.string.snoozetext);
                }
            } else {
                if (snooze != null) {
                    snooze.setClickable(false);
                    snooze.setText(R.string.remindertext);
                    snooze.setBackgroundColor(Color.parseColor("#808080"));
                }
            }
        } else {
            LogUtils.debug("This is a NURSE reminder. Hiding LEDs and showing a 'Nurse reminding' text.");
            if (snooze != null) {
                snooze.setClickable(false);
                snooze.setText(R.string.nurse_is_reminding_you);
                snooze.setBackgroundColor(Color.parseColor("#808080"));
            }
            if (LEDs != null) {
                LEDs.setEnabled(false);
                LEDs.setVisibility(View.INVISIBLE);
            }
        }
        LogUtils.debug("Animation Started.");
    }

    @Override
    public void onClick(View v) {
        LogUtils.debug("Detected onClick event. Will cancel Vibration.");
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        GeneralUtils.stopBeeping();
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(ReminderActivity.this);
        Intent medicineIntent = new Intent(ReminderActivity.this, MedicationActivity.class);
        medicineIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        medicineIntent.putExtra("alertId", alertId);
        medicineIntent.putExtra("beforeOrAfterFood", beforeOrAfterFood);
        medicineIntent.putExtra("timeSlot", timeSlot);
        medicineIntent.putExtra("timesAlreadyCalled", getIntent().getIntExtra("timesAlreadyCalled", -1));
        medicineIntent.putExtra("triggerAt", getIntent().getLongExtra("triggerAt", -1));
        LogUtils.debug("Launching MedicationActivity");
        startActivity(medicineIntent);
        finish();
    }

    @Override
    public void run() {
        LogUtils.debug("Timeout detected. The Reminder was not acknowledged through a click. reminderRetryCount: " + reminderRetryCount);
        GeneralUtils.stopSpeaking();
        GeneralUtils.stopBeeping();
        timeoutHandler.removeCallbacks(this);
        reminderRetryCount++;
        if (reminderRetryCount < 2) {
            LogUtils.debug("Will remind once again right now");
            GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    int medCount = MedicationScheduleInstance.getFromDB(beforeOrAfterFood, timeSlot).size();
                    if (medCount > 0) {
                        if (alertId == null) {
                            GeneralUtils.speak(GeneralUtils.getVoicePromptForRegularReminder(timeSlot, beforeOrAfterFood, medCount));
                        } else {
                            String nurseName = getIntent().getExtras().getString("nurseName");
                            GeneralUtils.speak(GeneralUtils.getVoicePromptForNurseReminder(nurseName, timeSlot, beforeOrAfterFood, medCount));
                        }
                    }
                }
            });
            //afterVoiceCompleted();
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        } else {
            LogUtils.debug("retryReminderCount >=" + reminderRetryCount + " Stopping Vibration.");
            ((Vibrator) Objects.requireNonNull(getSystemService(Context.VIBRATOR_SERVICE))).cancel();
            if (alertId == null) {
                LogUtils.debug("This is to schedule a Normal reminder (i.e. not a nurse reminder).");
                long triggerAt = getIntent().getLongExtra("triggerAt", -1);
                if (triggerAt == -1) {
                    triggerAt = System.currentTimeMillis();
                }

                int timesAlreadyCalled = getIntent().getIntExtra("timesAlreadyCalled", -1);

                LogUtils.debug("We have already shown reminder " + timesAlreadyCalled + " times before.");
                if (timesAlreadyCalled < MAX_REMINDER_REPETITION) {
                    if (getIntent().getStringExtra("beforeOrAfterFood").equalsIgnoreCase("Fixed")) {
                        ReminderUtils.next2MinReminder(triggerAt, timesAlreadyCalled, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"));
                    } else {
                        ReminderUtils.next15MinReminder(triggerAt, timesAlreadyCalled, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"));
                    }
                } else {
                    LogUtils.debug("We have reached maximum reminders limit. Will send a missed log to the server.");
                    List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(beforeOrAfterFood, timeSlot);
                    CommUtils.sendReminderDisplayedLogToServer(this, "MissedRegularReminder", alertId, beforeOrAfterFood, timeSlot, scheduleInstanceList);
                }
            } else {
                LogUtils.debug("This is to schedule a Nurse reminder.");
                List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(beforeOrAfterFood, timeSlot);
                CommUtils.sendReminderDisplayedLogToServer(this, "MissedNurseReminder", alertId, beforeOrAfterFood, timeSlot, scheduleInstanceList);
            }
            LogUtils.debug("Exiting the screen.");
            try {
                Thread.sleep(3500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
        }
    }

    private void next30MinReminder(int timesAlreadyCalled, Long triggerAt) {
        List<MedicationScheduleInstance> scheduleInstanceList = MedicationScheduleInstance.getFromDB(beforeOrAfterFood, timeSlot);
        CommUtils.sendReminderDisplayedLogToServer(ReminderActivity.this, "Snoozed Reminder", alertId, beforeOrAfterFood, timeSlot, scheduleInstanceList);
        LogUtils.debug("Will set a reminder once more to trigger at: " + triggerAt + REMINDER_TIMEOUT_FOR_30MIN);
        timesAlreadyCalled++;
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(System.currentTimeMillis() + REMINDER_TIMEOUT_FOR_30MIN);
        Globals.reminderAlertTimeoutIntent = ReminderUtils.setReminderAlarm(this, calendar, getIntent().getStringExtra("timeSlot"), getIntent().getStringExtra("beforeOrAfterFood"), timesAlreadyCalled);
        LogUtils.debug("30min AlertReminder is set at " + getIntent().getStringExtra("timeSlot") + getIntent().getStringExtra("beforeOrAfterFood") + ". It has already been called " + (timesAlreadyCalled - 1) + " times.");
        GeneralUtils.stopSpeaking();
        if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
            GeneralUtils.speak("Alright. I'll try REMINDING you again, in 30 minutes ");
        } else {
            GeneralUtils.speak("Esta bien. Le RECORDARÉ otra vez, en aproximadamente 30 minutos");
        }
    }
}

