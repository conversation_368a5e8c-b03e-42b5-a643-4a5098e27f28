package com.watchrx.watchrxhealth;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.text.method.ScrollingMovementMethod;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;

import java.util.Objects;

public class CustomAlertActivity extends AppCompatActivity implements Runnable {
    private static final long[] vibratePattern = {0, 500, 200};
    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private Handler timeoutHandler;
    private String alertType = "";
    private String alertDetail = "";
    private int reminderRetryCount = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_custom_alerts);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        alertType = getIntent().getStringExtra("alertType");
        alertDetail = getIntent().getStringExtra("alertDetail");

        if (alertType.equalsIgnoreCase("Battery Alert")) {
            int level = CommUtils.getBatteryPercentage(CustomAlertActivity.this);
            CommUtils.sendBatteryLogToServer(CustomAlertActivity.this, "Battery Alert",
                    " On Demand Battery Alert , Battery Level is " + level);
        } else {
            CommUtils.sendBatteryLogToServer(this, alertType,
                    alertDetail);
        }
        TextView alertDetailTextView = (TextView) findViewById(R.id.nurse_reminder);
        TextView alertTypeTextView = (TextView) findViewById(R.id.reminderText);
        if (alertTypeTextView != null) {
            alertTypeTextView.setText(alertType);
        }
        alertDetailTextView.setMovementMethod(new ScrollingMovementMethod());

        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        startAnimation(alertDetailTextView, alertDetail);

        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);

        GeneralUtils.stopBeeping();
        GeneralUtils.stopSpeaking();
        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                GeneralUtils.speak("Hi $ you have " + alertType + " $ " + alertDetail + "$ Tap ANYWHERE, to close once you done it");
            }
        });

        ConstraintLayout layout = (ConstraintLayout) findViewById(R.id.custom_alert);
        layout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                GeneralUtils.stopBeeping();
                GeneralUtils.stopSpeaking();
                timeoutHandler.removeCallbacks(CustomAlertActivity.this);
                ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                Globals.isScreenRunning = false;
                NotifyNewEntryInQueue.notifyNewEntryInQueue();
                CustomAlertActivity.this.finish();
            }
        });

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    public void run() {
        timeoutHandler.removeCallbacks(this);
        reminderRetryCount++;
        if (reminderRetryCount < 2) {
            GeneralUtils.stopBeeping();
            GeneralUtils.stopSpeaking();
            GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    GeneralUtils.speak("Hi $ you have " + alertType + " $ " + alertDetail + "$ Tap ANYWHERE, to close once you done it");
                }
            });
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        } else {
            GeneralUtils.stopSpeaking();
            timeoutHandler.removeCallbacks(this);
            ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
        }
    }

    private void startAnimation(TextView nurseReminderTextView, String message) {
        final Animation animation = new AlphaAnimation(1, 0); // Change alpha from fully visible to invisible
        animation.setDuration(300); // duration
        animation.setInterpolator(new LinearInterpolator()); // do not alter animation rate
        animation.setRepeatCount(Animation.INFINITE); // Repeat animation infinitely
        animation.setRepeatMode(Animation.REVERSE); // Reverse animation at the end so the button will fade back in
        nurseReminderTextView.startAnimation(animation);
        nurseReminderTextView.setText(message);
    }
}
