<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/tvTabletName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="My Text"
            android:textColor="#000"
            android:textSize="18sp"
            android:textStyle="bold" />


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:background="@drawable/background_with_border"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageType"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_medicine"
                app:tint="#4CAF50" />

            <TextView
                android:id="@+id/tvMedicineType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#4CAF50"
                android:textSize="14sp" />
        </LinearLayout>


        <TextView
            android:id="@+id/tvFrequency"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:textColor="#888"
            android:textSize="14sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/background_with_border"
                android:drawablePadding="8dp"
                android:gravity="center_vertical"
                android:padding="8dp"
                android:textColor="#000000"
                android:textSize="14sp"
                android:textStyle="bold"
                app:drawableLeftCompat="@drawable/clock" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>