<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="5dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground"
    app:cardBackgroundColor="#FFFFFF"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginBottom="4dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageType"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical"
                android:padding="8dp"
                android:src="@drawable/ic_heart_rate" />

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:background="@drawable/background_with_border"
                android:fontFamily="@font/lato_bold"
                android:gravity="center_vertical"
                android:padding="8dp"
                android:text="Vital Title"
                android:textSize="16sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/mainValue"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/lato_regular"
            android:text="120/80 mmHg"
            android:textColor="#000"
            android:textSize="28sp" />

        <LinearLayout
            android:id="@+id/details_layout"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/date_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/lato_black"
                android:text="22 Dec 2024, 3:45 PM"
                android:textSize="14sp" />

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/viewDetails"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/lato_regular"
                android:gravity="center"
                android:text="View details    "
                android:textSize="14sp"
                app:drawableRightCompat="@drawable/right_arrow" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <Button
                android:id="@+id/collectNow"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/button_gradient"
                android:fontFamily="@font/lato_bold"
                android:text="Collect Now" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>
