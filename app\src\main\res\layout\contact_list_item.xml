<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:cardView="http://schemas.android.com/apk/res-auto"
    android:id="@+id/carView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    cardView:cardCornerRadius="8dp"
    cardView:cardElevation="8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tvName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:fontFamily="@font/lato_bold"
            android:gravity="top"
            android:lines="1"
            android:padding="5dp"
            android:textColor="@android:color/black"
            android:textSize="30sp"
            android:textStyle="bold"
            cardView:layout_constraintLeft_toRightOf="@id/image"
            cardView:layout_constraintRight_toRightOf="parent"
            cardView:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvNumber"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="8dp"
            android:fontFamily="@font/lato_light"
            android:lines="1"
            android:padding="5dp"
            android:textColor="@android:color/black"
            android:textSize="25sp"
            android:textStyle="bold"
            cardView:layout_constraintBottom_toBottomOf="parent"
            cardView:layout_constraintLeft_toRightOf="@id/image"
            cardView:layout_constraintTop_toBottomOf="@id/tvName" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/image"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginTop="30dp"
            android:layout_marginStart="5dp"
            android:insetTop="0dp"
            android:insetBottom="0dp"
            android:textSize="25sp"
            cardView:cornerRadius="35dp"
            cardView:layout_constraintStart_toStartOf="parent"
            cardView:layout_constraintTop_toTopOf="parent"
            cardView:shapeAppearanceOverlay="@style/ShapeAppearanceOverlay.App.rounded" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>