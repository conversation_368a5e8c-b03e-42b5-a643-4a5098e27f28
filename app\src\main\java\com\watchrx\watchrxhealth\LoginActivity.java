package com.watchrx.watchrxhealth;

import android.Manifest;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.UnderlineSpan;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.ExactAlarmHelper;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONObject;

import java.net.URL;
import java.util.Objects;

@RequiresApi(api = Build.VERSION_CODES.S)
public class LoginActivity extends AppCompatActivity implements View.OnClickListener {

    static final String TAG = LoginActivity.class.getSimpleName();

    String[] PERMISSIONS_REQUIRED = new String[]{
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
    };

    String[] ANDROID_12_PERMISSIONS_REQUIRED = new String[]{
            Manifest.permission.READ_PHONE_STATE,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.CALL_PHONE,
            Manifest.permission.CAMERA,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.SCHEDULE_EXACT_ALARM,
            Manifest.permission.POST_NOTIFICATIONS
    };
    private static final int REQUEST_PERMISSIONS = 100;
    private static final int REQUEST_CODE_FOREGROUND_SERVICE_MEDIA_PLAYBACK = 200;

    public SharedPreferences sharedpreferences;
    public static final String mypreference = "WatchRx";
    public static final String userName = "username";
    public static final String password = "password";

    private EditText userNameET, passwordET, otpET;
    private ProgressBar progressBar;
    private Button loginBtn, validateOTPBtn, resendOTPBtn;
    private TextView validateTextView;
    private TextView loginTextView;
    private TextView forgotPasswordTextView;
    private ConstraintLayout layout;
    private ConstraintSet constraintSet;

    @RequiresApi(api = 34)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        LogUtils.setLogLevel(LogUtils.DEBUG_LEVEL);
        LogUtils.debug("********************** Application Started ********************");
        super.onCreate(savedInstanceState);
        if ((getIntent().getFlags() & Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT) != 0) {
            finish();
            return;
        }
        sharedpreferences = getSharedPreferences(mypreference, Context.MODE_PRIVATE);
        setContentView(R.layout.activity_login);
        Objects.requireNonNull(getSupportActionBar()).hide();

        if (checkPermission(ANDROID_12_PERMISSIONS_REQUIRED)) {
            ActivityCompat.requestPermissions(this, ANDROID_12_PERMISSIONS_REQUIRED, REQUEST_PERMISSIONS);
        } else {
            initView();
        }
    }

    private void initView() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (checkSelfPermission(Manifest.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK) != PackageManager.PERMISSION_GRANTED) {
                if (Build.VERSION.SDK_INT >= 34) {
                    requestPermissions(
                            new String[]{Manifest.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK},
                            REQUEST_CODE_FOREGROUND_SERVICE_MEDIA_PLAYBACK
                    );
                }
            }
        }
        if (!ExactAlarmHelper.hasExactAlarmPermission(this)) {
            ExactAlarmHelper.requestExactAlarmPermission(this);
        }

        layout = findViewById(R.id.layout);
        constraintSet = new ConstraintSet();
        constraintSet.clone(layout);

        userNameET = findViewById(R.id.userName);
        passwordET = findViewById(R.id.password);
        progressBar = findViewById(R.id.loginProgressBar);
        otpET = findViewById(R.id.otpET);
        loginBtn = findViewById(R.id.loginButton);
        loginBtn.setOnClickListener(this);
        validateOTPBtn = findViewById(R.id.validateOTP);
        validateOTPBtn.setOnClickListener(this);
        validateTextView = findViewById(R.id.validateText);
        validateTextView.setOnClickListener(this);
        loginTextView = findViewById(R.id.loginText);
        loginTextView.setOnClickListener(this);
        forgotPasswordTextView = findViewById(R.id.forgotPassword);
        forgotPasswordTextView.setOnClickListener(this);
        resendOTPBtn = findViewById(R.id.resendOTPButton);
        resendOTPBtn.setOnClickListener(this);
        TextView privacyPolicy = findViewById(R.id.privacyPolicy);

        SpannableString content = new SpannableString("Privacy Policy");
        content.setSpan(new UnderlineSpan(), 0, content.length(), 0);
        privacyPolicy.setText(content);

        privacyPolicy.setOnClickListener(this);
    }

    private boolean checkPermission(String[] permissions) {
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(getApplicationContext(), permission) != PackageManager.PERMISSION_GRANTED) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_CODE_FOREGROUND_SERVICE_MEDIA_PLAYBACK) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                initView();
            } else {
                new android.app.AlertDialog.Builder(LoginActivity.this)
                        .setTitle("Permission Denied")
                        .setMessage("Without this permission the app will be not work !!")
                        .setPositiveButton("RE-TRY", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                ActivityCompat.requestPermissions(LoginActivity.this, PERMISSIONS_REQUIRED, REQUEST_PERMISSIONS);
                            }
                        })
                        .setNegativeButton("I'M SURE", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        }).show();
            }
        }

        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            initView();
        } else {
            new android.app.AlertDialog.Builder(LoginActivity.this)
                    .setTitle("Permission Denied")
                    .setMessage("Without this permission the app will be not work !!")
                    .setPositiveButton("RE-TRY", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            ActivityCompat.requestPermissions(LoginActivity.this, PERMISSIONS_REQUIRED, REQUEST_PERMISSIONS);
                        }
                    })
                    .setNegativeButton("I'M SURE", new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            dialog.dismiss();
                        }
                    }).show();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.e("Login Screen", "onResume Detected...");
        LogUtils.debug("Login Screen onResume Detected...");
        if (sharedpreferences != null && sharedpreferences.contains(userName) && sharedpreferences.contains(password)) {
            LogUtils.debug("LoginActivity User Details Found");
            SharedPreferences prefs = this.getSharedPreferences("WatchRx", MODE_PRIVATE);
            String username = prefs.getString("username", null);
            String password = prefs.getString("password", null);
            loginMethod(username, password);
        }
    }

    @Override
    public void onClick(View v) {
        constraintSet.clone(layout);
        if (v.getId() == R.id.loginButton) {
            String enteredUserName = userNameET.getText().toString();
            if (android.text.TextUtils.isEmpty(enteredUserName)) {
                userNameET.setError("Email Id required");
                return;
            }
            String enteredPassword = passwordET.getText().toString();
            if (TextUtils.isEmpty(enteredPassword)) {
                passwordET.setError("Password required");
                return;
            }
            loginMethod(enteredUserName, enteredPassword);
        } else if (v.getId() == R.id.validateOTP) {
            String enteredUserName = userNameET.getText().toString();
            if (android.text.TextUtils.isEmpty(enteredUserName)) {
                userNameET.setError("Email Id required");
                return;
            }
            String enteredPassword = passwordET.getText().toString();
            if (TextUtils.isEmpty(enteredPassword)) {
                passwordET.setError("Password required");
                return;
            }
            String enteredOTP = otpET.getText().toString();
            if (TextUtils.isEmpty(enteredOTP)) {
                otpET.setError("OTP required");
                return;
            }
            validateOTPMethod(enteredUserName, enteredPassword, enteredOTP);
        } else if (v.getId() == R.id.resendOTPButton) {
            String enteredUserName = userNameET.getText().toString();
            if (android.text.TextUtils.isEmpty(enteredUserName)) {
                userNameET.setError("Email Id required");
                return;
            }
            resendOTPApiCall(enteredUserName);
        } else if (v.getId() == R.id.validateText) {

            constraintSet.connect(R.id.forgotPassword, ConstraintSet.TOP, R.id.validateOTP, ConstraintSet.BOTTOM, 8);
            constraintSet.connect(R.id.loginText, ConstraintSet.TOP, R.id.validateOTP, ConstraintSet.BOTTOM, 8);
            constraintSet.applyTo(layout);

            otpET.setVisibility(View.VISIBLE);
            validateOTPBtn.setVisibility(View.VISIBLE);
            loginBtn.setVisibility(View.GONE);
            validateTextView.setVisibility(View.GONE);
            loginTextView.setVisibility(View.VISIBLE);
            forgotPasswordTextView.setVisibility(View.VISIBLE);
        } else if (v.getId() == R.id.loginText) {
            constraintSet.connect(R.id.forgotPassword, ConstraintSet.TOP, R.id.loginButton, ConstraintSet.BOTTOM, 8);
            constraintSet.connect(R.id.validateText, ConstraintSet.TOP, R.id.loginButton, ConstraintSet.BOTTOM, 8);
            constraintSet.applyTo(layout);

            otpET.setVisibility(View.GONE);
            validateOTPBtn.setVisibility(View.GONE);
            loginBtn.setVisibility(View.VISIBLE);
            validateTextView.setVisibility(View.VISIBLE);
            loginTextView.setVisibility(View.GONE);
            forgotPasswordTextView.setVisibility(View.VISIBLE);
            userNameET.setVisibility(View.VISIBLE);

            passwordET.setVisibility(View.VISIBLE);
            resendOTPBtn.setVisibility(View.GONE);

        } else if (v.getId() == R.id.forgotPassword) {
            constraintSet.connect(R.id.loginText, ConstraintSet.TOP, R.id.resendOTPButton, ConstraintSet.BOTTOM, 8);
            constraintSet.applyTo(layout);

            otpET.setVisibility(View.GONE);
            validateOTPBtn.setVisibility(View.GONE);
            loginBtn.setVisibility(View.GONE);
            validateTextView.setVisibility(View.GONE);
            loginTextView.setVisibility(View.VISIBLE);
            passwordET.setVisibility(View.GONE);
            resendOTPBtn.setVisibility(View.VISIBLE);
        } else if (v.getId() == R.id.privacyPolicy) {
            String privacyPolicyUrl = "https://watchrx.io/privacy-policy/";
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(privacyPolicyUrl));
            startActivity(browserIntent);
        }

    }

    private void resendOTPApiCall(String emailId) {
        try {
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.RESEND_OTP);
            loginModel.username = emailId.trim();
            String json = new Gson().toJson(loginModel);
            new RestAsyncTask(url, json, progressBar, new ResendOTPResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class ResendOTPResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult result) {
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error :", "");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("200")
                            || jsonObject.optString("responseMessage").equalsIgnoreCase("Success")) {

                        constraintSet.connect(R.id.forgotPassword, ConstraintSet.TOP, R.id.validateOTP, ConstraintSet.BOTTOM, 8);
                        constraintSet.connect(R.id.loginText, ConstraintSet.TOP, R.id.validateOTP, ConstraintSet.BOTTOM, 8);
                        constraintSet.applyTo(layout);

                        otpET.setVisibility(View.VISIBLE);
                        validateOTPBtn.setVisibility(View.VISIBLE);
                        loginBtn.setVisibility(View.GONE);
                        passwordET.setVisibility(View.VISIBLE);
                        validateTextView.setVisibility(View.GONE);
                        loginTextView.setVisibility(View.VISIBLE);
                        forgotPasswordTextView.setVisibility(View.VISIBLE);
                        resendOTPBtn.setVisibility(View.GONE);

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("We have just sent you an OTP  to your registered Email, Please Validate to reset your password.")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Failed to resend OTP to your registered Email.")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }

    private void closeKeyboard() {
        View view = this.getCurrentFocus();
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    private void loginMethod(String enteredUserName, String enteredPassword) {
        Log.e("Login Method", "Login Method called \n\n");
        try {
            closeKeyboard();
            LogUtils.debug("Going call AUTHENTICATE API with User Name:" + enteredUserName + " Password: " + enteredPassword);
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.AUTHENTICATE_URL);
            loginModel.username = enteredUserName.trim();
            loginModel.password = enteredPassword.trim();
            userNameET.setText(enteredUserName);
            passwordET.setText(enteredPassword);
            String json = new Gson().toJson(loginModel);
            SharedPreferences.Editor editor = sharedpreferences.edit();
            editor.putString(userName, enteredUserName);
            editor.putString(password, enteredPassword);
            editor.apply();
            new RestAsyncTask(url, json, progressBar, new AuthTokenResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void validateOTPMethod(String enteredUserName, String enteredPassword, String otp) {
        try {
            LoginModel loginModel = new LoginModel();
            LogUtils.debug("Going to get auth token");
            URL url = new URL(URLConstants.VALIDATE_OTP);
            loginModel.emailId = enteredUserName.trim();
            loginModel.password = enteredPassword.trim();
            loginModel.otp = otp;
            String json = new Gson().toJson(loginModel);
            SharedPreferences.Editor editor = sharedpreferences.edit();
            editor.putString(userName, enteredUserName);
            editor.putString(password, enteredPassword);
            editor.apply();
            new RestAsyncTask(url, json, progressBar, new ValidateOTPResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class ValidateOTPResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error :", "");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            || jsonObject.optString("responseMessage").equalsIgnoreCase("OTP is already validated.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage(jsonObject.optString("responseMessage"))
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        Toast.makeText(LoginActivity.this, "OTP Validation Success.", Toast.LENGTH_SHORT).show();
                        Intent intent = new Intent(LoginActivity.this, SplashActivity.class);
                        startActivity(intent);
                        finishAffinity();
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }

    private class AuthTokenResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            LogUtils.debug("Received the response from AuthTokenResponseHandler");
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error :", "Failed to get authenticate token");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("Invalid Credentials.")) {

                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Please Validate OTP and Try Login")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else if (jsonObject.optString("responseCode").equalsIgnoreCase("500")
                            && jsonObject.optString("responseMessage").equalsIgnoreCase("OTP is already validated. Invalid Credentials.")) {
                        SharedPreferences.Editor editor = sharedpreferences.edit();
                        editor.clear();
                        editor.apply();
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(LoginActivity.this)
                                        .setTitle("WatchRx")
                                        .setMessage(jsonObject.optString("responseMessage"))
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        Globals.authToken = jsonObject.optString("token");
                        Toast.makeText(LoginActivity.this, "User verified...", Toast.LENGTH_SHORT).show();
                        Intent intent = new Intent(LoginActivity.this, SplashActivity.class);
                        startActivity(intent);
                        finishAffinity();
                    }
                } catch (Exception ignored) {
                }
            }
        }
    }
}