package com.watchrx.watchrxhealth.auth;

import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.style.UnderlineSpan;
import android.util.Log;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.gson.Gson;
import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.EmailHelper;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONObject;

import java.net.URL;
import java.util.Objects;

public class ResendOTPScreen extends AppCompatActivity {

    private Dialog loadingDialog;

    private EditText userNameET;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_resend_otpscreen);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.layout), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        Objects.requireNonNull(getSupportActionBar()).hide();
        userNameET = findViewById(R.id.userName);
        Button button = findViewById(R.id.loginButton);

        button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String email = userNameET.getText() != null ? userNameET.getText().toString().trim() : null;
                if (email == null || email.isEmpty() || (!EmailHelper.isValidEmail(email) && isValidPhoneNumber(email))) {
                    Toast.makeText(ResendOTPScreen.this, "Enter valid user name", Toast.LENGTH_SHORT).show();
                    return;
                }
                resendOTPApiCall(email);
            }
        });
        TextView privacyPolicy = findViewById(R.id.privacyPolicy);

        SpannableString content = new SpannableString("Privacy Policy");
        content.setSpan(new UnderlineSpan(), 0, content.length(), 0);
        privacyPolicy.setText(content);

        privacyPolicy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String privacyPolicyUrl = "https://watchrx.io/privacy-policy/";
                Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(privacyPolicyUrl));
                startActivity(browserIntent);
            }
        });

        TextView loginBack = findViewById(R.id.loginBack);
        SpannableString content1 = new SpannableString("Go back to login?");
        content1.setSpan(new UnderlineSpan(), 0, content1.length(), 0);
        loginBack.setText(content1);
        loginBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(ResendOTPScreen.this, LoginScreen.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
                finish();
            }
        });
    }

    private boolean isValidPhoneNumber(String phone) {
        return !phone.matches("^\\+[1-9]\\d{9,14}$");
    }

    private void resendOTPApiCall(String emailId) {
        try {
            showLoadingDialog();
            LoginModel loginModel = new LoginModel();
            URL url = new URL(URLConstants.RESEND_OTP);
            loginModel.username = emailId.trim();
            String json = new Gson().toJson(loginModel);
            new RestAsyncTask(url, json, null, new ResendOTPResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class ResendOTPResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            if (loadingDialog != null && !loadingDialog.isShowing()) {
                loadingDialog.dismiss();
            }

            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                Log.e("Error :", "");
            } else {
                try {
                    final JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    if (jsonObject.optString("responseCode").equalsIgnoreCase("200")
                            || jsonObject.optString("responseMessage").equalsIgnoreCase("Success")) {

                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(ResendOTPScreen.this)
                                        .setTitle("WatchRx")
                                        .setMessage("We have just sent you an OTP  to your registered Email, Please Validate to reset your password.")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                                if (loadingDialog != null) {
                                                    loadingDialog.dismiss();
                                                }
                                                Intent intent = new Intent(ResendOTPScreen.this, LoginScreen.class);
                                                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                                startActivity(intent);
                                                finish();
                                            }
                                        }).show();
                            }
                        });
                    } else {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                new android.app.AlertDialog.Builder(ResendOTPScreen.this)
                                        .setTitle("WatchRx")
                                        .setMessage("Failed to resend OTP to your registered Email.")
                                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                                            public void onClick(DialogInterface dialog, int which) {
                                                dialog.dismiss();
                                                if (loadingDialog != null) {
                                                    loadingDialog.dismiss();
                                                }
                                            }
                                        }).show();
                            }
                        });
                    }
                } catch (Exception ignored) {
                    ignored.printStackTrace();
                }
            }
        }
    }

    private void setupLoadingDialog() {
        loadingDialog = new Dialog(this);
        loadingDialog.setContentView(R.layout.dialog_loading);
        loadingDialog.setCancelable(false);

        loadingDialog.setOnShowListener(dialog -> {
            ImageView loaderImage = loadingDialog.findViewById(R.id.loaderImage);
            Animation rotateAnimation = AnimationUtils.loadAnimation(this, R.anim.rotate);
            loaderImage.startAnimation(rotateAnimation);
        });
    }

    private void showLoadingDialog() {
        if (loadingDialog == null) {
            setupLoadingDialog();
        }

        if (!loadingDialog.isShowing()) {
            loadingDialog.show();
        }
    }
}