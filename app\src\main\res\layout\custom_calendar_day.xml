<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/custom_day_background"
    android:padding="4dp">

    <!-- Label for the day number -->
    <TextView
        android:id="@+id/dayLabel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/black"
        android:textSize="16sp" />

    <!-- Optional icon for events -->
    <ImageView
        android:id="@+id/dayIcon"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:src="@drawable/sample_circle"
        android:visibility="gone" />
</RelativeLayout>
