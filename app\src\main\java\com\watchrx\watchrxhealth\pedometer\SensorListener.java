
package com.watchrx.watchrxhealth.pedometer;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.RequiresApi;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.Date;


public class SensorListener extends Service implements SensorEventListener {

    private static int lastSaveSteps;

    private int todayOffset;
    private int since_boot;

    @Override
    public void onAccuracyChanged(final Sensor sensor, int accuracy) {
    }

    @Override
    public void onSensorChanged(final SensorEvent event) {
        Log.e("Sensor Changed", "-----------------" + event.values[0]);

        if (todayOffset == Integer.MIN_VALUE || todayOffset == 0) {
            todayOffset = -(int) event.values[0];
            Database db = Database.getInstance(WatchApp.getContext());
            db.insertNewDay(Util.getToday(), (int) event.values[0]);
            LogUtils.debug("First Record Inserted DB  at :" + Util.getToday() + " Steps : " + (int) event.values[0]);
            db.close();
        }

        if (event.values[0] > Integer.MAX_VALUE) {
            LogUtils.debug("probably not a real value: " + event.values[0]);
            return;
        } else {
            int steps = (int) event.values[0];
            if (lastSaveSteps == steps) {
                Log.e("Sensor Changed", "" + steps);
            } else {
                Database db = Database.getInstance(WatchApp.getContext());
                db.saveCurrentSteps(steps);
                db.close();
            }
        }
        since_boot = (int) event.values[0];
        int steps_today = Math.max(todayOffset + since_boot, 0);
        Log.i("todayOffset", " todayOffset : " + todayOffset);
        Log.i("since_boot", " since_boot : " + since_boot);
        Log.i("Current Steps", " Date : " + new Date() + " -" + steps_today);
        // LogUtils.debug("Current Steps :" + steps_today + " Date : " + new Date());
    }


    @Override
    public IBinder onBind(final Intent intent) {
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public int onStartCommand(final Intent intent, int flags, int startId) {
        reRegisterSensor();
        return START_STICKY;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        LogUtils.debug("SensorListener onCreate");
        Database db = Database.getInstance(WatchApp.getContext());
        todayOffset = db.getSteps(Util.getToday());
        Log.i("todayOffset", "" + todayOffset);
        since_boot = db.getCurrentSteps();
        Log.i("since_boot", "" + since_boot);
        db.close();
    }

    @Override
    public void onTaskRemoved(final Intent rootIntent) {
        super.onTaskRemoved(rootIntent);
        ((AlarmManager) getSystemService(Context.ALARM_SERVICE))
                .set(AlarmManager.RTC, System.currentTimeMillis() + 30000, PendingIntent
                        .getService(this, 3, new Intent(this, SensorListener.class), 0));
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.i("Destory", "SensorListener onDestroy");

        SensorManager sm = (SensorManager) getSystemService(SENSOR_SERVICE);
        try {
            sm.unregisterListener(this);
        } catch (Exception e) {

            e.printStackTrace();
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    private void reRegisterSensor() {
        SensorManager sm = (SensorManager) getSystemService(SENSOR_SERVICE);
        try {
            sm.unregisterListener(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
        sm.registerListener(this, sm.getDefaultSensor(Sensor.TYPE_STEP_COUNTER),
                SensorManager.SENSOR_DELAY_FASTEST, (int) (15000));
    }
}
