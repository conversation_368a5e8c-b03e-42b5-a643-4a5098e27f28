<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    android:id="@+id/event_card"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/lato_regular"
            android:text="My Text"
            android:textColor="#000"
            android:textSize="18sp"
            android:textStyle="bold" />


        <TextView
            android:id="@+id/description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/lato_regular"
            android:text="My Text"
            android:textColor="#000"
            android:textSize="15sp" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/timeRange"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/background_with_border"
                android:drawablePadding="8dp"
                android:fontFamily="@font/lato_bold"
                android:gravity="center_vertical"
                android:padding="8dp"
                android:textColor="#000000"
                android:textSize="14sp"
                app:drawableLeftCompat="@drawable/clock" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView>