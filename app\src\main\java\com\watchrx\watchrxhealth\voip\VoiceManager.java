package com.watchrx.watchrxhealth.voip;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;

import com.google.firebase.messaging.FirebaseMessaging;
import com.twilio.voice.CallInvite;
import com.twilio.voice.RegistrationException;
import com.twilio.voice.RegistrationListener;
import com.twilio.voice.Voice;
import com.watchrx.watchrxhealth.gcm.NotificationHelper;

public class VoiceManager {
    private static final String TAG = "VoiceManager";
    private static VoiceManager instance;

    private final Context appContext;
    private String accessToken = "";
    private String fcmToken = "";

    private CallInvite activeCallInvite;

    public static VoiceManager getInstance(Context context) {
        if (instance == null) {
            instance = new VoiceManager(context.getApplicationContext());
        }
        return instance;
    }

    private VoiceManager(Context context) {
        this.appContext = context.getApplicationContext();
//        fetchAccessTokenAndRegister();
    }

    private void fetchAccessTokenAndRegister() {
        accessToken = "YOUR_TWILIO_ACCESS_TOKEN";

        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(task -> {
            if (!task.isSuccessful()) {
                Log.e(TAG, "Failed to get FCM token", task.getException());
                return;
            }

            fcmToken = task.getResult();
            Log.d(TAG, "FCM Token: " + fcmToken);
            Voice.register(
                    accessToken,
                    Voice.RegistrationChannel.FCM,
                    fcmToken,
                    registrationListener
            );
        });
    }

    private final RegistrationListener registrationListener = new RegistrationListener() {
        @Override
        public void onRegistered(@NonNull String accessToken, @NonNull String fcmToken) {
            Log.d(TAG, "Successfully registered with Twilio Voice.");
        }

        @Override
        public void onError(RegistrationException error, @NonNull String accessToken, @NonNull String fcmToken) {
            Log.e(TAG, "Twilio Voice registration failed: " + error.getMessage());
        }
    };

    public void handleCallInvite(CallInvite invite) {
        this.activeCallInvite = invite;
        Log.d(TAG, "Received CallInvite from: " + invite.getFrom());
        NotificationHelper.showIncomingCallNotification(appContext, invite);
    }

    public CallInvite getActiveCallInvite() {
        return activeCallInvite;
    }

    public void clearActiveCallInvite() {
        activeCallInvite = null;
    }

    public String getAccessToken() {
        return accessToken;
    }

}
