package com.watchrx.watchrxhealth;

import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.github.mikephil.charting.charts.Chart;
import com.github.mikephil.charting.charts.LineChart;
import com.github.mikephil.charting.components.AxisBase;
import com.github.mikephil.charting.components.Legend;
import com.github.mikephil.charting.components.XAxis;
import com.github.mikephil.charting.components.YAxis;
import com.github.mikephil.charting.data.Entry;
import com.github.mikephil.charting.data.LineData;
import com.github.mikephil.charting.data.LineDataSet;
import com.github.mikephil.charting.formatter.ValueFormatter;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.adapter.VitalDetailsAdapter;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.models.LatestVitalDataModel;
import com.watchrx.watchrxhealth.models.ResponseDTO;
import com.watchrx.watchrxhealth.models.VitalDataModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.LogUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

public class VitalDetailsActivity extends AppCompatActivity {
    private RecyclerView recyclerView;
    TextView noData;
    String vitalType = null;
    TextView mTxtViewDate;
    ImageView mImageViewPrevious, mImageViewNext;
    private ProgressBar progressbar;
    private LineChart lineChart;

    private static Date edDate = new Date(System.currentTimeMillis());
    private static Date stDate = new Date(System.currentTimeMillis() - 6L * 24 * 60 * 60 * 1000);

    private static final SimpleDateFormat startDateDF = new SimpleDateFormat("yyyy-MM-dd 00:00:00", Locale.ENGLISH);
    private static final SimpleDateFormat currentDateDF = new SimpleDateFormat("yyyy-MM-dd 23:59:59", Locale.ENGLISH);

    private static Boolean isGraphShowing = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_vital_details);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);

        recyclerView = findViewById(R.id.vitalRv);
        noData = findViewById(R.id.nodata);
        progressbar = findViewById(R.id.progressbar);
//        constraintLayout = findViewById(R.id.clDate);
        vitalType = getIntent().getStringExtra("vitalType");
        if (vitalType == null) {
            return;
        }
        getSupportActionBar().setTitle(vitalType);
        lineChart = findViewById(R.id.lineChart);
        setToggeleButton();
        setGraphData();

        mTxtViewDate = findViewById(R.id.date);
        mImageViewPrevious = findViewById(R.id.before_date);
        mImageViewNext = findViewById(R.id.after_date);

        mTxtViewDate.setText(getLast7Days());

        mTxtViewDate.setOnClickListener(v -> {
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH);
            int day = calendar.get(Calendar.DAY_OF_MONTH);

            DatePickerDialog datePickerDialog = new DatePickerDialog(this,
                    (view, selectedYear, selectedMonth, selectedDayOfMonth) -> {
                        Calendar selectedDate = Calendar.getInstance();
                        selectedDate.set(selectedYear, selectedMonth, selectedDayOfMonth);
                        String dateRange = getDateRange(selectedDate);
                        mTxtViewDate.setText(dateRange);
                        if (isGraphShowing) {
                            setGraphData();
                        } else {
                            getVitalDetails();
                        }
                    },
                    year, month, day);
            datePickerDialog.getDatePicker().setMaxDate(calendar.getTimeInMillis());
            datePickerDialog.show();
        });
        setListener();
    }

    private String getDateRange(Calendar selectedDate) {
        Calendar startDate = (Calendar) selectedDate.clone();
        Date endDate = selectedDate.getTime();
        startDate.add(Calendar.DAY_OF_YEAR, -6);
        Date startDateObj = startDate.getTime();

        SimpleDateFormat dateFormat = new SimpleDateFormat("d MMM", Locale.getDefault());
        String start = dateFormat.format(startDateObj);
        String end = dateFormat.format(endDate);

        stDate = startDateObj;
        edDate = endDate;

        return start + " - " + end;
    }

    private String getLast7Days() {
        Calendar calendar = Calendar.getInstance();
        return getDateRange(calendar);
    }

    private void setToggeleButton() {
        RadioGroup radioGroupToggle = findViewById(R.id.radioGroupToggle);
        radioGroupToggle.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.radioGraph) {
                isGraphShowing = true;
                recyclerView.setVisibility(View.GONE);
                setGraphData();
            } else {
                isGraphShowing = false;
                lineChart.setVisibility(View.GONE);
                getVitalDetails();
            }
        });
    }

    private void setGraphData() {
        lineChart.setVisibility(View.VISIBLE);
        lineChart.setNoDataText("No data available");
        lineChart.setNoDataTextColor(Color.RED);
        lineChart.getPaint(Chart.PAINT_INFO).setTextSize(40f);
        getVitalGraphData();
    }

    private void setListener() {
        View.OnClickListener listener = v -> {
            // Get current date and reset to start of the day
            Calendar currentDate = Calendar.getInstance();

            boolean isNext = (v == mImageViewNext);

            if (stDate != null && edDate != null) {
                // Adjust the start and end dates
                Calendar calendarStart = Calendar.getInstance();
                calendarStart.setTime(stDate);

                Calendar calendarEnd = Calendar.getInstance();
                calendarEnd.setTime(edDate);

                int adjustment = isNext ? 1 : -1;
                calendarStart.add(Calendar.DAY_OF_MONTH, adjustment);
                calendarEnd.add(Calendar.DAY_OF_MONTH, adjustment);

                // Temporarily update edDate for validation
                Date adjustedEndDate = calendarEnd.getTime();

                // Check if the adjusted end date is in the future
                if (adjustedEndDate.after(currentDate.getTime())) {
                    Toast.makeText(this, "End date cannot be in the future", Toast.LENGTH_SHORT).show();
                    return; // Stop further execution
                }

                // Update global variables after validation
                stDate = calendarStart.getTime();
                edDate = adjustedEndDate;

                // Format and display the updated date range
                SimpleDateFormat dateFormat = new SimpleDateFormat("d MMM", Locale.getDefault());
                String updatedStartDate = dateFormat.format(stDate);
                String updatedEndDate = dateFormat.format(edDate);

                mTxtViewDate.setText(String.format("%s - %s", updatedStartDate, updatedEndDate));

                // Update the UI or data depending on the current view
                if (isGraphShowing) {
                    setGraphData();
                } else {
                    getVitalDetails();
                }
            }
        };

        mImageViewPrevious.setOnClickListener(listener);
        mImageViewNext.setOnClickListener(listener);
    }


    private void getVitalDetails() {
        try {
            String startDateStr = startDateDF.format(stDate);
            String currentDate = currentDateDF.format(edDate);
            try {
                LogUtils.debug("Going to Vital Data");
                URL url = new URL(URLConstants.VITAL_DETAILS_DATA);
                String patientId = PatientDetails.getFromDB().getPatientId();
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate("patientId", patientId);
                jsonObject.accumulate("vitalType", vitalType);
                jsonObject.accumulate("startDate", startDateStr);
                jsonObject.accumulate("endDate", currentDate);
                LogUtils.debug("Getting Vital Info: " + jsonObject.toString());
                new RestAsyncTask(url, jsonObject.toString(), progressbar, new VitalsDetailsResponseHandler(), null).execute();
            } catch (MalformedURLException | JSONException e) {
                LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class VitalsDetailsResponseHandler implements TaskResultHandler {
        @SuppressLint("NotifyDataSetChanged")
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }
            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }

            if (result != null) {
                try {
                    if ((result instanceof String)) {
                        LogUtils.debug("Vital Response : " + result);
                        LatestVitalDataModel responseMessage = new Gson().fromJson((String) result, LatestVitalDataModel.class);
                        if (responseMessage != null && responseMessage.isStatus() && responseMessage.getData() != null && responseMessage.getData().size() > 0) {
                            List<VitalDataModel> dataModelList = new ArrayList<>(responseMessage.data);

                            Collections.sort(dataModelList, new Comparator<VitalDataModel>() {
                                public int compare(VitalDataModel m1, VitalDataModel m2) {
                                    return m2.getDate().compareTo(m1.getDate());
                                }
                            });
                            noData.setVisibility(View.GONE);
                            recyclerView.setVisibility(View.VISIBLE);
                            recyclerView.setHasFixedSize(true);
                            recyclerView.setLayoutManager(new LinearLayoutManager(VitalDetailsActivity.this));
                            recyclerView.setAdapter(new VitalDetailsAdapter(VitalDetailsActivity.this, dataModelList, new VitalDetailsAdapter.OnItemClickListener() {
                                @Override
                                public void onItemClick(VitalDataModel item) {
                                }
                            }));
                            recyclerView.invalidate();
                            Objects.requireNonNull(recyclerView.getAdapter()).notifyDataSetChanged();
                        } else {
                            recyclerView.setVisibility(View.GONE);
                            noData.setVisibility(View.VISIBLE);
                            noData.setText(R.string.no_data_found);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void getVitalGraphData() {
        if (vitalType.equalsIgnoreCase("Pedometer")) {
            getVitalPedometerGraphData();
        } else {
            SimpleDateFormat currentDateDF = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
            String currentDate = currentDateDF.format(edDate);
            try {
                LogUtils.debug("Going to Vital Graph Data");
                URL url = new URL(URLConstants.VITAL_GRAPH_DATA);

                JSONArray vitalTypeNameList = new JSONArray();
                if (vitalType.equalsIgnoreCase("Blood Pressure")) {
                    vitalTypeNameList.put("Systolic Blood Pressure");
                    vitalTypeNameList.put("Diastolic Blood Pressure");
                } else if (vitalType.equalsIgnoreCase("Blood Sugar")) {
                    vitalTypeNameList.put("Fasting Blood Sugar");
                    vitalTypeNameList.put("Random Blood Sugar");
                } else {
                    vitalTypeNameList.put(vitalType);
                }

                String patientId = PatientDetails.getFromDB().getPatientId();
                JSONObject jsonObject = new JSONObject();
                jsonObject.accumulate("patientId", patientId);
                jsonObject.accumulate("vitalTypeNameList", vitalTypeNameList);
                jsonObject.accumulate("periodType", "WEEKLY");
                jsonObject.accumulate("requestedDate", currentDate);
                LogUtils.debug("Getting Vital Info: " + jsonObject.toString());
                Log.e("Vital Details", "Getting Vital Info: " + jsonObject.toString());
                new RestAsyncTask(url, jsonObject.toString(), progressbar, new VitalsGraphResponseHandler(), null).execute();
            } catch (MalformedURLException | JSONException e) {
                LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private void getVitalPedometerGraphData() {
        SimpleDateFormat currentDateDF = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        String currentDate = currentDateDF.format(edDate);
        try {
            LogUtils.debug("Going to Vital Graph Data");
            URL url = new URL(URLConstants.VITAL_PEDO_GRAPH_DATA);
            String patientId = PatientDetails.getFromDB().getPatientId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.accumulate("patientId", patientId);
            jsonObject.accumulate("periodType", "WEEKLY");
            jsonObject.accumulate("requestedDate", currentDate);
            LogUtils.debug("Getting Vital Info: " + jsonObject);
            Log.e("Vital Details", "Getting Vital Info: " + jsonObject);
            new RestAsyncTask(url, jsonObject.toString(), progressbar, new VitalsPedoGraphResponseHandler(), null).execute();
        } catch (MalformedURLException | JSONException e) {
            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
            e.printStackTrace();
        }
    }

    private class VitalsGraphResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null || handlerResult.getResult() == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
                return;
            }
            try {
                Object result = handlerResult.getResult();
                if (result instanceof String) {
                    LogUtils.debug("Vital Response : " + result);
                    ResponseDTO responseMessage = new Gson().fromJson((String) result, ResponseDTO.class);
                    Log.i("RESPONSE:", "" + responseMessage);
                    if (responseMessage != null && responseMessage.isSuccess()
                            && responseMessage.getMeasuredDates() != null
                            && !responseMessage.getVitalsCountGraphVOs().isEmpty()) {
                        handleValidResponse(responseMessage);
                    } else {
                        showNoData();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private class VitalsPedoGraphResponseHandler implements TaskResultHandler {
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null || handlerResult.getResult() == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
                return;
            }
            try {
                Object result = handlerResult.getResult();
                if (result instanceof String) {
                    LogUtils.debug("Vital Response : " + result);
                    ResponseDTO responseMessage = new Gson().fromJson((String) result, ResponseDTO.class);
                    Log.i("RESPONSE:", "" + responseMessage);
                    if (responseMessage != null && responseMessage.isSuccess()
                            && responseMessage.getPeriod() != null
                            && !responseMessage.getVitalsCountGraphVOs().isEmpty()) {
                        handleValidPedoResponse(responseMessage);
                    } else {
                        showNoData();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void handleValidPedoResponse(ResponseDTO responseMessage) {
        List<Double> counts = responseMessage.getVitalsCountGraphVOs().get(0).getCounts();
        if (counts == null || counts.isEmpty()) {
            showNoData();
            return;
        }
        List<Double> counts1 = responseMessage.getVitalsCountGraphVOs().size() > 1
                ? responseMessage.getVitalsCountGraphVOs().get(1).getCounts()
                : new ArrayList<>();

        List<String> formattedDates = formatDates(responseMessage.getPeriod());

        float min = responseMessage.getThresholdConfigVO().getPedometerStepCountCriticalMin() - 10;
        float max = responseMessage.getThresholdConfigVO().getPedometerStepCountCriticalMax() + 10;

        LineData lineData = prepareLineData(responseMessage, counts, counts1, "Pedometer", "");
        configureLegends();
        configureLineChart(formattedDates, lineData, min, max);
    }

    private void handleValidResponse(ResponseDTO responseMessage) {
        List<Double> counts = responseMessage.getVitalsCountGraphVOs().get(0).getCounts();
        if (counts == null || counts.isEmpty()) {
            showNoData();
            return;
        }

        List<Double> counts1 = responseMessage.getVitalsCountGraphVOs().size() > 1
                ? responseMessage.getVitalsCountGraphVOs().get(1).getCounts()
                : new ArrayList<>();

        List<String> formattedDates = formatDates(responseMessage.getMeasuredDates());

        String label1 = responseMessage.getVitalsCountGraphVOs().get(0).getVitalTypeName();
        String label2 = responseMessage.getVitalsCountGraphVOs().size() > 1 ? responseMessage.getVitalsCountGraphVOs().get(1).getVitalTypeName() : "";

        float min = responseMessage.getThresholdConfig().geteList().get(0).getVitalCriticalMin() - 10;
        float max = responseMessage.getThresholdConfig().geteList().get(0).getVitalCriticalMax() + 10;

        LineData lineData = prepareLineData(responseMessage, counts, counts1, label1, label2);
        configureLegends();
        configureLineChart(formattedDates, lineData, min, max);
    }

    private List<String> formatDates(List<String> dates) {
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        SimpleDateFormat outputFormat = new SimpleDateFormat("MM/dd", Locale.getDefault());
        List<String> formattedDates = new ArrayList<>();
        for (String date : dates) {
            try {
                formattedDates.add(outputFormat.format(Objects.requireNonNull(inputFormat.parse(date))));
            } catch (ParseException e) {
                e.printStackTrace();
                formattedDates.add("");
            }
        }
        return formattedDates;
    }

    private LineData prepareLineData(ResponseDTO responseMessage, List<Double> counts, List<Double> counts1, String labele1, String label2) {
        List<Integer> circleColors1 = new ArrayList<>();
        List<Entry> entries1 = getEntries(counts, circleColors1);
        LineDataSet dataSet1 = createLineDataSet(entries1, circleColors1, labele1, Color.BLUE);

        if (counts1.isEmpty()) {
            return new LineData(dataSet1);
        }
        List<Integer> circleColors2 = new ArrayList<>();
        List<Entry> entries2 = getEntries(counts1, circleColors2);
        LineDataSet dataSet2 = createLineDataSet(entries2, circleColors2, label2, Color.parseColor("#FFA500"));

        return new LineData(dataSet1, dataSet2);
    }

    private void configureLegends() {
        Typeface customFont = ResourcesCompat.getFont(WatchApp.getContext(), R.font.lato_regular);
        Legend legend = lineChart.getLegend();
        legend.setVerticalAlignment(Legend.LegendVerticalAlignment.BOTTOM);
        legend.setHorizontalAlignment(Legend.LegendHorizontalAlignment.CENTER);
        legend.setOrientation(Legend.LegendOrientation.HORIZONTAL);
        legend.setDrawInside(false);
        legend.setYOffset(10f);
        legend.setTextSize(14f);
        legend.setFormSize(14f);
        legend.setTypeface(customFont);
    }

    private LineDataSet createLineDataSet(List<Entry> entries, List<Integer> circleColors, String label, int color) {
        LineDataSet dataSet = new LineDataSet(entries, label);
        dataSet.setColor(color);
        dataSet.setDrawCircles(true);
        dataSet.setCircleRadius(5f);
        dataSet.setDrawCircleHole(false);
        dataSet.setLineWidth(2f);
        dataSet.setValueTextSize(14f);
        dataSet.setValueTextColor(Color.BLACK);
        dataSet.setCircleColors(circleColors);
        return dataSet;
    }

    private void configureLineChart(List<String> formattedDates, LineData lineData, float min, float max) {
        lineChart.setData(lineData);
        lineChart.getDescription().setEnabled(false);
        lineChart.setDrawGridBackground(false);
        lineChart.animateX(1000);
        lineChart.animateY(1000);

        configureXAxis(formattedDates);
        configureYAxis(min, max);

        lineChart.invalidate();
    }

    private void configureXAxis(List<String> formattedDates) {
        XAxis xAxis = lineChart.getXAxis();
        xAxis.setPosition(XAxis.XAxisPosition.BOTTOM);
        xAxis.setGranularity(1f);
        xAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getAxisLabel(float value, AxisBase axis) {
                int index = Math.round(value);
                if (index >= 0 && index < formattedDates.size()) {
                    return formattedDates.get(index);
                } else {
                    return "";
                }
            }
        });
    }

    private void configureYAxis(float min, float max) {
        Typeface customFont = ResourcesCompat.getFont(WatchApp.getContext(), R.font.lato_regular);
        YAxis yAxis = lineChart.getAxisLeft();
        yAxis.setGranularity(3f);
        yAxis.setTextSize(12f);
        yAxis.setTypeface(customFont);
        yAxis.setGranularityEnabled(true);
        yAxis.setAxisMinimum(min);
        yAxis.setAxisMaximum(max);
        yAxis.setValueFormatter(new ValueFormatter() {
            @Override
            public String getFormattedValue(float value) {
                return String.valueOf((int) value);
            }
        });
        lineChart.getAxisRight().setEnabled(false);
    }

    private void showNoData() {
        lineChart.setVisibility(View.GONE);
        noData.setVisibility(View.VISIBLE);
        noData.setText(R.string.no_data_found);
        lineChart.invalidate();
    }

    @NonNull
    private List<Entry> getEntries(List<Double> counts, List<Integer> circleColors) {
        List<Entry> entries = new ArrayList<>();
        for (int i = 0; i < counts.size(); i++) {
            Double value = counts.get(i);
            if (value == null || value == 0) {
                Double interpolatedValue = interpolateValue(counts, i);
                entries.add(new Entry(i, interpolatedValue != null ? interpolatedValue.floatValue() : 0f));
                circleColors.add(Color.GRAY);
            } else {
                entries.add(new Entry(i, value.floatValue()));
                circleColors.add(Color.GREEN);
            }
        }
        return entries;
    }

    private Double interpolateValue(List<Double> counts, int index) {
        Double previousValue = null;
        for (int i = index - 1; i >= 0; i--) {
            if (counts.get(i) != null) {
                previousValue = counts.get(i);
                break;
            }
        }
        Double nextValue = null;
        for (int i = index + 1; i < counts.size(); i++) {
            if (counts.get(i) != null) {
                nextValue = counts.get(i);
                break;
            }
        }
        if (previousValue != null && nextValue != null) {
            return previousValue + (nextValue - previousValue) / 2;
        }
        return previousValue != null ? previousValue : nextValue;
    }
}