package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.ViewAllTextMessageModel;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class ViewAllTextMessageAdapter extends RecyclerView.Adapter<ViewAllTextMessageAdapter.MyViewHolder> {
    Context mContext;
    List<ViewAllTextMessageModel> detailsModelsList;

    public interface OnItemClickListener {
        void onItemClick(ViewAllTextMessageModel item);
    }

    private final ViewAllTextMessageAdapter.OnItemClickListener listener;

    public ViewAllTextMessageAdapter(Context context, List<ViewAllTextMessageModel> detailsModelsList, ViewAllTextMessageAdapter.OnItemClickListener listener) {
        this.mContext = context;
        this.detailsModelsList = detailsModelsList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewAllTextMessageAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.view_text_message_item, parent, false);
        return new ViewAllTextMessageAdapter.MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewAllTextMessageAdapter.MyViewHolder holder, int position) {
        ViewAllTextMessageModel detailsModel = detailsModelsList.get(position);
        holder.messageTime.setText(getDateReadable(detailsModel.getDate()));
        holder.message.setText(detailsModel.getMessage());
        holder.reply.setText(detailsModel.getReply());
        holder.incomingMessageTime.setText(getDateReadableTime(detailsModel.getDate()));
        holder.outgoingMessageTime.setText(getDateReadableTime(detailsModel.getDate()));
        holder.bind(detailsModel, listener);
    }

    @Override
    public int getItemCount() {
        return detailsModelsList.size();
    }

    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView messageTime, message, reply, incomingMessageTime, outgoingMessageTime;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            messageTime = itemView.findViewById(R.id.messageDateTime);
            message = itemView.findViewById(R.id.incomingMessage);
            reply = itemView.findViewById(R.id.outgoingMessage);
            incomingMessageTime = itemView.findViewById(R.id.incomingMessageTime);
            outgoingMessageTime = itemView.findViewById(R.id.outgoingMessageTime);
        }

        public void bind(final ViewAllTextMessageModel item, final ViewAllTextMessageAdapter.OnItemClickListener listener) {
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(item);
                }
            });
        }
    }

    private String getDateReadable(String dateStr) {
        try {
            DateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            DateFormat targetFormat = new SimpleDateFormat("MMM d, yyyy", Locale.ENGLISH);
            Date date = originalFormat.parse(dateStr);
            assert date != null;
            return targetFormat.format(date);
        } catch (Exception e) {
            return dateStr;
        }
    }

    private String getDateReadableTime(String dateStr) {
        try {
            DateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            DateFormat targetFormat = new SimpleDateFormat("hh:mm a", Locale.ENGLISH);
            Date date = originalFormat.parse(dateStr);
            assert date != null;
            return targetFormat.format(date);
        } catch (Exception e) {
            return dateStr;
        }
    }
}