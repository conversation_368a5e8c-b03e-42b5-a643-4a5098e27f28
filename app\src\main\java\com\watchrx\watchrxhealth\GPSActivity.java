package com.watchrx.watchrxhealth;

import android.content.Intent;
import android.media.MediaPlayer;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.util.Objects;

public class GPSActivity extends AppCompatActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_g_p_s);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        final Intent intent = getIntent();
        final String CaregiverName = intent.getStringExtra("CaregiverName");
        final String CaregiverNumber = intent.getStringExtra("CaregiverNumber");

        final Animation animation = new AlphaAnimation(1, 0); // Change alpha from fully visible to invisible
        animation.setDuration(200); // duration
        animation.setInterpolator(new LinearInterpolator()); // do not alter animation rate
        animation.setRepeatCount(Animation.INFINITE); // Repeat animation infinitely
        animation.setRepeatMode(Animation.REVERSE);

        final View imageView = findViewById(R.id.circle4);
        if (imageView != null) {
            imageView.startAnimation(animation);
        }

        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                final String from = intent.getStringExtra("From");

                TextView textView = findViewById(R.id.tvGpsCrossedMsg);
                if (textView != null) {
                    if (from.equalsIgnoreCase("Crossed")) {
                        textView.setText(R.string.far_home);
                    } else {
                        textView.setText(R.string.back_home);
                    }
                }
                if (from.equalsIgnoreCase("Crossed")) {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("You have come too far from home$ An alert has been sent to $" + CaregiverName + "$" + "Calling$" + CaregiverName);
                    } else {
                        GeneralUtils.speak("Usted está lejos de su casa$ Se ha enviado una alerta a $" + CaregiverName + "$" + "Llamando a$" + CaregiverName);
                    }
                    LogUtils.debug("you crossed the fencing are now alert is generating ");
                } else if (from.equalsIgnoreCase("Back")) {

                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("You are back home$ Your caregiver is notified $" + " Calling$" + CaregiverName);
                    } else {
                        GeneralUtils.speak("Usted esta en su hogar $ Su cuidador ha sido notificado $" + "Calling$" + CaregiverName);
                    }
                    LogUtils.debug("You are back home. Your caregiver is notified");
                }
                try {
                    final Handler timeoutHandler = new Handler();
                    timeoutHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            if (CaregiverNumber.isEmpty()) {
                                GeneralUtils.stopSpeaking();
                                GeneralUtils.speak("I don't have any number to call a CAREGIVER$");
                                LogUtils.debug("Mobile number not found to call while i " + from + " home fence.");
                                timeoutHandler.removeCallbacks(this);
                                GPSActivity.this.finish();
                            } else {
                                timeoutHandler.removeCallbacks(this);
                                LogUtils.debug("Mobile number " + CaregiverNumber + " found to call while i " + from + " home fence.");
                                Intent callIntent = new Intent(Intent.ACTION_CALL, Uri.parse("tel:" + CaregiverNumber));
                                startActivity(callIntent);
                                GPSActivity.this.finish();
                            }
                        }
                    }, 10000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));
    }
}