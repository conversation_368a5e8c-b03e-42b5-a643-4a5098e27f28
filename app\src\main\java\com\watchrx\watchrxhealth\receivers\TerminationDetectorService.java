package com.watchrx.watchrxhealth.receivers;

import android.app.Service;
import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.IBinder;
import androidx.annotation.Nullable;
import android.util.Log;

import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.syncup.MedicationScheduleSetupReceiver;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

public class TerminationDetectorService extends Service {

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onTaskRemoved(Intent rootIntent) {
        Log.d("TerminationDetectorSvc", "************************ Application termination detected.");

        ReminderUtils.clearExistingReminderAlarms(getApplicationContext());
        ReminderUtils.clearExistingDailyUpdaterAlarm(getApplicationContext());

        ComponentName receiver = new ComponentName(this, TimeChangedReceiver.class);
        PackageManager pm = this.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                0);


        receiver = new ComponentName(this, ReminderReceiver.class);
        pm = this.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                0);


        /*receiver = new ComponentName(this, MainActivity.ScheduleChangedReceiver.class);
        pm = this.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                0);*/

        receiver = new ComponentName(this, MedicationScheduleSetupReceiver.class);
        pm = this.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                0);

        receiver = new ComponentName(this, MainActivity.ServerCommRequestedReceiver.class);
        pm = this.getPackageManager();
        pm.setComponentEnabledSetting(receiver,
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                0);


        super.onTaskRemoved(rootIntent);
    }

}
