<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="@dimen/d_15_d"
    android:background="@drawable/background"
    android:orientation="vertical"
    android:padding="10dp">

    <Button
        android:id="@+id/header"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#B6EDF4"
        android:gravity="center_vertical|center_horizontal"
        android:text="@string/add_new_dairy"
        android:textSize="25sp" />

    <EditText
        android:id="@+id/medicationTime"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_margin="@dimen/d_5_d"
        android:gravity="center"
        android:hint="@string/enter_medication_taken_time" />

    <EditText
        android:id="@+id/mealTime"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_margin="@dimen/d_5_d"
        android:gravity="center"
        android:hint="@string/enter_meal_taken_time" />

    <Spinner
        android:id="@+id/medicationList"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:layout_margin="@dimen/d_5_d"
        android:layout_gravity="center_horizontal|center_vertical"
        android:background="@drawable/layout_corner"
        android:gravity="center" />


</LinearLayout>