<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white_smoke"
    android:isScrollContainer="true"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/medicationNameLay"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:orientation="horizontal"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.cardview.widget.CardView
                android:id="@+id/medicationNameCv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="5dp"
                app:cardCornerRadius="@dimen/d_12_d">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:hint="@string/medication_name"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/medicationName"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/dosageLay"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/medicationNameLay">

            <androidx.cardview.widget.CardView
                android:id="@+id/dosageCv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="5dp"
                app:cardCornerRadius="@dimen/d_12_d">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:hint="@string/dosage"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/dosage"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/colorLay"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dosageLay">

            <androidx.cardview.widget.CardView
                android:id="@+id/colorCv"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="5dp"
                app:cardCornerRadius="@dimen/d_12_d">

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:hint="@string/color"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/color"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/imagelayout"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:gravity="center"
            android:orientation="vertical"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/colorLay">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="0.5"
                android:gravity="center"
                android:orientation="horizontal"
                tools:ignore="UselessParent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/d_5_d"
                    android:text="@string/choose"
                    android:textSize="15sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="2.5"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="UselessParent">

                <ImageView
                    android:id="@+id/chooseImage"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/layout_corner" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/medsTypeLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="3"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/imagelayout">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="0.5"
                android:gravity="center"
                android:orientation="horizontal"
                tools:ignore="UselessParent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/d_5_d"
                    android:text="@string/meds_type"
                    android:textSize="15sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="2.5"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="UselessParent">

                <RadioGroup
                    android:id="@+id/medsTypeRadio"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <RadioButton
                        android:id="@+id/tablet"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="2dp"
                        android:layout_weight="1"
                        android:checked="true"
                        android:fontFamily="@font/lato_regular"
                        android:gravity="center"
                        android:text="@string/tablet"
                        android:textSize="20sp" />

                    <RadioButton
                        android:id="@+id/injection"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="2dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:gravity="center"
                        android:text="@string/injection"
                        android:textSize="20sp" />

                    <RadioButton
                        android:id="@+id/inhaler"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="2dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:gravity="center"
                        android:text="@string/inhaler"
                        android:textSize="20sp" />


                    <RadioButton
                        android:id="@+id/nasalSpray"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="2dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:gravity="center"
                        android:text="@string/spray"
                        android:textSize="20sp" />

                    <RadioButton
                        android:id="@+id/syrup"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="2dp"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:gravity="center"
                        android:text="@string/syrup"
                        android:textSize="20sp" />

                </RadioGroup>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/daysLayout"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="3"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/medsTypeLayout">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="0.7"
                android:gravity="center"
                android:orientation="horizontal"
                tools:ignore="UselessParent">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/d_5_d"
                    android:text="@string/select_days"
                    android:textSize="15sp" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_gravity="center"
                android:layout_margin="@dimen/d_5_d"
                android:layout_weight="2.3"
                android:gravity="center"
                android:orientation="vertical"
                tools:ignore="UselessParent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <CheckBox
                        android:id="@+id/all"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/all"
                        android:textSize="20sp" />

                    <CheckBox
                        android:id="@+id/sun"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/sun"
                        android:textSize="20sp" />

                    <CheckBox
                        android:id="@+id/mon"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/mon"
                        android:textSize="20sp" />

                    <CheckBox
                        android:id="@+id/tue"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/tue"
                        android:textSize="20sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <CheckBox
                        android:id="@+id/wed"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/wed"
                        android:textSize="20sp" />

                    <CheckBox
                        android:id="@+id/thur"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/thur"
                        android:textSize="20sp" />

                    <CheckBox
                        android:id="@+id/fri"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/fri"
                        android:textSize="20sp" />

                    <CheckBox
                        android:id="@+id/sat"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:fontFamily="@font/lato_regular"
                        android:text="@string/sat"
                        android:textSize="20sp" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/selectTimeSlotsLayout"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="1"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dinnerLayout">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="@string/select_timeslots"
                android:textSize="@dimen/d_20_s" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/earlyMorningLay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/selectTimeSlotsLayout">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/earlyMorningCheck"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/early_morning"
                    android:textSize="20sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/earlyMorningED"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2">

                <Spinner
                    android:id="@+id/earlyMorningSpn"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:spinnerMode="dropdown" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:hint="@string/quantity"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/earlyMorningQanty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/breakfastLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/earlyMorningLay">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/breakfastCheck"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/breakfast"
                    android:textSize="20sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/breakfastED"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2">

                <Spinner
                    android:id="@+id/breakfastSpn"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:spinnerMode="dropdown" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:hint="@string/quantity"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/breakfastQanty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/lunchLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/breakfastLayout">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/lunchCheck"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/lunch"
                    android:textSize="20sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/lunchED"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2">

                <Spinner
                    android:id="@+id/lunchSpn"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:spinnerMode="dropdown" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:hint="@string/quantity"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/lunchQanty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/afternoonLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/lunchLayout">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/afternoonCheck"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/afternoon"
                    android:textSize="20sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/afternoonED"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2">

                <Spinner
                    android:id="@+id/afternoonSpn"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:spinnerMode="dropdown" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:hint="@string/quantity"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/afternoonQanty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/dinnerLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/afternoonLayout">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/dinnerCheck"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/dinner"
                    android:textSize="20sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/dinnerED"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2">

                <Spinner
                    android:id="@+id/dinnerSpn"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:spinnerMode="dropdown" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:hint="@string/quantity"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/dinnerQanty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/bedLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dinnerLayout">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_gravity="start"
                android:layout_weight="1"
                android:gravity="start"
                android:orientation="horizontal">

                <CheckBox
                    android:id="@+id/bedCheck"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:fontFamily="@font/lato_regular"
                    android:text="@string/bed"
                    android:textSize="20sp" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/bedED"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="horizontal"
                android:visibility="gone"
                android:weightSum="2">

                <Spinner
                    android:id="@+id/bedSpn"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:spinnerMode="dropdown" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
                    android:layout_width="0dp"
                    android:layout_height="80dp"
                    android:layout_margin="5dp"
                    android:layout_weight="1"
                    android:hint="@string/quantity"
                    app:boxBackgroundColor="@color/white">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/bedQanty"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:ellipsize="end"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/submitButtonLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_5_d"
            android:background="@drawable/layout_corner"
            android:gravity="center"
            android:orientation="vertical"
            android:weightSum="2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/dinnerLayout">

            <Button
                android:id="@+id/submit"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/button_shape"
                android:text="@string/submit"
                android:textSize="@dimen/d_20_s" />

        </LinearLayout>

    </LinearLayout>
</androidx.core.widget.NestedScrollView>