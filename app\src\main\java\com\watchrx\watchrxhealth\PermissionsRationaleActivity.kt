package com.watchrx.watchrxhealth

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.watchrx.watchrxhealth.utils.LogUtils

class PermissionsRationaleActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "PermissionsRationaleActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_permissions_rationale)
        
        LogUtils.debug("PermissionsRationaleActivity created")
        
        setupViews()
    }
    
    private fun setupViews() {
        val tvTitle = findViewById<TextView>(R.id.tv_rationale_title)
        val tvDescription = findViewById<TextView>(R.id.tv_rationale_description)
        val btnContinue = findViewById<Button>(R.id.btn_continue)
        val btnCancel = findViewById<Button>(R.id.btn_cancel)
        
        tvTitle.text = "Health Data Access"
        tvDescription.text = "WatchRx Health needs access to your steps data from Health Connect to provide you with personalized health insights and tracking. Your data is used only within this app and is not shared with third parties. This helps us display your daily activity progress and health metrics."
        
        btnContinue.setOnClickListener {
            LogUtils.debug("User accepted Health Connect permissions rationale")
            setResult(RESULT_OK)
            finish()
        }
        
        btnCancel.setOnClickListener {
            LogUtils.debug("User cancelled Health Connect permissions rationale")
            setResult(RESULT_CANCELED)
            finish()
        }
    }
}