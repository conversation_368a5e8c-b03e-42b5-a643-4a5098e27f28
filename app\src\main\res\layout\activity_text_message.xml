<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/texMessageView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".TextMessageActivity">

    <LinearLayout
        android:id="@+id/test"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:orientation="vertical"
        android:weightSum="4"
        app:layout_constraintBottom_toTopOf="@+id/layout_gchat_chatbox"
        app:layout_constraintTop_toBottomOf="@+id/messageHeader">

        <androidx.cardview.widget.CardView
            android:id="@+id/messageCardView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/d_10_d"
            android:layout_weight="1"
            app:cardCornerRadius="@dimen/d_12_d"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/d_10_d"
                android:fontFamily="@font/lato_light"
                android:gravity="center_horizontal|center_vertical"
                android:textColor="@color/black"
                android:textSize="@dimen/message" />
        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:id="@+id/messageUserActionTextCv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_margin="@dimen/d_10_d"
            android:layout_weight="0.5"
            android:visibility="gone"
            app:cardBackgroundColor="@color/light_grey"
            app:cardCornerRadius="@dimen/d_12_d"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/messageUserActionText"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/d_10_d"
                android:fontFamily="@font/lato_light"
                android:gravity="center_horizontal|center_vertical"
                android:textColor="@color/black"
                android:textSize="@dimen/d_20_s" />
        </androidx.cardview.widget.CardView>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/answers"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_horizontal|center_vertical"
            android:layout_margin="@dimen/d_10_d"
            android:layout_weight="2"
            android:gravity="center_horizontal|center_vertical"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/message" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_gchat_chatbox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/d_5_d"
        android:layout_weight="0.5"
        android:background="@drawable/reply_view"
        android:orientation="horizontal"
        android:weightSum="2"
        app:layout_constraintBottom_toTopOf="@+id/nav_view"
        app:layout_constraintTop_toBottomOf="@+id/test">

        <EditText
            android:id="@+id/custom_message"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="5dp"
            android:layout_weight="1.7"
            android:autofillHints=""
            android:background="@android:color/transparent"
            android:gravity="center_horizontal|center_vertical"
            android:hint="@string/enter_message"
            android:inputType="text"
            android:maxLines="6" />

        <Button
            android:id="@+id/send"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.3"
            android:background="@drawable/send_button_background"
            android:textColor="@color/colorPrimary" />
    </LinearLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/messageHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/d_10_d"
        app:cardBackgroundColor="@color/teal_200"
        app:cardCornerRadius="@dimen/d_12_d"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/messageTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/d_10_d"
            android:fontFamily="@font/lato_regular"
            android:gravity="center_horizontal|center_vertical"
            android:text="@string/message"
            android:textColor="@color/black"
            android:textSize="@dimen/message" />
    </androidx.cardview.widget.CardView>

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/nav_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginStart="0dp"
        android:layout_marginEnd="0dp"
        android:layout_weight="0.3"
        android:background="@color/statusbar"
        android:visibility="gone"
        app:itemIconSize="40dp"
        app:itemIconTint="@drawable/bottom_navigation_selector"
        app:labelVisibilityMode="unlabeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:menu="@menu/bottom_nav_menu">


    </com.google.android.material.bottomnavigation.BottomNavigationView>

</androidx.constraintlayout.widget.ConstraintLayout>