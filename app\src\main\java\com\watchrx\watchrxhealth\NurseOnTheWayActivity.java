package com.watchrx.watchrxhealth;

import android.content.Context;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.appcompat.app.AppCompatActivity;
import android.text.method.ScrollingMovementMethod;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ToggleButton;

import com.watchrx.watchrxhealth.db.Alerts;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.SoundUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;

public class NurseOnTheWayActivity extends AppCompatActivity implements Runnable, View.OnClickListener {
    private static final long[] vibratePattern = {0, 500, 200};
    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private Handler timeoutHandler;
    String nurseName = "";
    String message = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");
        setContentView(R.layout.activity_daughter_reminder);
        Window window = this.getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        nurseName = getIntent().getStringExtra("nurseName");
        message = getIntent().getStringExtra("status");
        TextView nurseReminderTextView = (TextView) findViewById(R.id.nurse_reminder);
        nurseReminderTextView.setMovementMethod(new ScrollingMovementMethod());
        nurseReminderTextView.setOnClickListener(this);
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        startAnimation(nurseName, nurseReminderTextView, message);


        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);

        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                    GeneralUtils.speak("Hi $ you received an message from " + nurseName + " $Please read it once $ Tap ANYWHERE, to close ");
                } else {
                    GeneralUtils.speak("Hola $ recibió un mensaje de " + nurseName + "$Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                }

            }
        });
        ToggleButton muteUnmute = (ToggleButton) findViewById(R.id.toggleButton1);
        SoundUtils.checkStateOfSound(muteUnmute, NurseOnTheWayActivity.this);

        setupMuteUnmute();
        setupPhoneCalls();
        LinearLayout layout = (LinearLayout) findViewById(R.id.layout_reminder);
        layout.setOnClickListener(this);
    }

    private void setupPhoneCalls() {
        ImageView settingsButton = (ImageView) findViewById(R.id.imageView_setting);

        if (settingsButton != null) {
            settingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    LogUtils.debug("onClick event detected on Reminder screen for phone calls");
                    Intent intent = new Intent(NurseOnTheWayActivity.this, PhoneCallsActivity.class);
                    startActivity(intent);

                }

            });
        }
    }

    private void setupMuteUnmute() {
        ToggleButton muteUnmute = (ToggleButton) findViewById(R.id.toggleButton1);
        muteUnmute.setOnClickListener(new View.OnClickListener() {
            @SuppressWarnings("deprecation")
            @Override
            public void onClick(View v) {

                boolean on = ((ToggleButton) v).isChecked();
                if (!on) {
                    SoundUtils.soundMute(NurseOnTheWayActivity.this);
                } else {
                    SoundUtils.soundUnMute(NurseOnTheWayActivity.this);
                }

            }
        });
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    private void startAnimation(String nurseName, TextView nurseReminderTextView, String message) {
        final Animation animation = new AlphaAnimation(1, 0); // Change alpha from fully visible to invisible
        animation.setDuration(300); // duration
        animation.setInterpolator(new LinearInterpolator()); // do not alter animation rate
        animation.setRepeatCount(Animation.INFINITE); // Repeat animation infinitely
        animation.setRepeatMode(Animation.REVERSE); // Reverse animation at the end so the button will fade back in

        nurseReminderTextView.startAnimation(animation);
        nurseReminderTextView.setText(message + "\n" + "-" + nurseName);
    }

    @Override
    public void run() {
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(this);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();

        Alerts alerts = new Alerts();
        alerts.setAlertType("Incoming Message");
        alerts.setAlertMessage(message + "\n" + "-" + nurseName);
        alerts.setAlertDate(new SimpleDateFormat("MMM dd, hh:mm:ss a", Locale.US).format(new Date(System.currentTimeMillis())));
        alerts.setMedicineName("Incoming Message");
        Alerts.addToDB(alerts);
        Intent alertCount = new Intent(MainActivity.ALERT_COUNT_INCREASED_INDICATOR);
        LocalBroadcastManager.getInstance(this).sendBroadcast(alertCount);
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        finish();
    }

    @Override
    public void onClick(View v) {
        GeneralUtils.stopBeeping();
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(NurseOnTheWayActivity.this);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        NurseOnTheWayActivity.this.finish();
    }
}
