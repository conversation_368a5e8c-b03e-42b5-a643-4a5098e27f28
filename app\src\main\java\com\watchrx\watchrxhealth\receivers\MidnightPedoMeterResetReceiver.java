package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.watchrx.watchrxhealth.WatchApp;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.pedometer.Database;
import com.watchrx.watchrxhealth.pedometer.SensorListener;
import com.watchrx.watchrxhealth.pedometer.Util;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.ReminderUtils;

import java.util.Date;

public class MidnightPedoMeterResetReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {

        Log.e("MidnightPedoMeter ", "MidnightPedoMeterResetReceiver Triggered" + new Date());

        Database db = Database.getInstance(WatchApp.getContext());
        int todayOffset = db.getSteps(Util.getToday());
        int since_boot = db.getCurrentSteps();
        int steps_today = Math.max(todayOffset + since_boot, 0);
        CommUtils.sendPedoMeterLogToServer(WatchApp.getContext(), steps_today, false, true);
        PatientDetails details = PatientDetails.getFromDB();
        //ReminderUtils.setReminderToSendStepsToServer(context);
        if (details.getPedoMeterStatus().equalsIgnoreCase("enable")) {
            db.reCreateDB(context);
            context.stopService(new Intent(context, SensorListener.class));
            context.startService(new Intent(context, SensorListener.class));
            LogUtils.debug("Pedo Meter Reset Successfully");
            CommUtils.sendPedoMeterLogToServer(WatchApp.getContext(), 0, true, false);
            ReminderUtils.setupMidNightPedoMeterReset(context);
        } else {
            LogUtils.debug("To Reset Pedometer , It should turn on First");
        }
        LogUtils.debug("-----------Midnight Pedo Meter Reset-------------------");
    }
}
