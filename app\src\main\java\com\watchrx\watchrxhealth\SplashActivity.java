package com.watchrx.watchrxhealth;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.provider.Settings;
import android.speech.tts.TextToSpeech;
import android.text.TextUtils;
import android.util.Log;
import android.util.Patterns;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.FileProvider;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.gson.Gson;
import com.watchrx.watchrxhealth.constants.CommonConstants;
import com.watchrx.watchrxhealth.constants.URLConstants;
import com.watchrx.watchrxhealth.db.CustomAlertsDetails;
import com.watchrx.watchrxhealth.db.CustomerAlertsDB;
import com.watchrx.watchrxhealth.db.DBAdaptor;
import com.watchrx.watchrxhealth.db.MedicationDetail;
import com.watchrx.watchrxhealth.db.MedicationScheduleMaster;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.db.ScheduleMessageVO;
import com.watchrx.watchrxhealth.db.ScheduleMessagesDB;
import com.watchrx.watchrxhealth.db.VitalConfiguration;
import com.watchrx.watchrxhealth.db.VitalStatusDetails;
import com.watchrx.watchrxhealth.gcm.GCMRegistrationIntentService;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.messages.RegisterGcmRequestMessage;
import com.watchrx.watchrxhealth.messages.RegisterResponseMessage;
import com.watchrx.watchrxhealth.messages.RegisterWatch;
import com.watchrx.watchrxhealth.messages.VitalConfigVO;
import com.watchrx.watchrxhealth.models.LoginModel;
import com.watchrx.watchrxhealth.receivers.HandlerResult;
import com.watchrx.watchrxhealth.syncup.ReadDataFromMemory;
import com.watchrx.watchrxhealth.tasks.RestAsyncTask;
import com.watchrx.watchrxhealth.tasks.TaskResultHandler;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.InternetCheckConnectivity;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.SWStatusKeeper;
import com.watchrx.watchrxhealth.utils.SoftwareUpdateUtil;

import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Locale;
import java.util.Objects;
import java.util.Random;
import java.util.TimeZone;

@SuppressLint("CustomSplashScreen")
public class SplashActivity extends AppCompatActivity implements TextToSpeech.OnInitListener {

    private Context context;

    // Broadcast receiver for GCM registration
    private BroadcastReceiver mRegistrationBroadcastReceiver = null;
    InternetConnectedReceiver internetConnectedReceiver = new InternetConnectedReceiver();

    @SuppressLint("HardwareIds")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        setContentView(R.layout.activity_splash);
        getSupportActionBar().hide();
        CommUtils.AddRoundDialDevicesToList();
        afterPermissionMethod();
    }

    private void afterPermissionMethod() {

        String tz = TimeZone.getDefault().getDisplayName();
        LogUtils.SaveTimeZOneToFile(tz);
        TextView appVersion = findViewById(R.id.appVersion);
        getAppVersion(appVersion);
        LogUtils.debug("Process ID=====>" + android.os.Process.myPid());
        LogUtils.debug("Initializing Text to Speech");
        // Initialize TTS
        Globals.textToSpeech = new TextToSpeech(getApplicationContext(), this);
        if (Globals.textToSpeech.isLanguageAvailable(Locale.US) == TextToSpeech.LANG_AVAILABLE) {
            Globals.textToSpeech.setLanguage(Locale.US);
        }
        LogUtils.debug("Text To Speech initialized.");
        CommUtils.enableBroadcastReceiver(this);
        initMethod();
    }

    @SuppressLint("HardwareIds")
    private void initMethod() {
//        String randomString = generateRandomAlphaNumeric(4);
        Globals.imei = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID) /*+ randomString*/;
        LogUtils.debug("IMEI number of the device===>" + Globals.imei);
        LogUtils.debug("Globals.isWatchRegistered=" + Globals.isWatchRegistered);
        Globals.isWatchRegistered = false;

        ImageView clockLayout = findViewById(R.id.ivSplash);
        if (clockLayout != null) {
            clockLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (!Globals.isDownloading) {
                        killApp();
                    } else {
                        Toast.makeText(SplashActivity.this, "Downloading please wait", Toast.LENGTH_LONG).show();
                    }
                }
            });
        }
        @SuppressLint("HandlerLeak") Handler h = new Handler() {
            @Override
            public void handleMessage(Message msg) {

                if (msg.what != 1) { // code if not connected
                    LogUtils.debug("Most likely internet not connected or data transmission is not happening properly ");
                    if (SoftwareUpdateUtil.isSoftwareUpgradeInProgress()) {
                        rollBackMethod();
                    } else {
                        registerWithoutInternet();
                    }
                } else {
                    initRegistration();
                }
            }
        };
        if (!CommUtils.isNetworkAvailable(this)) {
            if (SoftwareUpdateUtil.isSoftwareUpgradeInProgress()) {
                rollBackMethod();
            } else {
                registerWithoutInternet();
            }
        } else {
            registerGCM();
            InternetCheckConnectivity.isNetworkAvailable(h, 5000);
        }
    }

    public static String generateRandomAlphaNumeric(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder result = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            result.append(chars.charAt(index));
        }
        return result.toString().toLowerCase();
    }

    private void rollBackMethod() {
        String oldVersionAPk = SoftwareUpdateUtil.getSWUpgradeStatusAPKVersion();
        String presentVersion = getAppVersion(SplashActivity.this);
        String status = SoftwareUpdateUtil.getSWUpgradeStatus();

        LogUtils.debug("Before Checking Software Roll back  old version =" + oldVersionAPk + " current version =" + presentVersion + " Status =" + status);
        assert oldVersionAPk != null;
        if (!oldVersionAPk.equalsIgnoreCase(presentVersion) && (status.equalsIgnoreCase("UPGRADEINTRANSIT"))) {
            getRollBack(oldVersionAPk);
        } else if (!oldVersionAPk.equalsIgnoreCase(presentVersion) && (status.equalsIgnoreCase("RELEASE_INSTALLED"))) {
            getRollBack(oldVersionAPk);
        } else if (!oldVersionAPk.equalsIgnoreCase(presentVersion) && (status.equalsIgnoreCase("ROLLINGBACK"))) {
            getRollBack(oldVersionAPk);
        } else {
            registerWithoutInternet();
        }
    }

    private void getRollBack(String oldVersionAPk) {
        LogUtils.debug("Internet is not connected, so status is set as ROLLINGBACK");

        SWStatusKeeper statusKeeper = new SWStatusKeeper();
        statusKeeper.setRollingBackStatus(oldVersionAPk);
        File sdcard = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        String file = sdcard.toString() + CommonConstants.BACK_UP_FILE_DIRECTORY;

        File[] files = new File(file).listFiles();

        File reqFile = null;
        for (File file1 : files) {
            reqFile = file1;
        }
        LogUtils.debug("Rolling back to this APK =" + (reqFile != null ? reqFile.toString() : null));
        if (reqFile != null) {
            Intent intent = new Intent(Intent.ACTION_INSTALL_PACKAGE);
            intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            Uri uriFile;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                uriFile = FileProvider.getUriForFile(WatchApp.getContext(), WatchApp.getContext().getApplicationContext().getPackageName() + ".fileprovider", reqFile);
            } else {
                uriFile = Uri.fromFile(reqFile);
            }
            intent.setDataAndType(uriFile, "application/vnd.android.package-archive");
            WatchApp.getContext().startActivity(Intent.createChooser(intent, "Open_Apk"));
        }
    }

    private void registerWithoutInternet() {
        TextView textView = findViewById(R.id.internet);
        Globals.isDownloading = false;
        /*textView.setVisibility(View.VISIBLE);*/
        if (textView != null) {
            LogUtils.debug("Internet is not connected...");
            if (DBAdaptor.getDbAdaptorInstance().validDbExists(SplashActivity.this)) {
                // change the watch register flag to true so that we dont re-register the watch
                Globals.isWatchRegistered = true;
                LogUtils.debug("Internet not connected...App started from cache memory.......");
                Toast.makeText(SplashActivity.this, "Internet is not connected,using cache memory", Toast.LENGTH_LONG).show();
                // Go to the next screen
                Intent intent = new Intent(SplashActivity.this, MainActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(intent);
                finishAffinity();
            } else {
                StringBuilder data = new StringBuilder();
                File rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
                File file = new File(rootPath + "/WatchRx_DataBase/", "WatchRx.txt");

                if (file.exists()) {
                    if (!DBAdaptor.getDbAdaptorInstance().isDBOpen()) {
                        DBAdaptor.getDbAdaptorInstance().open(SplashActivity.this);
                        DBAdaptor.getDbAdaptorInstance().createDBIfNeeded(false);
                        LogUtils.debug("---------------Creating and opening the Database------");
                    }
                    try {
                        InputStream fis = new FileInputStream(file);
                        DataInputStream in = new DataInputStream(fis);
                        BufferedReader br =
                                new BufferedReader(new InputStreamReader(in));
                        String strLine;
                        while ((strLine = br.readLine()) != null) {
                            data.append(strLine);
                        }
                        in.close();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    if (registerWatchWithoutInternet(data.toString()).equalsIgnoreCase("operationsuccessful")) {
                        Toast.makeText(SplashActivity.this, "Internet is not connected,using internal memory", Toast.LENGTH_LONG).show();
                        // change the watch register flag to true so that we dont re-register the watch
                        Globals.isWatchRegistered = true;

                        LogUtils.debug("Internet is no connected...App started from internal memory.......");
                        Intent intent = new Intent(SplashActivity.this, MainActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                        finishAffinity();

                    } else if (registerWatchWithoutInternet(data.toString()).equalsIgnoreCase("Imei is not found in the database")) {
                        //*setupInternetConnectedIndicator();*//*
                        textView.setVisibility(View.VISIBLE);
                        textView.setText(R.string.imei_not_found_in_db);
                        LogUtils.debug("Imei is not found in the database");
                    } else if (registerWatchWithoutInternet(data.toString()).equalsIgnoreCase("Imei is not associated with any patient")) {
                        textView.setText(R.string.imei_not_assigned);
                        textView.setVisibility(View.VISIBLE);
                        LogUtils.debug("Imei is not associated with any patient");
                    } else if (registerWatchWithoutInternet(ReadDataFromMemory.getDataFromMemory()).equalsIgnoreCase("Subscription is not active")) {
                        textView.setVisibility(View.VISIBLE);
                        textView.setText(R.string.subscription_not_active);
                        LogUtils.debug("Subscription is not active");
                        CommUtils.deleteDatabase();
                    } else {
                        setupInternetConnectedIndicator();
                        textView.setVisibility(View.VISIBLE);
                        textView.setText(R.string.internetcheck);
                        LogUtils.debug("Internet is not connected ! please connect");
                    }

                } else {
                    setupInternetConnectedIndicator();
                    textView.setVisibility(View.VISIBLE);
                    textView.setText(R.string.internetcheck);
                    LogUtils.debug("Internet is not connected ! please connect");
                }
            }
        }
        ProgressBar progressBar = findViewById(R.id.spinnerLoading);
        if (progressBar != null) {
            progressBar.setVisibility(View.GONE);
        }
    }


    private void killApp() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(this);
        alertDialog.setTitle("WatchRx");
        alertDialog.setCancelable(false);
        alertDialog.setIcon(R.drawable.watch);
        alertDialog.setMessage("CONFIRM TO EXIT !");
        alertDialog.setPositiveButton("YES",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        if (isMyLauncherDefault()) {
                            LogUtils.debug("App forcefully closed by User...");
                            LocalBroadcastManager.getInstance(SplashActivity.this).unregisterReceiver(internetConnectedReceiver);
                            getPackageManager().clearPackagePreferredActivities(getPackageName());
                            CommUtils.disableBroadcastReceiver(WatchApp.getContext());
                            DBAdaptor.getDbAdaptorInstance().close();
                            Globals.isWatchRegistered = false;
                            finish();
                            android.os.Process.killProcess(android.os.Process.myPid());
                            System.exit(1);
                        } else {
                            LogUtils.debug("App forcefully closed by User...");
                            LocalBroadcastManager.getInstance(SplashActivity.this).unregisterReceiver(internetConnectedReceiver);
                            DBAdaptor.getDbAdaptorInstance().close();
                            Globals.isWatchRegistered = false;
                            CommUtils.disableBroadcastReceiver(WatchApp.getContext());
                            finish();
                            android.os.Process.killProcess(android.os.Process.myPid());
                            System.exit(2);
                        }
                    }
                });
        alertDialog.setNegativeButton("NO",
                new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface arg0, int arg1) {
                        arg0.dismiss();
                    }
                });
        AlertDialog alertDialogBuilder = alertDialog.create();
        alertDialogBuilder.show();
    }

    private boolean isMyLauncherDefault() {
        PackageManager localPackageManager = getPackageManager();
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.addCategory("android.intent.category.HOME");
        String str = localPackageManager.resolveActivity(intent,
                PackageManager.MATCH_DEFAULT_ONLY).activityInfo.packageName;
        return str.equals(getPackageName());
    }


    private void initRegistration() {
        if (!DBAdaptor.getDbAdaptorInstance().isDBOpen()) {
            DBAdaptor.getDbAdaptorInstance().open(getApplication().getBaseContext());
            DBAdaptor.getDbAdaptorInstance().createDBIfNeeded(true);
            LogUtils.debug("---------Creating and opening the Database---------");
        }
        LogUtils.debug("Going to register the watch");
        registerWatch();
    }

    private void loginDialog() {
        final AlertDialog.Builder builder = new AlertDialog.Builder(this);
        LayoutInflater inflater = this.getLayoutInflater();
        View custom_dialog = inflater.inflate(R.layout.login_dialog, null);

        final EditText username = custom_dialog.findViewById(R.id.userName);
        final EditText password = custom_dialog.findViewById(R.id.password);

        builder.setPositiveButton("Login", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });

        builder.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                LocalBroadcastManager.getInstance(SplashActivity.this).unregisterReceiver(internetConnectedReceiver);
                DBAdaptor.getDbAdaptorInstance().close();
                Globals.isWatchRegistered = false;
                CommUtils.disableBroadcastReceiver(WatchApp.getContext());
                finish();
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(2);
            }
        });

        builder.setView(custom_dialog);
        builder.setCancelable(false);
        final AlertDialog dialog = builder.create();
        dialog.show();
        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String enteredUserName = username.getText().toString();
                if (TextUtils.isEmpty(enteredUserName)) {
                    username.setError("Email Id required");
                    return;
                }
                String enteredPassword = password.getText().toString();
                if (TextUtils.isEmpty(enteredPassword)) {
                    password.setError("Password required");
                    return;
                }
                Toast.makeText(context, "User Name :" + enteredUserName + " Password " + enteredPassword, Toast.LENGTH_SHORT).show();
                getAuthToken(enteredUserName, enteredPassword);
                dialog.dismiss();
            }
        });
    }

    private void getAuthToken(String enteredUserName, String enteredPassword) {
        try {
            LoginModel loginModel = new LoginModel();
            LogUtils.debug("Going to get auth token");
            URL url = new URL(URLConstants.AUTHENTICATE_URL);
            loginModel.username = enteredUserName.trim();
            loginModel.password = enteredPassword.trim();
            String json = new Gson().toJson(loginModel);
//            SharedPreferences.Editor editor = sharedpreferences.edit();
//            editor.putString(userName, enteredUserName);
//            editor.putString(password, enteredPassword);
//            editor.apply();
            new RestAsyncTask(url, json, null, new AuthTokenResponseHandler(), null).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private class AuthTokenResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            if (result.getResult() == null) {
                LogUtils.debug("Failed to get authenticate token");
                TextView textView = findViewById(R.id.internet);
                textView.setVisibility(View.VISIBLE);
                textView.setText(R.string.token_failed);
                loginDialog();
            } else {
                try {
                    JSONObject jsonObject = new JSONObject(result.getResult().toString());
                    Globals.authToken = jsonObject.optString("token");
                    registerWatch();
                } catch (Exception ignored) {
                }
            }
        }
    }

    public static boolean isValidEmail(CharSequence target) {
        return (!TextUtils.isEmpty(target) && Patterns.EMAIL_ADDRESS.matcher(target).matches());
    }

    private void getAppVersion(TextView appVersion) {
        PackageManager manager = context.getPackageManager();
        PackageInfo info = null;
        try {
            info = manager.getPackageInfo(
                    context.getPackageName(), 0);
            appVersion.setText(String.format("APP Version %s", info.versionName));
            LogUtils.debug("Application version is *******" + info.versionName);
        } catch (PackageManager.NameNotFoundException e) {
            LogUtils.debug("I got exception in getting App version ->" + e.getMessage());
            e.printStackTrace();
        }
    }

    public static String getAppVersion(Context context) {
        PackageManager manager = context.getPackageManager();
        PackageInfo info = null;
        try {
            info = manager.getPackageInfo(
                    context.getPackageName(), 0);
            return info.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            if (Globals.textToSpeech.isLanguageAvailable(Locale.US) == TextToSpeech.LANG_AVAILABLE) {
                Globals.textToSpeech.setLanguage(Locale.US);
            }
        }
    }

    private void registerGCM() {
        int resultCode = GoogleApiAvailability.getInstance().isGooglePlayServicesAvailable(getApplicationContext());
        if (ConnectionResult.SERVICE_DISABLED == resultCode || ConnectionResult.SERVICE_MISSING == resultCode) { // play service is not available
            if (GoogleApiAvailability.getInstance().isUserResolvableError(resultCode)) { // play service is supported but not installed
                TextView textView = findViewById(R.id.internet);
                textView.setVisibility(View.VISIBLE);
                if (textView != null) {
                    LogUtils.debug("GCM Registration failed. Google Play Service is not installed/enabled in this device!");
                    textView.setText(R.string.gcmRegFailedNoPlayServiceMsg);

                }
            } else {
                TextView textView = findViewById(R.id.internet);
                textView.setVisibility(View.VISIBLE);
                if (textView != null) {
                    LogUtils.debug("GCM Registration failed. This device does not support for Google Play Service!");
                    textView.setText(R.string.gcmRegFailedMsg);
                }
            }
            return;
        }

        //Initializing our broadcast receiver
        mRegistrationBroadcastReceiver = new BroadcastReceiver() {

            @Override
            public void onReceive(Context context, Intent intent) {
                LogUtils.debug("onReceive FCM Token Handler Action:" + intent.getAction());
                if (intent.getAction().equals(GCMRegistrationIntentService.REGISTRATION_SUCCESS)) {
                    final String token = intent.getStringExtra("token");
                    if (CommUtils.isNetworkAvailable(SplashActivity.this)) {
                        @SuppressLint("HandlerLeak")
                        Handler handler = new Handler() {
                            @Override
                            public void handleMessage(Message msg) {
                                if (msg.what != 1) {
                                    LogUtils.debug("Internet is connected ,but data transmission is not happening properly,while registering GCM");
                                } else {
                                    if (token != null) {
                                        try {
                                            LogUtils.debug("Going to register the GCM");
                                            URL url = new URL(URLConstants.REGISTER_GCM_URL);
                                            RegisterGcmRequestMessage message = new RegisterGcmRequestMessage();
                                            message.imeiNo = Globals.imei;
                                            message.registerId = token;
                                            message.deviceType = "watch";
                                            String json = new Gson().toJson(message);
                                            new RestAsyncTask(url, json, null, new GCMRegistrationResponseHandler(), null).execute();
                                        } catch (MalformedURLException e) {
                                            LogUtils.debug("I got Exception while sending gcm token to server" + e.getMessage());
                                            e.printStackTrace();
                                        }
                                    } else {
                                        LogUtils.debug("FCM Token is null");
                                    }
                                }
                            }
                        };
                        InternetCheckConnectivity.isNetworkAvailable(handler, CommonConstants.Max_Time_Out_For_Ping);
                    } else {
                        LogUtils.debug("Internet is not connected while registering GCM");
                    }
                } else if (intent.getAction().equals(GCMRegistrationIntentService.REGISTRATION_ERROR)) {
                    LogUtils.debug("GCM registration error!");
                } else {
                    LogUtils.debug("Error occurred during GCM Registration.");
                }
            }
        };
        Intent intent = new Intent(this, GCMRegistrationIntentService.class);
        startService(intent);
    }


    private void registerWatch() {
        try {
            URL url = new URL(URLConstants.REGISTER_WATCH_URL);
            ProgressBar progressBar = findViewById(R.id.spinnerLoading);

            if (Globals.imei != null) {
                String versionRelease = Build.VERSION.RELEASE;
                String apkVersion = Objects.requireNonNull(getAppVersion(this)).toLowerCase();
                RegisterWatch registerWatch = new RegisterWatch();
                registerWatch.imeiNo = Globals.imei;
                registerWatch.androidversionno = versionRelease.toLowerCase();
                registerWatch.apkversionno = apkVersion;
                registerWatch.timeZoneId = TimeZone.getDefault().getID();
                SharedPreferences prefs = context.getSharedPreferences("WatchRx", MODE_PRIVATE);
                LogUtils.debug("Sending data to server is ====>" + new Gson().toJson(registerWatch));
                new RestAsyncTask(url, new Gson().toJson(registerWatch), progressBar, new WatchRegistrationResponseHandler(), null).execute();
            } else {
                LogUtils.debug("IMEI Is :" + Globals.imei);
            }
        } catch (MalformedURLException e) {
            LogUtils.debug("I got exception while registering the watch" + e.getMessage());
            e.printStackTrace();
        }
    }

    private void registerWatchVeryFirstTime() {
        try {
            URL url = new URL(URLConstants.REGISTER_WATCH_URL);
            ProgressBar progressBar = findViewById(R.id.spinnerLoading);

            if (Globals.imei != null) {
                String versionRelease = Build.VERSION.RELEASE;
                String apkVersion = getAppVersion(this).toLowerCase();
                RegisterWatch registerWatch = new RegisterWatch();
                registerWatch.androidversionno = versionRelease.toLowerCase();
                registerWatch.apkversionno = apkVersion;
                registerWatch.deviceId = Globals.imei;
                registerWatch.timeZoneId = TimeZone.getDefault().getID();
                SharedPreferences prefs = context.getSharedPreferences("WatchRx", MODE_PRIVATE);
                registerWatch.emailId = prefs.getString("username", null);
                LogUtils.debug("Sending data to server is ====>" + new Gson().toJson(registerWatch));
                new RestAsyncTask(url, new Gson().toJson(registerWatch), progressBar, new WatchRegistrationResponseHandler(), null).execute();
            } else {
                LogUtils.debug("IMEI Is :" + Globals.imei);
            }
        } catch (MalformedURLException e) {
            LogUtils.debug("I got exception while registering the watch" + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        LogUtils.debug("onPause detected.");
        LocalBroadcastManager.getInstance(this).unregisterReceiver(mRegistrationBroadcastReceiver);
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.debug("onResume detected.");
        LocalBroadcastManager.getInstance(this).registerReceiver(mRegistrationBroadcastReceiver,
                new IntentFilter(GCMRegistrationIntentService.REGISTRATION_SUCCESS));
        LocalBroadcastManager.getInstance(this).registerReceiver(mRegistrationBroadcastReceiver,
                new IntentFilter(GCMRegistrationIntentService.REGISTRATION_ERROR));
    }


    private class WatchRegistrationResponseHandler implements TaskResultHandler {

        private byte[] getMedicineImage(String url) {
            try {
                URL imageUrl = new URL(url);
                URLConnection urlConnection = imageUrl.openConnection();

                InputStream is = urlConnection.getInputStream();
                BufferedInputStream bis = new BufferedInputStream(is);

                ByteArrayOutputStream baos = new ByteArrayOutputStream(7000);
                int nextByte;
                while ((nextByte = bis.read()) != -1) {
                    baos.write(nextByte);
                }
                return baos.toByteArray();
            } catch (Exception e) {
                return null;
            }
        }

        @SuppressLint("StaticFieldLeak")
        @Override
        public void handleResult(HandlerResult handlerResult) {
            if (handlerResult == null) {
                return;
            }

            final Object result = handlerResult.getResult();
            if (result == null) {
                LogUtils.debug("Encountered an error in server communication for Watch registration. No JSON arrived.");
            }

            new AsyncTask<Void, Void, String>() {

                @Override
                protected void onPreExecute() {
                    super.onPreExecute();
                    ProgressBar progressBar = findViewById(R.id.spinnerLoading);
                    progressBar.setVisibility(View.VISIBLE);
                    Globals.isDownloading = true;
                }

                @Override
                protected String doInBackground(Void... params) {
                    String isRegistrationSuccessful = "";
                    if ((result instanceof String)) {
                        RegisterResponseMessage responseMessage = new Gson().fromJson((String) result, RegisterResponseMessage.class);
                        LogUtils.debug("Response message ->" + result);
                        Log.w("Result", "Response message ->" + result);
                        LogUtils.saveDataToFile((String) result);
                        if (responseMessage != null && responseMessage.isSuccessResponse()) {
                            PatientDetails patientDetails = new PatientDetails();
                            patientDetails.setPatientId(responseMessage.patientId);
                            patientDetails.setPatientName(responseMessage.patientName);
                            patientDetails.setCaregiverId(responseMessage.patientId);
                            patientDetails.setCaregiverName(responseMessage.patientId);
                            patientDetails.setChosenTimeForTimeSlots(responseMessage.chosenTimeForTimeSlots);
                            patientDetails.setSosMobileNo(responseMessage.sosMobileNo);
                            patientDetails.setAddress(responseMessage.address);
                            patientDetails.setGpsStatus(responseMessage.gpsStatus);
                            patientDetails.setRadius(responseMessage.radius);
                            patientDetails.setTrackingStatus(responseMessage.trackingStatus);
                            patientDetails.setLatLong(responseMessage.latLong);
                            patientDetails.setAppColor(responseMessage.appColor);
                            patientDetails.setAppLanguage(responseMessage.appLanguage);
                            patientDetails.setHeartRateStatus(responseMessage.heartRateStatus);
                            patientDetails.setHeartScheduleDays(responseMessage.heartScheduleDaysOfWeek);
                            patientDetails.setHeartRateScheduleTimeSlots(responseMessage.heartRateScheduleTimeSlots);
                            if (responseMessage.pedometerConfigurationInfo != null) {
                                if (responseMessage.pedometerConfigurationInfo.state != null) {
                                    patientDetails.setPedoMeterStatus(responseMessage.pedometerConfigurationInfo.state);
                                } else {
                                    patientDetails.setPedoMeterStatus("disable");
                                }
                                if (responseMessage.pedometerConfigurationInfo.timeInterval != null) {
                                    patientDetails.setPedoMeterInterval(responseMessage.pedometerConfigurationInfo.timeInterval);
                                } else {
                                    patientDetails.setPedoMeterStatus("60");
                                }
                            } else {
                                patientDetails.setPedoMeterStatus("disable");
                                patientDetails.setPedoMeterStatus("60");
                            }
                            if (responseMessage.sleepData != null) {
                                patientDetails.setSleepStartTime(responseMessage.sleepData.sleepStartTime);
                                patientDetails.setSleepEndTime(responseMessage.sleepData.sleepEndTime);
                            } else {
                                patientDetails.setSleepStartTime("21:30");
                                patientDetails.setSleepEndTime("7:30");
                            }

                            patientDetails.setProviderName(responseMessage.provider);
                            patientDetails.setProviderPhone(responseMessage.providerPhone != null ? responseMessage.providerPhone : "--");
                            patientDetails.setCmName(responseMessage.caseManager);
                            patientDetails.setCmPhone(responseMessage.caseManagerPhone != null ? responseMessage.caseManagerPhone : "--");
                            patientDetails.setClinicName(responseMessage.clinicName != null ? responseMessage.clinicName : "--");

                            PatientDetails.addToDB(patientDetails);
                            MedicationScheduleMaster.deleteAllRows();

                            if (responseMessage.medicationDetail != null) {
                                LogUtils.debug("Going to add " + responseMessage.medicationDetail.size() + " medications in schedule master.");
                                for (MedicationDetail medicationDetail : responseMessage.medicationDetail) {

                                    MedicationScheduleMaster scheduleMaster = new MedicationScheduleMaster();
                                    scheduleMaster.setMedicineId(medicationDetail.medicineId);
                                    scheduleMaster.setMedicineName(medicationDetail.medicineName);
                                    scheduleMaster.setMedicineDosage(medicationDetail.dosage);
                                    scheduleMaster.setMedicineStrength(medicationDetail.strength);
                                    scheduleMaster.setMedicineReminderColor(medicationDetail.color != null ? medicationDetail.color : "");
                                    //scheduleMaster.setMedicineImage(getMedicineImage(medicationDetail.image));
                                    scheduleMaster.setMedicineForm(medicationDetail.medicineForm);

                                    if (medicationDetail.image != null && !medicationDetail.image.isEmpty()) {
                                        byte[] arr = getMedicineImage(medicationDetail.image);
                                        scheduleMaster.setMedicineImage(arr);
                                        assert arr != null;
                                        Bitmap bitmap = BitmapFactory.decodeByteArray(arr, 0, arr.length);
                                        LogUtils.SaveImage(bitmap, medicationDetail.medicineId);
                                    } else {
                                        scheduleMaster.setMedicineImage(new byte[0]);
                                    }

                                    scheduleMaster.setBeforeOrAfterFood(medicationDetail.medtime_rel_food);
                                    scheduleMaster.setTimeSlots(medicationDetail.timeSlots);
                                    scheduleMaster.setDaysOfWeek(medicationDetail.daysOfWeek);
                                    scheduleMaster.setQuantities(medicationDetail.quantities);
                                    MedicationScheduleMaster.addToDB(scheduleMaster);
                                    LogUtils.debug("Added a medication with details: [" + scheduleMaster.getMedicineName() + "; " + scheduleMaster.getTimeSlots() + "; " + scheduleMaster.getBeforeOrAfterFood() + "; " + scheduleMaster.getDaysOfWeek() + "]");
                                }
                            }
                            CustomerAlertsDB.deleteAllRows();
                            if (responseMessage.customAlertsDetails != null) {
                                for (CustomAlertsDetails medicationDetail : responseMessage.customAlertsDetails) {
                                    CustomerAlertsDB alertsDetails = new CustomerAlertsDB();
                                    alertsDetails.setPatientId(medicationDetail.patientId);
                                    alertsDetails.setAlterType(medicationDetail.alertType);
                                    alertsDetails.setAlterTime(medicationDetail.alertTime);
                                    alertsDetails.setAlterDetail(medicationDetail.alertDetail);
                                    alertsDetails.setType(medicationDetail.type);
                                    alertsDetails.setStartDate(medicationDetail.startDate);
                                    alertsDetails.setEndDate(medicationDetail.endDate);
                                    long res = CustomerAlertsDB.addToDB(alertsDetails);
                                    Log.e("SplashActivity ", " CustomAlertsDetails res : " + res);
                                }
                            }

                            ScheduleMessagesDB.deleteAllRows();
                            if (responseMessage.scheduledTextMessageInfoList != null && responseMessage.scheduledTextMessageInfoList.size() > 0) {
                                for (ScheduleMessageVO scheduleMessage : responseMessage.scheduledTextMessageInfoList) {
                                    ScheduleMessagesDB messagesDB = new ScheduleMessagesDB();
                                    messagesDB.setQuestionId(scheduleMessage.scheduledTextMessagesId);
                                    messagesDB.setQuestionName(scheduleMessage.question);
                                    messagesDB.setAnswer(scheduleMessage.answer);
                                    messagesDB.setSenderName(scheduleMessage.senderName);
                                    messagesDB.setDayOfWeek(scheduleMessage.dayOfWeek);
                                    messagesDB.setTimeSlots(scheduleMessage.timeSlots);
                                    long messageInserted = ScheduleMessagesDB.addToDB(messagesDB);
                                    Log.e("SplashActivity ", " Scheduled Message Inserted To DB : " + messageInserted);
                                }
                            }

                            VitalConfiguration.deleteAllRows();
                            if (responseMessage.vitalScheduleInfoList != null && responseMessage.vitalScheduleInfoList.size() > 0) {
                                for (VitalConfigVO configuration : responseMessage.vitalScheduleInfoList) {
                                    VitalConfiguration vitalConfiguration = new VitalConfiguration();
                                    vitalConfiguration.setVitalScheduleId(configuration.vitalScheduleId);
                                    vitalConfiguration.setVitalTypeName(configuration.vitalTypeName);
                                    vitalConfiguration.setCollectMode(configuration.collectMode);
                                    vitalConfiguration.setScheduleDayOfWeek(configuration.scheduleDayOfWeek);
                                    vitalConfiguration.setFrequency(configuration.frequency);
                                    vitalConfiguration.setTimeSlots(configuration.timeSlots);
                                    vitalConfiguration.setDeviceSerialId(configuration.deviceSerialId);
                                    vitalConfiguration.setDeviceMeasures(configuration.deviceMeasures);
                                    vitalConfiguration.setDeviceName(configuration.deviceName);
                                    vitalConfiguration.setStartDate(configuration.startDate);
                                    vitalConfiguration.setEndDate(configuration.endDate);
                                    long messageInserted = VitalConfiguration.addToDB(vitalConfiguration);
                                    Log.e("SplashActivity ", "Vital Added: " + messageInserted);
                                }
                            }
                            VitalStatusDetails.deleteAllRows();
                            if (responseMessage.vitalTypeStatusList != null && responseMessage.vitalTypeStatusList.size() > 0) {
                                for (VitalConfigVO configuration : responseMessage.vitalTypeStatusList) {
                                    VitalStatusDetails vitalStatusDetails = new VitalStatusDetails();
                                    vitalStatusDetails.setVitalTypeName(configuration.vitalTypeName);
                                    vitalStatusDetails.setVitalStatus(configuration.vitalStatus);
                                    long messageInserted = VitalStatusDetails.addToDB(vitalStatusDetails);
                                    Log.e("SplashActivity ", "Vital Status Added: " + messageInserted);
                                }
                            }
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        } else if (responseMessage != null && responseMessage.isImeNotFoundResponse()) {
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        } else if (responseMessage != null && responseMessage.isImeNotAssignedResponse()) {
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        } else if (responseMessage != null && responseMessage.isWatchInActive()) {
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        } else if (responseMessage != null && responseMessage.isPatientInfoNotFound()) {
                            isRegistrationSuccessful = responseMessage.responseMessage;
                        }
                    }
                    return isRegistrationSuccessful;
                }

                @SuppressLint("SetTextI18n")
                @Override
                protected void onPostExecute(String isRegistrationSuccessful) {
                    ProgressBar progressBar = findViewById(R.id.spinnerLoading);
                    progressBar.setVisibility(View.GONE);
                    Globals.isDownloading = false;
                    if (isRegistrationSuccessful.equalsIgnoreCase("operationsuccessful")) {
                        LogUtils.debug("Watch registered successfully.");
                        Toast.makeText(context, "Device registered successfully.", Toast.LENGTH_SHORT).show();
                        Globals.isWatchRegistered = true;
                        // Go to the next screen
                        Intent intent = new Intent(SplashActivity.this, MainActivity.class);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                    } else if (isRegistrationSuccessful.equalsIgnoreCase("Imei is not found in the database") ||
                            isRegistrationSuccessful.equalsIgnoreCase("Patient details not found with provided info.")) {
                        registerWatchVeryFirstTime();
                    } else if (isRegistrationSuccessful.equalsIgnoreCase("Imei is not associated with any patient")) {
                        TextView textView = findViewById(R.id.internet);
                        textView.setVisibility(View.VISIBLE);
                        textView.setText(R.string.imei_not_assigned);
                    } else if (isRegistrationSuccessful.equalsIgnoreCase("Subscription is not active")) {
                        TextView textView = findViewById(R.id.internet);
                        textView.setVisibility(View.VISIBLE);
                        textView.setText(R.string.subscription_not_active);
                        CommUtils.deleteDatabase();
                    } else {
                        if (!ReadDataFromMemory.getDataFromMemory().isEmpty()) {
                            if (registerWatchWithoutInternet(ReadDataFromMemory.getDataFromMemory()).equalsIgnoreCase("operationsuccessful")) {
                                LogUtils.debug("Watch registered successfully.");
                                Toast.makeText(context, "Device registered successfully.", Toast.LENGTH_SHORT).show();
                                Globals.isWatchRegistered = true;
                                // Go to the next screen
                                Intent intent = new Intent(SplashActivity.this, MainActivity.class);
                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                                startActivity(intent);
                                finishAffinity();
                            } else if (registerWatchWithoutInternet(ReadDataFromMemory.getDataFromMemory()).equalsIgnoreCase("Imei is not found in the database")) {
                                TextView textView = findViewById(R.id.internet);
                                textView.setVisibility(View.VISIBLE);
                                if (textView != null) {
                                    textView.setText(R.string.imei_not_found_in_db);
                                }

                            } else if (registerWatchWithoutInternet(ReadDataFromMemory.getDataFromMemory()).equalsIgnoreCase("Imei is not associated with any patient")) {
                                TextView textView = findViewById(R.id.internet);
                                textView.setVisibility(View.VISIBLE);
                                textView.setText(R.string.imei_not_assigned);

                            } else if (isRegistrationSuccessful.equalsIgnoreCase("Subscription is not active")) {
                                TextView textView = findViewById(R.id.internet);
                                textView.setVisibility(View.VISIBLE);
                                textView.setText(R.string.subscription_not_active);
                                CommUtils.deleteDatabase();
                            }

                        } else {
                            TextView textView = findViewById(R.id.internet);
                            textView.setVisibility(View.VISIBLE);
                            if (textView != null) {
                                if (SoftwareUpdateUtil.isSoftwareUpgradeInProgress()) {
                                    rollBackMethod();
                                } else {
//                                    registerWithoutInternet();
                                }
                                LogUtils.debug("Watch registration rejected by server. server communication problem ");
                                textView.setText("Watch registration rejected by server. server communication problem");
                                setupInternetConnectedIndicator();
                            }
                        }
                    }
                }
            }.execute();
        }
    }

    private class GCMRegistrationResponseHandler implements TaskResultHandler {

        @Override
        public void handleResult(HandlerResult result) {
            if (result.getResult() == null) {
                LogUtils.debug("GCM token Registration with server failed.");
                TextView textView = findViewById(R.id.internet);
                textView.setVisibility(View.VISIBLE);
                if (textView != null) {
                    LogUtils.debug("GCM token Registration with server failed.");
                    textView.setText(R.string.gcm_registration_failed);
                }
            } else {
                LogUtils.debug("GCM token Registration with server completed successfully.");
//                Toast.makeText(context, "FCM token Registration with server completed successfully.", Toast.LENGTH_SHORT).show();
            }
        }
    }

    public static final String INTERNET_CONNECTED_INDICATOR_INTENT_FILTER = "com.watchrx.watch.internetConnectedIndicator";

    public void setupInternetConnectedIndicator() {
        LogUtils.debug("Setting up listener to wait for Internet to become available.");
        IntentFilter i = new IntentFilter();
        i.addAction(INTERNET_CONNECTED_INDICATOR_INTENT_FILTER);
        LocalBroadcastManager.getInstance(this).registerReceiver(internetConnectedReceiver, i);
    }

    public class InternetConnectedReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            LogUtils.debug("Received Internet change notification. Will try to register again.");
            synchronized (Globals.synchronized_were) {
                if (!Globals.isWatchRegistered) {
                    @SuppressLint("HandlerLeak") Handler h = new Handler() {
                        @Override
                        public void handleMessage(Message msg) {
                            if (msg.what != 1) { // code if not connected
                                LogUtils.debug("Most likely internet not connected or data transmission is not happening properly ");
                            } else {
                                TextView textView = findViewById(R.id.internet);
                                textView.setVisibility(View.GONE);
                                LogUtils.debug("Will try to register again.");
                                Toast.makeText(WatchApp.getContext(), "Retrying registration...", Toast.LENGTH_LONG).show();
                                if (!DBAdaptor.getDbAdaptorInstance().isDBOpen()) {
                                    DBAdaptor.getDbAdaptorInstance().open(getApplication().getBaseContext());
                                    LogUtils.debug("@#@##@##@ Opening and creating the Database@#@#@#@#");
                                }
                                registerWatch();
                            }
                        }
                    };
                    InternetCheckConnectivity.isNetworkAvailable(h, CommonConstants.Max_Time_Out_For_Ping);
                } else {
                    LogUtils.debug("Already registered. No need to do anything.");
                }
            }
        }
    }

    public String registerWatchWithoutInternet(String result) {

        String isRegistrationSuccessful = "";

        if ((result != null)) {

            RegisterResponseMessage responseMessage = new Gson().fromJson(result, RegisterResponseMessage.class);
            LogUtils.debug("Response message ->" + result);

            if (responseMessage != null && responseMessage.isSuccessResponse()) {
                PatientDetails patientDetails = new PatientDetails();

                patientDetails.setPatientId(responseMessage.patientId);
                patientDetails.setPatientName(responseMessage.patientName);
                patientDetails.setCaregiverId(responseMessage.patientId);
                patientDetails.setCaregiverName(responseMessage.patientId);
                patientDetails.setChosenTimeForTimeSlots(responseMessage.chosenTimeForTimeSlots);
                patientDetails.setSosMobileNo(responseMessage.sosMobileNo);
                patientDetails.setAddress(responseMessage.address);

                patientDetails.setGpsStatus(responseMessage.gpsStatus);
                patientDetails.setRadius(responseMessage.radius);
                patientDetails.setTrackingStatus(responseMessage.trackingStatus);
                patientDetails.setLatLong(responseMessage.latLong);
                patientDetails.setAppColor(responseMessage.appColor);
                patientDetails.setAppLanguage(responseMessage.appLanguage);
                patientDetails.setHeartRateStatus(responseMessage.heartRateStatus);
                patientDetails.setHeartScheduleDays(responseMessage.heartScheduleDaysOfWeek);
                patientDetails.setHeartRateScheduleTimeSlots(responseMessage.heartRateScheduleTimeSlots);
                if (responseMessage.pedometerConfigurationInfo != null) {
                    if (responseMessage.pedometerConfigurationInfo.state != null) {
                        patientDetails.setPedoMeterStatus(responseMessage.pedometerConfigurationInfo.state);
                    } else {
                        patientDetails.setPedoMeterStatus("disable");
                    }

                    if (responseMessage.pedometerConfigurationInfo.timeInterval != null) {
                        patientDetails.setPedoMeterInterval(responseMessage.pedometerConfigurationInfo.timeInterval);
                    } else {
                        patientDetails.setPedoMeterStatus("60");
                    }
                } else {
                    patientDetails.setPedoMeterStatus("disable");
                    patientDetails.setPedoMeterStatus("60");
                }
                if (responseMessage.sleepData != null) {
                    patientDetails.setSleepStartTime(responseMessage.sleepData.sleepStartTime);
                    patientDetails.setSleepEndTime(responseMessage.sleepData.sleepEndTime);
                } else {
                    patientDetails.setSleepStartTime("21:30");
                    patientDetails.setSleepEndTime("7:30");
                }

                patientDetails.setProviderName(responseMessage.provider);
                patientDetails.setProviderPhone(responseMessage.providerPhone != null ? responseMessage.providerPhone : "--");
                patientDetails.setCmName(responseMessage.caseManager);
                patientDetails.setCmPhone(responseMessage.caseManagerPhone != null ? responseMessage.caseManagerPhone : "--");
                patientDetails.setClinicName(responseMessage.clinicName != null ? responseMessage.clinicName : "--");

                PatientDetails.addToDB(patientDetails);

                MedicationScheduleMaster.deleteAllRows();

                if (responseMessage.medicationDetail != null) {
                    LogUtils.debug("Going to add " + responseMessage.medicationDetail.size() + " medications in schedule master.");
                    for (MedicationDetail medicationDetail : responseMessage.medicationDetail) {

                        MedicationScheduleMaster scheduleMaster = new MedicationScheduleMaster();
                        scheduleMaster.setMedicineId(medicationDetail.medicineId);
                        scheduleMaster.setMedicineName(medicationDetail.medicineName);
                        scheduleMaster.setMedicineDosage(medicationDetail.dosage);
                        scheduleMaster.setMedicineStrength(medicationDetail.strength);
                        scheduleMaster.setMedicineReminderColor(medicationDetail.color != null ? medicationDetail.color : "");
                        scheduleMaster.setMedicineImage(getImageFromFlashMemory(medicationDetail.medicineId));
                        scheduleMaster.setMedicineForm(medicationDetail.medicineForm);
                        scheduleMaster.setBeforeOrAfterFood(medicationDetail.medtime_rel_food);
                        scheduleMaster.setTimeSlots(medicationDetail.timeSlots);
                        scheduleMaster.setDaysOfWeek(medicationDetail.daysOfWeek);
                        scheduleMaster.setQuantities(medicationDetail.quantities);
                        MedicationScheduleMaster.addToDB(scheduleMaster);

                        LogUtils.debug("Added a medication with details: [" + scheduleMaster.getMedicineName() + "; " + scheduleMaster.getTimeSlots() + "; " + scheduleMaster.getBeforeOrAfterFood() + "; " + scheduleMaster.getDaysOfWeek() + "]");
                    }
                }

                CustomerAlertsDB.deleteAllRows();
                if (responseMessage.customAlertsDetails != null) {

                    for (CustomAlertsDetails medicationDetail : responseMessage.customAlertsDetails) {
                        CustomerAlertsDB alertsDetails = new CustomerAlertsDB();

                        alertsDetails.setPatientId(medicationDetail.patientId);
                        alertsDetails.setAlterType(medicationDetail.alertType);
                        alertsDetails.setAlterTime(medicationDetail.alertTime);
                        alertsDetails.setAlterDetail(medicationDetail.alertDetail);
                        alertsDetails.setType(medicationDetail.type);
                        alertsDetails.setStartDate(medicationDetail.startDate);
                        alertsDetails.setEndDate(medicationDetail.endDate);
                        long res = CustomerAlertsDB.addToDB(alertsDetails);
                        Log.e("SplashActivity ", " CustomAlertsDetails res : " + res);

                    }

                }
                ScheduleMessagesDB.deleteAllRows();
                if (responseMessage.scheduledTextMessageInfoList != null) {
                    for (ScheduleMessageVO scheduleMessage : responseMessage.scheduledTextMessageInfoList) {
                        ScheduleMessagesDB messagesDB = new ScheduleMessagesDB();
                        messagesDB.setQuestionId(scheduleMessage.scheduledTextMessagesId);
                        messagesDB.setQuestionName(scheduleMessage.question);
                        messagesDB.setAnswer(scheduleMessage.answer);
                        messagesDB.setSenderName(scheduleMessage.senderName);
                        messagesDB.setDayOfWeek(scheduleMessage.dayOfWeek);
                        messagesDB.setTimeSlots(scheduleMessage.timeSlots);
                        Long messageInserted = ScheduleMessagesDB.addToDB(messagesDB);
                        Log.e("SplashActivity ", " Scheduled Message Inserted To DB : " + messageInserted);
                    }
                }

                VitalConfiguration.deleteAllRows();
                if (responseMessage.vitalScheduleInfoList != null && !responseMessage.vitalScheduleInfoList.isEmpty()) {
                    for (VitalConfigVO configuration : responseMessage.vitalScheduleInfoList) {
                        VitalConfiguration vitalConfiguration = new VitalConfiguration();
                        vitalConfiguration.setVitalScheduleId(configuration.vitalScheduleId);
                        vitalConfiguration.setVitalTypeName(configuration.vitalTypeName);
                        vitalConfiguration.setCollectMode(configuration.collectMode);
                        vitalConfiguration.setScheduleDayOfWeek(configuration.scheduleDayOfWeek);
                        vitalConfiguration.setFrequency(configuration.frequency);
                        vitalConfiguration.setTimeSlots(configuration.timeSlots);
                        vitalConfiguration.setDeviceSerialId(configuration.deviceSerialId);
                        vitalConfiguration.setDeviceMeasures(configuration.deviceMeasures);
                        vitalConfiguration.setDeviceName(configuration.deviceName);
                        vitalConfiguration.setStartDate(configuration.startDate);
                        vitalConfiguration.setEndDate(configuration.endDate);
                        long messageInserted = VitalConfiguration.addToDB(vitalConfiguration);
                        Log.e("SplashActivity ", "Vital Added: " + messageInserted);
                    }
                }
                VitalStatusDetails.deleteAllRows();
                if (responseMessage.vitalTypeStatusList != null && !responseMessage.vitalTypeStatusList.isEmpty()) {
                    for (VitalConfigVO configuration : responseMessage.vitalTypeStatusList) {
                        VitalStatusDetails vitalStatusDetails = new VitalStatusDetails();
                        vitalStatusDetails.setVitalTypeName(configuration.vitalTypeName);
                        vitalStatusDetails.setVitalStatus(configuration.vitalStatus);
                        long messageInserted = VitalStatusDetails.addToDB(vitalStatusDetails);
                        Log.e("SplashActivity ", "Vital Status Added: " + messageInserted);
                    }
                }
                isRegistrationSuccessful = responseMessage.responseMessage;
            } else if (responseMessage != null && responseMessage.isImeNotFoundResponse()) {
                isRegistrationSuccessful = responseMessage.responseMessage;

            } else if (responseMessage != null && responseMessage.isImeNotAssignedResponse()) {
                isRegistrationSuccessful = responseMessage.responseMessage;
            } else if (responseMessage != null && responseMessage.isWatchInActive()) {
                isRegistrationSuccessful = responseMessage.responseMessage;
            }
        }
        return isRegistrationSuccessful;
    }

    public byte[] getImageFromFlashMemory(String medicationId) {

        String rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
                .getAbsolutePath() + "/WatchRx_DataBase/WatchRx_Images";
        File file = new File(rootPath, medicationId + ".png");
        byte[] image = new byte[0];
        if (file.exists()) {
            Bitmap bitmap = BitmapFactory.decodeFile(file.toString());

            if (bitmap != null) {
                ByteArrayOutputStream stream = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream);
                image = stream.toByteArray();
            }
            return image;
        } else {
            LogUtils.debug("Image not found in FLash memory for this medication Id " + medicationId + "we are using default image");
            Bitmap bitmap = BitmapFactory.decodeResource(getResources(), R.drawable.deafult_medicine);
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, stream);
            return stream.toByteArray();
        }
    }
}
