package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;

import com.watchrx.watchrxhealth.SplashActivity;
import com.watchrx.watchrxhealth.db.ServerQueue;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class RebootComplete extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {

        if (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED)) {
            if (!isMyLauncherDefault(context)) {
                if (!Globals.isWatchRegistered) {
                    LogUtils.debug("WatchRx is not set as default launcher so App is restart in next 5 seconds.....");
                    try {
                        Thread.sleep(5000);
                        Intent i = new Intent(context, SplashActivity.class);
                        i.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        context.startActivity(i);

                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }

                } else {
                    LogUtils.debug("WatchRx is not set as default launcher so App try to restart but app started by user ");
                }
            } else {
                LogUtils.debug("WatchRx is Set as Default Launcher");
            }

        } else if (intent.getAction().equals(Intent.ACTION_SHUTDOWN)) {

           /* if (!isMyLauncherDefault(context)) {
                LogUtils.debug("Before shutdown app is not set as default launcher");
            } else {
                LogUtils.debug("@#@#@#@Before shutdown app is set as default launcher@#@#@#@");
                context.getPackageManager().clearPackagePreferredActivities(context.getPackageName());
            }*/
            CommUtils.disableBroadcastReceiver(context);
            LogUtils.debug("System going to SHUTDOWN******at-" + new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss.S", Locale.US).format(new Date()));
            if (Globals.isWatchRegistered) {
                List<ServerQueue> recordList = ServerQueue.getFromDB();
                LogUtils.debug("Number of records to push to server before SHUTDOWN=" + recordList.size());
                if (recordList.size() != 0) {
                    for (ServerQueue record : recordList) {
                        if (record.getState().equalsIgnoreCase("NOT_SENT")) {
                            LogUtils.SaveLogToFile(record.getUrl() + "%" + record.getMsg() + "#");
                        }
                    }
                }
            } else {
                LogUtils.debug("Watch is not registered.before shutdown the system");
            }
        }
    }

    private boolean isMyLauncherDefault(Context context) {
        PackageManager localPackageManager = context.getPackageManager();
        Intent intent = new Intent("android.intent.action.MAIN");
        intent.addCategory("android.intent.category.HOME");
        String str = localPackageManager.resolveActivity(intent,
                PackageManager.MATCH_DEFAULT_ONLY).activityInfo.packageName;
        return str.equals(context.getPackageName());
    }
}




