<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <TextView
            android:id="@+id/vitalData"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_bold"
            android:textColor="@color/black"
            android:textSize="22sp" />

        <TextView
            android:id="@+id/vitalDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:fontFamily="@font/lato_regular"
            android:gravity="start"
            android:textColor="@color/colorText"
            android:textSize="18sp" />
    </LinearLayout>
</androidx.cardview.widget.CardView>
