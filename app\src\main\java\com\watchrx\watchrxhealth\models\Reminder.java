package com.watchrx.watchrxhealth.models;

public class Reminder {
    private final String tabletName;
    private final String medicineType;
    private final String frequency;
    private final String nextReminder;
    private final String time;
    private final Long duration;
    private final int imageId;

    public Reminder(String tabletName, String medicineType, String frequency, String nextReminder, String time, Long duration, int imageId) {
        this.tabletName = tabletName;
        this.medicineType = medicineType;
        this.frequency = frequency;
        this.nextReminder = nextReminder;
        this.time = time;
        this.duration = duration;
        this.imageId = imageId;
    }

    public String getTabletName() {
        return tabletName;
    }

    public String getMedicineType() {
        return medicineType;
    }

    public String getFrequency() {
        return frequency;
    }

    public String getNextReminder() {
        return nextReminder;
    }

    public String getTime() {
        return time;
    }

    public Long getDuration() {
        return duration;
    }

    public int getImageId() {
        return imageId;
    }
}
