<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/incomingCallLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#121212"
    android:padding="24dp">

    <!-- Caller Image -->
    <ImageView
        android:id="@+id/imgCaller"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="32dp"
        android:contentDescription="@string/app_logo"

        android:src="@drawable/logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Caller Text -->
    <TextView
        android:id="@+id/txtCaller"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/incoming_call"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imgCaller" />

    <!-- Call Timer -->
    <TextView
        android:id="@+id/txtTimer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/_00_00"
        android:textColor="#B0BEC5"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtCaller" />

    <!-- Call Quality -->
    <TextView
        android:id="@+id/txtQuality"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/quality_good"
        android:textColor="#90CAF9"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/txtTimer" />

    <!-- Button Row -->
    <LinearLayout
        android:id="@+id/buttonRow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- Accept -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnAccept"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/app_logo"
            app:backgroundTint="#4CAF50"
            app:srcCompat="@drawable/call_open"
            app:tint="@color/white" />

        <!-- Mute -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnMute"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/app_logo"
            app:backgroundTint="@color/white"
            app:srcCompat="@drawable/mic_on"
            app:tint="@color/black" />

        <!-- Speaker -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnSpeaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/app_logo"
            app:backgroundTint="@color/white"
            app:srcCompat="@drawable/speaker_off"
            app:tint="@color/black" />


        <!-- Reject -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/btnReject"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/app_logo"
            app:backgroundTint="#F44336"
            app:srcCompat="@drawable/call_end"
            app:tint="@color/white" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
