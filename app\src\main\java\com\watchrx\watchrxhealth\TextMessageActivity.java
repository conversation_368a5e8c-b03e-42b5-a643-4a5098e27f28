package com.watchrx.watchrxhealth;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.watchrx.watchrxhealth.adapter.AnswersAdapter;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.models.AnswersModel;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.BottomNavItemSelectedListener;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TextMessageActivity extends AppCompatActivity implements Runnable {

    private static final long[] vibratePattern = {0, 500, 200};
    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private Handler timeoutHandler;
    private String data = null;
    private int reminderRetryCount = 0;
    private final List<AnswersModel> answerList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_text_message);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON | WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD | 2621440);
        getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        TextView message = findViewById(R.id.message);
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(TextMessageActivity.this, REMINDER_TIME_OUT);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);

        Bundle bundle = getIntent().getExtras();
        data = bundle != null ? bundle.getString("data") : null;
        try {
            if (data != null) {
                JSONObject name = new JSONObject(data);
                message.setText(name.getString("question"));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (data != null) {
            try {
                JSONObject jsonObject = new JSONObject(data);
                String answerData = jsonObject.getString("answer");
                if (answerData != null && (answerData.length() > 2)) {
                    answerData = answerData.replace("[", "");
                    answerData = answerData.replace("]", "");
                    String[] tokens = answerData.split(",");
                    for (String str : tokens) {
                        AnswersModel answersModel = new AnswersModel();
                        answersModel.setAnswer(str);
                        answerList.add(answersModel);
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        ConstraintLayout constraintLayout = findViewById(R.id.texMessageView);
        TextView textView = findViewById(R.id.messageUserActionText);
        RecyclerView recyclerView = findViewById(R.id.answers);
        if (answerList.size() > 0) {
            recyclerView.setVisibility(View.VISIBLE);
            recyclerView.setHasFixedSize(true);
            recyclerView.setLayoutManager(new LinearLayoutManager(this));
            recyclerView.setAdapter(new AnswersAdapter(TextMessageActivity.this, answerList, new AnswersAdapter.OnItemClickListener() {
                @Override
                public void onItemClick(AnswersModel item) {
                    JSONObject jsonObject;
                    try {
                        jsonObject = new JSONObject(data);
                        CommUtils.sendTextMessageResponseLogToServer(WatchApp.getContext(), jsonObject.getString("questionId"), item.getAnswer());
                        GeneralUtils.stopBeeping();
                        GeneralUtils.stopSpeaking();
                        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                        GeneralUtils.speak("Thanks for your answer");
                        timeoutHandler.removeCallbacks(TextMessageActivity.this);
                        Globals.isScreenRunning = false;
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                        finish();
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }));
            // textView.setText(R.string.user_respond_text);
        } else {
            recyclerView.setVisibility(View.GONE);
            constraintLayout.setVisibility(View.VISIBLE);
            // textView.setText(R.string.user_respond_text_no_answer_text);
        }

        constraintLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    JSONObject jsondata = new JSONObject(data);
                    if ((jsondata.getString("answer").isEmpty()) || (jsondata.getString("answer").length() == 2)) {
                        jsondata = new JSONObject(data);
                        CommUtils.sendTextMessageResponseLogToServer(WatchApp.getContext(), jsondata.getString("questionId"), "User responded");
                        GeneralUtils.stopBeeping();
                        GeneralUtils.stopSpeaking();
                        GeneralUtils.speak("Thanks for your confirmation");
                        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                        Globals.isScreenRunning = false;
                        timeoutHandler.removeCallbacks(TextMessageActivity.this);
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                        TextMessageActivity.this.finish();
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });

        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                if (answerList.size() > 0) {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Hi $ you received a message  $Please Select Tap Any option , to respond it ");
                    } else {
                        GeneralUtils.speak("Hola $ recibió un mensaje  $Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                    }
                } else {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Hi $ you received a message  $Please Tap ANYWHERE, to respond it ");
                    } else {
                        GeneralUtils.speak("Hola $ recibió un mensaje  $Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                    }
                }
            }
        });

        BottomNavigationView bottomNavigationView = findViewById(R.id.nav_view);
        bottomNavigationView.setOnItemSelectedListener(new BottomNavItemSelectedListener(this));

        ((Button) findViewById(R.id.send)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                EditText editText = findViewById(R.id.custom_message);
                if (editText != null && !TextUtils.isEmpty(editText.getText().toString())) {
                    JSONObject jsondata = null;
                    try {
                        jsondata = new JSONObject(data);
                        CommUtils.sendTextMessageResponseLogToServer(WatchApp.getContext(), jsondata.getString("questionId"), editText.getText().toString());
                        GeneralUtils.stopBeeping();
                        GeneralUtils.stopSpeaking();
                        GeneralUtils.speak("Thanks for your confirmation");
                        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
                        Globals.isScreenRunning = false;
                        timeoutHandler.removeCallbacks(TextMessageActivity.this);
                        NotifyNewEntryInQueue.notifyNewEntryInQueue();
                        TextMessageActivity.this.finish();
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                } else {
                    editText.setError("Please enter your message.");
                }
            }
        });
    }

    @Override
    public void run() {
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(this);
        reminderRetryCount++;
        if (reminderRetryCount < 2) {
            GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
                @Override
                public void onCompletion(MediaPlayer mp) {
                    if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
                        GeneralUtils.speak("Hi $ you received a message  $Please Tap ANYWHERE, to respond it ");
                    } else {
                        GeneralUtils.speak("Hola $ recibió un mensaje  $Favor de leerlo $ Toque su reloj dondequiera para cerrarlo");
                    }
                }
            });
            timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);
        } else {
            ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
            JSONObject jsonObject = null;
            try {
                jsonObject = new JSONObject(data);
                CommUtils.sendTextMessageResponseLogToServer(WatchApp.getContext(), jsonObject.getString("questionId"), "No response from user");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            Globals.isScreenRunning = false;
            NotifyNewEntryInQueue.notifyNewEntryInQueue();
            finish();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
    }
}