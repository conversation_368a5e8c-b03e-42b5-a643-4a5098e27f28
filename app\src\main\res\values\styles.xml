<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>


    <style name="FullscreenTheme" parent="AppTheme">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="metaButtonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="metaButtonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
    </style>

    <style name="FullscreenActionBarStyle" parent="Widget.AppCompat.ActionBar">
        <item name="android:background">@color/black_overlay</item>
    </style>

    <style name="UiTestTextView">
        <item name="android:textColor">#ff0000</item>
        <item name="android:textSize">16sp</item>

        <item name="android:shadowColor">#ffffff</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">3</item>
    </style>

    <style name="CustomProgressDialog" parent="Theme.AppCompat.Dialog">
        <!--This property controls whether the background of the floating window is dimmed-->
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@android:color/white</item>
    </style>

    <style name="Theme.WatchRx" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/blue</item>
        <item name="colorPrimaryVariant">@color/dark_blue</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">@color/blue</item>
    </style>


    <style name="ShapeAppearanceOverlay.App.rounded" parent="">
        <item name="cornerSize">50%</item>
    </style>

    <style name="AppTheme1" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/transperant</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="loginButton" parent="@android:style/Widget.Button">
        <item name="android:background">#8BC34A</item>
    </style>

    <style name="cancelButton" parent="@android:style/Widget.Button">
        <item name="android:background">#FF5722</item>
    </style>

    <style name="PasswordCriteria">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#FF0000</item>
        <item name="android:textSize">12sp</item>
        <item name="android:fontFamily">@font/lato_regular</item>
        <item name="android:paddingBottom">2dp</item>
    </style>

    <style name="CircleImage" parent="">
        <item name="cornerSize">50%</item>
    </style>

</resources>
