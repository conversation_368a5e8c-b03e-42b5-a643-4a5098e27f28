package com.watchrx.watchrxhealth;

import android.media.MediaPlayer;
import android.os.Bundle;
import android.util.Log;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.amazonaws.mobile.client.AWSMobileClient;
import com.amazonaws.mobile.client.Callback;
import com.amazonaws.mobile.client.UserStateDetails;
import com.amazonaws.mobileconnectors.lex.interactionkit.Response;
import com.amazonaws.mobileconnectors.lex.interactionkit.config.InteractionConfig;
import com.amazonaws.mobileconnectors.lex.interactionkit.ui.InteractiveVoiceView;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.CommUtils;
import com.watchrx.watchrxhealth.utils.GeneralUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Map;
import java.util.Objects;

public class InteractiveVoiceActivity extends AppCompatActivity
        implements InteractiveVoiceView.InteractiveVoiceListener {
    private static final String TAG = "VoiceActivity";
    private InteractiveVoiceView voiceView;
    private TextView transcriptTextView;
    private TextView responseTextView;
    private String botName = null;
    private boolean anyError = false;

    @Override
    protected void onCreate(final Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED | WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON);
        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        setContentView(R.layout.activity_interactive_voice);
        transcriptTextView = findViewById(R.id.transcriptTextView);
        responseTextView = findViewById(R.id.responseTextView);
        voiceView = findViewById(R.id.voiceInterface);

        botName = getIntent().getStringExtra("botName");
        Log.w(TAG, "onCreate: botName" + botName);
        if (!CommUtils.isNetworkAvailable(this)) {
            Toast.makeText(this, "Internet is not connected, Please connect to internet.", Toast.LENGTH_SHORT).show();
            voiceView.setEnabled(false);
            finish();
            return;
        }
        init();
        GeneralUtils.stopSpeaking();
        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                GeneralUtils.speak("Hi, $ Tap on mic icon $ and Say Hello $ to start conversations with watchrx remote helpline center");
            }
        });
    }

    @Override
    public void onBackPressed() {
        exit();
    }

    private void init() {

        voiceView.setInteractiveVoiceListener(this);
        AWSMobileClient.getInstance().initialize(this, new Callback<UserStateDetails>() {
            @Override
            public void onResult(UserStateDetails result) {
                try {
                    voiceView.getViewAdapter().setCredentialProvider(AWSMobileClient.getInstance());
                    AWSMobileClient.getInstance().getCredentials();

                    String identityId = AWSMobileClient.getInstance().getIdentityId();
                    String botAlias = null;
                    String botRegion = null;
                    JSONObject lexConfig;
                    try {
                        lexConfig = AWSMobileClient.getInstance().getConfiguration().optJsonObject("Lex");
                        lexConfig = lexConfig.getJSONObject(lexConfig.keys().next());
                        if (botName == null || botName.isEmpty()) {
                            botName = lexConfig.getString("Name");
                        }
                        botAlias = lexConfig.getString("Alias");
                        botRegion = lexConfig.getString("Region");
                    } catch (JSONException e) {
                        Log.e(TAG, "onResult: Failed to read configuration", e);
                    }
                    InteractionConfig lexInteractionConfig = new InteractionConfig(
                            botName,
                            botAlias,
                            identityId);

                    Map<String, String> sessionMap = lexInteractionConfig.getGlobalSessionAttributes();
                    sessionMap.put("patientId", PatientDetails.getFromDB().getPatientId());
                    lexInteractionConfig.setGlobalSessionAttributes(sessionMap);

                    voiceView.getViewAdapter().setInteractionConfig(lexInteractionConfig);
                    voiceView.getViewAdapter().setAwsRegion(botRegion);
                } catch (Exception e) {
                    e.printStackTrace();
                    voiceView.setEnabled(false);
                }
            }

            @Override
            public void onError(Exception e) {
                Log.e(TAG, "onError:121 ", e);
                voiceView.setEnabled(false);
            }
        });
    }

    private void exit() {
        finishAndRemoveTask();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
    }

    @Override
    public void dialogReadyForFulfillment(final Map<String, String> slots, final String intent) {
        Log.e(TAG, "dialogReadyForFulfillment");
    }

    @Override
    public void onResponse(Response response) {
        try {
            responseTextView.setText(response.getTextResponse());
            transcriptTextView.setText(response.getInputTranscript());
            if (response.getTextResponse() != null && ((response.getTextResponse().contains("Bye")) || (response.getTextResponse().contains("Thank")))) {
                exit();
            }
        } catch (Exception e) {
            e.printStackTrace();
            voiceView.setEnabled(false);
        }

    }

    @Override
    public void onError(final String responseText, final Exception e) {
        Log.e(TAG, "Error:152 " + responseText, e);
        Log.w(TAG, "Mic on onError");
    }
}
