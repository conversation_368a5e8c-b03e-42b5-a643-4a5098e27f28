package com.watchrx.watchrxhealth;

import android.content.Context;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import androidx.appcompat.app.AppCompatActivity;
import android.view.KeyEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ToggleButton;

import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.GeneralUtils;
import com.watchrx.watchrxhealth.utils.LogUtils;
import com.watchrx.watchrxhealth.utils.SoundUtils;

public class VisitVerificationActivity extends AppCompatActivity implements View.OnClickListener, Runnable {
    private static final long[] vibratePattern = {0, 500, 200};
    private static final long REMINDER_TIME_OUT = 30 * 1000;
    private Handler timeoutHandler;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        setContentView(R.layout.activity_visit_verification);
        Window window = this.getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        final String nurseName = getIntent().getStringExtra("nurseName");

        LinearLayout visitVerification = (LinearLayout) findViewById(R.id.layout_verify_visit);
        visitVerification.setOnClickListener(this);


        TextView verifyVisitCodeTextView = (TextView) findViewById(R.id.verify_visit_code);
        String code = getIntent().getStringExtra("visitVerificationCode");
        verifyVisitCodeTextView.setText(code);
        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(VisitVerificationActivity.this, REMINDER_TIME_OUT);
        startAnimation(verifyVisitCodeTextView, code);

        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).vibrate(vibratePattern, 0);

        GeneralUtils.startBeeping(this, new MediaPlayer.OnCompletionListener() {
            @Override
            public void onCompletion(MediaPlayer mp) {
                GeneralUtils.speak("Hi $ Verifying Nurse $ " + nurseName + "'s visit" + " " +
                        " $ PLease SHOW this code" + " " + " $ Tap ANYWHERE when its is done");

            }
        });

        ToggleButton muteUnmute = (ToggleButton) findViewById(R.id.toggleButton1);
        SoundUtils.checkStateOfSound(muteUnmute, VisitVerificationActivity.this);

        setupMuteUnmute();
        setupPhoneCalls();

    }

    @Override
    public void onClick(View v) {
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        GeneralUtils.stopSpeaking();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        finish();

    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    private void setupPhoneCalls() {
        ImageView settingsButton = (ImageView) findViewById(R.id.imageView_setting);

        if (settingsButton != null) {
            settingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    LogUtils.debug("onClick event detected on Reminder screen for phone calls");
                    Intent intent = new Intent(VisitVerificationActivity.this, PhoneCallsActivity.class);
                    startActivity(intent);

                }

            });
        }
    }

    private void setupMuteUnmute() {
        ToggleButton muteUnmute = (ToggleButton) findViewById(R.id.toggleButton1);
        muteUnmute.setOnClickListener(new View.OnClickListener() {
            @SuppressWarnings("deprecation")
            @Override
            public void onClick(View v) {

                boolean on = ((ToggleButton) v).isChecked();
                if (!on) {
                    SoundUtils.soundMute(VisitVerificationActivity.this);
                } else {
                    SoundUtils.soundUnMute(VisitVerificationActivity.this);
                }

            }
        });
    }

    @Override
    public void run() {
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(this);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        finish();
    }


    private void startAnimation(TextView nurseReminderTextView, String message) {
        final Animation animation = new AlphaAnimation(1, 0); // Change alpha from fully visible to invisible
        animation.setDuration(300); // duration
        animation.setInterpolator(new LinearInterpolator()); // do not alter animation rate
        animation.setRepeatCount(Animation.INFINITE); // Repeat animation infinitely
        animation.setRepeatMode(Animation.REVERSE); // Reverse animation at the end so the button will fade back in

        nurseReminderTextView.startAnimation(animation);
        nurseReminderTextView.setText(message);
    }
}
