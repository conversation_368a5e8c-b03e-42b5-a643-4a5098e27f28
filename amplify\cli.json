{"features": {"graphqltransformer": {"addmissingownerfields": true, "improvepluralization": true, "validatetypenamereservedwords": true, "useexperimentalpipelinedtransformer": false, "enableiterativegsiupdates": true, "secondarykeyasgsi": true, "skipoverridemutationinputtypes": true}, "frontend-ios": {"enablexcodeintegration": true}, "auth": {"enablecaseinsensitivity": true, "useinclusiveterminology": true, "breakcirculardependency": true}, "codegen": {"useappsyncmodelgenplugin": true, "usedocsgeneratorplugin": true, "usetypesgeneratorplugin": true, "cleangeneratedmodelsdirectory": true, "retaincasestyle": true, "addtimestampfields": true, "handlelistnullabilitytransparently": true, "emitauthprovider": true, "generateindexrules": true, "enabledartnullsafety": true}, "appsync": {"generategraphqlpermissions": true}}}