package com.watchrx.watchrxhealth.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.watchrx.watchrxhealth.R;
import com.watchrx.watchrxhealth.models.VitalDataModel;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

public class VitalDetailsAdapter extends RecyclerView.Adapter<VitalDetailsAdapter.MyViewHolder> {
    Context mContext;
    List<VitalDataModel> detailsModelsList;

    public interface OnItemClickListener {
        void onItemClick(VitalDataModel item);
    }

    private final VitalDetailsAdapter.OnItemClickListener listener;

    public VitalDetailsAdapter(Context context, List<VitalDataModel> detailsModelsList, VitalDetailsAdapter.OnItemClickListener listener) {
        this.mContext = context;
        this.detailsModelsList = detailsModelsList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public VitalDetailsAdapter.MyViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View listItem = LayoutInflater.from(parent.getContext()).inflate(R.layout.vital_details_lit_item, parent, false);
        return new VitalDetailsAdapter.MyViewHolder(listItem);
    }

    @Override
    public void onBindViewHolder(@NonNull VitalDetailsAdapter.MyViewHolder holder, int position) {
        VitalDataModel detailsModel = detailsModelsList.get(position);
        holder.vitalData.setText((detailsModel.getVitalData()));
        if (detailsModel.getVitalType().equalsIgnoreCase("Fasting Blood Sugar")) {
            String str = detailsModel.getVitalData() + "(" + detailsModel.getVitalType() + ")";
            holder.vitalData.setText(str);
        }
        holder.vitalDate.setText(getDateReadable(detailsModel.getDate()));
        if (detailsModel.getVitalType().equalsIgnoreCase("Sleep Monitor")) {
            holder.vitalData.setText(detailsModel.getVitalData().replaceAll(",", "\n"));
            holder.vitalDate.setText(getDateReadableForSleep(detailsModel.getDate()));
        }
        holder.bind(detailsModel, listener);
    }

    @Override
    public int getItemCount() {
        return detailsModelsList.size();
    }


    public static class MyViewHolder extends RecyclerView.ViewHolder {
        TextView vitalData;
        TextView vitalDate;

        public MyViewHolder(@NonNull View itemView) {
            super(itemView);
            vitalData = itemView.findViewById(R.id.vitalData);
            vitalDate = itemView.findViewById(R.id.vitalDate);
        }

        public void bind(final VitalDataModel item, final VitalDetailsAdapter.OnItemClickListener listener) {
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    listener.onItemClick(item);
                }
            });
        }
    }

    private String getDateReadable(String dateStr) {
        try {
            DateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            DateFormat targetFormat = new SimpleDateFormat("EEE MM-dd-yyyy hh:mm a", Locale.ENGLISH);
            Date date = originalFormat.parse(dateStr);
            assert date != null;
            return targetFormat.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return dateStr;
        }
    }

    private String getDateReadableForSleep(String dateStr) {
        try {
            DateFormat originalFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            DateFormat targetFormat = new SimpleDateFormat("EEE MM-dd-yyyy", Locale.ENGLISH);
            Date date = originalFormat.parse(dateStr);
            assert date != null;
            return targetFormat.format(date);
        } catch (Exception e) {
            e.printStackTrace();
            return dateStr;
        }
    }
}
