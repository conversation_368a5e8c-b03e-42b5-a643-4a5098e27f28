<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="@dimen/d_15_d"
    android:orientation="vertical"
    tools:context=".SleepMonitorActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/sleepStartTimeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/d_15_d"
            android:text="@string/sleep_start_time"
            android:fontFamily="@font/lato_bold"
            android:textSize="@dimen/d_25_s"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/sleepStartTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ems="10"
            android:fontFamily="@font/lato_bold"
            android:textSize="@dimen/d_25_s"
            android:textStyle="bold"
            />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/sleepEndTimeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/d_15_d"
            android:text="@string/sleep_end_time"
            android:fontFamily="@font/lato_bold"
            android:textSize="@dimen/d_25_s"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/sleepEndTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ems="10"
            android:fontFamily="@font/lato_bold"
            android:textSize="@dimen/d_25_s"
            android:textStyle="bold"
            android:autofillHints="" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/d_15_d"
        android:background="@color/background"
        android:orientation="horizontal">

        <Button
            android:id="@+id/updateSettings"
            android:layout_width="match_parent"
            android:background="@drawable/button_login"
            android:layout_height="wrap_content"
            android:text="@string/update_settings" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clDate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/d_10_d"
        android:background="@color/grey"
        app:layout_constraintTop_toBottomOf="@+id/vitalCardView">

        <ImageView
            android:id="@+id/before_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/d_20_d"
            android:padding="@dimen/d_10_d"
            android:rotation="180"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/after_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/d_20_d"
            android:padding="@dimen/d_10_d"
            android:src="@drawable/ic_baseline_navigate_next"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_bold"
            android:textColor="@color/black"
            android:textSize="25sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical|center_horizontal"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/vitalRv"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@+id/nav_view"
            app:layout_constraintTop_toBottomOf="@+id/clDate"
            tools:layout_editor_absoluteX="10dp" />

        <TextView
            android:id="@+id/nodata"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/lato_bold"
            android:padding="@dimen/d_10_d"
            android:text="@string/no_data_found"
            android:textColor="@color/black"
            android:textSize="@dimen/d_30_s"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ProgressBar
            android:id="@+id/progressbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </LinearLayout>
</LinearLayout>