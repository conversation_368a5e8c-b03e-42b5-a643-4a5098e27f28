package com.watchrx.watchrxhealth;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Vibrator;
import android.view.KeyEvent;
import android.view.View;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.watchrx.watchrxhealth.globals.Globals;
import com.watchrx.watchrxhealth.queue.NotifyNewEntryInQueue;
import com.watchrx.watchrxhealth.utils.GeneralUtils;

import java.util.Objects;

public class PedoMeterActivity extends AppCompatActivity implements Runnable, View.OnClickListener {
    private static final long REMINDER_TIME_OUT = 10 * 1000;
    private Handler timeoutHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pedometer_new);

        Objects.requireNonNull(getSupportActionBar()).setDisplayShowHomeEnabled(true);
        getSupportActionBar().setLogo(R.drawable.home_logo);
        getSupportActionBar().setDisplayUseLogoEnabled(true);
        getSupportActionBar().setTitle("WatchRx");

        int stepsCount = getIntent().getIntExtra("stepsCount", 0);

        timeoutHandler = new Handler();
        timeoutHandler.postDelayed(this, REMINDER_TIME_OUT);

        TextView stepsCountTv = findViewById(R.id.stepsCount);
        if (stepsCountTv != null) {
            stepsCountTv.setText("" + stepsCount);
        }
        TextView calorieTv = findViewById(R.id.calorieCount);
        if (calorieTv != null) {
            double calorie = stepsCount * 0.04;
            calorieTv.setText("" + calorie);
        }
    }


    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return (event.getKeyCode() == KeyEvent.KEYCODE_BACK) || super.dispatchKeyEvent(event);
    }

    @Override
    public void run() {
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(this);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        finish();
    }

    @Override
    public void onClick(View v) {
        GeneralUtils.stopBeeping();
        GeneralUtils.stopSpeaking();
        timeoutHandler.removeCallbacks(PedoMeterActivity.this);
        ((Vibrator) getSystemService(Context.VIBRATOR_SERVICE)).cancel();
        Globals.isScreenRunning = false;
        NotifyNewEntryInQueue.notifyNewEntryInQueue();
        PedoMeterActivity.this.finish();
    }
}
