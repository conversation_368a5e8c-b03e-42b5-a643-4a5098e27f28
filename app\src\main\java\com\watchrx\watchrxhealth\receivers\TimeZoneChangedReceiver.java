package com.watchrx.watchrxhealth.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.os.Environment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.watchrx.watchrxhealth.MainActivity;
import com.watchrx.watchrxhealth.utils.LogUtils;

import java.io.BufferedReader;
import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.TimeZone;


public class TimeZoneChangedReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String tz = TimeZone.getDefault().getDisplayName();
        if (tz.equalsIgnoreCase(getTimeFromMemory())) {
            LogUtils.debug("###$$Time zone change event detected.....but earlier Time is matched present time zone no need to restart the app ");
        } else {
            LogUtils.debug("@#@#@#TIMEZONE CHANGED EVENT DETECTED#@#@#@APP RESTARTING");
            Intent alertCount = new Intent(MainActivity.TIMEZONE_CHANGED);
            LocalBroadcastManager.getInstance(context).sendBroadcast(alertCount);
        }
    }

    public static String getTimeFromMemory() {
        String data = "";
        File rootPath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
        File file = new File(rootPath + "/WatchRx_DataBase/", "TimeZoneFile.txt");

        if (file.exists()) {
            try {
                InputStream fis = new FileInputStream(file);
                DataInputStream in = new DataInputStream(fis);
                BufferedReader br = new BufferedReader(new InputStreamReader(in));
                String strLine;
                while ((strLine = br.readLine()) != null) {
                    data = data + strLine;
                }
                in.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return data;
    }
}
