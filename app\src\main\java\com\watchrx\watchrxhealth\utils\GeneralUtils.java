package com.watchrx.watchrxhealth.utils;


import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;

import com.watchrx.watchrxhealth.constants.VoicePrompts;
import com.watchrx.watchrxhealth.db.PatientDetails;
import com.watchrx.watchrxhealth.globals.Globals;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Locale;
import java.util.regex.Pattern;

import static com.watchrx.watchrxhealth.constants.VoicePrompts.BEFORE_FOOD;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.MED_COUNT;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.NURSE_NAME;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.REGULAR_REMINDER;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.REGULAR_REMINDER_SPANISH;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.SILENCE;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.TIME_SLOT;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.VOICE_PROMPT_NURSE_REMINDER;
import static com.watchrx.watchrxhealth.constants.VoicePrompts.VOICE_PROMPT_NURSE_REMINDER_SPANISH;

public class GeneralUtils {

    public static void delay(long millisecs) {
        try {
            Thread.sleep(millisecs);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public static void startBeeping(Context context, MediaPlayer.OnCompletionListener completionListener) {
        try {
            if (Globals.player == null) {
                Globals.player = new MediaPlayer();
            }
            Globals.player.reset();
            AssetFileDescriptor afd = context.getAssets().openFd("beeps.mp3");
            Globals.player.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());

            Globals.player.setOnCompletionListener(completionListener);

            Globals.player.prepare();
            Globals.player.start();
        } catch (IOException e) {
            // ignore
        }
    }

    public static void stopBeeping() {
        if (Globals.player == null) {
            Globals.player = new MediaPlayer();
        }
        Globals.player.reset();
        LogUtils.debug("Stopping Beeping.");
    }

    @SuppressWarnings("deprecation")
    public static void speak(String message) {
        if (Globals.alreadySpeaking) {
            LogUtils.debug("Already speaking. Will exit and not queue this speech until the current one is finished.");
            return;
        }

        Globals.alreadySpeaking = true;

        String[] prompts = message.split(Pattern.quote(VoicePrompts.SILENCE));

        if (Globals.textToSpeech == null) {
            return;
        }

        Globals.textToSpeech.setOnUtteranceProgressListener(new UtteranceProgressListener() {
            @Override
            public void onStart(String utteranceId) {
                Globals.alreadySpeaking = true;
            }

            @Override
            public void onDone(String utteranceId) {
                Globals.alreadySpeaking = false;
                LogUtils.debug("Speech completed. alreadySpeaking: " + Globals.alreadySpeaking);
            }

            @Override
            public void onError(String utteranceId) {
                Globals.alreadySpeaking = false;
            }
        });
        HashMap<String, String> speechParams = new HashMap<>();
        speechParams.put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, "test");

        for (String prompt : prompts) {
            Globals.textToSpeech.speak(prompt, TextToSpeech.QUEUE_ADD, speechParams);
            Globals.textToSpeech.playSilence(250, TextToSpeech.QUEUE_ADD, speechParams);
        }
    }

    public static void stopSpeaking() {
        LogUtils.debug("Stopping speech if already speaking.");
        if (Globals.textToSpeech != null) {
            Globals.textToSpeech.stop();
            Globals.alreadySpeaking = false;
        }
    }

    public static String getVoicePromptForRegularReminder(String timeSlot, String beforeOrAfterFood, int medCount) {

        String message;
        if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
            message = REGULAR_REMINDER;

        } else {
            message = REGULAR_REMINDER_SPANISH;
        }
        message = message.replace(TIME_SLOT, getSpeakableTimeslot(timeSlot));
        message = message.replace(BEFORE_FOOD, getSpeakableBeforeAfterFood(beforeOrAfterFood));
        message = message.replace(MED_COUNT, Integer.toString(medCount));

        return message;
    }

    public static String getVoicePromptForNurseReminder(String nurseName, String timeSlot, String beforeOrAfterFood, int medCount) {

        String message;
        if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
            message = VOICE_PROMPT_NURSE_REMINDER;
        } else {
            message = VOICE_PROMPT_NURSE_REMINDER_SPANISH;
        }
        message = message.replace(TIME_SLOT, getSpeakableTimeslot(timeSlot));
        message = message.replace(BEFORE_FOOD, getSpeakableBeforeAfterFood(beforeOrAfterFood));
        message = message.replace(NURSE_NAME, nurseName);
        message = message.replace(MED_COUNT, Integer.toString(medCount));

        return message;
    }

    private static String getSpeakableTimeslot(String timeSlot) {
        if (timeSlot == null) {
            return VoicePrompts.SILENCE;
        }

        timeSlot = timeSlot.trim();
        if ("EarlyMorning".equalsIgnoreCase(timeSlot)) {
            return "early morning";
        } else if ("Breakfast".equalsIgnoreCase(timeSlot)) {
            return "Breakfast";
        } else if ("Lunch".equalsIgnoreCase(timeSlot)) {
            return "Lunch";
        } else if ("AfternoonSnack".equalsIgnoreCase(timeSlot)) {
            return "Afternoon Snack";
        } else if ("Dinner".equalsIgnoreCase(timeSlot)) {
            return "Dinner";
        } else if ("Bed".equalsIgnoreCase(timeSlot)) {
            return "Night";
        } else {
            return " ";
        }
    }

    private static String getSpeakableBeforeAfterFood(String beforeOrAfterFood) {
        if (beforeOrAfterFood == null) {
            return VoicePrompts.SILENCE;
        }

        beforeOrAfterFood = beforeOrAfterFood.trim();
        if ("Before".equalsIgnoreCase(beforeOrAfterFood)) {
            return "Before Food";
        } else if ("After".equalsIgnoreCase(beforeOrAfterFood)) {
            return "After Food";
        } else if ("Any".equalsIgnoreCase(beforeOrAfterFood)) {
            return "With Food";
        }

        return SILENCE;
    }

    public static String getHumanReadableDateTime() {
        Calendar c = Calendar.getInstance();

        // Welcome message
        String readableDateTime;
        if (PatientDetails.getFromDB().getAppLanguage().equalsIgnoreCase("English")) {
            readableDateTime = "Hello, it's ";
        } else {
            readableDateTime = "Hola, son las";
        }


        // Get time 12-hour format
        SimpleDateFormat df = new SimpleDateFormat("hh:mm aa", Locale.US);

        String s = df.format(c.getTime());

        readableDateTime += df.format(c.getTime());

        // Get day of week in full name
        df.applyPattern("EEEE");
        readableDateTime += ", " + df.format(c.getTime());

        // Get month in full name
        df.applyPattern("MMMM");
        readableDateTime += ", " + df.format(c.getTime());

        // Get date
        df.applyPattern("d");
        int dayInMonth = c.get(Calendar.DAY_OF_MONTH);
        readableDateTime += " " + dayInMonth;

        // Apply ordinal for date
        switch (dayInMonth) {
            case 1:
            case 21:
            case 31:
                readableDateTime += "st";
                break;

            case 2:
            case 22:
                readableDateTime += "nd";
                break;

            case 3:
            case 23:
                readableDateTime += "rd";
                break;

            default:
                readableDateTime += "th";
        }

        return readableDateTime;
    }

}
